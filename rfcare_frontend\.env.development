# 页面标题
VUE_APP_TITLE = 与安服务管理系统
# 接口请求地址，会设置到 axios 的 baseURL 参数上
# VUE_APP_API_ROOT = https://test.rfcare.cn/api
# VUE_APP_WS_API_ROOT =  wss://test.rfcare.cn
VUE_APP_API_ROOT = /api
VUE_APP_WS_API_ROOT = ws://localhost:8090
# 是否开启 CDN 支持，开启设置 ON，关闭设置 OFF
# 详情介绍请阅读 https://hooray.gitee.io/fantastic-admin/guide/cdn.html
VUE_APP_CDN = OFF
# 是否开启 gzip 压缩，开启设置 ON，关闭设置 OFF
VUE_APP_GZIP = OFF
# 调试工具，可设置 eruda 或 vconsole，如果不需要开启则留空
VUE_APP_DEBUG_TOOL =
