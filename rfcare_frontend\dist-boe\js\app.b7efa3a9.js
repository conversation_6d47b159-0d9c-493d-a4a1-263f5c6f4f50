(function(e){function t(t){for(var a,o,s=t[0],r=t[1],d=t[2],l=0,m=[];l<s.length;l++)o=s[l],Object.prototype.hasOwnProperty.call(i,o)&&i[o]&&m.push(i[o][0]),i[o]=0;for(a in r)Object.prototype.hasOwnProperty.call(r,a)&&(e[a]=r[a]);u&&u(t);while(m.length)m.shift()();return c.push.apply(c,d||[]),n()}function n(){for(var e,t=0;t<c.length;t++){for(var n=c[t],a=!0,o=1;o<n.length;o++){var s=n[o];0!==i[s]&&(a=!1)}a&&(c.splice(t--,1),e=r(r.s=n[0]))}return e}var a={},o={app:0},i={app:0},c=[];function s(e){return r.p+"js/"+({contact:"contact",daily:"daily",family:"family",multilevel_menu_example:"multilevel_menu_example",older:"older",person_butler:"person_butler",person_user:"person_user"}[e]||e)+"."+{"chunk-0b75a5ee":"9b4f8341","chunk-1057cd98":"ac6522c7","chunk-1512520a":"196094bf","chunk-18286672":"f1c4128a","chunk-1f24b7aa":"09fb188b","chunk-221bf3a5":"15d581a3","chunk-228cfa60":"c310c038","chunk-2a8db916":"f25fec37","chunk-2bbcde3c":"1bc8c9dc","chunk-2c13f016":"7ef9a8c4","chunk-2cc8b2d8":"21614444","chunk-2d0b16d0":"6b6ab991","chunk-2d0b59f4":"2ab61a54","chunk-2d0c1f0b":"efc5a536","chunk-2d0c7e72":"2a6f4587","chunk-2d0d6592":"d8279db3","chunk-2ff2a3e5":"d59a653b","chunk-34edc248":"c10058a3","chunk-36b86e8a":"66f60d0b","chunk-3d04d692":"a8c1fe51","chunk-3fb9bb48":"1b1159a0","chunk-4357e93a":"0d2f3663","chunk-4b706208":"80f1f158","chunk-53c4c40a":"19db76ce","chunk-5458a1a2":"967d4028","chunk-58b14f72":"3cbda026","chunk-5a23524a":"cd93a8a2","chunk-5b256ac2":"27bf1932","chunk-5c033668":"bb96fc9b","chunk-687551bc":"d3219894","chunk-695a0040":"2febda7d","chunk-6965e4aa":"75d17d44","chunk-69cc9a3b":"708fc3e8","chunk-6a4c9664":"898e3f62","chunk-6ec3bedc":"9c18c071","chunk-75a17c6b":"05c3d970","chunk-76b299c6":"43b3a47b","chunk-79963f93":"68d98b53","chunk-79e35d2b":"286dfa46","chunk-7e208fb6":"3e4db056","chunk-53e65238":"77c9b1ee","chunk-9e621516":"afaa20f8","chunk-7f68a383":"2fdd8a86","chunk-97d9faee":"d6746a0f","chunk-a7b27e72":"cf10daea","chunk-af6f243c":"037368bc","chunk-b66d24d4":"ab607770","chunk-c456c89c":"58ff9cc8","chunk-cbed2192":"79c3bffc","chunk-e047df54":"c45fbe5a","chunk-e0efc5a4":"8c2fb90a","chunk-e151fbec":"0d728c9b","chunk-f44f626a":"bd4b2781",contact:"0b9ef69c",daily:"cf64a570",family:"6277ddb7",multilevel_menu_example:"d53ea4f9",older:"ed47ebc6",person_butler:"82c88732",person_user:"ddc34430"}[e]+".js"}function r(t){if(a[t])return a[t].exports;var n=a[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,r),n.l=!0,n.exports}r.e=function(e){var t=[],n={"chunk-0b75a5ee":1,"chunk-1057cd98":1,"chunk-1512520a":1,"chunk-18286672":1,"chunk-1f24b7aa":1,"chunk-221bf3a5":1,"chunk-228cfa60":1,"chunk-2a8db916":1,"chunk-2bbcde3c":1,"chunk-2c13f016":1,"chunk-2cc8b2d8":1,"chunk-2ff2a3e5":1,"chunk-34edc248":1,"chunk-36b86e8a":1,"chunk-3d04d692":1,"chunk-3fb9bb48":1,"chunk-4357e93a":1,"chunk-4b706208":1,"chunk-53c4c40a":1,"chunk-5458a1a2":1,"chunk-58b14f72":1,"chunk-5a23524a":1,"chunk-5b256ac2":1,"chunk-5c033668":1,"chunk-687551bc":1,"chunk-695a0040":1,"chunk-6965e4aa":1,"chunk-69cc9a3b":1,"chunk-6a4c9664":1,"chunk-6ec3bedc":1,"chunk-75a17c6b":1,"chunk-76b299c6":1,"chunk-79963f93":1,"chunk-79e35d2b":1,"chunk-7e208fb6":1,"chunk-53e65238":1,"chunk-9e621516":1,"chunk-7f68a383":1,"chunk-97d9faee":1,"chunk-a7b27e72":1,"chunk-af6f243c":1,"chunk-b66d24d4":1,"chunk-c456c89c":1,"chunk-cbed2192":1,"chunk-e047df54":1,"chunk-e0efc5a4":1,"chunk-e151fbec":1,"chunk-f44f626a":1,contact:1,daily:1,family:1,multilevel_menu_example:1,older:1,person_butler:1,person_user:1};o[e]?t.push(o[e]):0!==o[e]&&n[e]&&t.push(o[e]=new Promise((function(t,n){for(var a="css/"+({contact:"contact",daily:"daily",family:"family",multilevel_menu_example:"multilevel_menu_example",older:"older",person_butler:"person_butler",person_user:"person_user"}[e]||e)+"."+{"chunk-0b75a5ee":"39ede1cc","chunk-1057cd98":"2f203f6e","chunk-1512520a":"a097b131","chunk-18286672":"7f23604e","chunk-1f24b7aa":"f5ae7af6","chunk-221bf3a5":"04abe73a","chunk-228cfa60":"cc026f2c","chunk-2a8db916":"9b920b15","chunk-2bbcde3c":"0c526763","chunk-2c13f016":"84a286e9","chunk-2cc8b2d8":"6226d6fb","chunk-2d0b16d0":"31d6cfe0","chunk-2d0b59f4":"31d6cfe0","chunk-2d0c1f0b":"31d6cfe0","chunk-2d0c7e72":"31d6cfe0","chunk-2d0d6592":"31d6cfe0","chunk-2ff2a3e5":"6e5307a9","chunk-34edc248":"5b709ac6","chunk-36b86e8a":"e743e821","chunk-3d04d692":"186ef33d","chunk-3fb9bb48":"d6f4fe61","chunk-4357e93a":"33871109","chunk-4b706208":"87561e8d","chunk-53c4c40a":"e9942556","chunk-5458a1a2":"a1208f95","chunk-58b14f72":"180902c0","chunk-5a23524a":"26f01c2d","chunk-5b256ac2":"63307a12","chunk-5c033668":"f9af3d3c","chunk-687551bc":"89c24305","chunk-695a0040":"bd4c4d5f","chunk-6965e4aa":"86ff13fa","chunk-69cc9a3b":"7907ea1d","chunk-6a4c9664":"6e4183d6","chunk-6ec3bedc":"1379a4f0","chunk-75a17c6b":"60f89f40","chunk-76b299c6":"552592c2","chunk-79963f93":"fba04411","chunk-79e35d2b":"869936fb","chunk-7e208fb6":"89c0276e","chunk-53e65238":"5bee183e","chunk-9e621516":"ca71252d","chunk-7f68a383":"21ae29c1","chunk-97d9faee":"308a6da4","chunk-a7b27e72":"a306d47d","chunk-af6f243c":"33ba5270","chunk-b66d24d4":"16c6f940","chunk-c456c89c":"d70d7eac","chunk-cbed2192":"11eb14d0","chunk-e047df54":"21a13edb","chunk-e0efc5a4":"fb49a61a","chunk-e151fbec":"2e3caa47","chunk-f44f626a":"70b75224",contact:"1dac8a99",daily:"6ac05603",family:"4b1092a9",multilevel_menu_example:"c1ad3a16",older:"d3e8c450",person_butler:"9d1364c7",person_user:"db295ec6"}[e]+".css",i=r.p+a,c=document.getElementsByTagName("link"),s=0;s<c.length;s++){var d=c[s],l=d.getAttribute("data-href")||d.getAttribute("href");if("stylesheet"===d.rel&&(l===a||l===i))return t()}var m=document.getElementsByTagName("style");for(s=0;s<m.length;s++){d=m[s],l=d.getAttribute("data-href");if(l===a||l===i)return t()}var u=document.createElement("link");u.rel="stylesheet",u.type="text/css",u.onload=t,u.onerror=function(t){var a=t&&t.target&&t.target.src||i,c=new Error("Loading CSS chunk "+e+" failed.\n("+a+")");c.code="CSS_CHUNK_LOAD_FAILED",c.request=a,delete o[e],u.parentNode.removeChild(u),n(c)},u.href=i;var h=document.getElementsByTagName("head")[0];h.appendChild(u)})).then((function(){o[e]=0})));var a=i[e];if(0!==a)if(a)t.push(a[2]);else{var c=new Promise((function(t,n){a=i[e]=[t,n]}));t.push(a[2]=c);var d,l=document.createElement("script");l.charset="utf-8",l.timeout=120,r.nc&&l.setAttribute("nonce",r.nc),l.src=s(e);var m=new Error;d=function(t){l.onerror=l.onload=null,clearTimeout(u);var n=i[e];if(0!==n){if(n){var a=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;m.message="Loading chunk "+e+" failed.\n("+a+": "+o+")",m.name="ChunkLoadError",m.type=a,m.request=o,n[1](m)}i[e]=void 0}};var u=setTimeout((function(){d({type:"timeout",target:l})}),12e4);l.onerror=l.onload=d,document.head.appendChild(l)}return Promise.all(t)},r.m=e,r.c=a,r.d=function(e,t,n){r.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},r.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},r.t=function(e,t){if(1&t&&(e=r(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)r.d(n,a,function(t){return e[t]}.bind(null,a));return n},r.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return r.d(t,"a",t),t},r.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},r.p="/",r.oe=function(e){throw console.error(e),e};var d=window["webpackJsonp"]=window["webpackJsonp"]||[],l=d.push.bind(d);d.push=t,d=d.slice();for(var m=0;m<d.length;m++)t(d[m]);var u=l;c.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"0069":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-spinning-circles",use:"icon-loading-spinning-circles-usage",viewBox:"0 0 58 58",content:'<symbol viewBox="0 0 58 58" xmlns="http://www.w3.org/2000/svg" id="icon-loading-spinning-circles">\r\n    <g fill="none" fill-rule="evenodd">\r\n        <g transform="translate(2 1)" stroke="#FFF" stroke-width="1.5">\r\n            <circle cx="42.601" cy="11.462" r="5" fill-opacity="1" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="1;0;0;0;0;0;0;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="49.063" cy="27.063" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;1;0;0;0;0;0;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="42.601" cy="42.663" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;0;1;0;0;0;0;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="27" cy="49.125" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;0;0;1;0;0;0;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="11.399" cy="42.663" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;0;0;0;1;0;0;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="4.938" cy="27.063" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;0;0;0;0;1;0;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="11.399" cy="11.462" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;0;0;0;0;0;1;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="27" cy="5" r="5" fill-opacity="0" fill="#fff">\r\n                <animate attributeName="fill-opacity" begin="0s" dur="1.3s" values="0;0;0;0;0;0;0;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n        </g>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},"0143":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-404",use:"icon-404-usage",viewBox:"0 0 859 586",content:'<symbol viewBox="0 0 859 586" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-404">\r\n    \x3c!-- Generator: Sketch 54 (76480) - https://sketchapp.com --\x3e\r\n    <title>500-彩色-01</title>\r\n    <desc>Created with Sketch.</desc>\r\n    <g id="icon-404_页面1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">\r\n        <g id="icon-404_500-彩色-01" transform="translate(-1.000000, -1.000000)">\r\n            <g id="icon-404_图层_2" transform="translate(1.000000, 1.000000)">\r\n                <ellipse id="icon-404_椭圆形" fill="#E7F4FE" fill-rule="nonzero" cx="452" cy="344" rx="406.2" ry="241.1" />\r\n                <g id="icon-404_编组" transform="translate(646.000000, 401.000000)" fill="#96CEF7" fill-rule="nonzero">\r\n                    <path d="M20.7,9.5 L20.7,5.7 C20.7,2.6 23.2,0.1 26.3,0.1 L119.2,0.1 C122.3,0.1 124.8,2.6 124.8,5.7 L124.8,9.6 C124.8,12.7 122.3,15.1 119.3,15.1 L78.4,15.1 C75.3,15.1 72.8,17.6 72.8,20.7 L72.8,24.8 C72.8,27.9 75.3,30.4 78.4,30.4 L86.2,30.4 C89.2,30.4 91.7,32.8 91.8,35.9 L91.9,42.5 C91.9,45.6 89.4,48.1 86.3,48.1 L6.2,48.1 C3.1,48.1 0.6,45.6 0.6,42.5 L0.6,36.1 C0.6,33 3.1,30.6 6.1,30.5 L42.5,30.3 C45.5,30.3 48,27.9 48,24.8 L48.1,20.4 C48.2,17.3 45.6,14.7 42.5,14.7 L26.4,14.9 C23.3,15.1 20.7,12.6 20.7,9.5 Z" id="icon-404_路径" />\r\n                    <circle id="icon-404_椭圆形" cx="144.6" cy="8.4" r="8.2" />\r\n                </g>\r\n                <g id="icon-404_编组" transform="translate(0.000000, 249.000000)" fill="#96CEF7" fill-rule="nonzero">\r\n                    <path d="M158.3,12 L158.3,7.5 C158.3,3.8 155.3,0.8 151.6,0.8 L40.3,0.8 C36.6,0.8 33.6,3.8 33.6,7.5 L33.6,12.1 C33.6,15.8 36.6,18.7 40.2,18.7 L89.2,18.7 C92.9,18.7 95.9,21.7 95.9,25.4 L95.9,30.3 C95.9,34 92.9,37 89.2,37 L79.9,37 C76.3,37 73.3,39.9 73.2,43.5 L73.1,51.4 C73,55.1 76,58.2 79.8,58.2 L175.8,58.2 C179.5,58.2 182.5,55.2 182.5,51.5 L182.5,43.9 C182.5,40.2 179.5,37.3 175.9,37.2 L132.3,36.9 C128.7,36.9 125.8,34 125.7,30.4 L125.6,25.2 C125.5,21.5 128.6,18.4 132.3,18.4 L151.6,18.6 C155.2,18.7 158.3,15.7 158.3,12 Z" id="icon-404_路径" />\r\n                    <circle id="icon-404_椭圆形" cx="9.9" cy="10.6" r="9.8" />\r\n                </g>\r\n                <g id="icon-404_XMLID_3_" transform="translate(306.000000, 202.000000)" fill-rule="nonzero">\r\n                    <g id="icon-404_编组">\r\n                        <path d="M207.5,152.8 C207.5,152.8 205.5,167.9 218.6,174.6 L218.6,174.6 C213.1,184.2 206.2,193 198.3,200.7 L198.3,200.7 C178.2,164.9 157.7,186.2 157.7,186.2 C157.7,186.2 145.1,159.7 126.7,164.3 C144.9,152.4 163.5,139 181.6,125.3 C186.5,152.3 207.5,152.8 207.5,152.8 Z" id="icon-404_路径" fill="#96CEF7" />\r\n                        <path d="M218.7,174.6 L218.7,174.6 C205.6,167.9 207.5,152.8 207.5,152.8 C207.5,152.8 186.5,152.2 181.7,125.2 C198.4,112.5 214.7,99.4 230,86.7 C232.6,96.3 234,106.4 233.9,116.9 C233.9,138 228.4,157.6 218.7,174.6 Z" id="icon-404_路径" fill="#309EED" />\r\n                        <path d="M198.4,200.7 L198.4,200.7 C177.4,221 148.9,233.5 117.4,233.5 C114.6,233.5 111.9,233.4 109.2,233.2 L109.2,233.2 C109.2,233.2 100.2,217.1 112.9,208 C112.9,208 96.3,190.5 113.4,172.8 C117.8,170.1 122.3,167.2 126.7,164.3 C145.1,159.7 157.7,186.2 157.7,186.2 C157.7,186.2 178.3,164.9 198.4,200.7 Z" id="icon-404_路径" fill="#309EED" />\r\n                        <path d="M230,86.8 C214.7,99.5 198.4,112.6 181.7,125.3 C181.6,124.8 181.5,124.3 181.5,123.8 C177.3,95.3 205.7,84.4 205.7,84.4 C205.7,84.4 200.7,63.6 224.7,71.6 C226.8,76.4 228.6,81.5 230,86.8 Z" id="icon-404_路径" fill="#309EED" />\r\n                        <path d="M181.4,123.7 C181.5,124.2 181.6,124.7 181.6,125.2 C163.5,139 144.9,152.3 126.7,164.2 C123.7,164.9 120.6,166.5 117.3,169.1 C115.8,170.3 114.5,171.5 113.3,172.7 C88.3,188.2 64.3,200.5 43.4,207 C18.6,186.7 2.4,156.3 0.9,122.1 C0.9,122.1 0.9,122 0.9,122 C1.3,111.4 2.9,101 5.6,91.1 C10.4,92.2 23.3,94.8 29.1,90.6 C36.4,85.4 28.4,142.4 59.7,142.4 C91,142.4 93.4,90.2 93.4,90.2 C93.4,90.2 120.1,91.4 106.7,61.1 C106.7,61.1 124.5,62.6 129.3,44.7 C132.7,32.2 127.1,19.1 116.5,11.6 C112.3,8.6 106.8,5.2 99.6,1.5 C105.3,0.6 111.2,0.2 117.2,0.2 C165.4,0.2 206.8,29.5 224.5,71.2 C200.5,63.2 205.5,84 205.5,84 C205.5,84 177.2,95.2 181.4,123.7 Z M152,117 C152,117 159.6,110.4 157.8,104.2 C156,98 147.5,104.9 147.5,104.9 C147.5,104.9 146.9,86.2 130.5,92.8 C114.1,99.5 125,117 125,117 C137.8,130.8 152,117 152,117 Z" id="icon-404_形状" fill="#96CEF7" />\r\n                        <path d="M157.8,104.2 C159.6,110.4 152,117 152,117 C152,117 137.7,130.8 125,117 C125,117 114.1,99.5 130.5,92.8 C146.9,86.1 147.5,104.9 147.5,104.9 C147.5,104.9 156,98 157.8,104.2 Z" id="icon-404_路径" fill="#309EED" />\r\n                        <path d="M43.6,207.1 C64.5,200.6 88.5,188.3 113.5,172.8 C96.4,190.5 113,208 113,208 C100.3,217.1 109.3,233.2 109.3,233.2 L109.3,233.2 C84.4,231.5 61.7,222 43.6,207.1 Z" id="icon-404_路径" fill="#96CEF7" />\r\n                        <path d="M129.5,44.9 C124.6,62.8 106.9,61.3 106.9,61.3 C120.2,91.6 93.6,90.4 93.6,90.4 C93.6,90.4 91.2,142.6 59.9,142.6 C28.6,142.6 36.6,85.6 29.3,90.8 C23.4,95 10.5,92.4 5.8,91.3 C12.5,66.2 26,44 44.2,26.5 C59.9,13.8 79,5.1 99.9,1.9 C107.1,5.6 112.6,9 116.8,12 C127.3,19.3 132.9,32.4 129.5,44.9 Z" id="icon-404_路径" fill="#309EED" />\r\n                    </g>\r\n                </g>\r\n                <circle id="icon-404_椭圆形" cx="423.4" cy="319" r="116.5" />\r\n                <path d="M487,419.1 C484.6,420.5 482.1,421.9 479.6,423.2" id="icon-404_路径" stroke="#000000" />\r\n                <path d="M459.6,429.9 C448.3,433.5 436.3,435.5 423.8,435.5 C359.5,435.5 307.3,383.3 307.3,319 C307.3,254.7 359.5,202.5 423.8,202.5 C488.1,202.5 540.3,254.6 540.3,319 C540.3,358.8 520.3,394 489.8,415" id="icon-404_路径" stroke="#000000" stroke-width="2" />\r\n                <path d="M445.5,470.4 C523.151236,470.4 586.1,407.451236 586.1,329.8 C586.1,252.148764 523.151236,189.2 445.5,189.2 C367.848764,189.2 304.9,252.148764 304.9,329.8 C304.9,407.451236 367.848764,470.4 445.5,470.4 Z" id="icon-404_椭圆形" stroke="#5C5C5C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="3.0047,3.0047" />\r\n                <path d="M441.4,524 C547.217758,524 633,438.217758 633,332.4 C633,226.582242 547.217758,140.8 441.4,140.8 C335.582242,140.8 249.8,226.582242 249.8,332.4 C249.8,438.217758 335.582242,524 441.4,524 Z" id="icon-404_椭圆形" stroke="#5C5C5C" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="2.994,2.994" />\r\n                <path d="M303.3,324 C303.3,324 236.4,436.7 338.2,411.2 C440,385.7 668.8,176 668.8,176" id="icon-404_路径" stroke="#EF706D" stroke-width="2" stroke-dasharray="3,3" />\r\n                <path d="M304.8,330.5 C304.8,330.5 254.3,415.6 331.2,396.3 C408.1,377.1 652.2,153.4 652.2,153.4" id="icon-404_路径" stroke="#EF706D" stroke-width="2" stroke-dasharray="3,3" />\r\n                <g id="icon-404_XMLID_1_" transform="translate(624.000000, 0.000000)">\r\n                    <g id="icon-404_编组" transform="translate(0.000000, 19.000000)" fill-rule="nonzero">\r\n                        <path d="M161.4,96 C179.2,122.5 186,145.3 176.8,154.4 C163.1,168.1 118.5,145.6 77.2,104.1 C59.5,86.3 45.3,68 35.9,51.7 C34.9,50 34,48.4 33.2,46.8 C31.4,43.5 29.9,40.3 28.6,37.2 C22.2,22.2 21.3,10.3 27.4,4.3 C35.9,-4.2 56.1,1.2 80.1,16.3 C95,25.6 111.2,38.7 127,54.6 C136.3,64 144.7,73.5 151.9,82.8 C155.3,87.3 158.5,91.7 161.4,96 Z M138.4,118.4 C143.7,113.1 130.7,91.5 109.3,70.1 C88,48.7 66.4,35.6 61.1,40.8 C55.8,46 68.9,67.7 90.2,89.1 C111.5,110.5 133.2,123.7 138.4,118.4 Z" id="icon-404_形状" fill="#AFD7A3" />\r\n                        <path d="M167.3,47.8 C172.5,60.9 174.4,78.5 161.3,96 C158.4,91.7 155.2,87.3 151.7,82.8 C172.5,69.5 165.6,44 165.6,44 C166.3,45.2 166.8,46.5 167.3,47.8 Z" id="icon-404_路径" fill="#E7B976" />\r\n                        <path d="M109.4,70.2 C130.7,91.6 143.8,113.2 138.5,118.5 C133.2,123.7 111.7,110.6 90.3,89.2 C68.9,67.8 55.9,46.2 61.2,40.9 C66.5,35.6 88,48.7 109.4,70.2 Z" id="icon-404_路径" fill="#E7B976" />\r\n                        <path d="M165.6,44 C165.6,44 172.4,69.5 151.8,82.8 C144.6,73.5 136.3,64 126.9,54.6 C111.1,38.7 94.8,25.7 80,16.3 C80,16.3 101.3,-3.4 138.4,16.1 C141.9,17.9 145.2,20.1 148.3,22.5 C150.6,24.3 152.8,26.3 154.8,28.4 C159.2,33 162.9,38.2 165.6,44 Z M132.3,24.4 C133,23.2 132.7,21.7 131.5,20.9 C130.3,20.2 128.7,20.5 128,21.7 C127.3,22.9 127.6,24.5 128.8,25.2 C130,25.9 131.6,25.6 132.3,24.4 Z M120.4,23.4 C121.9,23.5 123.3,22.7 123.7,21.3 C124,20.2 123.6,18.9 121.2,17.8 C117.1,15.8 109.4,16.3 106.2,16.6 C104.9,16.7 103.6,17.2 102.7,18 C99.6,20.6 102.7,23.8 106.3,23.1 C109.6,22.3 117.1,23 120.4,23.4 Z" id="icon-404_形状" fill="#FED37C" />\r\n                        <path d="M176,2.8 C177.2,3.5 154.8,28.4 154.8,28.4 C152.8,26.3 150.6,24.3 148.3,22.5 C148.3,22.6 174.8,2.1 176,2.8 Z" id="icon-404_路径" fill="#AFD7A3" />\r\n                        <path d="M131.5,20.9 C132.7,21.6 133.1,23.2 132.3,24.4 C131.6,25.6 130,26 128.8,25.2 C127.6,24.5 127.2,22.9 128,21.7 C128.7,20.5 130.3,20.2 131.5,20.9 Z" id="icon-404_路径" fill="#FFFFFF" />\r\n                        <path d="M123.7,21.2 C123.3,22.6 121.9,23.5 120.4,23.3 C117.1,22.9 109.6,22.2 106.3,22.9 C102.7,23.7 99.6,20.5 102.7,17.8 C103.7,16.9 104.9,16.5 106.2,16.4 C109.4,16.1 117.1,15.6 121.2,17.6 C123.6,18.9 124,20.2 123.7,21.2 Z" id="icon-404_路径" fill="#FFFFFF" />\r\n                        <path d="M3.9,161.6 C3.7,161.7 3.5,161.7 3.3,161.8 C2.2,159.9 1.1,158.1 0,156.2 C1.2,158 2.6,159.8 3.9,161.6 Z" id="icon-404_路径" fill="#D8D7D7" />\r\n                    </g>\r\n                    <g id="icon-404_编组" transform="translate(23.000000, 0.000000)" stroke="#000000" stroke-width="2">\r\n                        <path d="M10.2,65.9 C8.4,62.6 6.9,59.4 5.6,56.3" id="icon-404_路径" />\r\n                        <path d="M12.9,70.8 C11.9,69.1 11,67.5 10.2,65.9" id="icon-404_路径" />\r\n                        <path d="M5.6,56.3 C-0.8,41.3 -1.7,29.4 4.4,23.4 C12.9,14.9 33.1,20.3 57.1,35.4 C72,44.7 88.2,57.8 104,73.7 C113.3,83.1 121.7,92.6 128.9,101.9 C132.4,106.4 135.6,110.8 138.5,115.1 C156.3,141.6 163.1,164.4 153.9,173.5 C140.2,187.2 95.6,164.7 54.3,123.2 C36.6,105.4 22.4,87.1 13,70.8" id="icon-404_路径" />\r\n                        <path d="M57,35.4 C57,35.4 78.3,15.7 115.4,35.2 C118.9,37 122.2,39.2 125.3,41.6 C127.6,43.4 129.8,45.4 131.8,47.5 C136.2,52.1 139.9,57.3 142.7,63.1 C143.3,64.3 143.9,65.6 144.4,66.9 C149.6,80 151.5,97.6 138.4,115.1" id="icon-404_路径" />\r\n                        <path d="M115.4,137.4 C110.1,142.6 88.6,129.5 67.2,108.1 C45.8,86.7 32.8,65.1 38.1,59.8 C43.4,54.6 64.9,67.7 86.3,89.1 C107.7,110.6 120.7,132.2 115.4,137.4 Z" id="icon-404_路径" />\r\n                        <path d="M125.3,41.6 C125.3,41.6 151.8,21.2 153,21.9 C154.2,22.6 131.8,47.5 131.8,47.5" id="icon-404_路径" />\r\n                        <path d="M149.4,15.2 L155.5,0.8" id="icon-404_路径" />\r\n                        <path d="M156.8,19.8 L166.9,10.1" id="icon-404_路径" />\r\n                        <path d="M159.3,25.7 L172.2,25" id="icon-404_路径" />\r\n                    </g>\r\n                </g>\r\n                <path d="M624,70.7 C624,70.7 631.6,66.6 639,70.7" id="icon-404_路径" stroke="#FFFFFF" />\r\n                <path d="M657.2,22.8 C657.2,22.8 671.2,17.5 716.5,48.2" id="icon-404_路径" stroke="#FFFFFF" stroke-linecap="round" stroke-linejoin="round" />\r\n                <path d="M720.5,51 C720.5,51 776.5,94.6 796.4,142.7" id="icon-404_路径" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />\r\n            </g>\r\n        </g>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},"0284":function(e,t,n){(function(e){(function(){var t="https://www.rfcare.cn/api/";e.extend({ctxOutcall:t,mshost:"**************",msport:"8083"})})()}).call(this,n("1157"))},"030a":function(e,t,n){"use strict";n("2fa1")},"0781":function(e,t,n){"use strict";n.r(t);var a=n("5530"),o=n("83d6"),i=Object(a["a"])(Object(a["a"])({},o["a"]),{},{sidebarCollapseLastStatus:o["a"].sidebarCollapse,mode:"pc",title:""}),c={},s={},r={setMode:function(e,t){e.enableMobileAdaptation&&t<992?e.mode="mobile":e.mode="pc"},setTitle:function(e,t){e.title=t},toggleSidebarCollapse:function(e){e.sidebarCollapse=!e.sidebarCollapse,"pc"==e.mode&&(e.sidebarCollapseLastStatus=!e.sidebarCollapseLastStatus)},setDefaultLang:function(e,t){e.defaultLang=t},updateThemeSetting:function(e,t){Object.assign(e,t)}};t["default"]={namespaced:!0,state:i,actions:s,getters:c,mutations:r}},"0835":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"upload-container"},[n("el-upload",{attrs:{"show-file-list":!1,headers:e.headers,action:e.action,data:e.data,name:e.name,"before-upload":e.beforeUpload,"on-progress":e.onProgress,"on-success":e.onSuccess,drag:""}},[""===e.url?n("el-image",{style:"width:"+e.width+"px;height:"+e.height+"px;",attrs:{src:""===e.url?e.placeholder:e.url,fit:"fill"}},[n("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[n("i",{staticClass:"el-icon-plus"})])]):n("div",{staticClass:"image"},[n("el-image",{style:"width:"+e.width+"px;height:"+e.height+"px;",attrs:{src:e.url,fit:"fill"}}),n("div",{staticClass:"mask"},[n("div",{staticClass:"actions"},[n("span",{attrs:{title:"预览"},on:{click:function(t){t.stopPropagation(),e.imageViewerVisible=!0}}},[n("i",{staticClass:"el-icon-zoom-in"})]),n("span",{attrs:{title:"移除"},on:{click:function(t){return t.stopPropagation(),e.remove(t)}}},[n("i",{staticClass:"el-icon-delete"})])])])],1),n("div",{directives:[{name:"show",rawName:"v-show",value:e.progress.percent,expression:"progress.percent"}],staticClass:"progress",style:"width:"+e.width+"px;height:"+e.height+"px;"},[n("el-image",{style:"width:"+e.width+"px;height:"+e.height+"px;",attrs:{src:e.progress.preview,fit:"fill"}}),n("el-progress",{attrs:{type:"circle",width:.8*Math.min(e.width,e.height),percentage:e.progress.percent}})],1)],1),e.notip?e._e():n("div",{staticClass:"el-upload__tip"},[n("div",{staticStyle:{display:"inline-block"}},[n("el-alert",{attrs:{title:"上传图片支持 "+e.ext.join(" / ")+" 格式，且图片大小不超过 "+e.size+"MB，建议图片尺寸为 "+e.width+"*"+e.height,type:"info","show-icon":"",closable:!1}})],1)]),e.imageViewerVisible?n("el-image-viewer",{attrs:{"on-close":function(){e.imageViewerVisible=!1},"url-list":[e.url]}}):e._e()],1)},o=[],i=(n("a9e3"),n("ac1f"),n("1276"),n("b0c0"),n("a15b"),n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("08a9")),c={name:"ImageUpload",components:{ElImageViewer:i["a"]},props:{action:{type:String,required:!0},headers:{type:Object,default:function(){}},data:{type:Object,default:function(){}},name:{type:String,default:"file"},url:{type:String,default:""},size:{type:Number,default:2},width:{type:Number,default:150},height:{type:Number,default:150},placeholder:{type:String,default:""},notip:{type:Boolean,default:!1},ext:{type:Array,default:function(){return["jpg","png","gif","bmp"]}}},data:function(){return{imageViewerVisible:!1,progress:{preview:"",percent:0}}},methods:{remove:function(){this.$emit("on-remove",{}),this.$emit("update:url","")},beforeUpload:function(e){var t=e.name.split("."),n=t[t.length-1],a=this.ext.indexOf(n)>=0,o=e.size/1024/1024<this.size;return a||this.$message.error("上传图片只支持 ".concat(this.ext.join(" / ")," 格式！")),o||this.$message.error("上传图片大小不能超过 ".concat(this.size,"MB！")),a&&o&&(this.progress.preview=URL.createObjectURL(e)),a&&o},onProgress:function(e){var t=this;this.progress.percent=~~e.percent,100==this.progress.percent&&setTimeout((function(){t.progress.preview="",t.progress.percent=0}),1e3)},onSuccess:function(e){this.$emit("on-success",e)}}},s=c,r=(n("efc0"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"615360bf",null);t["default"]=d.exports},"089a":function(e,t,n){},"08ba":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{"page-main":!0,"is-collaspe":e.collaspeData},style:{height:e.collaspeData?e.height:""}},[e.title?n("div",{staticClass:"title-container"},[e._v(e._s(e.title))]):e._e(),e._t("default"),e.collaspeData?n("div",{staticClass:"collaspe",attrs:{title:"展开"},on:{click:e.uncollaspe}},[n("i",{staticClass:"el-icon-arrow-down"})]):e._e()],2)},o=[],i={name:"PageMain",props:{title:{type:String,default:""},collaspe:{type:Boolean,default:!1},height:{type:String,default:""}},data:function(){return{collaspeData:this.collaspe}},methods:{uncollaspe:function(){this.collaspeData=!1}}},c=i,s=(n("5e7a"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"2764b560",null);t["default"]=r.exports},"095c":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"editor"},[n("TinymceEditor",{attrs:{init:e.completeSetting,disabled:e.disabled},model:{value:e.myValue,callback:function(t){e.myValue=t},expression:"myValue"}})],1)},o=[],i=n("e562"),c=n.n(i),s=n("ca72"),r=(n("030f"),n("fc39"),n("2fec"),n("7193"),n("2d33"),n("f557"),n("3154"),n("2b07"),n("4ea8"),n("8863"),n("4bd0"),n("4237"),n("84ec"),n("3aea"),n("0a9d"),n("07d1"),n("34de0"),n("9434"),n("64d8"),n("62e5"),{name:"Editor",components:{TinymceEditor:s["a"]},props:{value:{type:String,default:""},setting:{type:Object,default:function(){}},disabled:{type:Boolean,default:!1}},data:function(){return{defaultSetting:{convert_urls:!1,relative_urls:!1,remove_script_host:!1,language_url:"tinymce/langs/zh_CN.js",language:"zh_CN",skin_url:"tinymce/skins/ui/oxide",min_height:250,max_height:600,selector:"textarea",plugins:"autolink autoresize contextmenu fullscreen hr image imagetools insertdatetime link lists media preview table textcolor wordcount code searchreplace",toolbar:"undo redo | formatselect | bold italic strikethrough forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | hr link image media table insertdatetime searchreplace removeformat | preview code fullscreen",branding:!1,menubar:!1,toolbar_mode:"sliding",insertdatetime_formats:["%Y年%m月%d日","%H点%M分%S秒","%Y-%m-%d","%H:%M:%S"],images_upload_handler:function(e,t){var n="data:image/jpeg;base64,"+e.base64();t(n)}},myValue:this.value}},computed:{completeSetting:function(){return Object.assign(this.defaultSetting,this.setting)}},watch:{myValue:function(e){this.$emit("input",e)},value:function(e){this.myValue=e}},mounted:function(){c.a.init({})}}),d=r,l=(n("491a"),n("2877")),m=Object(l["a"])(d,a,o,!1,null,"59063933",null);t["default"]=m.exports},"09af":function(e,t,n){"use strict";n("e635")},"0c6e":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-three-dots",use:"icon-loading-three-dots-usage",viewBox:"0 0 120 30",content:'<symbol viewBox="0 0 120 30" xmlns="http://www.w3.org/2000/svg" fill="#fff" id="icon-loading-three-dots">\r\n    <circle cx="15" cy="15" r="15">\r\n        <animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="60" cy="15" r="9" fill-opacity="0.3">\r\n        <animate attributeName="r" from="9" to="9" begin="0s" dur="0.8s" values="9;15;9" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="fill-opacity" from="0.5" to="0.5" begin="0s" dur="0.8s" values=".5;1;.5" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="105" cy="15" r="15">\r\n        <animate attributeName="r" from="15" to="15" begin="0s" dur="0.8s" values="15;9;15" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="fill-opacity" from="1" to="1" begin="0s" dur="0.8s" values="1;.5;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n</symbol>'});c.a.add(s);t["default"]=s},"0d07":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{actionbar:!0,shadow:!e.isBottom},attrs:{"data-fixed-calc-width":""}},[e._t("default")],2)},o=[],i={name:"FixedActionBar",data:function(){return{isBottom:!0}},mounted:function(){this.onScroll(),window.addEventListener("scroll",this.onScroll)},destroyed:function(){window.removeEventListener("scroll",this.onScroll)},methods:{onScroll:function(){var e=document.documentElement.scrollTop||document.body.scrollTop,t=document.documentElement.clientHeight||document.body.clientHeight,n=document.documentElement.scrollHeight||document.body.scrollHeight;Math.ceil(e+t)>=n?this.isBottom=!0:this.isBottom=!1}}},c=i,s=(n("1564"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"4e6f57c0",null);t["default"]=r.exports},"0ef0":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("bm-overlay",{ref:"customOverlay",class:{sample:!0},style:e.pointColor,attrs:{pane:"labelPane"},on:{draw:e.draw}},[n("div",[e._v("用户数: "),n("span",[e._v(e._s(e.content.userNum||0))])]),n("div",[e._v("安全管家: "),n("span",[e._v(e._s(e.content.butlerNum||0))])])])},o=[],i=(n("d81d"),n("bd0c")),c={name:"BMapDot",components:{BmOverlay:i["BmOverlay"]},props:["content","position","color"],data:function(){return{pointColor:""}},watch:{position:{handler:function(){this.$refs.customOverlay.reload()},deep:!0}},mounted:function(){this.pointColor=this.color},methods:{draw:function(e){var t=e.el,n=e.BMap,a=e.map,o=this.position,i=o.lng,c=o.lat,s=a.pointToOverlayPixel(new n.Point(i,c));t.style.left=s.x-170+"px",t.style.top=s.y-70+"px"}}},s=c,r=(n("030a"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"5e071f61",null);t["default"]=d.exports},"0f1a":function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return o}));n("ac1f"),n("1276");var a=n("56d7"),o=function(t){t.loadSeatInfo=function(){e.ajax({headers:{token:localStorage.getItem("fa_token")},type:"GET",url:e.ctxOutcall+"/bms/outcall/koala/getAgentInfo",contentType:"application/json;charset=utf-8",dataType:"json",success:function(e){if("00000"==e.status){var n=e.data,a=n.entId,o=n.agentId,i=a+"_"+o,c=a+"_8000,";t.data=e.data;var s=new Array;if(""!=c)for(var r=c.split(","),d=0;d<r.length;d++)""!=r[d]&&s.push(r[d]);console.log(i,o,s),cti.Agent.getInstance().init(i,null,o,s),cti.doOpen()}},error:function(e){errorToast(e.msg)}})},t.call=function(e){cti.agentLogout(),cti.agentLogin(),cti.makeCall(e)},t.redialAPI=function(e){var t=cti.Line.getInstance().getCurrentLineId();t&&"0"!=t&&cti.releaseCall(t),cti.agentLogout(),cti.agentLogin(),cti.makeCall(e)},t.hangUpAPI=function(){var e=cti.Line.getInstance().getCurrentLineId();cti.releaseCall(e),cti.agentLogout()};var n={};return t.setCallData=function(e){n=e},t.setCCKoala=function(t){"580"==t.messageId&&"2"!=t.deviceState&&(console.log("登录成功"+JSON.stringify(t)),a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","AUTH_LOGIN_SUCCESS")),"580"==t.messageId&&"2"==t.deviceState&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","NOT_RUNING_SIP"),console.log("登录失败，SIP软件没开启"+JSON.stringify(t)),e("#outCallBtn").attr("disabled",!1),e("#hangupBtn").attr("disabled",!0),e("#saveResultBtn").attr("disabled",!0)),"EVENT_WEBAGENT_LOGIN_FAIL"==t.type&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","AUTH_LOGIN_FAILURE"),console.log("登录失败"+JSON.stringify(t)),e("#outCallBtn").attr("disabled",!1),e("#hangupBtn").attr("disabled",!0),e("#saveResultBtn").attr("disabled",!0)),"581"==t.messageId&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","AUTH_LOGOUT_SUCCESS"),console.log("坐席登出成功"+JSON.stringify(t))),"EVENT_WEBAGENT_LOGOUT_FAIL"==t.type&&(console.log("登出失败"+JSON.stringify(t)),a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","AUTH_LOGOUT_FAILURE")),"EVENT_TP_DISCONNECT"==t.type&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","SEAT_HANG_UP"),console.log("坐席挂断事件"+JSON.stringify(t)),e("#outCallBtn").attr("disabled",!1),e("#hangupBtn").attr("disabled",!0),e("#saveResultBtn").attr("disabled",!1)),"515"==t.messageId&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","CUSTOMER_HANG_UP"),console.log("客户挂断事件"+JSON.stringify(t)),e("#outCallBtn").attr("disabled",!1),e("#hangupBtn").attr("disabled",!0),e("#saveResultBtn").attr("disabled",!1)),"505"==t.messageId&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","SEAT_SHOCK_RING"),console.log("外呼坐席振铃事件"+JSON.stringify(t)),e("#outCallBtn").attr("disabled",!0),e("#hangupBtn").attr("disabled",!1),e("#saveResultBtn").attr("disabled",!0)),"506"==t.messageId&&(a["default"].$eventBus.$emit("ServicePlatformCallDialog-Handle","SEAT_COLLECTED"),console.log("外呼坐席接通事件"+JSON.stringify(t)),e("#outCallBtn").attr("disabled",!0),e("#hangupBtn").attr("disabled",!1),e("#saveResultBtn").attr("disabled",!0))},t.koalaOptLog=function(t,a){var o=JSON.parse(JSON.stringify(n));"contact"===o.type?o.type=1:"older"===o.type?o.type=3:"chanmb"===o.type?o.type=2:o.type;var i={apiMethod:a,agentId:cti.Agent.getInstance().getAgentID(),result:t,phone:o.contactPhone,orgId:o.orgId,stationId:o.stationId,orderId:o.orderId,calledId:o.contactId,calledType:o.type,houseId:o.houseId,olderId:o.olderId,calledName:o.contactName,relationTypeDesc:o.relationTypeDesc,contactLevel:o.contactLevel};e.ajax({url:e.ctxOutcall+"/bms/outcall/koala/addOptLog",headers:{token:localStorage.getItem("fa_token")},type:"POST",contentType:"application/json;charset=utf-8",data:JSON.stringify(i),dataType:"json",success:function(e){"00000"==e.status?console.log("日志记录成功",a):console.log("*****日志记录失败*****",a)},error:function(t){e(".notice").html("Error:"+t)}})},t.saveCallResult=function(){var t={phone:e("#outCallForm #callNumber").val(),orgId:e("#outCallForm #orgId").val(),stationId:e("#outCallForm #stationId").val(),olderId:e("#outCallForm #olderId").val(),orderId:e("#outCallForm #orderId").val(),calledId:e("#outCallForm #calledId").val(),calledType:e("#outCallForm #calledType").val(),callStatus:e("#outCallForm #callStatus").val(),descMsg:e("#outCallForm #descMsg").val()};e.ajax({url:e.ctxOutcall+"/bms/outcall/koala/addCallResult",headers:{token:localStorage.getItem("fa_token")},type:"POST",contentType:"application/json;charset=utf-8",data:JSON.stringify(t),dataType:"json",success:function(e){"00000"==e.status?(console.log("保存外呼结果成功"),alert("保存成功"),window.opener=null,window.open("","_self"),window.close()):console.log("*****保存外呼结果失败*****")},error:function(t){e(".notice").html("Error:"+t)}})},t}(window.OutCallClass||{})}).call(this,n("1157"))},"0f68":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("footer",{staticClass:"copyright"},[e._v(" Copyright © "+e._s(e.$store.state.settings.copyrightDates)+" "),n("component",e._b({},"component",e.linkProps(),!1),[e._v(e._s(e.$store.state.settings.copyrightCompany))])],1)},o=[],i={name:"Copyright",methods:{linkProps:function(){return this.$store.state.settings.copyrightWebsite?{is:"a",href:this.$store.state.settings.copyrightWebsite,target:"_blank",rel:"noopener"}:{is:"span"}}}},c=i,s=(n("c6db"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"c3043396",null);t["default"]=r.exports},"0f9a":function(e,t,n){"use strict";n.r(t);var a=n("5530"),o=(n("d3b7"),n("4de4"),n("2b61")),i=n("365c"),c={account:o["a"].local.get("account")||"",token:o["a"].local.get("token")||"",failure_time:o["a"].local.get("failure_time")||"",member:JSON.parse(o["a"].local.get("user"))||{},permissions:[]},s={isLogin:function(e){var t=!1;if(e.token){var n=Date.parse(new Date);n<1e3*e.failure_time&&(t=!0)}return t}},r={login:function(e,t){var n=e.commit;return new Promise((function(e,o){var c=Object(a["a"])({},t);c["orgCode"]="test",i["b"].post("bms/login",c).then((function(t){n("setUserData",t.data),e()})).catch((function(e){console.log(345,e),o(e)}))}))},getCaptcha:function(e,t){e.commit;return new Promise((function(e,n){i["b"].get("bms/getCaptcha?account=".concat(t)).then((function(t){e(t)})).catch((function(e){n(e)}))}))},resetPassword:function(e,t){e.commit;return new Promise((function(e,n){i["b"].post("bms/resetPassword",t).then((function(t){e(t)})).catch((function(e){n(e)}))}))},logout:function(e){var t=e.commit;t("removeUserData"),t("menu/invalidRoutes",null,{root:!0}),t("tabbar/clean",null,{root:!0})},getPermissions:function(e){var t=e.state,n=e.commit;return new Promise((function(e){var a=t.member.role,o=[];"system"===a?o=["self.system","device.list","station.station","station.org","service","service.feedback","memberCard.delivery","report"]:"org"===a?o=["self.org","event.unprocessed","event.my","event.query","device.list","person.user","person.butler","station.station","person.station","bigscreen.org","service.org","service.feedback","family.family","family.older","family.contact","report","tplhouse","stationOlder","stationHouse"]:"station"===a?o=["self.station","event.unprocessed","event.my","event.query","device.list","person.user","person.butler","report","tplhouse","stationOlder","stationHouse"]:"chanmb"===a?o=["self.chanmb","event.unprocessed","event.my","event.query","report"]:"seat"===a&&(o=["self.seat","event.unprocessed","event.my","event.query"]),"2"!=t.member.systemVersion&&(o=o.filter((function(e){return"report"!==e&&"tplhouse"!==e&&"stationOlder"!==e&&"stationHouse"!==e&&"person.butler"!==e&&"bigscreen.station"!==e}))),n("setPermissions",o),e(o)}))},getListFirstOrgsByRole:function(){return new Promise((function(e){i["b"].get("/safety-server/systemOrg/listFirstOrgsByRole",{}).then((function(t){e(t.data)}))}))},getListSecondOrgsByRole:function(e,t){return new Promise((function(e){i["b"].get("/safety-server/systemOrg/listSecondOrgsByRole?firstOrgcode=".concat(t)).then((function(t){e(t)}))}))},getListThirdOrgsByRole:function(e,t){return new Promise((function(e){i["b"].get("/safety-server/systemOrg/listThirdOrgsByRole?secondOrgcode=".concat(t)).then((function(t){e(t)}))}))},setSystemInfo:function(e,t){var n=e.commit;n("setSysInfos",t)}},d={setUserData:function(e,t){o["a"].local.set("account",t.user.userName),o["a"].local.set("token",t.token),o["a"].local.set("failure_time",t.failure_time||**********),o["a"].local.set("user",JSON.stringify(t.user||{})),e.account=t.user.phone,e.token=t.token,e.failure_time=t.failure_time||**********,e.member=t.user},removeUserData:function(e){o["a"].local.remove("account"),o["a"].local.remove("token"),o["a"].local.remove("failure_time"),o["a"].local.remove("user"),e.account="",e.token="",e.failure_time="",e.member={}},setPermissions:function(e,t){e.permissions=t},setSysInfos:function(e,t){if(e.member&&e.member.id&&"system"!==e.member.role){e.member.logo=t.logo,e.member.systemName=t.systemName;var n=JSON.parse(o["a"].local.get("user"))||{};n.systemName===t.systemName&&n.logo===t.logo||(n.systemName=t.systemName,n.logo=t.logo,o["a"].local.set("user",JSON.stringify(n)))}}};t["default"]={namespaced:!0,state:c,actions:r,getters:s,mutations:d}},"10d6":function(module,exports,__webpack_require__){(function(jQuery){var _typeof=__webpack_require__("7037").default;__webpack_require__("bf19"),__webpack_require__("a15b"),__webpack_require__("ac1f"),__webpack_require__("5319"),__webpack_require__("466d"),__webpack_require__("d3b7"),__webpack_require__("25f0"),function($){var escapeable=/["\\\x00-\x1f\x7f-\x9f]/g,meta={"\b":"\\b","\t":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"};$.toJSON="object"===("undefined"===typeof JSON?"undefined":_typeof(JSON))&&JSON.stringify?JSON.stringify:function(e){if(null===e)return"null";var t=_typeof(e);if("undefined"!==t){if("number"===t||"boolean"===t)return""+e;if("string"===t)return $.quoteString(e);if("object"===t){if("function"===typeof e.toJSON)return $.toJSON(e.toJSON());if(e.constructor===Date){var n=e.getUTCMonth()+1,a=e.getUTCDate(),o=e.getUTCFullYear(),i=e.getUTCHours(),c=e.getUTCMinutes(),s=e.getUTCSeconds(),r=e.getUTCMilliseconds();return n<10&&(n="0"+n),a<10&&(a="0"+a),i<10&&(i="0"+i),c<10&&(c="0"+c),s<10&&(s="0"+s),r<100&&(r="0"+r),r<10&&(r="0"+r),'"'+o+"-"+n+"-"+a+"T"+i+":"+c+":"+s+"."+r+'Z"'}if(e.constructor===Array){for(var d=[],l=0;l<e.length;l++)d.push($.toJSON(e[l])||"null");return"["+d.join(",")+"]"}var m,u,h=[];for(var f in e){if(t=_typeof(f),"number"===t)m='"'+f+'"';else{if("string"!==t)continue;m=$.quoteString(f)}t=_typeof(e[f]),"function"!==t&&"undefined"!==t&&(u=$.toJSON(e[f]),h.push(m+":"+u))}return"{"+h.join(",")+"}"}}},$.evalJSON="object"===("undefined"===typeof JSON?"undefined":_typeof(JSON))&&JSON.parse?JSON.parse:function(src){return eval("("+src+")")},$.secureEvalJSON="object"===("undefined"===typeof JSON?"undefined":_typeof(JSON))&&JSON.parse?JSON.parse:function(src){var filtered=src.replace(/\\["\\\/bfnrtu]/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"");if(/^[\],:{}\s]*$/.test(filtered))return eval("("+src+")");throw new SyntaxError("Error parsing JSON, source is not valid.")},$.quoteString=function(e){return e.match(escapeable)?'"'+e.replace(escapeable,(function(e){var t=meta[e];return"string"===typeof t?t:(t=e.charCodeAt(),"\\u00"+Math.floor(t/16).toString(16)+(t%16).toString(16))}))+'"':'"'+e+'"'}}(jQuery)}).call(this,__webpack_require__("1157"))},"11ef":function(e,t,n){"use strict";n("665e")},1307:function(e,t,n){"use strict";n.r(t);var a=n("2909"),o=n("1da1"),i=n("5530"),c=(n("96cf"),n("159b"),n("99af"),n("d81d"),n("d3b7"),n("4de4"),n("4260"));function s(e,t){var n=!1;return n=!t.meta||!t.meta.auth||e.some((function(e){return"string"==typeof t.meta.auth?t.meta.auth===e:t.meta.auth.some((function(t){return t===e}))})),n}function r(e,t){var n=[];return e.forEach((function(e){var a=Object(i["a"])({},e);s(t,a)&&(a.children?(a.children=r(a.children,t),a.children.length&&n.push(a)):n.push(a))})),n}function d(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"",a=[];return e.forEach((function(e){var o=Object(i["a"])({},e);if(o.children){var s="";""==n?s=o.path:""!=o.path&&(s="".concat(n,"/").concat(o.path));var r=Object(c["c"])(t);!1!==e.meta.breadcrumb&&r.push({path:s,title:e.meta.title,i18n:e.meta.i18n});var l=Object(c["c"])(e);l.path=s,l.meta.breadcrumbNeste=r,delete l.children,a.push(l);var m=d(o.children,r,s);m.map((function(e){a.some((function(t){return t.path==e.path}))?a.forEach((function(t,n){t.path==e.path&&(a[n]=e)})):a.push(e)}))}else{""!=n&&(""!=o.path?o.path="".concat(n,"/").concat(o.path):o.path=n);var u=Object(c["c"])(t);!1!==o.meta.breadcrumb&&u.push({path:o.path,title:o.meta.title,i18n:o.meta.i18n}),o.meta.breadcrumbNeste=u,a.push(o)}})),a}var l={isGenerate:!1,routes:[],headerActived:0},m={sidebarRoutes:function(e){return e.routes.length>0?e.routes[e.headerActived].children:[]}},u={generateRoutes:function(e,t){var n=e.rootState,i=e.dispatch,c=e.commit;return new Promise(function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(o){var s,l,m;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!n.settings.openPermission){e.next=7;break}return e.next=3,i("user/getPermissions",null,{root:!0});case 3:l=e.sent,s=r(t.asyncRoutes,l),e.next=8;break;case 7:s=t.asyncRoutes;case 8:c("setRoutes",s),c("setHeaderActived",t.currentPath),m=[],s.map((function(e){m.push.apply(m,Object(a["a"])(e.children))})),n.settings.enableFlatRoutes&&m.map((function(e){e.children&&(e.children=d(e.children,[{path:e.path,title:e.meta.title,i18n:e.meta.i18n}]))})),o(m);case 14:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}())}},h={invalidRoutes:function(e){e.isGenerate=!1,e.headerActived=0},setRoutes:function(e,t){e.isGenerate=!0;var n=Object(c["c"])(t);e.routes=n.filter((function(e){return 0!=e.children.length}))},setHeaderActived:function(e,t){e.routes.map((function(n,a){n.children.some((function(e){return 0===t.indexOf(e.path+"/")||t==e.path}))&&(e.headerActived=a)}))},switchHeaderActived:function(e,t){e.headerActived=t}};t["default"]={namespaced:!0,state:l,actions:u,getters:m,mutations:h}},"13ff":function(e,t,n){"use strict";n("bb60")},1470:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-index-component",use:"icon-index-component-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-index-component"><defs><style type="text/css"></style></defs><path d="M972.09863 1016.986301H259.506849c-19.638356 0-35.068493-15.430137-35.068493-35.068493V768.70137c-8.416438 5.610959-18.235616 9.819178-28.054794 12.624657h-1.40274c-12.624658 4.208219-26.652055 5.610959-39.276712 5.610959-77.150685 0-138.871233-63.123288-138.871233-138.871233 0-77.150685 63.123288-138.871233 138.871233-138.871232 14.027397 0 28.054795 2.805479 42.082191 7.013698 1.40274 0 2.805479 1.40274 4.20822 1.40274 7.013699 2.805479 14.027397 5.610959 19.638356 8.416438l2.805479 1.40274V269.326027c0-19.638356 15.430137-35.068493 35.068493-35.068493h228.646576l-4.20822-8.416438c-7.013699-11.221918-12.624658-22.443836-15.430137-35.068493v-1.40274c-4.208219-12.624658-5.610959-26.652055-5.610958-39.276712C462.90411 72.942466 526.027397 11.221918 601.775342 11.221918c77.150685 0 138.871233 63.123288 138.871233 138.871233 0 14.027397-2.805479 28.054795-7.013698 42.082191 0 1.40274-1.40274 2.805479-1.40274 4.20822-2.805479 7.013699-5.610959 14.027397-8.416438 19.638356l-7.013699 16.832877h255.29863c19.638356 0 35.068493 15.430137 35.068493 35.068493v314.213698c0 11.221918-5.610959 22.443836-15.430137 29.457535-9.819178 7.013699-22.443836 7.013699-33.665753 2.805479L897.753425 587.747945c-1.40274 0-1.40274-1.40274-2.80548-1.40274-2.805479-1.40274-7.013699-4.208219-11.221918-5.610958h-1.402739c-7.013699-2.805479-14.027397-2.805479-21.041096-2.80548-37.873973 0-68.734247 30.860274-68.734247 68.734247s30.860274 68.734247 68.734247 68.734246c7.013699 0 14.027397-1.40274 19.638356-2.805479 7.013699-1.40274 12.624658-5.610959 18.235616-8.416439 1.40274-1.40274 2.805479-1.40274 4.20822-2.805479l53.304109-25.249315c11.221918-5.610959 23.846575-4.208219 33.665754 1.40274s16.832877 18.235616 16.832876 29.457534V981.917808c0 19.638356-15.430137 35.068493-35.068493 35.068493z m-677.523288-70.136986h642.454795v-182.356164h-1.40274c-11.221918 7.013699-22.443836 12.624658-35.068493 16.832876h-1.40274c-12.624658 4.208219-26.652055 5.610959-39.276712 5.610959-77.150685 0-138.871233-63.123288-138.871233-138.871233 0-77.150685 63.123288-138.871233 138.871233-138.871232 14.027397 0 28.054795 2.805479 42.082192 7.013698 1.40274 0 2.805479 1.40274 4.208219 1.40274 7.013699 2.805479 14.027397 5.610959 19.638356 8.416438l9.819178 4.208219v-224.438356H662.093151c-11.221918 0-22.443836-5.610959-29.457535-15.430137-7.013699-9.819178-7.013699-22.443836-2.805479-33.665753l29.457534-67.331507c0-1.40274 1.40274-1.40274 1.40274-2.805479 1.40274-2.805479 4.208219-7.013699 5.610959-11.221918v-1.40274c2.805479-7.013699 2.805479-14.027397 2.805479-21.041096 0-37.873973-30.860274-68.734247-68.734246-68.734246s-68.734247 30.860274-68.734247 68.734246c0 7.013699 1.40274 14.027397 2.80548 19.638356 1.40274 7.013699 5.610959 12.624658 8.416438 18.235617 1.40274 1.40274 1.40274 2.805479 2.805479 4.208219l28.054795 60.317808c5.610959 11.221918 4.208219 23.846575-1.40274 33.665754s-18.235616 16.832877-29.457534 16.832876H294.575342v274.936987c0 11.221918-5.610959 22.443836-15.430137 29.457534-9.819178 7.013699-22.443836 7.013699-33.665753 2.805479L192.175342 589.150685c-1.40274 0-1.40274-1.40274-2.805479-1.40274-2.805479-1.40274-7.013699-4.208219-11.221918-5.610959h-1.40274c-7.013699-2.805479-14.027397-2.805479-21.041095-2.805479-37.873973 0-68.734247 30.860274-68.734247 68.734246s30.860274 68.734247 68.734247 68.734247c7.013699 0 14.027397-1.40274 19.638356-2.805479 7.013699-1.40274 12.624658-5.610959 18.235616-8.416439 1.40274-1.40274 2.805479-1.40274 4.208219-2.805479l46.290411-22.443836c11.221918-5.610959 23.846575-4.208219 33.665754 1.40274 9.819178 7.013699 16.832877 18.235616 16.832876 29.457534v235.660274z" p-id="2909" /></symbol>'});c.a.add(s);t["default"]=s},1564:function(e,t,n){"use strict";n("178f")},1676:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-toolbar-reload",use:"icon-toolbar-reload-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-toolbar-reload"><defs><style type="text/css"></style></defs><path d="M960 416V192l-73.056 73.056a447.712 447.712 0 0 0-373.6-201.088C265.92 63.968 65.312 264.544 65.312 512S265.92 960.032 513.344 960.032a448.064 448.064 0 0 0 415.232-279.488 38.368 38.368 0 1 0-71.136-28.896 371.36 371.36 0 0 1-344.096 231.584c-205.024 0-371.232-166.208-371.232-371.232S308.32 140.768 513.344 140.768c132.448 0 251.936 70.08 318.016 179.84L736 416h224z" p-id="4700" /></symbol>'});c.a.add(s);t["default"]=s},"16fc7":function(e,t,n){"use strict";n("175a")},"16fe":function(e,t,n){},"171d":function(e,t,n){},"175a":function(e,t,n){},"175f":function(e,t,n){"use strict";n("6920")},"178f":function(e,t,n){},"184d":function(e,t,n){"use strict";n("7633")},"18e7":function(e,t,n){},"1a5c":function(e,t){window.onload=function(){}},"1ec3":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-example-emotion-laugh-line",use:"icon-example-emotion-laugh-line-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-example-emotion-laugh-line"><defs><style type="text/css"></style></defs><path d="M512 85.333333c235.648 0 426.666667 191.018667 426.666667 426.666667s-191.018667 426.666667-426.666667 426.666667S85.333333 747.648 85.333333 512 276.352 85.333333 512 85.333333z m0 85.333334a341.333333 341.333333 0 1 0 0 682.666666 341.333333 341.333333 0 0 0 0-682.666666z m0 298.666666c85.333333 0 156.458667 14.208 213.333333 42.666667a213.333333 213.333333 0 0 1-426.666666 0c56.874667-28.458667 128-42.666667 213.333333-42.666667zM362.666667 298.666667a106.666667 106.666667 0 0 1 104.533333 85.333333h-209.066667A106.666667 106.666667 0 0 1 362.666667 298.666667z m298.666666 0a106.666667 106.666667 0 0 1 104.533334 85.333333h-209.066667a106.666667 106.666667 0 0 1 104.533333-85.333333z" p-id="1667" /></symbol>'});c.a.add(s);t["default"]=s},"220d":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-cascader",{attrs:{options:e.options,props:e.props,size:e.size,disabled:e.disabled,clearable:"",filterable:""},model:{value:e.myValue,callback:function(t){e.myValue=t},expression:"myValue"}})},o=[],i=n("3835"),c=(n("d81d"),n("b0c0"),[{name:"北京市",code:"110000",region:"north",provinceLevelCity:!0,children:[{name:"市辖区",code:"110100",children:[{name:"东城区",code:"110101"},{name:"西城区",code:"110102"},{name:"朝阳区",code:"110105"},{name:"丰台区",code:"110106"},{name:"石景山区",code:"110107"},{name:"海淀区",code:"110108"},{name:"门头沟区",code:"110109"},{name:"房山区",code:"110111"},{name:"通州区",code:"110112"},{name:"顺义区",code:"110113"},{name:"昌平区",code:"110114"},{name:"大兴区",code:"110115"},{name:"怀柔区",code:"110116"},{name:"平谷区",code:"110117"},{name:"密云区",code:"110118"},{name:"延庆区",code:"110119"}]}]},{name:"天津市",code:"120000",region:"north",provinceLevelCity:!0,children:[{name:"市辖区",code:"120100",children:[{name:"和平区",code:"120101"},{name:"河东区",code:"120102"},{name:"河西区",code:"120103"},{name:"南开区",code:"120104"},{name:"河北区",code:"120105"},{name:"红桥区",code:"120106"},{name:"东丽区",code:"120110"},{name:"西青区",code:"120111"},{name:"津南区",code:"120112"},{name:"北辰区",code:"120113"},{name:"武清区",code:"120114"},{name:"宝坻区",code:"120115"},{name:"滨海新区",code:"120116"},{name:"宁河区",code:"120117"},{name:"静海区",code:"120118"},{name:"蓟州区",code:"120119"}]}]},{name:"河北省",code:"130000",region:"north",children:[{name:"石家庄市",code:"130100",children:[{name:"长安区",code:"130102"},{name:"桥西区",code:"130104"},{name:"新华区",code:"130105"},{name:"井陉矿区",code:"130107"},{name:"裕华区",code:"130108"},{name:"藁城区",code:"130109"},{name:"鹿泉区",code:"130110"},{name:"栾城区",code:"130111"},{name:"井陉县",code:"130121"},{name:"正定县",code:"130123"},{name:"行唐县",code:"130125"},{name:"灵寿县",code:"130126"},{name:"高邑县",code:"130127"},{name:"深泽县",code:"130128"},{name:"赞皇县",code:"130129"},{name:"无极县",code:"130130"},{name:"平山县",code:"130131"},{name:"元氏县",code:"130132"},{name:"赵县",code:"130133"},{name:"辛集市",code:"130181"},{name:"晋州市",code:"130183"},{name:"新乐市",code:"130184"}]},{name:"唐山市",code:"130200",children:[{name:"路南区",code:"130202"},{name:"路北区",code:"130203"},{name:"古冶区",code:"130204"},{name:"开平区",code:"130205"},{name:"丰南区",code:"130207"},{name:"丰润区",code:"130208"},{name:"曹妃甸区",code:"130209"},{name:"滦南县",code:"130224"},{name:"乐亭县",code:"130225"},{name:"迁西县",code:"130227"},{name:"玉田县",code:"130229"},{name:"遵化市",code:"130281"},{name:"迁安市",code:"130283"},{name:"滦州市",code:"130284"}]},{name:"秦皇岛市",code:"130300",children:[{name:"海港区",code:"130302"},{name:"山海关区",code:"130303"},{name:"北戴河区",code:"130304"},{name:"抚宁区",code:"130306"},{name:"青龙满族自治县",code:"130321"},{name:"昌黎县",code:"130322"},{name:"卢龙县",code:"130324"}]},{name:"邯郸市",code:"130400",children:[{name:"邯山区",code:"130402"},{name:"丛台区",code:"130403"},{name:"复兴区",code:"130404"},{name:"峰峰矿区",code:"130406"},{name:"肥乡区",code:"130407"},{name:"永年区",code:"130408"},{name:"临漳县",code:"130423"},{name:"成安县",code:"130424"},{name:"大名县",code:"130425"},{name:"涉县",code:"130426"},{name:"磁县",code:"130427"},{name:"邱县",code:"130430"},{name:"鸡泽县",code:"130431"},{name:"广平县",code:"130432"},{name:"馆陶县",code:"130433"},{name:"魏县",code:"130434"},{name:"曲周县",code:"130435"},{name:"武安市",code:"130481"}]},{name:"邢台市",code:"130500",children:[{name:"桥东区",code:"130502"},{name:"桥西区",code:"130503"},{name:"邢台县",code:"130521"},{name:"临城县",code:"130522"},{name:"内丘县",code:"130523"},{name:"柏乡县",code:"130524"},{name:"隆尧县",code:"130525"},{name:"任县",code:"130526"},{name:"南和县",code:"130527"},{name:"宁晋县",code:"130528"},{name:"巨鹿县",code:"130529"},{name:"新河县",code:"130530"},{name:"广宗县",code:"130531"},{name:"平乡县",code:"130532"},{name:"威县",code:"130533"},{name:"清河县",code:"130534"},{name:"临西县",code:"130535"},{name:"南宫市",code:"130581"},{name:"沙河市",code:"130582"}]},{name:"保定市",code:"130600",children:[{name:"竞秀区",code:"130602"},{name:"莲池区",code:"130606"},{name:"满城区",code:"130607"},{name:"清苑区",code:"130608"},{name:"徐水区",code:"130609"},{name:"涞水县",code:"130623"},{name:"阜平县",code:"130624"},{name:"定兴县",code:"130626"},{name:"唐县",code:"130627"},{name:"高阳县",code:"130628"},{name:"容城县",code:"130629"},{name:"涞源县",code:"130630"},{name:"望都县",code:"130631"},{name:"安新县",code:"130632"},{name:"易县",code:"130633"},{name:"曲阳县",code:"130634"},{name:"蠡县",code:"130635"},{name:"顺平县",code:"130636"},{name:"博野县",code:"130637"},{name:"雄县",code:"130638"},{name:"涿州市",code:"130681"},{name:"定州市",code:"130682"},{name:"安国市",code:"130683"},{name:"高碑店市",code:"130684"}]},{name:"张家口市",code:"130700",children:[{name:"桥东区",code:"130702"},{name:"桥西区",code:"130703"},{name:"宣化区",code:"130705"},{name:"下花园区",code:"130706"},{name:"万全区",code:"130708"},{name:"崇礼区",code:"130709"},{name:"张北县",code:"130722"},{name:"康保县",code:"130723"},{name:"沽源县",code:"130724"},{name:"尚义县",code:"130725"},{name:"蔚县",code:"130726"},{name:"阳原县",code:"130727"},{name:"怀安县",code:"130728"},{name:"怀来县",code:"130730"},{name:"涿鹿县",code:"130731"},{name:"赤城县",code:"130732"}]},{name:"承德市",code:"130800",children:[{name:"双桥区",code:"130802"},{name:"双滦区",code:"130803"},{name:"鹰手营子矿区",code:"130804"},{name:"承德县",code:"130821"},{name:"兴隆县",code:"130822"},{name:"滦平县",code:"130824"},{name:"隆化县",code:"130825"},{name:"丰宁满族自治县",code:"130826"},{name:"宽城满族自治县",code:"130827"},{name:"围场满族蒙古族自治县",code:"130828"},{name:"平泉市",code:"130881"}]},{name:"沧州市",code:"130900",children:[{name:"新华区",code:"130902"},{name:"运河区",code:"130903"},{name:"沧县",code:"130921"},{name:"青县",code:"130922"},{name:"东光县",code:"130923"},{name:"海兴县",code:"130924"},{name:"盐山县",code:"130925"},{name:"肃宁县",code:"130926"},{name:"南皮县",code:"130927"},{name:"吴桥县",code:"130928"},{name:"献县",code:"130929"},{name:"孟村回族自治县",code:"130930"},{name:"泊头市",code:"130981"},{name:"任丘市",code:"130982"},{name:"黄骅市",code:"130983"},{name:"河间市",code:"130984"}]},{name:"廊坊市",code:"131000",children:[{name:"安次区",code:"131002"},{name:"广阳区",code:"131003"},{name:"固安县",code:"131022"},{name:"永清县",code:"131023"},{name:"香河县",code:"131024"},{name:"大城县",code:"131025"},{name:"文安县",code:"131026"},{name:"大厂回族自治县",code:"131028"},{name:"霸州市",code:"131081"},{name:"三河市",code:"131082"}]},{name:"衡水市",code:"131100",children:[{name:"桃城区",code:"131102"},{name:"冀州区",code:"131103"},{name:"枣强县",code:"131121"},{name:"武邑县",code:"131122"},{name:"武强县",code:"131123"},{name:"饶阳县",code:"131124"},{name:"安平县",code:"131125"},{name:"故城县",code:"131126"},{name:"景县",code:"131127"},{name:"阜城县",code:"131128"},{name:"深州市",code:"131182"}]}]},{name:"山西省",code:"140000",region:"north",children:[{name:"太原市",code:"140100",children:[{name:"小店区",code:"140105"},{name:"迎泽区",code:"140106"},{name:"杏花岭区",code:"140107"},{name:"尖草坪区",code:"140108"},{name:"万柏林区",code:"140109"},{name:"晋源区",code:"140110"},{name:"清徐县",code:"140121"},{name:"阳曲县",code:"140122"},{name:"娄烦县",code:"140123"},{name:"古交市",code:"140181"}]},{name:"大同市",code:"140200",children:[{name:"新荣区",code:"140212"},{name:"平城区",code:"140213"},{name:"云冈区",code:"140214"},{name:"云州区",code:"140215"},{name:"阳高县",code:"140221"},{name:"天镇县",code:"140222"},{name:"广灵县",code:"140223"},{name:"灵丘县",code:"140224"},{name:"浑源县",code:"140225"},{name:"左云县",code:"140226"}]},{name:"阳泉市",code:"140300",children:[{name:"城区",code:"140302"},{name:"矿区",code:"140303"},{name:"郊区",code:"140311"},{name:"平定县",code:"140321"},{name:"盂县",code:"140322"}]},{name:"长治市",code:"140400",children:[{name:"潞州区",code:"140403"},{name:"上党区",code:"140404"},{name:"屯留区",code:"140405"},{name:"潞城区",code:"140406"},{name:"襄垣县",code:"140423"},{name:"平顺县",code:"140425"},{name:"黎城县",code:"140426"},{name:"壶关县",code:"140427"},{name:"长子县",code:"140428"},{name:"武乡县",code:"140429"},{name:"沁县",code:"140430"},{name:"沁源县",code:"140431"}]},{name:"晋城市",code:"140500",children:[{name:"城区",code:"140502"},{name:"沁水县",code:"140521"},{name:"阳城县",code:"140522"},{name:"陵川县",code:"140524"},{name:"泽州县",code:"140525"},{name:"高平市",code:"140581"}]},{name:"朔州市",code:"140600",children:[{name:"朔城区",code:"140602"},{name:"平鲁区",code:"140603"},{name:"山阴县",code:"140621"},{name:"应县",code:"140622"},{name:"右玉县",code:"140623"},{name:"怀仁市",code:"140681"}]},{name:"晋中市",code:"140700",children:[{name:"榆次区",code:"140702"},{name:"榆社县",code:"140721"},{name:"左权县",code:"140722"},{name:"和顺县",code:"140723"},{name:"昔阳县",code:"140724"},{name:"寿阳县",code:"140725"},{name:"太谷县",code:"140726"},{name:"祁县",code:"140727"},{name:"平遥县",code:"140728"},{name:"灵石县",code:"140729"},{name:"介休市",code:"140781"}]},{name:"运城市",code:"140800",children:[{name:"盐湖区",code:"140802"},{name:"临猗县",code:"140821"},{name:"万荣县",code:"140822"},{name:"闻喜县",code:"140823"},{name:"稷山县",code:"140824"},{name:"新绛县",code:"140825"},{name:"绛县",code:"140826"},{name:"垣曲县",code:"140827"},{name:"夏县",code:"140828"},{name:"平陆县",code:"140829"},{name:"芮城县",code:"140830"},{name:"永济市",code:"140881"},{name:"河津市",code:"140882"}]},{name:"忻州市",code:"140900",children:[{name:"忻府区",code:"140902"},{name:"定襄县",code:"140921"},{name:"五台县",code:"140922"},{name:"代县",code:"140923"},{name:"繁峙县",code:"140924"},{name:"宁武县",code:"140925"},{name:"静乐县",code:"140926"},{name:"神池县",code:"140927"},{name:"五寨县",code:"140928"},{name:"岢岚县",code:"140929"},{name:"河曲县",code:"140930"},{name:"保德县",code:"140931"},{name:"偏关县",code:"140932"},{name:"原平市",code:"140981"}]},{name:"临汾市",code:"141000",children:[{name:"尧都区",code:"141002"},{name:"曲沃县",code:"141021"},{name:"翼城县",code:"141022"},{name:"襄汾县",code:"141023"},{name:"洪洞县",code:"141024"},{name:"古县",code:"141025"},{name:"安泽县",code:"141026"},{name:"浮山县",code:"141027"},{name:"吉县",code:"141028"},{name:"乡宁县",code:"141029"},{name:"大宁县",code:"141030"},{name:"隰县",code:"141031"},{name:"永和县",code:"141032"},{name:"蒲县",code:"141033"},{name:"汾西县",code:"141034"},{name:"侯马市",code:"141081"},{name:"霍州市",code:"141082"}]},{name:"吕梁市",code:"141100",children:[{name:"离石区",code:"141102"},{name:"文水县",code:"141121"},{name:"交城县",code:"141122"},{name:"兴县",code:"141123"},{name:"临县",code:"141124"},{name:"柳林县",code:"141125"},{name:"石楼县",code:"141126"},{name:"岚县",code:"141127"},{name:"方山县",code:"141128"},{name:"中阳县",code:"141129"},{name:"交口县",code:"141130"},{name:"孝义市",code:"141181"},{name:"汾阳市",code:"141182"}]}]},{name:"内蒙古自治区",code:"150000",region:"north",autonomousRegion:!0,children:[{name:"呼和浩特市",code:"150100",children:[{name:"新城区",code:"150102"},{name:"回民区",code:"150103"},{name:"玉泉区",code:"150104"},{name:"赛罕区",code:"150105"},{name:"土默特左旗",code:"150121"},{name:"托克托县",code:"150122"},{name:"和林格尔县",code:"150123"},{name:"清水河县",code:"150124"},{name:"武川县",code:"150125"}]},{name:"包头市",code:"150200",children:[{name:"东河区",code:"150202"},{name:"昆都仑区",code:"150203"},{name:"青山区",code:"150204"},{name:"石拐区",code:"150205"},{name:"白云鄂博矿区",code:"150206"},{name:"九原区",code:"150207"},{name:"土默特右旗",code:"150221"},{name:"固阳县",code:"150222"},{name:"达尔罕茂明安联合旗",code:"150223"}]},{name:"乌海市",code:"150300",children:[{name:"海勃湾区",code:"150302"},{name:"海南区",code:"150303"},{name:"乌达区",code:"150304"}]},{name:"赤峰市",code:"150400",children:[{name:"红山区",code:"150402"},{name:"元宝山区",code:"150403"},{name:"松山区",code:"150404"},{name:"阿鲁科尔沁旗",code:"150421"},{name:"巴林左旗",code:"150422"},{name:"巴林右旗",code:"150423"},{name:"林西县",code:"150424"},{name:"克什克腾旗",code:"150425"},{name:"翁牛特旗",code:"150426"},{name:"喀喇沁旗",code:"150428"},{name:"宁城县",code:"150429"},{name:"敖汉旗",code:"150430"}]},{name:"通辽市",code:"150500",children:[{name:"科尔沁区",code:"150502"},{name:"科尔沁左翼中旗",code:"150521"},{name:"科尔沁左翼后旗",code:"150522"},{name:"开鲁县",code:"150523"},{name:"库伦旗",code:"150524"},{name:"奈曼旗",code:"150525"},{name:"扎鲁特旗",code:"150526"},{name:"霍林郭勒市",code:"150581"}]},{name:"鄂尔多斯市",code:"150600",children:[{name:"东胜区",code:"150602"},{name:"康巴什区",code:"150603"},{name:"达拉特旗",code:"150621"},{name:"准格尔旗",code:"150622"},{name:"鄂托克前旗",code:"150623"},{name:"鄂托克旗",code:"150624"},{name:"杭锦旗",code:"150625"},{name:"乌审旗",code:"150626"},{name:"伊金霍洛旗",code:"150627"}]},{name:"呼伦贝尔市",code:"150700",children:[{name:"海拉尔区",code:"150702"},{name:"扎赉诺尔区",code:"150703"},{name:"阿荣旗",code:"150721"},{name:"莫力达瓦达斡尔族自治旗",code:"150722"},{name:"鄂伦春自治旗",code:"150723"},{name:"鄂温克族自治旗",code:"150724"},{name:"陈巴尔虎旗",code:"150725"},{name:"新巴尔虎左旗",code:"150726"},{name:"新巴尔虎右旗",code:"150727"},{name:"满洲里市",code:"150781"},{name:"牙克石市",code:"150782"},{name:"扎兰屯市",code:"150783"},{name:"额尔古纳市",code:"150784"},{name:"根河市",code:"150785"}]},{name:"巴彦淖尔市",code:"150800",children:[{name:"临河区",code:"150802"},{name:"五原县",code:"150821"},{name:"磴口县",code:"150822"},{name:"乌拉特前旗",code:"150823"},{name:"乌拉特中旗",code:"150824"},{name:"乌拉特后旗",code:"150825"},{name:"杭锦后旗",code:"150826"}]},{name:"乌兰察布市",code:"150900",children:[{name:"集宁区",code:"150902"},{name:"卓资县",code:"150921"},{name:"化德县",code:"150922"},{name:"商都县",code:"150923"},{name:"兴和县",code:"150924"},{name:"凉城县",code:"150925"},{name:"察哈尔右翼前旗",code:"150926"},{name:"察哈尔右翼中旗",code:"150927"},{name:"察哈尔右翼后旗",code:"150928"},{name:"四子王旗",code:"150929"},{name:"丰镇市",code:"150981"}]},{name:"兴安盟",code:"152200",children:[{name:"乌兰浩特市",code:"152201"},{name:"阿尔山市",code:"152202"},{name:"科尔沁右翼前旗",code:"152221"},{name:"科尔沁右翼中旗",code:"152222"},{name:"扎赉特旗",code:"152223"},{name:"突泉县",code:"152224"}]},{name:"锡林郭勒盟",code:"152500",children:[{name:"二连浩特市",code:"152501"},{name:"锡林浩特市",code:"152502"},{name:"阿巴嘎旗",code:"152522"},{name:"苏尼特左旗",code:"152523"},{name:"苏尼特右旗",code:"152524"},{name:"东乌珠穆沁旗",code:"152525"},{name:"西乌珠穆沁旗",code:"152526"},{name:"太仆寺旗",code:"152527"},{name:"镶黄旗",code:"152528"},{name:"正镶白旗",code:"152529"},{name:"正蓝旗",code:"152530"},{name:"多伦县",code:"152531"}]},{name:"阿拉善盟",code:"152900",children:[{name:"阿拉善左旗",code:"152921"},{name:"阿拉善右旗",code:"152922"},{name:"额济纳旗",code:"152923"}]}]},{name:"辽宁省",code:"210000",region:"northeast",children:[{name:"沈阳市",code:"210100",children:[{name:"和平区",code:"210102"},{name:"沈河区",code:"210103"},{name:"大东区",code:"210104"},{name:"皇姑区",code:"210105"},{name:"铁西区",code:"210106"},{name:"苏家屯区",code:"210111"},{name:"浑南区",code:"210112"},{name:"沈北新区",code:"210113"},{name:"于洪区",code:"210114"},{name:"辽中区",code:"210115"},{name:"康平县",code:"210123"},{name:"法库县",code:"210124"},{name:"新民市",code:"210181"}]},{name:"大连市",code:"210200",children:[{name:"中山区",code:"210202"},{name:"西岗区",code:"210203"},{name:"沙河口区",code:"210204"},{name:"甘井子区",code:"210211"},{name:"旅顺口区",code:"210212"},{name:"金州区",code:"210213"},{name:"普兰店区",code:"210214"},{name:"长海县",code:"210224"},{name:"瓦房店市",code:"210281"},{name:"庄河市",code:"210283"}]},{name:"鞍山市",code:"210300",children:[{name:"铁东区",code:"210302"},{name:"铁西区",code:"210303"},{name:"立山区",code:"210304"},{name:"千山区",code:"210311"},{name:"台安县",code:"210321"},{name:"岫岩满族自治县",code:"210323"},{name:"海城市",code:"210381"}]},{name:"抚顺市",code:"210400",children:[{name:"新抚区",code:"210402"},{name:"东洲区",code:"210403"},{name:"望花区",code:"210404"},{name:"顺城区",code:"210411"},{name:"抚顺县",code:"210421"},{name:"新宾满族自治县",code:"210422"},{name:"清原满族自治县",code:"210423"}]},{name:"本溪市",code:"210500",children:[{name:"平山区",code:"210502"},{name:"溪湖区",code:"210503"},{name:"明山区",code:"210504"},{name:"南芬区",code:"210505"},{name:"本溪满族自治县",code:"210521"},{name:"桓仁满族自治县",code:"210522"}]},{name:"丹东市",code:"210600",children:[{name:"元宝区",code:"210602"},{name:"振兴区",code:"210603"},{name:"振安区",code:"210604"},{name:"宽甸满族自治县",code:"210624"},{name:"东港市",code:"210681"},{name:"凤城市",code:"210682"}]},{name:"锦州市",code:"210700",children:[{name:"古塔区",code:"210702"},{name:"凌河区",code:"210703"},{name:"太和区",code:"210711"},{name:"黑山县",code:"210726"},{name:"义县",code:"210727"},{name:"凌海市",code:"210781"},{name:"北镇市",code:"210782"}]},{name:"营口市",code:"210800",children:[{name:"站前区",code:"210802"},{name:"西市区",code:"210803"},{name:"鲅鱼圈区",code:"210804"},{name:"老边区",code:"210811"},{name:"盖州市",code:"210881"},{name:"大石桥市",code:"210882"}]},{name:"阜新市",code:"210900",children:[{name:"海州区",code:"210902"},{name:"新邱区",code:"210903"},{name:"太平区",code:"210904"},{name:"清河门区",code:"210905"},{name:"细河区",code:"210911"},{name:"阜新蒙古族自治县",code:"210921"},{name:"彰武县",code:"210922"}]},{name:"辽阳市",code:"211000",children:[{name:"白塔区",code:"211002"},{name:"文圣区",code:"211003"},{name:"宏伟区",code:"211004"},{name:"弓长岭区",code:"211005"},{name:"太子河区",code:"211011"},{name:"辽阳县",code:"211021"},{name:"灯塔市",code:"211081"}]},{name:"盘锦市",code:"211100",children:[{name:"双台子区",code:"211102"},{name:"兴隆台区",code:"211103"},{name:"大洼区",code:"211104"},{name:"盘山县",code:"211122"}]},{name:"铁岭市",code:"211200",children:[{name:"银州区",code:"211202"},{name:"清河区",code:"211204"},{name:"铁岭县",code:"211221"},{name:"西丰县",code:"211223"},{name:"昌图县",code:"211224"},{name:"调兵山市",code:"211281"},{name:"开原市",code:"211282"}]},{name:"朝阳市",code:"211300",children:[{name:"双塔区",code:"211302"},{name:"龙城区",code:"211303"},{name:"朝阳县",code:"211321"},{name:"建平县",code:"211322"},{name:"喀喇沁左翼蒙古族自治县",code:"211324"},{name:"北票市",code:"211381"},{name:"凌源市",code:"211382"}]},{name:"葫芦岛市",code:"211400",children:[{name:"连山区",code:"211402"},{name:"龙港区",code:"211403"},{name:"南票区",code:"211404"},{name:"绥中县",code:"211421"},{name:"建昌县",code:"211422"},{name:"兴城市",code:"211481"}]}]},{name:"吉林省",code:"220000",region:"northeast",children:[{name:"长春市",code:"220100",children:[{name:"南关区",code:"220102"},{name:"宽城区",code:"220103"},{name:"朝阳区",code:"220104"},{name:"二道区",code:"220105"},{name:"绿园区",code:"220106"},{name:"双阳区",code:"220112"},{name:"九台区",code:"220113"},{name:"农安县",code:"220122"},{name:"榆树市",code:"220182"},{name:"德惠市",code:"220183"}]},{name:"吉林市",code:"220200",children:[{name:"昌邑区",code:"220202"},{name:"龙潭区",code:"220203"},{name:"船营区",code:"220204"},{name:"丰满区",code:"220211"},{name:"永吉县",code:"220221"},{name:"蛟河市",code:"220281"},{name:"桦甸市",code:"220282"},{name:"舒兰市",code:"220283"},{name:"磐石市",code:"220284"}]},{name:"四平市",code:"220300",children:[{name:"铁西区",code:"220302"},{name:"铁东区",code:"220303"},{name:"梨树县",code:"220322"},{name:"伊通满族自治县",code:"220323"},{name:"公主岭市",code:"220381"},{name:"双辽市",code:"220382"}]},{name:"辽源市",code:"220400",children:[{name:"龙山区",code:"220402"},{name:"西安区",code:"220403"},{name:"东丰县",code:"220421"},{name:"东辽县",code:"220422"}]},{name:"通化市",code:"220500",children:[{name:"东昌区",code:"220502"},{name:"二道江区",code:"220503"},{name:"通化县",code:"220521"},{name:"辉南县",code:"220523"},{name:"柳河县",code:"220524"},{name:"梅河口市",code:"220581"},{name:"集安市",code:"220582"}]},{name:"白山市",code:"220600",children:[{name:"浑江区",code:"220602"},{name:"江源区",code:"220605"},{name:"抚松县",code:"220621"},{name:"靖宇县",code:"220622"},{name:"长白朝鲜族自治县",code:"220623"},{name:"临江市",code:"220681"}]},{name:"松原市",code:"220700",children:[{name:"宁江区",code:"220702"},{name:"前郭尔罗斯蒙古族自治县",code:"220721"},{name:"长岭县",code:"220722"},{name:"乾安县",code:"220723"},{name:"扶余市",code:"220781"}]},{name:"白城市",code:"220800",children:[{name:"洮北区",code:"220802"},{name:"镇赉县",code:"220821"},{name:"通榆县",code:"220822"},{name:"洮南市",code:"220881"},{name:"大安市",code:"220882"}]},{name:"延边朝鲜族自治州",code:"222400",children:[{name:"延吉市",code:"222401"},{name:"图们市",code:"222402"},{name:"敦化市",code:"222403"},{name:"珲春市",code:"222404"},{name:"龙井市",code:"222405"},{name:"和龙市",code:"222406"},{name:"汪清县",code:"222424"},{name:"安图县",code:"222426"}]}]},{name:"黑龙江省",code:"230000",region:"northeast",children:[{name:"哈尔滨市",code:"230100",children:[{name:"道里区",code:"230102"},{name:"南岗区",code:"230103"},{name:"道外区",code:"230104"},{name:"平房区",code:"230108"},{name:"松北区",code:"230109"},{name:"香坊区",code:"230110"},{name:"呼兰区",code:"230111"},{name:"阿城区",code:"230112"},{name:"双城区",code:"230113"},{name:"依兰县",code:"230123"},{name:"方正县",code:"230124"},{name:"宾县",code:"230125"},{name:"巴彦县",code:"230126"},{name:"木兰县",code:"230127"},{name:"通河县",code:"230128"},{name:"延寿县",code:"230129"},{name:"尚志市",code:"230183"},{name:"五常市",code:"230184"}]},{name:"齐齐哈尔市",code:"230200",children:[{name:"龙沙区",code:"230202"},{name:"建华区",code:"230203"},{name:"铁锋区",code:"230204"},{name:"昂昂溪区",code:"230205"},{name:"富拉尔基区",code:"230206"},{name:"碾子山区",code:"230207"},{name:"梅里斯达斡尔族区",code:"230208"},{name:"龙江县",code:"230221"},{name:"依安县",code:"230223"},{name:"泰来县",code:"230224"},{name:"甘南县",code:"230225"},{name:"富裕县",code:"230227"},{name:"克山县",code:"230229"},{name:"克东县",code:"230230"},{name:"拜泉县",code:"230231"},{name:"讷河市",code:"230281"}]},{name:"鸡西市",code:"230300",children:[{name:"鸡冠区",code:"230302"},{name:"恒山区",code:"230303"},{name:"滴道区",code:"230304"},{name:"梨树区",code:"230305"},{name:"城子河区",code:"230306"},{name:"麻山区",code:"230307"},{name:"鸡东县",code:"230321"},{name:"虎林市",code:"230381"},{name:"密山市",code:"230382"}]},{name:"鹤岗市",code:"230400",children:[{name:"向阳区",code:"230402"},{name:"工农区",code:"230403"},{name:"南山区",code:"230404"},{name:"兴安区",code:"230405"},{name:"东山区",code:"230406"},{name:"兴山区",code:"230407"},{name:"萝北县",code:"230421"},{name:"绥滨县",code:"230422"}]},{name:"双鸭山市",code:"230500",children:[{name:"尖山区",code:"230502"},{name:"岭东区",code:"230503"},{name:"四方台区",code:"230505"},{name:"宝山区",code:"230506"},{name:"集贤县",code:"230521"},{name:"友谊县",code:"230522"},{name:"宝清县",code:"230523"},{name:"饶河县",code:"230524"}]},{name:"大庆市",code:"230600",children:[{name:"萨尔图区",code:"230602"},{name:"龙凤区",code:"230603"},{name:"让胡路区",code:"230604"},{name:"红岗区",code:"230605"},{name:"大同区",code:"230606"},{name:"肇州县",code:"230621"},{name:"肇源县",code:"230622"},{name:"林甸县",code:"230623"},{name:"杜尔伯特蒙古族自治县",code:"230624"}]},{name:"伊春市",code:"230700",children:[{name:"伊春区",code:"230702"},{name:"南岔区",code:"230703"},{name:"友好区",code:"230704"},{name:"西林区",code:"230705"},{name:"翠峦区",code:"230706"},{name:"新青区",code:"230707"},{name:"美溪区",code:"230708"},{name:"金山屯区",code:"230709"},{name:"五营区",code:"230710"},{name:"乌马河区",code:"230711"},{name:"汤旺河区",code:"230712"},{name:"带岭区",code:"230713"},{name:"乌伊岭区",code:"230714"},{name:"红星区",code:"230715"},{name:"上甘岭区",code:"230716"},{name:"嘉荫县",code:"230722"},{name:"铁力市",code:"230781"}]},{name:"佳木斯市",code:"230800",children:[{name:"向阳区",code:"230803"},{name:"前进区",code:"230804"},{name:"东风区",code:"230805"},{name:"郊区",code:"230811"},{name:"桦南县",code:"230822"},{name:"桦川县",code:"230826"},{name:"汤原县",code:"230828"},{name:"同江市",code:"230881"},{name:"富锦市",code:"230882"},{name:"抚远市",code:"230883"}]},{name:"七台河市",code:"230900",children:[{name:"新兴区",code:"230902"},{name:"桃山区",code:"230903"},{name:"茄子河区",code:"230904"},{name:"勃利县",code:"230921"}]},{name:"牡丹江市",code:"231000",children:[{name:"东安区",code:"231002"},{name:"阳明区",code:"231003"},{name:"爱民区",code:"231004"},{name:"西安区",code:"231005"},{name:"林口县",code:"231025"},{name:"绥芬河市",code:"231081"},{name:"海林市",code:"231083"},{name:"宁安市",code:"231084"},{name:"穆棱市",code:"231085"},{name:"东宁市",code:"231086"}]},{name:"黑河市",code:"231100",children:[{name:"爱辉区",code:"231102"},{name:"嫩江县",code:"231121"},{name:"逊克县",code:"231123"},{name:"孙吴县",code:"231124"},{name:"北安市",code:"231181"},{name:"五大连池市",code:"231182"}]},{name:"绥化市",code:"231200",children:[{name:"北林区",code:"231202"},{name:"望奎县",code:"231221"},{name:"兰西县",code:"231222"},{name:"青冈县",code:"231223"},{name:"庆安县",code:"231224"},{name:"明水县",code:"231225"},{name:"绥棱县",code:"231226"},{name:"安达市",code:"231281"},{name:"肇东市",code:"231282"},{name:"海伦市",code:"231283"}]},{name:"大兴安岭地区",code:"232700",children:[{name:"漠河市",code:"232701"},{name:"呼玛县",code:"232721"},{name:"塔河县",code:"232722"}]}]},{name:"上海市",code:"310000",region:"east",provinceLevelCity:!0,children:[{name:"市辖区",code:"310100",children:[{name:"黄浦区",code:"310101"},{name:"徐汇区",code:"310104"},{name:"长宁区",code:"310105"},{name:"静安区",code:"310106"},{name:"普陀区",code:"310107"},{name:"虹口区",code:"310109"},{name:"杨浦区",code:"310110"},{name:"闵行区",code:"310112"},{name:"宝山区",code:"310113"},{name:"嘉定区",code:"310114"},{name:"浦东新区",code:"310115"},{name:"金山区",code:"310116"},{name:"松江区",code:"310117"},{name:"青浦区",code:"310118"},{name:"奉贤区",code:"310120"},{name:"崇明区",code:"310151"}]}]},{name:"江苏省",code:"320000",region:"east",children:[{name:"南京市",code:"320100",children:[{name:"玄武区",code:"320102"},{name:"秦淮区",code:"320104"},{name:"建邺区",code:"320105"},{name:"鼓楼区",code:"320106"},{name:"浦口区",code:"320111"},{name:"栖霞区",code:"320113"},{name:"雨花台区",code:"320114"},{name:"江宁区",code:"320115"},{name:"六合区",code:"320116"},{name:"溧水区",code:"320117"},{name:"高淳区",code:"320118"}]},{name:"无锡市",code:"320200",children:[{name:"锡山区",code:"320205"},{name:"惠山区",code:"320206"},{name:"滨湖区",code:"320211"},{name:"梁溪区",code:"320213"},{name:"新吴区",code:"320214"},{name:"江阴市",code:"320281"},{name:"宜兴市",code:"320282"}]},{name:"徐州市",code:"320300",children:[{name:"鼓楼区",code:"320302"},{name:"云龙区",code:"320303"},{name:"贾汪区",code:"320305"},{name:"泉山区",code:"320311"},{name:"铜山区",code:"320312"},{name:"丰县",code:"320321"},{name:"沛县",code:"320322"},{name:"睢宁县",code:"320324"},{name:"新沂市",code:"320381"},{name:"邳州市",code:"320382"}]},{name:"常州市",code:"320400",children:[{name:"天宁区",code:"320402"},{name:"钟楼区",code:"320404"},{name:"新北区",code:"320411"},{name:"武进区",code:"320412"},{name:"金坛区",code:"320413"},{name:"溧阳市",code:"320481"}]},{name:"苏州市",code:"320500",children:[{name:"虎丘区",code:"320505"},{name:"吴中区",code:"320506"},{name:"相城区",code:"320507"},{name:"姑苏区",code:"320508"},{name:"吴江区",code:"320509"},{name:"常熟市",code:"320581"},{name:"张家港市",code:"320582"},{name:"昆山市",code:"320583"},{name:"太仓市",code:"320585"}]},{name:"南通市",code:"320600",children:[{name:"崇川区",code:"320602"},{name:"港闸区",code:"320611"},{name:"通州区",code:"320612"},{name:"如东县",code:"320623"},{name:"启东市",code:"320681"},{name:"如皋市",code:"320682"},{name:"海门市",code:"320684"},{name:"海安市",code:"320685"}]},{name:"连云港市",code:"320700",children:[{name:"连云区",code:"320703"},{name:"海州区",code:"320706"},{name:"赣榆区",code:"320707"},{name:"东海县",code:"320722"},{name:"灌云县",code:"320723"},{name:"灌南县",code:"320724"}]},{name:"淮安市",code:"320800",children:[{name:"淮安区",code:"320803"},{name:"淮阴区",code:"320804"},{name:"清江浦区",code:"320812"},{name:"洪泽区",code:"320813"},{name:"涟水县",code:"320826"},{name:"盱眙县",code:"320830"},{name:"金湖县",code:"320831"}]},{name:"盐城市",code:"320900",children:[{name:"亭湖区",code:"320902"},{name:"盐都区",code:"320903"},{name:"大丰区",code:"320904"},{name:"响水县",code:"320921"},{name:"滨海县",code:"320922"},{name:"阜宁县",code:"320923"},{name:"射阳县",code:"320924"},{name:"建湖县",code:"320925"},{name:"东台市",code:"320981"}]},{name:"扬州市",code:"321000",children:[{name:"广陵区",code:"321002"},{name:"邗江区",code:"321003"},{name:"江都区",code:"321012"},{name:"宝应县",code:"321023"},{name:"仪征市",code:"321081"},{name:"高邮市",code:"321084"}]},{name:"镇江市",code:"321100",children:[{name:"京口区",code:"321102"},{name:"润州区",code:"321111"},{name:"丹徒区",code:"321112"},{name:"丹阳市",code:"321181"},{name:"扬中市",code:"321182"},{name:"句容市",code:"321183"}]},{name:"泰州市",code:"321200",children:[{name:"海陵区",code:"321202"},{name:"高港区",code:"321203"},{name:"姜堰区",code:"321204"},{name:"兴化市",code:"321281"},{name:"靖江市",code:"321282"},{name:"泰兴市",code:"321283"}]},{name:"宿迁市",code:"321300",children:[{name:"宿城区",code:"321302"},{name:"宿豫区",code:"321311"},{name:"沭阳县",code:"321322"},{name:"泗阳县",code:"321323"},{name:"泗洪县",code:"321324"}]}]},{name:"浙江省",code:"330000",region:"east",children:[{name:"杭州市",code:"330100",children:[{name:"上城区",code:"330102"},{name:"下城区",code:"330103"},{name:"江干区",code:"330104"},{name:"拱墅区",code:"330105"},{name:"西湖区",code:"330106"},{name:"滨江区",code:"330108"},{name:"萧山区",code:"330109"},{name:"余杭区",code:"330110"},{name:"富阳区",code:"330111"},{name:"临安区",code:"330112"},{name:"桐庐县",code:"330122"},{name:"淳安县",code:"330127"},{name:"建德市",code:"330182"}]},{name:"宁波市",code:"330200",children:[{name:"海曙区",code:"330203"},{name:"江北区",code:"330205"},{name:"北仑区",code:"330206"},{name:"镇海区",code:"330211"},{name:"鄞州区",code:"330212"},{name:"奉化区",code:"330213"},{name:"象山县",code:"330225"},{name:"宁海县",code:"330226"},{name:"余姚市",code:"330281"},{name:"慈溪市",code:"330282"}]},{name:"温州市",code:"330300",children:[{name:"鹿城区",code:"330302"},{name:"龙湾区",code:"330303"},{name:"瓯海区",code:"330304"},{name:"洞头区",code:"330305"},{name:"永嘉县",code:"330324"},{name:"平阳县",code:"330326"},{name:"苍南县",code:"330327"},{name:"文成县",code:"330328"},{name:"泰顺县",code:"330329"},{name:"瑞安市",code:"330381"},{name:"乐清市",code:"330382"}]},{name:"嘉兴市",code:"330400",children:[{name:"南湖区",code:"330402"},{name:"秀洲区",code:"330411"},{name:"嘉善县",code:"330421"},{name:"海盐县",code:"330424"},{name:"海宁市",code:"330481"},{name:"平湖市",code:"330482"},{name:"桐乡市",code:"330483"}]},{name:"湖州市",code:"330500",children:[{name:"吴兴区",code:"330502"},{name:"南浔区",code:"330503"},{name:"德清县",code:"330521"},{name:"长兴县",code:"330522"},{name:"安吉县",code:"330523"}]},{name:"绍兴市",code:"330600",children:[{name:"越城区",code:"330602"},{name:"柯桥区",code:"330603"},{name:"上虞区",code:"330604"},{name:"新昌县",code:"330624"},{name:"诸暨市",code:"330681"},{name:"嵊州市",code:"330683"}]},{name:"金华市",code:"330700",children:[{name:"婺城区",code:"330702"},{name:"金东区",code:"330703"},{name:"武义县",code:"330723"},{name:"浦江县",code:"330726"},{name:"磐安县",code:"330727"},{name:"兰溪市",code:"330781"},{name:"义乌市",code:"330782"},{name:"东阳市",code:"330783"},{name:"永康市",code:"330784"}]},{name:"衢州市",code:"330800",children:[{name:"柯城区",code:"330802"},{name:"衢江区",code:"330803"},{name:"常山县",code:"330822"},{name:"开化县",code:"330824"},{name:"龙游县",code:"330825"},{name:"江山市",code:"330881"}]},{name:"舟山市",code:"330900",children:[{name:"定海区",code:"330902"},{name:"普陀区",code:"330903"},{name:"岱山县",code:"330921"},{name:"嵊泗县",code:"330922"}]},{name:"台州市",code:"331000",children:[{name:"椒江区",code:"331002"},{name:"黄岩区",code:"331003"},{name:"路桥区",code:"331004"},{name:"三门县",code:"331022"},{name:"天台县",code:"331023"},{name:"仙居县",code:"331024"},{name:"温岭市",code:"331081"},{name:"临海市",code:"331082"},{name:"玉环市",code:"331083"}]},{name:"丽水市",code:"331100",children:[{name:"莲都区",code:"331102"},{name:"青田县",code:"331121"},{name:"缙云县",code:"331122"},{name:"遂昌县",code:"331123"},{name:"松阳县",code:"331124"},{name:"云和县",code:"331125"},{name:"庆元县",code:"331126"},{name:"景宁畲族自治县",code:"331127"},{name:"龙泉市",code:"331181"}]}]},{name:"安徽省",code:"340000",region:"east",children:[{name:"合肥市",code:"340100",children:[{name:"瑶海区",code:"340102"},{name:"庐阳区",code:"340103"},{name:"蜀山区",code:"340104"},{name:"包河区",code:"340111"},{name:"长丰县",code:"340121"},{name:"肥东县",code:"340122"},{name:"肥西县",code:"340123"},{name:"庐江县",code:"340124"},{name:"巢湖市",code:"340181"}]},{name:"芜湖市",code:"340200",children:[{name:"镜湖区",code:"340202"},{name:"弋江区",code:"340203"},{name:"鸠江区",code:"340207"},{name:"三山区",code:"340208"},{name:"芜湖县",code:"340221"},{name:"繁昌县",code:"340222"},{name:"南陵县",code:"340223"},{name:"无为县",code:"340225"}]},{name:"蚌埠市",code:"340300",children:[{name:"龙子湖区",code:"340302"},{name:"蚌山区",code:"340303"},{name:"禹会区",code:"340304"},{name:"淮上区",code:"340311"},{name:"怀远县",code:"340321"},{name:"五河县",code:"340322"},{name:"固镇县",code:"340323"}]},{name:"淮南市",code:"340400",children:[{name:"大通区",code:"340402"},{name:"田家庵区",code:"340403"},{name:"谢家集区",code:"340404"},{name:"八公山区",code:"340405"},{name:"潘集区",code:"340406"},{name:"凤台县",code:"340421"},{name:"寿县",code:"340422"}]},{name:"马鞍山市",code:"340500",children:[{name:"花山区",code:"340503"},{name:"雨山区",code:"340504"},{name:"博望区",code:"340506"},{name:"当涂县",code:"340521"},{name:"含山县",code:"340522"},{name:"和县",code:"340523"}]},{name:"淮北市",code:"340600",children:[{name:"杜集区",code:"340602"},{name:"相山区",code:"340603"},{name:"烈山区",code:"340604"},{name:"濉溪县",code:"340621"}]},{name:"铜陵市",code:"340700",children:[{name:"铜官区",code:"340705"},{name:"义安区",code:"340706"},{name:"郊区",code:"340711"},{name:"枞阳县",code:"340722"}]},{name:"安庆市",code:"340800",children:[{name:"迎江区",code:"340802"},{name:"大观区",code:"340803"},{name:"宜秀区",code:"340811"},{name:"怀宁县",code:"340822"},{name:"太湖县",code:"340825"},{name:"宿松县",code:"340826"},{name:"望江县",code:"340827"},{name:"岳西县",code:"340828"},{name:"桐城市",code:"340881"},{name:"潜山市",code:"340882"}]},{name:"黄山市",code:"341000",children:[{name:"屯溪区",code:"341002"},{name:"黄山区",code:"341003"},{name:"徽州区",code:"341004"},{name:"歙县",code:"341021"},{name:"休宁县",code:"341022"},{name:"黟县",code:"341023"},{name:"祁门县",code:"341024"}]},{name:"滁州市",code:"341100",children:[{name:"琅琊区",code:"341102"},{name:"南谯区",code:"341103"},{name:"来安县",code:"341122"},{name:"全椒县",code:"341124"},{name:"定远县",code:"341125"},{name:"凤阳县",code:"341126"},{name:"天长市",code:"341181"},{name:"明光市",code:"341182"}]},{name:"阜阳市",code:"341200",children:[{name:"颍州区",code:"341202"},{name:"颍东区",code:"341203"},{name:"颍泉区",code:"341204"},{name:"临泉县",code:"341221"},{name:"太和县",code:"341222"},{name:"阜南县",code:"341225"},{name:"颍上县",code:"341226"},{name:"界首市",code:"341282"}]},{name:"宿州市",code:"341300",children:[{name:"埇桥区",code:"341302"},{name:"砀山县",code:"341321"},{name:"萧县",code:"341322"},{name:"灵璧县",code:"341323"},{name:"泗县",code:"341324"}]},{name:"六安市",code:"341500",children:[{name:"金安区",code:"341502"},{name:"裕安区",code:"341503"},{name:"叶集区",code:"341504"},{name:"霍邱县",code:"341522"},{name:"舒城县",code:"341523"},{name:"金寨县",code:"341524"},{name:"霍山县",code:"341525"}]},{name:"亳州市",code:"341600",children:[{name:"谯城区",code:"341602"},{name:"涡阳县",code:"341621"},{name:"蒙城县",code:"341622"},{name:"利辛县",code:"341623"}]},{name:"池州市",code:"341700",children:[{name:"贵池区",code:"341702"},{name:"东至县",code:"341721"},{name:"石台县",code:"341722"},{name:"青阳县",code:"341723"}]},{name:"宣城市",code:"341800",children:[{name:"宣州区",code:"341802"},{name:"郎溪县",code:"341821"},{name:"广德县",code:"341822"},{name:"泾县",code:"341823"},{name:"绩溪县",code:"341824"},{name:"旌德县",code:"341825"},{name:"宁国市",code:"341881"}]}]},{name:"福建省",code:"350000",region:"east",children:[{name:"福州市",code:"350100",children:[{name:"鼓楼区",code:"350102"},{name:"台江区",code:"350103"},{name:"仓山区",code:"350104"},{name:"马尾区",code:"350105"},{name:"晋安区",code:"350111"},{name:"长乐区",code:"350112"},{name:"闽侯县",code:"350121"},{name:"连江县",code:"350122"},{name:"罗源县",code:"350123"},{name:"闽清县",code:"350124"},{name:"永泰县",code:"350125"},{name:"平潭县",code:"350128"},{name:"福清市",code:"350181"}]},{name:"厦门市",code:"350200",children:[{name:"思明区",code:"350203"},{name:"海沧区",code:"350205"},{name:"湖里区",code:"350206"},{name:"集美区",code:"350211"},{name:"同安区",code:"350212"},{name:"翔安区",code:"350213"}]},{name:"莆田市",code:"350300",children:[{name:"城厢区",code:"350302"},{name:"涵江区",code:"350303"},{name:"荔城区",code:"350304"},{name:"秀屿区",code:"350305"},{name:"仙游县",code:"350322"}]},{name:"三明市",code:"350400",children:[{name:"梅列区",code:"350402"},{name:"三元区",code:"350403"},{name:"明溪县",code:"350421"},{name:"清流县",code:"350423"},{name:"宁化县",code:"350424"},{name:"大田县",code:"350425"},{name:"尤溪县",code:"350426"},{name:"沙县",code:"350427"},{name:"将乐县",code:"350428"},{name:"泰宁县",code:"350429"},{name:"建宁县",code:"350430"},{name:"永安市",code:"350481"}]},{name:"泉州市",code:"350500",children:[{name:"鲤城区",code:"350502"},{name:"丰泽区",code:"350503"},{name:"洛江区",code:"350504"},{name:"泉港区",code:"350505"},{name:"惠安县",code:"350521"},{name:"安溪县",code:"350524"},{name:"永春县",code:"350525"},{name:"德化县",code:"350526"},{name:"金门县",code:"350527"},{name:"石狮市",code:"350581"},{name:"晋江市",code:"350582"},{name:"南安市",code:"350583"}]},{name:"漳州市",code:"350600",children:[{name:"芗城区",code:"350602"},{name:"龙文区",code:"350603"},{name:"云霄县",code:"350622"},{name:"漳浦县",code:"350623"},{name:"诏安县",code:"350624"},{name:"长泰县",code:"350625"},{name:"东山县",code:"350626"},{name:"南靖县",code:"350627"},{name:"平和县",code:"350628"},{name:"华安县",code:"350629"},{name:"龙海市",code:"350681"}]},{name:"南平市",code:"350700",children:[{name:"延平区",code:"350702"},{name:"建阳区",code:"350703"},{name:"顺昌县",code:"350721"},{name:"浦城县",code:"350722"},{name:"光泽县",code:"350723"},{name:"松溪县",code:"350724"},{name:"政和县",code:"350725"},{name:"邵武市",code:"350781"},{name:"武夷山市",code:"350782"},{name:"建瓯市",code:"350783"}]},{name:"龙岩市",code:"350800",children:[{name:"新罗区",code:"350802"},{name:"永定区",code:"350803"},{name:"长汀县",code:"350821"},{name:"上杭县",code:"350823"},{name:"武平县",code:"350824"},{name:"连城县",code:"350825"},{name:"漳平市",code:"350881"}]},{name:"宁德市",code:"350900",children:[{name:"蕉城区",code:"350902"},{name:"霞浦县",code:"350921"},{name:"古田县",code:"350922"},{name:"屏南县",code:"350923"},{name:"寿宁县",code:"350924"},{name:"周宁县",code:"350925"},{name:"柘荣县",code:"350926"},{name:"福安市",code:"350981"},{name:"福鼎市",code:"350982"}]}]},{name:"江西省",code:"360000",region:"east",children:[{name:"南昌市",code:"360100",children:[{name:"东湖区",code:"360102"},{name:"西湖区",code:"360103"},{name:"青云谱区",code:"360104"},{name:"湾里区",code:"360105"},{name:"青山湖区",code:"360111"},{name:"新建区",code:"360112"},{name:"南昌县",code:"360121"},{name:"安义县",code:"360123"},{name:"进贤县",code:"360124"}]},{name:"景德镇市",code:"360200",children:[{name:"昌江区",code:"360202"},{name:"珠山区",code:"360203"},{name:"浮梁县",code:"360222"},{name:"乐平市",code:"360281"}]},{name:"萍乡市",code:"360300",children:[{name:"安源区",code:"360302"},{name:"湘东区",code:"360313"},{name:"莲花县",code:"360321"},{name:"上栗县",code:"360322"},{name:"芦溪县",code:"360323"}]},{name:"九江市",code:"360400",children:[{name:"濂溪区",code:"360402"},{name:"浔阳区",code:"360403"},{name:"柴桑区",code:"360404"},{name:"武宁县",code:"360423"},{name:"修水县",code:"360424"},{name:"永修县",code:"360425"},{name:"德安县",code:"360426"},{name:"都昌县",code:"360428"},{name:"湖口县",code:"360429"},{name:"彭泽县",code:"360430"},{name:"瑞昌市",code:"360481"},{name:"共青城市",code:"360482"},{name:"庐山市",code:"360483"}]},{name:"新余市",code:"360500",children:[{name:"渝水区",code:"360502"},{name:"分宜县",code:"360521"}]},{name:"鹰潭市",code:"360600",children:[{name:"月湖区",code:"360602"},{name:"余江区",code:"360603"},{name:"贵溪市",code:"360681"}]},{name:"赣州市",code:"360700",children:[{name:"章贡区",code:"360702"},{name:"南康区",code:"360703"},{name:"赣县区",code:"360704"},{name:"信丰县",code:"360722"},{name:"大余县",code:"360723"},{name:"上犹县",code:"360724"},{name:"崇义县",code:"360725"},{name:"安远县",code:"360726"},{name:"龙南县",code:"360727"},{name:"定南县",code:"360728"},{name:"全南县",code:"360729"},{name:"宁都县",code:"360730"},{name:"于都县",code:"360731"},{name:"兴国县",code:"360732"},{name:"会昌县",code:"360733"},{name:"寻乌县",code:"360734"},{name:"石城县",code:"360735"},{name:"瑞金市",code:"360781"}]},{name:"吉安市",code:"360800",children:[{name:"吉州区",code:"360802"},{name:"青原区",code:"360803"},{name:"吉安县",code:"360821"},{name:"吉水县",code:"360822"},{name:"峡江县",code:"360823"},{name:"新干县",code:"360824"},{name:"永丰县",code:"360825"},{name:"泰和县",code:"360826"},{name:"遂川县",code:"360827"},{name:"万安县",code:"360828"},{name:"安福县",code:"360829"},{name:"永新县",code:"360830"},{name:"井冈山市",code:"360881"}]},{name:"宜春市",code:"360900",children:[{name:"袁州区",code:"360902"},{name:"奉新县",code:"360921"},{name:"万载县",code:"360922"},{name:"上高县",code:"360923"},{name:"宜丰县",code:"360924"},{name:"靖安县",code:"360925"},{name:"铜鼓县",code:"360926"},{name:"丰城市",code:"360981"},{name:"樟树市",code:"360982"},{name:"高安市",code:"360983"}]},{name:"抚州市",code:"361000",children:[{name:"临川区",code:"361002"},{name:"东乡区",code:"361003"},{name:"南城县",code:"361021"},{name:"黎川县",code:"361022"},{name:"南丰县",code:"361023"},{name:"崇仁县",code:"361024"},{name:"乐安县",code:"361025"},{name:"宜黄县",code:"361026"},{name:"金溪县",code:"361027"},{name:"资溪县",code:"361028"},{name:"广昌县",code:"361030"}]},{name:"上饶市",code:"361100",children:[{name:"信州区",code:"361102"},{name:"广丰区",code:"361103"},{name:"上饶县",code:"361121"},{name:"玉山县",code:"361123"},{name:"铅山县",code:"361124"},{name:"横峰县",code:"361125"},{name:"弋阳县",code:"361126"},{name:"余干县",code:"361127"},{name:"鄱阳县",code:"361128"},{name:"万年县",code:"361129"},{name:"婺源县",code:"361130"},{name:"德兴市",code:"361181"}]}]},{name:"山东省",code:"370000",region:"east",children:[{name:"济南市",code:"370100",children:[{name:"历下区",code:"370102"},{name:"市中区",code:"370103"},{name:"槐荫区",code:"370104"},{name:"天桥区",code:"370105"},{name:"历城区",code:"370112"},{name:"长清区",code:"370113"},{name:"章丘区",code:"370114"},{name:"济阳区",code:"370115"},{name:"莱芜区",code:"370116"},{name:"钢城区",code:"370117"},{name:"平阴县",code:"370124"},{name:"商河县",code:"370126"}]},{name:"青岛市",code:"370200",children:[{name:"市南区",code:"370202"},{name:"市北区",code:"370203"},{name:"黄岛区",code:"370211"},{name:"崂山区",code:"370212"},{name:"李沧区",code:"370213"},{name:"城阳区",code:"370214"},{name:"即墨区",code:"370215"},{name:"胶州市",code:"370281"},{name:"平度市",code:"370283"},{name:"莱西市",code:"370285"}]},{name:"淄博市",code:"370300",children:[{name:"淄川区",code:"370302"},{name:"张店区",code:"370303"},{name:"博山区",code:"370304"},{name:"临淄区",code:"370305"},{name:"周村区",code:"370306"},{name:"桓台县",code:"370321"},{name:"高青县",code:"370322"},{name:"沂源县",code:"370323"}]},{name:"枣庄市",code:"370400",children:[{name:"市中区",code:"370402"},{name:"薛城区",code:"370403"},{name:"峄城区",code:"370404"},{name:"台儿庄区",code:"370405"},{name:"山亭区",code:"370406"},{name:"滕州市",code:"370481"}]},{name:"东营市",code:"370500",children:[{name:"东营区",code:"370502"},{name:"河口区",code:"370503"},{name:"垦利区",code:"370505"},{name:"利津县",code:"370522"},{name:"广饶县",code:"370523"}]},{name:"烟台市",code:"370600",children:[{name:"芝罘区",code:"370602"},{name:"福山区",code:"370611"},{name:"牟平区",code:"370612"},{name:"莱山区",code:"370613"},{name:"长岛县",code:"370634"},{name:"龙口市",code:"370681"},{name:"莱阳市",code:"370682"},{name:"莱州市",code:"370683"},{name:"蓬莱市",code:"370684"},{name:"招远市",code:"370685"},{name:"栖霞市",code:"370686"},{name:"海阳市",code:"370687"}]},{name:"潍坊市",code:"370700",children:[{name:"潍城区",code:"370702"},{name:"寒亭区",code:"370703"},{name:"坊子区",code:"370704"},{name:"奎文区",code:"370705"},{name:"临朐县",code:"370724"},{name:"昌乐县",code:"370725"},{name:"青州市",code:"370781"},{name:"诸城市",code:"370782"},{name:"寿光市",code:"370783"},{name:"安丘市",code:"370784"},{name:"高密市",code:"370785"},{name:"昌邑市",code:"370786"}]},{name:"济宁市",code:"370800",children:[{name:"任城区",code:"370811"},{name:"兖州区",code:"370812"},{name:"微山县",code:"370826"},{name:"鱼台县",code:"370827"},{name:"金乡县",code:"370828"},{name:"嘉祥县",code:"370829"},{name:"汶上县",code:"370830"},{name:"泗水县",code:"370831"},{name:"梁山县",code:"370832"},{name:"曲阜市",code:"370881"},{name:"邹城市",code:"370883"}]},{name:"泰安市",code:"370900",children:[{name:"泰山区",code:"370902"},{name:"岱岳区",code:"370911"},{name:"宁阳县",code:"370921"},{name:"东平县",code:"370923"},{name:"新泰市",code:"370982"},{name:"肥城市",code:"370983"}]},{name:"威海市",code:"371000",children:[{name:"环翠区",code:"371002"},{name:"文登区",code:"371003"},{name:"荣成市",code:"371082"},{name:"乳山市",code:"371083"}]},{name:"日照市",code:"371100",children:[{name:"东港区",code:"371102"},{name:"岚山区",code:"371103"},{name:"五莲县",code:"371121"},{name:"莒县",code:"371122"}]},{name:"临沂市",code:"371300",children:[{name:"兰山区",code:"371302"},{name:"罗庄区",code:"371311"},{name:"河东区",code:"371312"},{name:"沂南县",code:"371321"},{name:"郯城县",code:"371322"},{name:"沂水县",code:"371323"},{name:"兰陵县",code:"371324"},{name:"费县",code:"371325"},{name:"平邑县",code:"371326"},{name:"莒南县",code:"371327"},{name:"蒙阴县",code:"371328"},{name:"临沭县",code:"371329"}]},{name:"德州市",code:"371400",children:[{name:"德城区",code:"371402"},{name:"陵城区",code:"371403"},{name:"宁津县",code:"371422"},{name:"庆云县",code:"371423"},{name:"临邑县",code:"371424"},{name:"齐河县",code:"371425"},{name:"平原县",code:"371426"},{name:"夏津县",code:"371427"},{name:"武城县",code:"371428"},{name:"乐陵市",code:"371481"},{name:"禹城市",code:"371482"}]},{name:"聊城市",code:"371500",children:[{name:"东昌府区",code:"371502"},{name:"阳谷县",code:"371521"},{name:"莘县",code:"371522"},{name:"茌平县",code:"371523"},{name:"东阿县",code:"371524"},{name:"冠县",code:"371525"},{name:"高唐县",code:"371526"},{name:"临清市",code:"371581"}]},{name:"滨州市",code:"371600",children:[{name:"滨城区",code:"371602"},{name:"沾化区",code:"371603"},{name:"惠民县",code:"371621"},{name:"阳信县",code:"371622"},{name:"无棣县",code:"371623"},{name:"博兴县",code:"371625"},{name:"邹平市",code:"371681"}]},{name:"菏泽市",code:"371700",children:[{name:"牡丹区",code:"371702"},{name:"定陶区",code:"371703"},{name:"曹县",code:"371721"},{name:"单县",code:"371722"},{name:"成武县",code:"371723"},{name:"巨野县",code:"371724"},{name:"郓城县",code:"371725"},{name:"鄄城县",code:"371726"},{name:"东明县",code:"371728"}]}]},{name:"河南省",code:"410000",region:"central",children:[{name:"郑州市",code:"410100",children:[{name:"中原区",code:"410102"},{name:"二七区",code:"410103"},{name:"管城回族区",code:"410104"},{name:"金水区",code:"410105"},{name:"上街区",code:"410106"},{name:"惠济区",code:"410108"},{name:"中牟县",code:"410122"},{name:"巩义市",code:"410181"},{name:"荥阳市",code:"410182"},{name:"新密市",code:"410183"},{name:"新郑市",code:"410184"},{name:"登封市",code:"410185"}]},{name:"开封市",code:"410200",children:[{name:"龙亭区",code:"410202"},{name:"顺河回族区",code:"410203"},{name:"鼓楼区",code:"410204"},{name:"禹王台区",code:"410205"},{name:"祥符区",code:"410212"},{name:"杞县",code:"410221"},{name:"通许县",code:"410222"},{name:"尉氏县",code:"410223"},{name:"兰考县",code:"410225"}]},{name:"洛阳市",code:"410300",children:[{name:"老城区",code:"410302"},{name:"西工区",code:"410303"},{name:"瀍河回族区",code:"410304"},{name:"涧西区",code:"410305"},{name:"吉利区",code:"410306"},{name:"洛龙区",code:"410311"},{name:"孟津县",code:"410322"},{name:"新安县",code:"410323"},{name:"栾川县",code:"410324"},{name:"嵩县",code:"410325"},{name:"汝阳县",code:"410326"},{name:"宜阳县",code:"410327"},{name:"洛宁县",code:"410328"},{name:"伊川县",code:"410329"},{name:"偃师市",code:"410381"}]},{name:"平顶山市",code:"410400",children:[{name:"新华区",code:"410402"},{name:"卫东区",code:"410403"},{name:"石龙区",code:"410404"},{name:"湛河区",code:"410411"},{name:"宝丰县",code:"410421"},{name:"叶县",code:"410422"},{name:"鲁山县",code:"410423"},{name:"郏县",code:"410425"},{name:"舞钢市",code:"410481"},{name:"汝州市",code:"410482"}]},{name:"安阳市",code:"410500",children:[{name:"文峰区",code:"410502"},{name:"北关区",code:"410503"},{name:"殷都区",code:"410505"},{name:"龙安区",code:"410506"},{name:"安阳县",code:"410522"},{name:"汤阴县",code:"410523"},{name:"滑县",code:"410526"},{name:"内黄县",code:"410527"},{name:"林州市",code:"410581"}]},{name:"鹤壁市",code:"410600",children:[{name:"鹤山区",code:"410602"},{name:"山城区",code:"410603"},{name:"淇滨区",code:"410611"},{name:"浚县",code:"410621"},{name:"淇县",code:"410622"}]},{name:"新乡市",code:"410700",children:[{name:"红旗区",code:"410702"},{name:"卫滨区",code:"410703"},{name:"凤泉区",code:"410704"},{name:"牧野区",code:"410711"},{name:"新乡县",code:"410721"},{name:"获嘉县",code:"410724"},{name:"原阳县",code:"410725"},{name:"延津县",code:"410726"},{name:"封丘县",code:"410727"},{name:"长垣县",code:"410728"},{name:"卫辉市",code:"410781"},{name:"辉县市",code:"410782"}]},{name:"焦作市",code:"410800",children:[{name:"解放区",code:"410802"},{name:"中站区",code:"410803"},{name:"马村区",code:"410804"},{name:"山阳区",code:"410811"},{name:"修武县",code:"410821"},{name:"博爱县",code:"410822"},{name:"武陟县",code:"410823"},{name:"温县",code:"410825"},{name:"沁阳市",code:"410882"},{name:"孟州市",code:"410883"}]},{name:"濮阳市",code:"410900",children:[{name:"华龙区",code:"410902"},{name:"清丰县",code:"410922"},{name:"南乐县",code:"410923"},{name:"范县",code:"410926"},{name:"台前县",code:"410927"},{name:"濮阳县",code:"410928"}]},{name:"许昌市",code:"411000",children:[{name:"魏都区",code:"411002"},{name:"建安区",code:"411003"},{name:"鄢陵县",code:"411024"},{name:"襄城县",code:"411025"},{name:"禹州市",code:"411081"},{name:"长葛市",code:"411082"}]},{name:"漯河市",code:"411100",children:[{name:"源汇区",code:"411102"},{name:"郾城区",code:"411103"},{name:"召陵区",code:"411104"},{name:"舞阳县",code:"411121"},{name:"临颍县",code:"411122"}]},{name:"三门峡市",code:"411200",children:[{name:"湖滨区",code:"411202"},{name:"陕州区",code:"411203"},{name:"渑池县",code:"411221"},{name:"卢氏县",code:"411224"},{name:"义马市",code:"411281"},{name:"灵宝市",code:"411282"}]},{name:"南阳市",code:"411300",children:[{name:"宛城区",code:"411302"},{name:"卧龙区",code:"411303"},{name:"南召县",code:"411321"},{name:"方城县",code:"411322"},{name:"西峡县",code:"411323"},{name:"镇平县",code:"411324"},{name:"内乡县",code:"411325"},{name:"淅川县",code:"411326"},{name:"社旗县",code:"411327"},{name:"唐河县",code:"411328"},{name:"新野县",code:"411329"},{name:"桐柏县",code:"411330"},{name:"邓州市",code:"411381"}]},{name:"商丘市",code:"411400",children:[{name:"梁园区",code:"411402"},{name:"睢阳区",code:"411403"},{name:"民权县",code:"411421"},{name:"睢县",code:"411422"},{name:"宁陵县",code:"411423"},{name:"柘城县",code:"411424"},{name:"虞城县",code:"411425"},{name:"夏邑县",code:"411426"},{name:"永城市",code:"411481"}]},{name:"信阳市",code:"411500",children:[{name:"浉河区",code:"411502"},{name:"平桥区",code:"411503"},{name:"罗山县",code:"411521"},{name:"光山县",code:"411522"},{name:"新县",code:"411523"},{name:"商城县",code:"411524"},{name:"固始县",code:"411525"},{name:"潢川县",code:"411526"},{name:"淮滨县",code:"411527"},{name:"息县",code:"411528"}]},{name:"周口市",code:"411600",children:[{name:"川汇区",code:"411602"},{name:"扶沟县",code:"411621"},{name:"西华县",code:"411622"},{name:"商水县",code:"411623"},{name:"沈丘县",code:"411624"},{name:"郸城县",code:"411625"},{name:"淮阳县",code:"411626"},{name:"太康县",code:"411627"},{name:"鹿邑县",code:"411628"},{name:"项城市",code:"411681"}]},{name:"驻马店市",code:"411700",children:[{name:"驿城区",code:"411702"},{name:"西平县",code:"411721"},{name:"上蔡县",code:"411722"},{name:"平舆县",code:"411723"},{name:"正阳县",code:"411724"},{name:"确山县",code:"411725"},{name:"泌阳县",code:"411726"},{name:"汝南县",code:"411727"},{name:"遂平县",code:"411728"},{name:"新蔡县",code:"411729"}]},{name:"直辖县",code:"419000",children:[{name:"济源市",code:"419001"}]}]},{name:"湖北省",code:"420000",region:"central",children:[{name:"武汉市",code:"420100",children:[{name:"江岸区",code:"420102"},{name:"江汉区",code:"420103"},{name:"硚口区",code:"420104"},{name:"汉阳区",code:"420105"},{name:"武昌区",code:"420106"},{name:"青山区",code:"420107"},{name:"洪山区",code:"420111"},{name:"东西湖区",code:"420112"},{name:"汉南区",code:"420113"},{name:"蔡甸区",code:"420114"},{name:"江夏区",code:"420115"},{name:"黄陂区",code:"420116"},{name:"新洲区",code:"420117"}]},{name:"黄石市",code:"420200",children:[{name:"黄石港区",code:"420202"},{name:"西塞山区",code:"420203"},{name:"下陆区",code:"420204"},{name:"铁山区",code:"420205"},{name:"阳新县",code:"420222"},{name:"大冶市",code:"420281"}]},{name:"十堰市",code:"420300",children:[{name:"茅箭区",code:"420302"},{name:"张湾区",code:"420303"},{name:"郧阳区",code:"420304"},{name:"郧西县",code:"420322"},{name:"竹山县",code:"420323"},{name:"竹溪县",code:"420324"},{name:"房县",code:"420325"},{name:"丹江口市",code:"420381"}]},{name:"宜昌市",code:"420500",children:[{name:"西陵区",code:"420502"},{name:"伍家岗区",code:"420503"},{name:"点军区",code:"420504"},{name:"猇亭区",code:"420505"},{name:"夷陵区",code:"420506"},{name:"远安县",code:"420525"},{name:"兴山县",code:"420526"},{name:"秭归县",code:"420527"},{name:"长阳土家族自治县",code:"420528"},{name:"五峰土家族自治县",code:"420529"},{name:"宜都市",code:"420581"},{name:"当阳市",code:"420582"},{name:"枝江市",code:"420583"}]},{name:"襄阳市",code:"420600",children:[{name:"襄城区",code:"420602"},{name:"樊城区",code:"420606"},{name:"襄州区",code:"420607"},{name:"南漳县",code:"420624"},{name:"谷城县",code:"420625"},{name:"保康县",code:"420626"},{name:"老河口市",code:"420682"},{name:"枣阳市",code:"420683"},{name:"宜城市",code:"420684"}]},{name:"鄂州市",code:"420700",children:[{name:"梁子湖区",code:"420702"},{name:"华容区",code:"420703"},{name:"鄂城区",code:"420704"}]},{name:"荆门市",code:"420800",children:[{name:"东宝区",code:"420802"},{name:"掇刀区",code:"420804"},{name:"沙洋县",code:"420822"},{name:"钟祥市",code:"420881"},{name:"京山市",code:"420882"}]},{name:"孝感市",code:"420900",children:[{name:"孝南区",code:"420902"},{name:"孝昌县",code:"420921"},{name:"大悟县",code:"420922"},{name:"云梦县",code:"420923"},{name:"应城市",code:"420981"},{name:"安陆市",code:"420982"},{name:"汉川市",code:"420984"}]},{name:"荆州市",code:"421000",children:[{name:"沙市区",code:"421002"},{name:"荆州区",code:"421003"},{name:"公安县",code:"421022"},{name:"监利县",code:"421023"},{name:"江陵县",code:"421024"},{name:"石首市",code:"421081"},{name:"洪湖市",code:"421083"},{name:"松滋市",code:"421087"}]},{name:"黄冈市",code:"421100",children:[{name:"黄州区",code:"421102"},{name:"团风县",code:"421121"},{name:"红安县",code:"421122"},{name:"罗田县",code:"421123"},{name:"英山县",code:"421124"},{name:"浠水县",code:"421125"},{name:"蕲春县",code:"421126"},{name:"黄梅县",code:"421127"},{name:"麻城市",code:"421181"},{name:"武穴市",code:"421182"}]},{name:"咸宁市",code:"421200",children:[{name:"咸安区",code:"421202"},{name:"嘉鱼县",code:"421221"},{name:"通城县",code:"421222"},{name:"崇阳县",code:"421223"},{name:"通山县",code:"421224"},{name:"赤壁市",code:"421281"}]},{name:"随州市",code:"421300",children:[{name:"曾都区",code:"421303"},{name:"随县",code:"421321"},{name:"广水市",code:"421381"}]},{name:"恩施土家族苗族自治州",code:"422800",children:[{name:"恩施市",code:"422801"},{name:"利川市",code:"422802"},{name:"建始县",code:"422822"},{name:"巴东县",code:"422823"},{name:"宣恩县",code:"422825"},{name:"咸丰县",code:"422826"},{name:"来凤县",code:"422827"},{name:"鹤峰县",code:"422828"}]},{name:"直辖县",code:"429000",children:[{name:"仙桃市",code:"429004"},{name:"潜江市",code:"429005"},{name:"天门市",code:"429006"},{name:"神农架林区",code:"429021"}]}]},{name:"湖南省",code:"430000",region:"central",children:[{name:"长沙市",code:"430100",children:[{name:"芙蓉区",code:"430102"},{name:"天心区",code:"430103"},{name:"岳麓区",code:"430104"},{name:"开福区",code:"430105"},{name:"雨花区",code:"430111"},{name:"望城区",code:"430112"},{name:"长沙县",code:"430121"},{name:"浏阳市",code:"430181"},{name:"宁乡市",code:"430182"}]},{name:"株洲市",code:"430200",children:[{name:"荷塘区",code:"430202"},{name:"芦淞区",code:"430203"},{name:"石峰区",code:"430204"},{name:"天元区",code:"430211"},{name:"渌口区",code:"430212"},{name:"攸县",code:"430223"},{name:"茶陵县",code:"430224"},{name:"炎陵县",code:"430225"},{name:"醴陵市",code:"430281"}]},{name:"湘潭市",code:"430300",children:[{name:"雨湖区",code:"430302"},{name:"岳塘区",code:"430304"},{name:"湘潭县",code:"430321"},{name:"湘乡市",code:"430381"},{name:"韶山市",code:"430382"}]},{name:"衡阳市",code:"430400",children:[{name:"珠晖区",code:"430405"},{name:"雁峰区",code:"430406"},{name:"石鼓区",code:"430407"},{name:"蒸湘区",code:"430408"},{name:"南岳区",code:"430412"},{name:"衡阳县",code:"430421"},{name:"衡南县",code:"430422"},{name:"衡山县",code:"430423"},{name:"衡东县",code:"430424"},{name:"祁东县",code:"430426"},{name:"耒阳市",code:"430481"},{name:"常宁市",code:"430482"}]},{name:"邵阳市",code:"430500",children:[{name:"双清区",code:"430502"},{name:"大祥区",code:"430503"},{name:"北塔区",code:"430511"},{name:"邵东县",code:"430521"},{name:"新邵县",code:"430522"},{name:"邵阳县",code:"430523"},{name:"隆回县",code:"430524"},{name:"洞口县",code:"430525"},{name:"绥宁县",code:"430527"},{name:"新宁县",code:"430528"},{name:"城步苗族自治县",code:"430529"},{name:"武冈市",code:"430581"}]},{name:"岳阳市",code:"430600",children:[{name:"岳阳楼区",code:"430602"},{name:"云溪区",code:"430603"},{name:"君山区",code:"430611"},{name:"岳阳县",code:"430621"},{name:"华容县",code:"430623"},{name:"湘阴县",code:"430624"},{name:"平江县",code:"430626"},{name:"汨罗市",code:"430681"},{name:"临湘市",code:"430682"}]},{name:"常德市",code:"430700",children:[{name:"武陵区",code:"430702"},{name:"鼎城区",code:"430703"},{name:"安乡县",code:"430721"},{name:"汉寿县",code:"430722"},{name:"澧县",code:"430723"},{name:"临澧县",code:"430724"},{name:"桃源县",code:"430725"},{name:"石门县",code:"430726"},{name:"津市市",code:"430781"}]},{name:"张家界市",code:"430800",children:[{name:"永定区",code:"430802"},{name:"武陵源区",code:"430811"},{name:"慈利县",code:"430821"},{name:"桑植县",code:"430822"}]},{name:"益阳市",code:"430900",children:[{name:"资阳区",code:"430902"},{name:"赫山区",code:"430903"},{name:"南县",code:"430921"},{name:"桃江县",code:"430922"},{name:"安化县",code:"430923"},{name:"沅江市",code:"430981"}]},{name:"郴州市",code:"431000",children:[{name:"北湖区",code:"431002"},{name:"苏仙区",code:"431003"},{name:"桂阳县",code:"431021"},{name:"宜章县",code:"431022"},{name:"永兴县",code:"431023"},{name:"嘉禾县",code:"431024"},{name:"临武县",code:"431025"},{name:"汝城县",code:"431026"},{name:"桂东县",code:"431027"},{name:"安仁县",code:"431028"},{name:"资兴市",code:"431081"}]},{name:"永州市",code:"431100",children:[{name:"零陵区",code:"431102"},{name:"冷水滩区",code:"431103"},{name:"祁阳县",code:"431121"},{name:"东安县",code:"431122"},{name:"双牌县",code:"431123"},{name:"道县",code:"431124"},{name:"江永县",code:"431125"},{name:"宁远县",code:"431126"},{name:"蓝山县",code:"431127"},{name:"新田县",code:"431128"},{name:"江华瑶族自治县",code:"431129"}]},{name:"怀化市",code:"431200",children:[{name:"鹤城区",code:"431202"},{name:"中方县",code:"431221"},{name:"沅陵县",code:"431222"},{name:"辰溪县",code:"431223"},{name:"溆浦县",code:"431224"},{name:"会同县",code:"431225"},{name:"麻阳苗族自治县",code:"431226"},{name:"新晃侗族自治县",code:"431227"},{name:"芷江侗族自治县",code:"431228"},{name:"靖州苗族侗族自治县",code:"431229"},{name:"通道侗族自治县",code:"431230"},{name:"洪江市",code:"431281"}]},{name:"娄底市",code:"431300",children:[{name:"娄星区",code:"431302"},{name:"双峰县",code:"431321"},{name:"新化县",code:"431322"},{name:"冷水江市",code:"431381"},{name:"涟源市",code:"431382"}]},{name:"湘西土家族苗族自治州",code:"433100",children:[{name:"吉首市",code:"433101"},{name:"泸溪县",code:"433122"},{name:"凤凰县",code:"433123"},{name:"花垣县",code:"433124"},{name:"保靖县",code:"433125"},{name:"古丈县",code:"433126"},{name:"永顺县",code:"433127"},{name:"龙山县",code:"433130"}]}]},{name:"广东省",code:"440000",region:"south",children:[{name:"广州市",code:"440100",children:[{name:"荔湾区",code:"440103"},{name:"越秀区",code:"440104"},{name:"海珠区",code:"440105"},{name:"天河区",code:"440106"},{name:"白云区",code:"440111"},{name:"黄埔区",code:"440112"},{name:"番禺区",code:"440113"},{name:"花都区",code:"440114"},{name:"南沙区",code:"440115"},{name:"从化区",code:"440117"},{name:"增城区",code:"440118"}]},{name:"韶关市",code:"440200",children:[{name:"武江区",code:"440203"},{name:"浈江区",code:"440204"},{name:"曲江区",code:"440205"},{name:"始兴县",code:"440222"},{name:"仁化县",code:"440224"},{name:"翁源县",code:"440229"},{name:"乳源瑶族自治县",code:"440232"},{name:"新丰县",code:"440233"},{name:"乐昌市",code:"440281"},{name:"南雄市",code:"440282"}]},{name:"深圳市",code:"440300",children:[{name:"罗湖区",code:"440303"},{name:"福田区",code:"440304"},{name:"南山区",code:"440305"},{name:"宝安区",code:"440306"},{name:"龙岗区",code:"440307"},{name:"盐田区",code:"440308"},{name:"龙华区",code:"440309"},{name:"坪山区",code:"440310"},{name:"光明区",code:"440311"}]},{name:"珠海市",code:"440400",children:[{name:"香洲区",code:"440402"},{name:"斗门区",code:"440403"},{name:"金湾区",code:"440404"}]},{name:"汕头市",code:"440500",children:[{name:"龙湖区",code:"440507"},{name:"金平区",code:"440511"},{name:"濠江区",code:"440512"},{name:"潮阳区",code:"440513"},{name:"潮南区",code:"440514"},{name:"澄海区",code:"440515"},{name:"南澳县",code:"440523"}]},{name:"佛山市",code:"440600",children:[{name:"禅城区",code:"440604"},{name:"南海区",code:"440605"},{name:"顺德区",code:"440606"},{name:"三水区",code:"440607"},{name:"高明区",code:"440608"}]},{name:"江门市",code:"440700",children:[{name:"蓬江区",code:"440703"},{name:"江海区",code:"440704"},{name:"新会区",code:"440705"},{name:"台山市",code:"440781"},{name:"开平市",code:"440783"},{name:"鹤山市",code:"440784"},{name:"恩平市",code:"440785"}]},{name:"湛江市",code:"440800",children:[{name:"赤坎区",code:"440802"},{name:"霞山区",code:"440803"},{name:"坡头区",code:"440804"},{name:"麻章区",code:"440811"},{name:"遂溪县",code:"440823"},{name:"徐闻县",code:"440825"},{name:"廉江市",code:"440881"},{name:"雷州市",code:"440882"},{name:"吴川市",code:"440883"}]},{name:"茂名市",code:"440900",children:[{name:"茂南区",code:"440902"},{name:"电白区",code:"440904"},{name:"高州市",code:"440981"},{name:"化州市",code:"440982"},{name:"信宜市",code:"440983"}]},{name:"肇庆市",code:"441200",children:[{name:"端州区",code:"441202"},{name:"鼎湖区",code:"441203"},{name:"高要区",code:"441204"},{name:"广宁县",code:"441223"},{name:"怀集县",code:"441224"},{name:"封开县",code:"441225"},{name:"德庆县",code:"441226"},{name:"四会市",code:"441284"}]},{name:"惠州市",code:"441300",children:[{name:"惠城区",code:"441302"},{name:"惠阳区",code:"441303"},{name:"博罗县",code:"441322"},{name:"惠东县",code:"441323"},{name:"龙门县",code:"441324"}]},{name:"梅州市",code:"441400",children:[{name:"梅江区",code:"441402"},{name:"梅县区",code:"441403"},{name:"大埔县",code:"441422"},{name:"丰顺县",code:"441423"},{name:"五华县",code:"441424"},{name:"平远县",code:"441426"},{name:"蕉岭县",code:"441427"},{name:"兴宁市",code:"441481"}]},{name:"汕尾市",code:"441500",children:[{name:"城区",code:"441502"},{name:"海丰县",code:"441521"},{name:"陆河县",code:"441523"},{name:"陆丰市",code:"441581"}]},{name:"河源市",code:"441600",children:[{name:"源城区",code:"441602"},{name:"紫金县",code:"441621"},{name:"龙川县",code:"441622"},{name:"连平县",code:"441623"},{name:"和平县",code:"441624"},{name:"东源县",code:"441625"}]},{name:"阳江市",code:"441700",children:[{name:"江城区",code:"441702"},{name:"阳东区",code:"441704"},{name:"阳西县",code:"441721"},{name:"阳春市",code:"441781"}]},{name:"清远市",code:"441800",children:[{name:"清城区",code:"441802"},{name:"清新区",code:"441803"},{name:"佛冈县",code:"441821"},{name:"阳山县",code:"441823"},{name:"连山壮族瑶族自治县",code:"441825"},{name:"连南瑶族自治县",code:"441826"},{name:"英德市",code:"441881"},{name:"连州市",code:"441882"}]},{name:"东莞市",code:"441900",children:[]},{name:"中山市",code:"442000",children:[]},{name:"潮州市",code:"445100",children:[{name:"湘桥区",code:"445102"},{name:"潮安区",code:"445103"},{name:"饶平县",code:"445122"}]},{name:"揭阳市",code:"445200",children:[{name:"榕城区",code:"445202"},{name:"揭东区",code:"445203"},{name:"揭西县",code:"445222"},{name:"惠来县",code:"445224"},{name:"普宁市",code:"445281"}]},{name:"云浮市",code:"445300",children:[{name:"云城区",code:"445302"},{name:"云安区",code:"445303"},{name:"新兴县",code:"445321"},{name:"郁南县",code:"445322"},{name:"罗定市",code:"445381"}]}]},{name:"广西壮族自治区",code:"450000",region:"south",autonomousRegion:!0,children:[{name:"南宁市",code:"450100",children:[{name:"兴宁区",code:"450102"},{name:"青秀区",code:"450103"},{name:"江南区",code:"450105"},{name:"西乡塘区",code:"450107"},{name:"良庆区",code:"450108"},{name:"邕宁区",code:"450109"},{name:"武鸣区",code:"450110"},{name:"隆安县",code:"450123"},{name:"马山县",code:"450124"},{name:"上林县",code:"450125"},{name:"宾阳县",code:"450126"},{name:"横县",code:"450127"}]},{name:"柳州市",code:"450200",children:[{name:"城中区",code:"450202"},{name:"鱼峰区",code:"450203"},{name:"柳南区",code:"450204"},{name:"柳北区",code:"450205"},{name:"柳江区",code:"450206"},{name:"柳城县",code:"450222"},{name:"鹿寨县",code:"450223"},{name:"融安县",code:"450224"},{name:"融水苗族自治县",code:"450225"},{name:"三江侗族自治县",code:"450226"}]},{name:"桂林市",code:"450300",children:[{name:"秀峰区",code:"450302"},{name:"叠彩区",code:"450303"},{name:"象山区",code:"450304"},{name:"七星区",code:"450305"},{name:"雁山区",code:"450311"},{name:"临桂区",code:"450312"},{name:"阳朔县",code:"450321"},{name:"灵川县",code:"450323"},{name:"全州县",code:"450324"},{name:"兴安县",code:"450325"},{name:"永福县",code:"450326"},{name:"灌阳县",code:"450327"},{name:"龙胜各族自治县",code:"450328"},{name:"资源县",code:"450329"},{name:"平乐县",code:"450330"},{name:"荔浦市",code:"450381"},{name:"恭城瑶族自治县",code:"450332"}]},{name:"梧州市",code:"450400",children:[{name:"万秀区",code:"450403"},{name:"长洲区",code:"450405"},{name:"龙圩区",code:"450406"},{name:"苍梧县",code:"450421"},{name:"藤县",code:"450422"},{name:"蒙山县",code:"450423"},{name:"岑溪市",code:"450481"}]},{name:"北海市",code:"450500",children:[{name:"海城区",code:"450502"},{name:"银海区",code:"450503"},{name:"铁山港区",code:"450512"},{name:"合浦县",code:"450521"}]},{name:"防城港市",code:"450600",children:[{name:"港口区",code:"450602"},{name:"防城区",code:"450603"},{name:"上思县",code:"450621"},{name:"东兴市",code:"450681"}]},{name:"钦州市",code:"450700",children:[{name:"钦南区",code:"450702"},{name:"钦北区",code:"450703"},{name:"灵山县",code:"450721"},{name:"浦北县",code:"450722"}]},{name:"贵港市",code:"450800",children:[{name:"港北区",code:"450802"},{name:"港南区",code:"450803"},{name:"覃塘区",code:"450804"},{name:"平南县",code:"450821"},{name:"桂平市",code:"450881"}]},{name:"玉林市",code:"450900",children:[{name:"玉州区",code:"450902"},{name:"福绵区",code:"450903"},{name:"容县",code:"450921"},{name:"陆川县",code:"450922"},{name:"博白县",code:"450923"},{name:"兴业县",code:"450924"},{name:"北流市",code:"450981"}]},{name:"百色市",code:"451000",children:[{name:"右江区",code:"451002"},{name:"田阳县",code:"451021"},{name:"田东县",code:"451022"},{name:"平果县",code:"451023"},{name:"德保县",code:"451024"},{name:"那坡县",code:"451026"},{name:"凌云县",code:"451027"},{name:"乐业县",code:"451028"},{name:"田林县",code:"451029"},{name:"西林县",code:"451030"},{name:"隆林各族自治县",code:"451031"},{name:"靖西市",code:"451081"}]},{name:"贺州市",code:"451100",children:[{name:"八步区",code:"451102"},{name:"平桂区",code:"451103"},{name:"昭平县",code:"451121"},{name:"钟山县",code:"451122"},{name:"富川瑶族自治县",code:"451123"}]},{name:"河池市",code:"451200",children:[{name:"金城江区",code:"451202"},{name:"宜州区",code:"451203"},{name:"南丹县",code:"451221"},{name:"天峨县",code:"451222"},{name:"凤山县",code:"451223"},{name:"东兰县",code:"451224"},{name:"罗城仫佬族自治县",code:"451225"},{name:"环江毛南族自治县",code:"451226"},{name:"巴马瑶族自治县",code:"451227"},{name:"都安瑶族自治县",code:"451228"},{name:"大化瑶族自治县",code:"451229"}]},{name:"来宾市",code:"451300",children:[{name:"兴宾区",code:"451302"},{name:"忻城县",code:"451321"},{name:"象州县",code:"451322"},{name:"武宣县",code:"451323"},{name:"金秀瑶族自治县",code:"451324"},{name:"合山市",code:"451381"}]},{name:"崇左市",code:"451400",children:[{name:"江州区",code:"451402"},{name:"扶绥县",code:"451421"},{name:"宁明县",code:"451422"},{name:"龙州县",code:"451423"},{name:"大新县",code:"451424"},{name:"天等县",code:"451425"},{name:"凭祥市",code:"451481"}]}]},{name:"海南省",code:"460000",region:"south",children:[{name:"海口市",code:"460100",children:[{name:"秀英区",code:"460105"},{name:"龙华区",code:"460106"},{name:"琼山区",code:"460107"},{name:"美兰区",code:"460108"}]},{name:"三亚市",code:"460200",children:[{name:"海棠区",code:"460202"},{name:"吉阳区",code:"460203"},{name:"天涯区",code:"460204"},{name:"崖州区",code:"460205"}]},{name:"三沙市",code:"460300",children:[]},{name:"儋州市",code:"460400",children:[]},{name:"直辖县",code:"469000",children:[{name:"五指山市",code:"469001"},{name:"琼海市",code:"469002"},{name:"文昌市",code:"469005"},{name:"万宁市",code:"469006"},{name:"东方市",code:"469007"},{name:"定安县",code:"469021"},{name:"屯昌县",code:"469022"},{name:"澄迈县",code:"469023"},{name:"临高县",code:"469024"},{name:"白沙黎族自治县",code:"469025"},{name:"昌江黎族自治县",code:"469026"},{name:"乐东黎族自治县",code:"469027"},{name:"陵水黎族自治县",code:"469028"},{name:"保亭黎族苗族自治县",code:"469029"},{name:"琼中黎族苗族自治县",code:"469030"}]}]},{name:"重庆市",code:"500000",region:"southwest",provinceLevelCity:!0,children:[{name:"市辖区",code:"500100",children:[{name:"万州区",code:"500101"},{name:"涪陵区",code:"500102"},{name:"渝中区",code:"500103"},{name:"大渡口区",code:"500104"},{name:"江北区",code:"500105"},{name:"沙坪坝区",code:"500106"},{name:"九龙坡区",code:"500107"},{name:"南岸区",code:"500108"},{name:"北碚区",code:"500109"},{name:"綦江区",code:"500110"},{name:"大足区",code:"500111"},{name:"渝北区",code:"500112"},{name:"巴南区",code:"500113"},{name:"黔江区",code:"500114"},{name:"长寿区",code:"500115"},{name:"江津区",code:"500116"},{name:"合川区",code:"500117"},{name:"永川区",code:"500118"},{name:"南川区",code:"500119"},{name:"璧山区",code:"500120"},{name:"铜梁区",code:"500151"},{name:"潼南区",code:"500152"},{name:"荣昌区",code:"500153"},{name:"开州区",code:"500154"},{name:"梁平区",code:"500155"},{name:"武隆区",code:"500156"}]},{name:"县辖区",code:"500200",children:[{name:"城口县",code:"500229"},{name:"丰都县",code:"500230"},{name:"垫江县",code:"500231"},{name:"忠县",code:"500233"},{name:"云阳县",code:"500235"},{name:"奉节县",code:"500236"},{name:"巫山县",code:"500237"},{name:"巫溪县",code:"500238"},{name:"石柱土家族自治县",code:"500240"},{name:"秀山土家族苗族自治县",code:"500241"},{name:"酉阳土家族苗族自治县",code:"500242"},{name:"彭水苗族土家族自治县",code:"500243"}]}]},{name:"四川省",code:"510000",region:"southwest",children:[{name:"成都市",code:"510100",children:[{name:"锦江区",code:"510104"},{name:"青羊区",code:"510105"},{name:"金牛区",code:"510106"},{name:"武侯区",code:"510107"},{name:"成华区",code:"510108"},{name:"龙泉驿区",code:"510112"},{name:"青白江区",code:"510113"},{name:"新都区",code:"510114"},{name:"温江区",code:"510115"},{name:"双流区",code:"510116"},{name:"郫都区",code:"510117"},{name:"金堂县",code:"510121"},{name:"大邑县",code:"510129"},{name:"蒲江县",code:"510131"},{name:"新津县",code:"510132"},{name:"都江堰市",code:"510181"},{name:"彭州市",code:"510182"},{name:"邛崃市",code:"510183"},{name:"崇州市",code:"510184"},{name:"简阳市",code:"510185"}]},{name:"自贡市",code:"510300",children:[{name:"自流井区",code:"510302"},{name:"贡井区",code:"510303"},{name:"大安区",code:"510304"},{name:"沿滩区",code:"510311"},{name:"荣县",code:"510321"},{name:"富顺县",code:"510322"}]},{name:"攀枝花市",code:"510400",children:[{name:"东区",code:"510402"},{name:"西区",code:"510403"},{name:"仁和区",code:"510411"},{name:"米易县",code:"510421"},{name:"盐边县",code:"510422"}]},{name:"泸州市",code:"510500",children:[{name:"江阳区",code:"510502"},{name:"纳溪区",code:"510503"},{name:"龙马潭区",code:"510504"},{name:"泸县",code:"510521"},{name:"合江县",code:"510522"},{name:"叙永县",code:"510524"},{name:"古蔺县",code:"510525"}]},{name:"德阳市",code:"510600",children:[{name:"旌阳区",code:"510603"},{name:"罗江区",code:"510604"},{name:"中江县",code:"510623"},{name:"广汉市",code:"510681"},{name:"什邡市",code:"510682"},{name:"绵竹市",code:"510683"}]},{name:"绵阳市",code:"510700",children:[{name:"涪城区",code:"510703"},{name:"游仙区",code:"510704"},{name:"安州区",code:"510705"},{name:"三台县",code:"510722"},{name:"盐亭县",code:"510723"},{name:"梓潼县",code:"510725"},{name:"北川羌族自治县",code:"510726"},{name:"平武县",code:"510727"},{name:"江油市",code:"510781"}]},{name:"广元市",code:"510800",children:[{name:"利州区",code:"510802"},{name:"昭化区",code:"510811"},{name:"朝天区",code:"510812"},{name:"旺苍县",code:"510821"},{name:"青川县",code:"510822"},{name:"剑阁县",code:"510823"},{name:"苍溪县",code:"510824"}]},{name:"遂宁市",code:"510900",children:[{name:"船山区",code:"510903"},{name:"安居区",code:"510904"},{name:"蓬溪县",code:"510921"},{name:"射洪县",code:"510922"},{name:"大英县",code:"510923"}]},{name:"内江市",code:"511000",children:[{name:"市中区",code:"511002"},{name:"东兴区",code:"511011"},{name:"威远县",code:"511024"},{name:"资中县",code:"511025"},{name:"隆昌市",code:"511083"}]},{name:"乐山市",code:"511100",children:[{name:"市中区",code:"511102"},{name:"沙湾区",code:"511111"},{name:"五通桥区",code:"511112"},{name:"金口河区",code:"511113"},{name:"犍为县",code:"511123"},{name:"井研县",code:"511124"},{name:"夹江县",code:"511126"},{name:"沐川县",code:"511129"},{name:"峨边彝族自治县",code:"511132"},{name:"马边彝族自治县",code:"511133"},{name:"峨眉山市",code:"511181"}]},{name:"南充市",code:"511300",children:[{name:"顺庆区",code:"511302"},{name:"高坪区",code:"511303"},{name:"嘉陵区",code:"511304"},{name:"南部县",code:"511321"},{name:"营山县",code:"511322"},{name:"蓬安县",code:"511323"},{name:"仪陇县",code:"511324"},{name:"西充县",code:"511325"},{name:"阆中市",code:"511381"}]},{name:"眉山市",code:"511400",children:[{name:"东坡区",code:"511402"},{name:"彭山区",code:"511403"},{name:"仁寿县",code:"511421"},{name:"洪雅县",code:"511423"},{name:"丹棱县",code:"511424"},{name:"青神县",code:"511425"}]},{name:"宜宾市",code:"511500",children:[{name:"翠屏区",code:"511502"},{name:"南溪区",code:"511503"},{name:"叙州区",code:"511504"},{name:"江安县",code:"511523"},{name:"长宁县",code:"511524"},{name:"高县",code:"511525"},{name:"珙县",code:"511526"},{name:"筠连县",code:"511527"},{name:"兴文县",code:"511528"},{name:"屏山县",code:"511529"}]},{name:"广安市",code:"511600",children:[{name:"广安区",code:"511602"},{name:"前锋区",code:"511603"},{name:"岳池县",code:"511621"},{name:"武胜县",code:"511622"},{name:"邻水县",code:"511623"},{name:"华蓥市",code:"511681"}]},{name:"达州市",code:"511700",children:[{name:"通川区",code:"511702"},{name:"达川区",code:"511703"},{name:"宣汉县",code:"511722"},{name:"开江县",code:"511723"},{name:"大竹县",code:"511724"},{name:"渠县",code:"511725"},{name:"万源市",code:"511781"}]},{name:"雅安市",code:"511800",children:[{name:"雨城区",code:"511802"},{name:"名山区",code:"511803"},{name:"荥经县",code:"511822"},{name:"汉源县",code:"511823"},{name:"石棉县",code:"511824"},{name:"天全县",code:"511825"},{name:"芦山县",code:"511826"},{name:"宝兴县",code:"511827"}]},{name:"巴中市",code:"511900",children:[{name:"巴州区",code:"511902"},{name:"恩阳区",code:"511903"},{name:"通江县",code:"511921"},{name:"南江县",code:"511922"},{name:"平昌县",code:"511923"}]},{name:"资阳市",code:"512000",children:[{name:"雁江区",code:"512002"},{name:"安岳县",code:"512021"},{name:"乐至县",code:"512022"}]},{name:"阿坝藏族羌族自治州",code:"513200",children:[{name:"马尔康市",code:"513201"},{name:"汶川县",code:"513221"},{name:"理县",code:"513222"},{name:"茂县",code:"513223"},{name:"松潘县",code:"513224"},{name:"九寨沟县",code:"513225"},{name:"金川县",code:"513226"},{name:"小金县",code:"513227"},{name:"黑水县",code:"513228"},{name:"壤塘县",code:"513230"},{name:"阿坝县",code:"513231"},{name:"若尔盖县",code:"513232"},{name:"红原县",code:"513233"}]},{name:"甘孜藏族自治州",code:"513300",children:[{name:"康定市",code:"513301"},{name:"泸定县",code:"513322"},{name:"丹巴县",code:"513323"},{name:"九龙县",code:"513324"},{name:"雅江县",code:"513325"},{name:"道孚县",code:"513326"},{name:"炉霍县",code:"513327"},{name:"甘孜县",code:"513328"},{name:"新龙县",code:"513329"},{name:"德格县",code:"513330"},{name:"白玉县",code:"513331"},{name:"石渠县",code:"513332"},{name:"色达县",code:"513333"},{name:"理塘县",code:"513334"},{name:"巴塘县",code:"513335"},{name:"乡城县",code:"513336"},{name:"稻城县",code:"513337"},{name:"得荣县",code:"513338"}]},{name:"凉山彝族自治州",code:"513400",children:[{name:"西昌市",code:"513401"},{name:"木里藏族自治县",code:"513422"},{name:"盐源县",code:"513423"},{name:"德昌县",code:"513424"},{name:"会理县",code:"513425"},{name:"会东县",code:"513426"},{name:"宁南县",code:"513427"},{name:"普格县",code:"513428"},{name:"布拖县",code:"513429"},{name:"金阳县",code:"513430"},{name:"昭觉县",code:"513431"},{name:"喜德县",code:"513432"},{name:"冕宁县",code:"513433"},{name:"越西县",code:"513434"},{name:"甘洛县",code:"513435"},{name:"美姑县",code:"513436"},{name:"雷波县",code:"513437"}]}]},{name:"贵州省",code:"520000",region:"southwest",children:[{name:"贵阳市",code:"520100",children:[{name:"南明区",code:"520102"},{name:"云岩区",code:"520103"},{name:"花溪区",code:"520111"},{name:"乌当区",code:"520112"},{name:"白云区",code:"520113"},{name:"观山湖区",code:"520115"},{name:"开阳县",code:"520121"},{name:"息烽县",code:"520122"},{name:"修文县",code:"520123"},{name:"清镇市",code:"520181"}]},{name:"六盘水市",code:"520200",children:[{name:"钟山区",code:"520201"},{name:"六枝特区",code:"520203"},{name:"水城县",code:"520221"},{name:"盘州市",code:"520281"}]},{name:"遵义市",code:"520300",children:[{name:"红花岗区",code:"520302"},{name:"汇川区",code:"520303"},{name:"播州区",code:"520304"},{name:"桐梓县",code:"520322"},{name:"绥阳县",code:"520323"},{name:"正安县",code:"520324"},{name:"道真仡佬族苗族自治县",code:"520325"},{name:"务川仡佬族苗族自治县",code:"520326"},{name:"凤冈县",code:"520327"},{name:"湄潭县",code:"520328"},{name:"余庆县",code:"520329"},{name:"习水县",code:"520330"},{name:"赤水市",code:"520381"},{name:"仁怀市",code:"520382"}]},{name:"安顺市",code:"520400",children:[{name:"西秀区",code:"520402"},{name:"平坝区",code:"520403"},{name:"普定县",code:"520422"},{name:"镇宁布依族苗族自治县",code:"520423"},{name:"关岭布依族苗族自治县",code:"520424"},{name:"紫云苗族布依族自治县",code:"520425"}]},{name:"毕节市",code:"520500",children:[{name:"七星关区",code:"520502"},{name:"大方县",code:"520521"},{name:"黔西县",code:"520522"},{name:"金沙县",code:"520523"},{name:"织金县",code:"520524"},{name:"纳雍县",code:"520525"},{name:"威宁彝族回族苗族自治县",code:"520526"},{name:"赫章县",code:"520527"}]},{name:"铜仁市",code:"520600",children:[{name:"碧江区",code:"520602"},{name:"万山区",code:"520603"},{name:"江口县",code:"520621"},{name:"玉屏侗族自治县",code:"520622"},{name:"石阡县",code:"520623"},{name:"思南县",code:"520624"},{name:"印江土家族苗族自治县",code:"520625"},{name:"德江县",code:"520626"},{name:"沿河土家族自治县",code:"520627"},{name:"松桃苗族自治县",code:"520628"}]},{name:"黔西南布依族苗族自治州",code:"522300",children:[{name:"兴义市",code:"522301"},{name:"兴仁市",code:"522302"},{name:"普安县",code:"522323"},{name:"晴隆县",code:"522324"},{name:"贞丰县",code:"522325"},{name:"望谟县",code:"522326"},{name:"册亨县",code:"522327"},{name:"安龙县",code:"522328"}]},{name:"黔东南苗族侗族自治州",code:"522600",children:[{name:"凯里市",code:"522601"},{name:"黄平县",code:"522622"},{name:"施秉县",code:"522623"},{name:"三穗县",code:"522624"},{name:"镇远县",code:"522625"},{name:"岑巩县",code:"522626"},{name:"天柱县",code:"522627"},{name:"锦屏县",code:"522628"},{name:"剑河县",code:"522629"},{name:"台江县",code:"522630"},{name:"黎平县",code:"522631"},{name:"榕江县",code:"522632"},{name:"从江县",code:"522633"},{name:"雷山县",code:"522634"},{name:"麻江县",code:"522635"},{name:"丹寨县",code:"522636"}]},{name:"黔南布依族苗族自治州",code:"522700",children:[{name:"都匀市",code:"522701"},{name:"福泉市",code:"522702"},{name:"荔波县",code:"522722"},{name:"贵定县",code:"522723"},{name:"瓮安县",code:"522725"},{name:"独山县",code:"522726"},{name:"平塘县",code:"522727"},{name:"罗甸县",code:"522728"},{name:"长顺县",code:"522729"},{name:"龙里县",code:"522730"},{name:"惠水县",code:"522731"},{name:"三都水族自治县",code:"522732"}]}]},{name:"云南省",code:"530000",region:"southwest",children:[{name:"昆明市",code:"530100",children:[{name:"五华区",code:"530102"},{name:"盘龙区",code:"530103"},{name:"官渡区",code:"530111"},{name:"西山区",code:"530112"},{name:"东川区",code:"530113"},{name:"呈贡区",code:"530114"},{name:"晋宁区",code:"530115"},{name:"富民县",code:"530124"},{name:"宜良县",code:"530125"},{name:"石林彝族自治县",code:"530126"},{name:"嵩明县",code:"530127"},{name:"禄劝彝族苗族自治县",code:"530128"},{name:"寻甸回族彝族自治县",code:"530129"},{name:"安宁市",code:"530181"}]},{name:"曲靖市",code:"530300",children:[{name:"麒麟区",code:"530302"},{name:"沾益区",code:"530303"},{name:"马龙区",code:"530304"},{name:"陆良县",code:"530322"},{name:"师宗县",code:"530323"},{name:"罗平县",code:"530324"},{name:"富源县",code:"530325"},{name:"会泽县",code:"530326"},{name:"宣威市",code:"530381"}]},{name:"玉溪市",code:"530400",children:[{name:"红塔区",code:"530402"},{name:"江川区",code:"530403"},{name:"澄江县",code:"530422"},{name:"通海县",code:"530423"},{name:"华宁县",code:"530424"},{name:"易门县",code:"530425"},{name:"峨山彝族自治县",code:"530426"},{name:"新平彝族傣族自治县",code:"530427"},{name:"元江哈尼族彝族傣族自治县",code:"530428"}]},{name:"保山市",code:"530500",children:[{name:"隆阳区",code:"530502"},{name:"施甸县",code:"530521"},{name:"龙陵县",code:"530523"},{name:"昌宁县",code:"530524"},{name:"腾冲市",code:"530581"}]},{name:"昭通市",code:"530600",children:[{name:"昭阳区",code:"530602"},{name:"鲁甸县",code:"530621"},{name:"巧家县",code:"530622"},{name:"盐津县",code:"530623"},{name:"大关县",code:"530624"},{name:"永善县",code:"530625"},{name:"绥江县",code:"530626"},{name:"镇雄县",code:"530627"},{name:"彝良县",code:"530628"},{name:"威信县",code:"530629"},{name:"水富市",code:"530681"}]},{name:"丽江市",code:"530700",children:[{name:"古城区",code:"530702"},{name:"玉龙纳西族自治县",code:"530721"},{name:"永胜县",code:"530722"},{name:"华坪县",code:"530723"},{name:"宁蒗彝族自治县",code:"530724"}]},{name:"普洱市",code:"530800",children:[{name:"思茅区",code:"530802"},{name:"宁洱哈尼族彝族自治县",code:"530821"},{name:"墨江哈尼族自治县",code:"530822"},{name:"景东彝族自治县",code:"530823"},{name:"景谷傣族彝族自治县",code:"530824"},{name:"镇沅彝族哈尼族拉祜族自治县",code:"530825"},{name:"江城哈尼族彝族自治县",code:"530826"},{name:"孟连傣族拉祜族佤族自治县",code:"530827"},{name:"澜沧拉祜族自治县",code:"530828"},{name:"西盟佤族自治县",code:"530829"}]},{name:"临沧市",code:"530900",children:[{name:"临翔区",code:"530902"},{name:"凤庆县",code:"530921"},{name:"云县",code:"530922"},{name:"永德县",code:"530923"},{name:"镇康县",code:"530924"},{name:"双江拉祜族佤族布朗族傣族自治县",code:"530925"},{name:"耿马傣族佤族自治县",code:"530926"},{name:"沧源佤族自治县",code:"530927"}]},{name:"楚雄彝族自治州",code:"532300",children:[{name:"楚雄市",code:"532301"},{name:"双柏县",code:"532322"},{name:"牟定县",code:"532323"},{name:"南华县",code:"532324"},{name:"姚安县",code:"532325"},{name:"大姚县",code:"532326"},{name:"永仁县",code:"532327"},{name:"元谋县",code:"532328"},{name:"武定县",code:"532329"},{name:"禄丰县",code:"532331"}]},{name:"红河哈尼族彝族自治州",code:"532500",children:[{name:"个旧市",code:"532501"},{name:"开远市",code:"532502"},{name:"蒙自市",code:"532503"},{name:"弥勒市",code:"532504"},{name:"屏边苗族自治县",code:"532523"},{name:"建水县",code:"532524"},{name:"石屏县",code:"532525"},{name:"泸西县",code:"532527"},{name:"元阳县",code:"532528"},{name:"红河县",code:"532529"},{name:"金平苗族瑶族傣族自治县",code:"532530"},{name:"绿春县",code:"532531"},{name:"河口瑶族自治县",code:"532532"}]},{name:"文山壮族苗族自治州",code:"532600",children:[{name:"文山市",code:"532601"},{name:"砚山县",code:"532622"},{name:"西畴县",code:"532623"},{name:"麻栗坡县",code:"532624"},{name:"马关县",code:"532625"},{name:"丘北县",code:"532626"},{name:"广南县",code:"532627"},{name:"富宁县",code:"532628"}]},{name:"西双版纳傣族自治州",code:"532800",children:[{name:"景洪市",code:"532801"},{name:"勐海县",code:"532822"},{name:"勐腊县",code:"532823"}]},{name:"大理白族自治州",code:"532900",children:[{name:"大理市",code:"532901"},{name:"漾濞彝族自治县",code:"532922"},{name:"祥云县",code:"532923"},{name:"宾川县",code:"532924"},{name:"弥渡县",code:"532925"},{name:"南涧彝族自治县",code:"532926"},{name:"巍山彝族回族自治县",code:"532927"},{name:"永平县",code:"532928"},{name:"云龙县",code:"532929"},{name:"洱源县",code:"532930"},{name:"剑川县",code:"532931"},{name:"鹤庆县",code:"532932"}]},{name:"德宏傣族景颇族自治州",code:"533100",children:[{name:"瑞丽市",code:"533102"},{name:"芒市",code:"533103"},{name:"梁河县",code:"533122"},{name:"盈江县",code:"533123"},{name:"陇川县",code:"533124"}]},{name:"怒江傈僳族自治州",code:"533300",children:[{name:"泸水市",code:"533301"},{name:"福贡县",code:"533323"},{name:"贡山独龙族怒族自治县",code:"533324"},{name:"兰坪白族普米族自治县",code:"533325"}]},{name:"迪庆藏族自治州",code:"533400",children:[{name:"香格里拉市",code:"533401"},{name:"德钦县",code:"533422"},{name:"维西傈僳族自治县",code:"533423"}]}]},{name:"西藏自治区",code:"540000",region:"southwest",autonomousRegion:!0,children:[{name:"拉萨市",code:"540100",children:[{name:"城关区",code:"540102"},{name:"堆龙德庆区",code:"540103"},{name:"达孜区",code:"540104"},{name:"林周县",code:"540121"},{name:"当雄县",code:"540122"},{name:"尼木县",code:"540123"},{name:"曲水县",code:"540124"},{name:"墨竹工卡县",code:"540127"}]},{name:"日喀则市",code:"540200",children:[{name:"桑珠孜区",code:"540202"},{name:"南木林县",code:"540221"},{name:"江孜县",code:"540222"},{name:"定日县",code:"540223"},{name:"萨迦县",code:"540224"},{name:"拉孜县",code:"540225"},{name:"昂仁县",code:"540226"},{name:"谢通门县",code:"540227"},{name:"白朗县",code:"540228"},{name:"仁布县",code:"540229"},{name:"康马县",code:"540230"},{name:"定结县",code:"540231"},{name:"仲巴县",code:"540232"},{name:"亚东县",code:"540233"},{name:"吉隆县",code:"540234"},{name:"聂拉木县",code:"540235"},{name:"萨嘎县",code:"540236"},{name:"岗巴县",code:"540237"}]},{name:"昌都市",code:"540300",children:[{name:"卡若区",code:"540302"},{name:"江达县",code:"540321"},{name:"贡觉县",code:"540322"},{name:"类乌齐县",code:"540323"},{name:"丁青县",code:"540324"},{name:"察雅县",code:"540325"},{name:"八宿县",code:"540326"},{name:"左贡县",code:"540327"},{name:"芒康县",code:"540328"},{name:"洛隆县",code:"540329"},{name:"边坝县",code:"540330"}]},{name:"林芝市",code:"540400",children:[{name:"巴宜区",code:"540402"},{name:"工布江达县",code:"540421"},{name:"米林县",code:"540422"},{name:"墨脱县",code:"540423"},{name:"波密县",code:"540424"},{name:"察隅县",code:"540425"},{name:"朗县",code:"540426"}]},{name:"山南市",code:"540500",children:[{name:"乃东区",code:"540502"},{name:"扎囊县",code:"540521"},{name:"贡嘎县",code:"540522"},{name:"桑日县",code:"540523"},{name:"琼结县",code:"540524"},{name:"曲松县",code:"540525"},{name:"措美县",code:"540526"},{name:"洛扎县",code:"540527"},{name:"加查县",code:"540528"},{name:"隆子县",code:"540529"},{name:"错那县",code:"540530"},{name:"浪卡子县",code:"540531"}]},{name:"那曲市",code:"540600",children:[{name:"色尼区",code:"540602"},{name:"嘉黎县",code:"540621"},{name:"比如县",code:"540622"},{name:"聂荣县",code:"540623"},{name:"安多县",code:"540624"},{name:"申扎县",code:"540625"},{name:"索县",code:"540626"},{name:"班戈县",code:"540627"},{name:"巴青县",code:"540628"},{name:"尼玛县",code:"540629"},{name:"双湖县",code:"540630"}]},{name:"阿里地区",code:"542500",children:[{name:"普兰县",code:"542521"},{name:"札达县",code:"542522"},{name:"噶尔县",code:"542523"},{name:"日土县",code:"542524"},{name:"革吉县",code:"542525"},{name:"改则县",code:"542526"},{name:"措勤县",code:"542527"}]}]},{name:"陕西省",code:"610000",region:"northwest",children:[{name:"西安市",code:"610100",children:[{name:"新城区",code:"610102"},{name:"碑林区",code:"610103"},{name:"莲湖区",code:"610104"},{name:"灞桥区",code:"610111"},{name:"未央区",code:"610112"},{name:"雁塔区",code:"610113"},{name:"阎良区",code:"610114"},{name:"临潼区",code:"610115"},{name:"长安区",code:"610116"},{name:"高陵区",code:"610117"},{name:"鄠邑区",code:"610118"},{name:"蓝田县",code:"610122"},{name:"周至县",code:"610124"}]},{name:"铜川市",code:"610200",children:[{name:"王益区",code:"610202"},{name:"印台区",code:"610203"},{name:"耀州区",code:"610204"},{name:"宜君县",code:"610222"}]},{name:"宝鸡市",code:"610300",children:[{name:"渭滨区",code:"610302"},{name:"金台区",code:"610303"},{name:"陈仓区",code:"610304"},{name:"凤翔县",code:"610322"},{name:"岐山县",code:"610323"},{name:"扶风县",code:"610324"},{name:"眉县",code:"610326"},{name:"陇县",code:"610327"},{name:"千阳县",code:"610328"},{name:"麟游县",code:"610329"},{name:"凤县",code:"610330"},{name:"太白县",code:"610331"}]},{name:"咸阳市",code:"610400",children:[{name:"秦都区",code:"610402"},{name:"杨陵区",code:"610403"},{name:"渭城区",code:"610404"},{name:"三原县",code:"610422"},{name:"泾阳县",code:"610423"},{name:"乾县",code:"610424"},{name:"礼泉县",code:"610425"},{name:"永寿县",code:"610426"},{name:"长武县",code:"610428"},{name:"旬邑县",code:"610429"},{name:"淳化县",code:"610430"},{name:"武功县",code:"610431"},{name:"兴平市",code:"610481"},{name:"彬州市",code:"610482"}]},{name:"渭南市",code:"610500",children:[{name:"临渭区",code:"610502"},{name:"华州区",code:"610503"},{name:"潼关县",code:"610522"},{name:"大荔县",code:"610523"},{name:"合阳县",code:"610524"},{name:"澄城县",code:"610525"},{name:"蒲城县",code:"610526"},{name:"白水县",code:"610527"},{name:"富平县",code:"610528"},{name:"韩城市",code:"610581"},{name:"华阴市",code:"610582"}]},{name:"延安市",code:"610600",children:[{name:"宝塔区",code:"610602"},{name:"安塞区",code:"610603"},{name:"延长县",code:"610621"},{name:"延川县",code:"610622"},{name:"子长县",code:"610623"},{name:"志丹县",code:"610625"},{name:"吴起县",code:"610626"},{name:"甘泉县",code:"610627"},{name:"富县",code:"610628"},{name:"洛川县",code:"610629"},{name:"宜川县",code:"610630"},{name:"黄龙县",code:"610631"},{name:"黄陵县",code:"610632"}]},{name:"汉中市",code:"610700",children:[{name:"汉台区",code:"610702"},{name:"南郑区",code:"610703"},{name:"城固县",code:"610722"},{name:"洋县",code:"610723"},{name:"西乡县",code:"610724"},{name:"勉县",code:"610725"},{name:"宁强县",code:"610726"},{name:"略阳县",code:"610727"},{name:"镇巴县",code:"610728"},{name:"留坝县",code:"610729"},{name:"佛坪县",code:"610730"}]},{name:"榆林市",code:"610800",children:[{name:"榆阳区",code:"610802"},{name:"横山区",code:"610803"},{name:"府谷县",code:"610822"},{name:"靖边县",code:"610824"},{name:"定边县",code:"610825"},{name:"绥德县",code:"610826"},{name:"米脂县",code:"610827"},{name:"佳县",code:"610828"},{name:"吴堡县",code:"610829"},{name:"清涧县",code:"610830"},{name:"子洲县",code:"610831"},{name:"神木市",code:"610881"}]},{name:"安康市",code:"610900",children:[{name:"汉滨区",code:"610902"},{name:"汉阴县",code:"610921"},{name:"石泉县",code:"610922"},{name:"宁陕县",code:"610923"},{name:"紫阳县",code:"610924"},{name:"岚皋县",code:"610925"},{name:"平利县",code:"610926"},{name:"镇坪县",code:"610927"},{name:"旬阳县",code:"610928"},{name:"白河县",code:"610929"}]},{name:"商洛市",code:"611000",children:[{name:"商州区",code:"611002"},{name:"洛南县",code:"611021"},{name:"丹凤县",code:"611022"},{name:"商南县",code:"611023"},{name:"山阳县",code:"611024"},{name:"镇安县",code:"611025"},{name:"柞水县",code:"611026"}]}]},{name:"甘肃省",code:"620000",region:"northwest",children:[{name:"兰州市",code:"620100",children:[{name:"城关区",code:"620102"},{name:"七里河区",code:"620103"},{name:"西固区",code:"620104"},{name:"安宁区",code:"620105"},{name:"红古区",code:"620111"},{name:"永登县",code:"620121"},{name:"皋兰县",code:"620122"},{name:"榆中县",code:"620123"}]},{name:"嘉峪关市",code:"620200",children:[]},{name:"金昌市",code:"620300",children:[{name:"金川区",code:"620302"},{name:"永昌县",code:"620321"}]},{name:"白银市",code:"620400",children:[{name:"白银区",code:"620402"},{name:"平川区",code:"620403"},{name:"靖远县",code:"620421"},{name:"会宁县",code:"620422"},{name:"景泰县",code:"620423"}]},{name:"天水市",code:"620500",children:[{name:"秦州区",code:"620502"},{name:"麦积区",code:"620503"},{name:"清水县",code:"620521"},{name:"秦安县",code:"620522"},{name:"甘谷县",code:"620523"},{name:"武山县",code:"620524"},{name:"张家川回族自治县",code:"620525"}]},{name:"武威市",code:"620600",children:[{name:"凉州区",code:"620602"},{name:"民勤县",code:"620621"},{name:"古浪县",code:"620622"},{name:"天祝藏族自治县",code:"620623"}]},{name:"张掖市",code:"620700",children:[{name:"甘州区",code:"620702"},{name:"肃南裕固族自治县",code:"620721"},{name:"民乐县",code:"620722"},{name:"临泽县",code:"620723"},{name:"高台县",code:"620724"},{name:"山丹县",code:"620725"}]},{name:"平凉市",code:"620800",children:[{name:"崆峒区",code:"620802"},{name:"泾川县",code:"620821"},{name:"灵台县",code:"620822"},{name:"崇信县",code:"620823"},{name:"庄浪县",code:"620825"},{name:"静宁县",code:"620826"},{name:"华亭市",code:"620881"}]},{name:"酒泉市",code:"620900",children:[{name:"肃州区",code:"620902"},{name:"金塔县",code:"620921"},{name:"瓜州县",code:"620922"},{name:"肃北蒙古族自治县",code:"620923"},{name:"阿克塞哈萨克族自治县",code:"620924"},{name:"玉门市",code:"620981"},{name:"敦煌市",code:"620982"}]},{name:"庆阳市",code:"621000",children:[{name:"西峰区",code:"621002"},{name:"庆城县",code:"621021"},{name:"环县",code:"621022"},{name:"华池县",code:"621023"},{name:"合水县",code:"621024"},{name:"正宁县",code:"621025"},{name:"宁县",code:"621026"},{name:"镇原县",code:"621027"}]},{name:"定西市",code:"621100",children:[{name:"安定区",code:"621102"},{name:"通渭县",code:"621121"},{name:"陇西县",code:"621122"},{name:"渭源县",code:"621123"},{name:"临洮县",code:"621124"},{name:"漳县",code:"621125"},{name:"岷县",code:"621126"}]},{name:"陇南市",code:"621200",children:[{name:"武都区",code:"621202"},{name:"成县",code:"621221"},{name:"文县",code:"621222"},{name:"宕昌县",code:"621223"},{name:"康县",code:"621224"},{name:"西和县",code:"621225"},{name:"礼县",code:"621226"},{name:"徽县",code:"621227"},{name:"两当县",code:"621228"}]},{name:"临夏回族自治州",code:"622900",children:[{name:"临夏市",code:"622901"},{name:"临夏县",code:"622921"},{name:"康乐县",code:"622922"},{name:"永靖县",code:"622923"},{name:"广河县",code:"622924"},{name:"和政县",code:"622925"},{name:"东乡族自治县",code:"622926"},{name:"积石山保安族东乡族撒拉族自治县",code:"622927"}]},{name:"甘南藏族自治州",code:"623000",children:[{name:"合作市",code:"623001"},{name:"临潭县",code:"623021"},{name:"卓尼县",code:"623022"},{name:"舟曲县",code:"623023"},{name:"迭部县",code:"623024"},{name:"玛曲县",code:"623025"},{name:"碌曲县",code:"623026"},{name:"夏河县",code:"623027"}]}]},{name:"青海省",code:"630000",region:"northwest",children:[{name:"西宁市",code:"630100",children:[{name:"城东区",code:"630102"},{name:"城中区",code:"630103"},{name:"城西区",code:"630104"},{name:"城北区",code:"630105"},{name:"大通回族土族自治县",code:"630121"},{name:"湟中县",code:"630122"},{name:"湟源县",code:"630123"}]},{name:"海东市",code:"630200",children:[{name:"乐都区",code:"630202"},{name:"平安区",code:"630203"},{name:"民和回族土族自治县",code:"630222"},{name:"互助土族自治县",code:"630223"},{name:"化隆回族自治县",code:"630224"},{name:"循化撒拉族自治县",code:"630225"}]},{name:"海北藏族自治州",code:"632200",children:[{name:"门源回族自治县",code:"632221"},{name:"祁连县",code:"632222"},{name:"海晏县",code:"632223"},{name:"刚察县",code:"632224"}]},{name:"黄南藏族自治州",code:"632300",children:[{name:"同仁县",code:"632321"},{name:"尖扎县",code:"632322"},{name:"泽库县",code:"632323"},{name:"河南蒙古族自治县",code:"632324"}]},{name:"海南藏族自治州",code:"632500",children:[{name:"共和县",code:"632521"},{name:"同德县",code:"632522"},{name:"贵德县",code:"632523"},{name:"兴海县",code:"632524"},{name:"贵南县",code:"632525"}]},{name:"果洛藏族自治州",code:"632600",children:[{name:"玛沁县",code:"632621"},{name:"班玛县",code:"632622"},{name:"甘德县",code:"632623"},{name:"达日县",code:"632624"},{name:"久治县",code:"632625"},{name:"玛多县",code:"632626"}]},{name:"玉树藏族自治州",code:"632700",children:[{name:"玉树市",code:"632701"},{name:"杂多县",code:"632722"},{name:"称多县",code:"632723"},{name:"治多县",code:"632724"},{name:"囊谦县",code:"632725"},{name:"曲麻莱县",code:"632726"}]},{name:"海西蒙古族藏族自治州",code:"632800",children:[{name:"格尔木市",code:"632801"},{name:"德令哈市",code:"632802"},{name:"茫崖市",code:"632803"},{name:"乌兰县",code:"632821"},{name:"都兰县",code:"632822"},{name:"天峻县",code:"632823"}]}]},{name:"宁夏回族自治区",code:"640000",region:"northwest",autonomousRegion:!0,children:[{name:"银川市",code:"640100",children:[{name:"兴庆区",code:"640104"},{name:"西夏区",code:"640105"},{name:"金凤区",code:"640106"},{name:"永宁县",code:"640121"},{name:"贺兰县",code:"640122"},{name:"灵武市",code:"640181"}]},{name:"石嘴山市",code:"640200",children:[{name:"大武口区",code:"640202"},{name:"惠农区",code:"640205"},{name:"平罗县",code:"640221"}]},{name:"吴忠市",code:"640300",children:[{name:"利通区",code:"640302"},{name:"红寺堡区",code:"640303"},{name:"盐池县",code:"640323"},{name:"同心县",code:"640324"},{name:"青铜峡市",code:"640381"}]},{name:"固原市",code:"640400",children:[{name:"原州区",code:"640402"},{name:"西吉县",code:"640422"},{name:"隆德县",code:"640423"},{name:"泾源县",code:"640424"},{name:"彭阳县",code:"640425"}]},{name:"中卫市",code:"640500",children:[{name:"沙坡头区",code:"640502"},{name:"中宁县",code:"640521"},{name:"海原县",code:"640522"}]}]},{name:"新疆维吾尔自治区",code:"650000",region:"northwest",autonomousRegion:!0,children:[{name:"乌鲁木齐市",code:"650100",children:[{name:"天山区",code:"650102"},{name:"沙依巴克区",code:"650103"},{name:"新市区",code:"650104"},{name:"水磨沟区",code:"650105"},{name:"头屯河区",code:"650106"},{name:"达坂城区",code:"650107"},{name:"米东区",code:"650109"},{name:"乌鲁木齐县",code:"650121"}]},{name:"克拉玛依市",code:"650200",children:[{name:"独山子区",code:"650202"},{name:"克拉玛依区",code:"650203"},{name:"白碱滩区",code:"650204"},{name:"乌尔禾区",code:"650205"}]},{name:"吐鲁番市",code:"650400",children:[{name:"高昌区",code:"650402"},{name:"鄯善县",code:"650421"},{name:"托克逊县",code:"650422"}]},{name:"哈密市",code:"650500",children:[{name:"伊州区",code:"650502"},{name:"巴里坤哈萨克自治县",code:"650521"},{name:"伊吾县",code:"650522"}]},{name:"昌吉回族自治州",code:"652300",children:[{name:"昌吉市",code:"652301"},{name:"阜康市",code:"652302"},{name:"呼图壁县",code:"652323"},{name:"玛纳斯县",code:"652324"},{name:"奇台县",code:"652325"},{name:"吉木萨尔县",code:"652327"},{name:"木垒哈萨克自治县",code:"652328"}]},{name:"博尔塔拉蒙古自治州",code:"652700",children:[{name:"博乐市",code:"652701"},{name:"阿拉山口市",code:"652702"},{name:"精河县",code:"652722"},{name:"温泉县",code:"652723"}]},{name:"巴音郭楞蒙古自治州",code:"652800",children:[{name:"库尔勒市",code:"652801"},{name:"轮台县",code:"652822"},{name:"尉犁县",code:"652823"},{name:"若羌县",code:"652824"},{name:"且末县",code:"652825"},{name:"焉耆回族自治县",code:"652826"},{name:"和静县",code:"652827"},{name:"和硕县",code:"652828"},{name:"博湖县",code:"652829"}]},{name:"阿克苏地区",code:"652900",children:[{name:"阿克苏市",code:"652901"},{name:"温宿县",code:"652922"},{name:"库车县",code:"652923"},{name:"沙雅县",code:"652924"},{name:"新和县",code:"652925"},{name:"拜城县",code:"652926"},{name:"乌什县",code:"652927"},{name:"阿瓦提县",code:"652928"},{name:"柯坪县",code:"652929"}]},{name:"克孜勒苏柯尔克孜自治州",code:"653000",children:[{name:"阿图什市",code:"653001"},{name:"阿克陶县",code:"653022"},{name:"阿合奇县",code:"653023"},{name:"乌恰县",code:"653024"}]},{name:"喀什地区",code:"653100",children:[{name:"喀什市",code:"653101"},{name:"疏附县",code:"653121"},{name:"疏勒县",code:"653122"},{name:"英吉沙县",code:"653123"},{name:"泽普县",code:"653124"},{name:"莎车县",code:"653125"},{name:"叶城县",code:"653126"},{name:"麦盖提县",code:"653127"},{name:"岳普湖县",code:"653128"},{name:"伽师县",code:"653129"},{name:"巴楚县",code:"653130"},{name:"塔什库尔干塔吉克自治县",code:"653131"}]},{name:"和田地区",code:"653200",children:[{name:"和田市",code:"653201"},{name:"和田县",code:"653221"},{name:"墨玉县",code:"653222"},{name:"皮山县",code:"653223"},{name:"洛浦县",code:"653224"},{name:"策勒县",code:"653225"},{name:"于田县",code:"653226"},{name:"民丰县",code:"653227"}]},{name:"伊犁哈萨克自治州",code:"654000",children:[{name:"伊宁市",code:"654002"},{name:"奎屯市",code:"654003"},{name:"霍尔果斯市",code:"654004"},{name:"伊宁县",code:"654021"},{name:"察布查尔锡伯自治县",code:"654022"},{name:"霍城县",code:"654023"},{name:"巩留县",code:"654024"},{name:"新源县",code:"654025"},{name:"昭苏县",code:"654026"},{name:"特克斯县",code:"654027"},{name:"尼勒克县",code:"654028"}]},{name:"塔城地区",code:"654200",children:[{name:"塔城市",code:"654201"},{name:"乌苏市",code:"654202"},{name:"额敏县",code:"654221"},{name:"沙湾县",code:"654223"},{name:"托里县",code:"654224"},{name:"裕民县",code:"654225"},{name:"和布克赛尔蒙古自治县",code:"654226"}]},{name:"阿勒泰地区",code:"654300",children:[{name:"阿勒泰市",code:"654301"},{name:"布尔津县",code:"654321"},{name:"富蕴县",code:"654322"},{name:"福海县",code:"654323"},{name:"哈巴河县",code:"654324"},{name:"青河县",code:"654325"},{name:"吉木乃县",code:"654326"}]},{name:"直辖县",code:"659000",children:[{name:"石河子市",code:"659001"},{name:"阿拉尔市",code:"659002"},{name:"图木舒克市",code:"659003"},{name:"五家渠市",code:"659004"},{name:"北屯市",code:"659005"},{name:"铁门关市",code:"659006"},{name:"双河市",code:"659007"},{name:"可克达拉市",code:"659008"},{name:"昆玉市",code:"659009"}]}]}]),s=c,r={name:"CascaderArea",props:{value:{type:Array,default:function(){return[]}},size:{type:String,default:""},disabled:{type:Boolean,default:!1}},data:function(){return{options:s,props:{value:"code",label:"name"}}},computed:{myValue:{get:function(){var e=[];if(3===this.value.length){var t=Object(i["a"])(this.value,3),n=t[0],a=t[1],o=t[2];s.map((function(t){t.name===n&&(e.push(t.code),t.children.map((function(t){t.name===a&&(e.push(t.code),t.children.map((function(t){t.name===o&&e.push(t.code)})))})))}))}return e},set:function(e){var t=Object(i["a"])(e,3),n=t[0],a=t[1],o=t[2];s.map((function(e){e.code===n&&(n=e.name,e.children.map((function(e){e.code===a&&(a=e.name,e.children.map((function(e){e.code===o&&(o=e.name)})))})))})),this.$emit("input",[n,a,o])}}}},d=r,l=(n("9f1c"),n("2877")),m=Object(l["a"])(d,a,o,!1,null,"612fbed9",null);t["default"]=m.exports},"253b":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-example-star",use:"icon-example-star-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-example-star"><defs><style type="text/css"></style></defs><path d="M512 768l-249.941333 131.413333a28.444444 28.444444 0 0 1-41.244445-30.008889l47.701334-278.300444-202.183112-197.091556a28.444444 28.444444 0 0 1 15.758223-48.526222l279.438222-40.590222 124.956444-253.212444a28.444444 28.444444 0 0 1 51.029334 0l124.956444 253.212444 279.438222 40.590222a28.444444 28.444444 0 0 1 15.758223 48.526222l-202.183112 197.12 47.729778 278.272a28.444444 28.444444 0 0 1-41.272889 29.980445L512 768z" fill="#FFC500" p-id="873" /><path d="M512 768l-249.941333 131.413333a28.444444 28.444444 0 0 1-41.244445-30.008889l47.701334-278.300444c36.124444-190.805333 67.128889-286.208 93.013333-286.208 38.826667 0 393.955556 261.774222 393.955555 286.208 0 16.298667-81.180444 75.264-243.484444 176.896z" fill="#FED902" p-id="874" /></symbol>'});c.a.add(s);t["default"]=s},2933:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-grid",use:"icon-loading-grid-usage",viewBox:"0 0 105 105",content:'<symbol viewBox="0 0 105 105" xmlns="http://www.w3.org/2000/svg" fill="#fff" id="icon-loading-grid">\r\n    <circle cx="12.5" cy="12.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="0s" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="12.5" cy="52.5" r="12.5" fill-opacity=".5">\r\n        <animate attributeName="fill-opacity" begin="100ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="52.5" cy="12.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="300ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="52.5" cy="52.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="600ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="92.5" cy="12.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="800ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="92.5" cy="52.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="400ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="12.5" cy="92.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="700ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="52.5" cy="92.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="500ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n    <circle cx="92.5" cy="92.5" r="12.5">\r\n        <animate attributeName="fill-opacity" begin="200ms" dur="1s" values="1;.2;1" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </circle>\r\n</symbol>'});c.a.add(s);t["default"]=s},2968:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"upload-container"},[e._l(e.url,(function(t,a){return n("div",{key:a,staticClass:"images"},[a<e.max?n("el-image",{style:"width:"+e.width+"px;height:"+e.height+"px;",attrs:{src:t,fit:"cover"}}):e._e(),n("div",{staticClass:"mask"},[n("div",{staticClass:"actions"},[n("span",{attrs:{title:"预览"},on:{click:function(t){return e.preview(a)}}},[n("i",{staticClass:"el-icon-zoom-in"})]),n("span",{attrs:{title:"移除"},on:{click:function(t){return e.remove(a)}}},[n("i",{staticClass:"el-icon-delete"})]),n("span",{directives:[{name:"show",rawName:"v-show",value:e.url.length>1,expression:"url.length > 1"}],class:{disabled:0==a},attrs:{title:"左移"},on:{click:function(t){return e.move(a,"left")}}},[n("i",{staticClass:"el-icon-back"})]),n("span",{directives:[{name:"show",rawName:"v-show",value:e.url.length>1,expression:"url.length > 1"}],class:{disabled:a==e.url.length-1},attrs:{title:"右移"},on:{click:function(t){return e.move(a,"right")}}},[n("i",{staticClass:"el-icon-right"})])])])],1)})),n("el-upload",{directives:[{name:"show",rawName:"v-show",value:e.url.length<e.max,expression:"url.length < max"}],staticClass:"images-upload",attrs:{"show-file-list":!1,headers:e.headers,action:e.action,data:e.data,name:e.name,"before-upload":e.beforeUpload,"on-progress":e.onProgress,"on-success":e.onSuccess,drag:""}},[n("div",{staticClass:"image-slot",style:"width:"+e.width+"px;height:"+e.height+"px;"},[n("i",{staticClass:"el-icon-plus"})]),n("div",{directives:[{name:"show",rawName:"v-show",value:e.progress.percent,expression:"progress.percent"}],staticClass:"progress",style:"width:"+e.width+"px;height:"+e.height+"px;"},[n("el-image",{style:"width:"+e.width+"px;height:"+e.height+"px;",attrs:{src:e.progress.preview,fit:"fill"}}),n("el-progress",{attrs:{type:"circle",width:.8*Math.min(e.width,e.height),percentage:e.progress.percent}})],1)]),e.notip?e._e():n("div",{staticClass:"el-upload__tip"},[n("div",{staticStyle:{display:"inline-block"}},[n("el-alert",{attrs:{title:"上传图片支持 "+e.ext.join(" / ")+" 格式，单张图片大小不超过 "+e.size+"MB，建议图片尺寸为 "+e.width+"*"+e.height+"，且图片数量不超过 "+e.max+" 张",type:"info","show-icon":"",closable:!1}})],1)]),e.imageViewerVisible?n("el-image-viewer",{attrs:{"on-close":function(){e.imageViewerVisible=!1},"url-list":[e.dialogImageUrl]}}):e._e()],2)},o=[],i=(n("a9e3"),n("a434"),n("ac1f"),n("1276"),n("b0c0"),n("a15b"),n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("08a9")),c={name:"ImagesUpload",components:{ElImageViewer:i["a"]},props:{action:{type:String,required:!0},headers:{type:Object,default:function(){}},data:{type:Object,default:function(){}},name:{type:String,default:"file"},url:{type:Array,default:function(){return[]}},max:{type:Number,default:3},size:{type:Number,default:2},width:{type:Number,default:150},height:{type:Number,default:150},placeholder:{type:String,default:""},notip:{type:Boolean,default:!1},ext:{type:Array,default:function(){return["jpg","png","gif","bmp"]}}},data:function(){return{dialogImageUrl:"",imageViewerVisible:!1,progress:{preview:"",percent:0}}},methods:{preview:function(e){this.dialogImageUrl=this.url[e],this.imageViewerVisible=!0},remove:function(e){var t=this.url;t.splice(e,1),this.$emit("update:url",t)},move:function(e,t){var n=this.url;"left"==t&&0!=e&&(n[e]=n.splice(e-1,1,n[e])[0]),"right"==t&&e!=n.length-1&&(n[e]=n.splice(e+1,1,n[e])[0]),this.$emit("update:url",n)},beforeUpload:function(e){var t=e.name.split("."),n=t[t.length-1],a=this.ext.indexOf(n)>=0,o=e.size/1024/1024<this.size;return a||this.$message.error("上传图片只支持 ".concat(this.ext.join(" / ")," 格式！")),o||this.$message.error("上传图片大小不能超过 ".concat(this.size,"MB！")),a&&o&&(this.progress.preview=URL.createObjectURL(e)),a&&o},onProgress:function(e){this.progress.percent=~~e.percent,100==this.progress.percent&&(this.progress.preview="",this.progress.percent=0)},onSuccess:function(e){this.$emit("on-success",e)}}},s=c,r=(n("5531"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"58d8d87e",null);t["default"]=d.exports},"29e7":function(e,t,n){"use strict";n.r(t);var a={payStatus:{1:"待支付",2:"已支付",3:"已取消"},orderType:{org:"机构",personal:"个人"}},o={},i={},c={};t["default"]={namespaced:!0,state:a,actions:i,getters:o,mutations:c}},"2a06":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"dynamic-form"},[e._l(e.formItems,(function(t,a){return n("div",{key:a,staticClass:"item"},[n("el-row",{staticClass:"block-row",attrs:{gutter:20}},[n("el-col",{attrs:{md:23}},[e._t("formFields",null,{item:t})],2),n("el-col",{attrs:{md:1}},[n("el-popconfirm",{attrs:{"confirm-button-text":"确定","cancel-button-text":"取消",icon:"el-icon-info","icon-color":"red",title:"是否删除?"},on:{confirm:function(t){return e.removeItem(a)}}},[n("span",{staticClass:"el-icon-delete icon-button",attrs:{slot:"reference"},slot:"reference"})])],1)],1)],1)})),n("div",{staticClass:"bottom"},[n("el-button",{attrs:{type:"primary",plain:"",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:e.addItem}},[e._v(e._s(e.btnName))])],1)],2)},o=[],i=(n("a434"),{name:"DynamicForm",props:{formItems:{type:Array,required:!0},onRemove:{type:Function,required:!0},btnName:{type:String,default:"增加"}},methods:{addItem:function(){var e={};this.formItems.push(e)},removeItem:function(e){this.onRemove(e)&&this.formItems.splice(e,1)}}}),c=i,s=(n("dd3d"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"7e827f8e",null);t["default"]=r.exports},"2a1d":function(e,t,n){"use strict";(function(e,a){n.d(t,"a",(function(){return o})),n.d(t,"c",(function(){return c})),n.d(t,"b",(function(){return s})),n.d(t,"d",(function(){return d})),n.d(t,"e",(function(){return l}));n("ac1f"),n("1276"),n("a434"),n("d3b7"),n("ddb0"),n("498a");e.namespace=function(){var e,t,n,a=arguments,o=null;for(e=0;e<a.length;e+=1)for(n=a[e].split("."),o=window,t=0;t<n.length;t+=1)o[n[t]]=o[n[t]]||{},o=o[n[t]];return o};var o={LOGOUT:0,READY:1,NOTREADY:2},c={RequestAgentLogin:100,RequestAgentReady:101,RequestAgentNotReady:102,RequestAgentLogout:103,RequestMakeCall:200,RequestAnswerCall:201,RequestReleaseCall:203,RequestHoldCall:204,RequestRedirectCall:212,RequestClearCall:213,RequestSingleStepConference:214,RequestSingleStepTransfer:215,RequestDeleteFromConference:216,RequestRetrieveCall:217,RequestInitiateConference:220,RequestInitiateTransfer:221,RequestCompleteConference:222,RequestCompleteTransfer:223,RequestTransferToIVR:224,RequestSendDtmf:250,RequestAttachUserData:230,RequestDeleteUserData:231,RequestUpdateUserData:232,RequestRegisterAddress:261,RequestUnregisterAddress:263,RequestMonitorCall:265,RequestQueryAgentStatus:266,RequestQueueState:270,EventAgentLogin:580,EventAgentLogout:581,EventAgentNotReady:582,EventAgentReady:583,EventRinging:503,EventAbandoned:504,EventDialing:505,EventEstablished:506,EventAttachedDataChanged:507,EventDtmfSent:508,EventHeld:509,EventPartyAdded:510,EventPartyChanged:511,EventPartyDeleted:512,EventRetrieved:513,EventReleased:515,EventAgentInfo:588,EventRegistered:572,EventUnregistered:574,EventLinkConnected:590,EventLinkDisconnected:4500,EventError:9999,EventAgentDailyStatistics:2500,EventCampaignLoaded:1500,EventCampaignUnloaded:1501,EventDialingStarted:1502,EventDialingStopped:1503,EventUpdateTenantIP:1504,EventCampaignSearch:1505,EventCampaignDelete:1506,EventOutboundInfo:1509,EventCampaignLoadByFileName:1510,EventRetrieveCampaign:1511,EventDownRecord:3501};function s(){this.elements=new Array,this.size=function(){return this.elements.length},this.isEmpty=function(){return this.elements.length<1},this.clear=function(){this.elements=new Array},this.put=function(e,t){this.elements.push({key:e,value:t})},this.remove=function(e){var t=!1;try{for(i=0;i<this.elements.length;i++)if(this.elements[i].key==e)return this.elements.splice(i,1),!0}catch(n){t=!1}return t},this.get=function(e){try{for(i=0;i<this.elements.length;i++)if(this.elements[i].key==e)return this.elements[i].value}catch(t){return null}return null},this.element=function(e){return e<0||e>=this.elements.length?null:this.elements[e]},this.containsKey=function(e){var t=!1;try{for(i=0;i<this.elements.length;i++)this.elements[i].key==e&&(t=!0)}catch(n){t=!1}return t},this.containsValue=function(e){var t=!1;try{for(i=0;i<this.elements.length;i++)this.elements[i].value==e&&(t=!0)}catch(n){t=!1}return t},this.values=function(){var e=new Array;for(i=0;i<this.elements.length;i++)e.push(this.elements[i].value);return e},this.keys=function(){var e=new Array;for(i=0;i<this.elements.length;i++)e.push(this.elements[i].key);return e}}var r=function(e){"undefined"!=typeof art?art.dialog({lock:!0,width:"24em",time:5e3,window:"top",content:e}):alert(e)},d=function(e){if(null==e||0==e.length)return!1;var t="*#0123456789";e=a("#phoneNumber").val();e=a.trim(e);for(var n=0;n<e.length;n++){var o=e.charAt(n);if(-1==t.indexOf(o))return r("输入的电话号码含有不符合规范,请检查是否含有空格或者其他非数字字符."),!1}return!0},l=function(e){return null==e||0==e.length?null:e.substring(0,e.indexOf("_"))}}).call(this,n("1157"),n("1157"))},"2a75":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-upload",{attrs:{action:e.action,data:e.data,name:e.name,"before-upload":e.beforeUpload,"on-exceed":e.onExceed,"on-success":e.onSuccess,"file-list":e.files,limit:e.max,drag:""}},[n("div",{staticClass:"slot"},[n("i",{staticClass:"el-icon-upload"}),n("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),n("em",[e._v("点击上传")])])]),e.notip?e._e():n("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[n("div",{staticStyle:{display:"inline-block"}},[n("el-alert",{attrs:{title:"上传文件支持 "+e.ext.join(" / ")+" 格式，单个文件大小不超过 "+e.size+"MB，且文件数量不超过 "+e.max+" 个",type:"info","show-icon":"",closable:!1}})],1)])])},o=[],i=(n("a9e3"),n("ac1f"),n("1276"),n("b0c0"),n("a15b"),{name:"FileUpload",props:{action:{type:String,required:!0},headers:{type:Object,default:function(){}},data:{type:Object,default:function(){}},name:{type:String,default:"file"},size:{type:Number,default:2},max:{type:Number,default:3},files:{type:Array,default:function(){return[]}},notip:{type:Boolean,default:!1},ext:{type:Array,default:function(){return["zip","rar"]}}},methods:{beforeUpload:function(e){var t=e.name.split("."),n=t[t.length-1],a=this.ext.indexOf(n)>=0,o=e.size/1024/1024<this.size;return a||this.$message.error("上传文件只支持 ".concat(this.ext.join(" / ")," 格式！")),o||this.$message.error("上传文件大小不能超过 ".concat(this.size,"MB！")),a&&o},onExceed:function(){this.$message.warning("文件上传超过限制")},onSuccess:function(e,t){this.$emit("on-success",e,t)}}}),c=i,s=(n("7c25"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"29072b41",null);t["default"]=r.exports},"2aaa":function(e,t,n){},"2b55":function(e,t,n){"use strict";(function(e,t){n("bf19");var a=n("2a1d"),o=n("0f1a"),i=n("56d7");e.namespace("cti"),t(document).ready((function(){top.currentUrl;var e=null;if(ws.browserSupportsWebSockets())e=new ws.webSocketClient;else{var n=ws.MSG_WS_NOT_SUPPORTED;console.log(n),showMessage(n)}cti.logon=function(){var n=ws.getServerURL("ws",t.mshost,t.msport,"/websocket"),a=cti.Agent.getInstance().getThisDN(),o=cti.Agent.getInstance().getAgentID(),i=cti.Agent.getInstance().getThisQueues(),c={messageId:100,thisDN:a,agentID:o,thisQueues:i};console.log(c);e.logon(n,a,t.toJSON(c),{OnOpen:function(t){console.log("OnOpen",333333333),e.startKeepAlive({immediate:!1,interval:3e4})},OnMessage:function(e){console.log("OnMessage",1111111111111);var n=t.parseJSON(e.data);console.log(n,"websoket返回信息"),cti.parseMessage(n)},OnClose:function(t){console.log("OnClose",222222222),e.stopKeepAlive()}})},cti.doOpen=function(){e.isConnected()||cti.logon()},cti.doClose=function(){e.isConnected()&&(e.stopKeepAlive(),e.close())},cti.unsubscribe=function(){if(e){var t=cti.Agent.getInstance().getThisDN(),n={messageId:263,thisDN:t};cti.send(n)}else showMessage("与消息服务器的连接已断开")},cti.send=function(n){var a=cti.Agent.getInstance().getThisDN();if(e){var o={thisDN:a,type:"request",message:t.toJSON(n)};e.sendToken(o)}else showMessage("与消息服务器的连接已断开")};var c=null;cti.parseMessage=function(e){if(console.log(e,"这是websoket返回的值"),console.log(e.messageId,"这是websoket返回的ID值"),null!=e)if(e.messageId==a["c"].EventAgentLogin){var n=202;e.state==a["a"].NOTREADY?n=201:e.state==a["a"].READY?n=200:e.state==a["a"].LOGOUT&&(n=202),window.clearTimeout(c),t("#loading").remove(),cti.handleAgentEvent(n,e.reasonCode,e.deviceState),o["a"].setCCKoala(e)}else if(e.messageId==a["c"].EventAgentNotReady)cti.handleAgentEvent(201,e.reasonCode,e.deviceState),console.log(a["c"].EventAgentNotReady);else if(e.messageId==a["c"].EventAgentReady)cti.handleAgentEvent(200,e.reasonCode,e.deviceState),console.log(a["c"].EventAgentReady),o["a"].setCCKoala(e);else if(e.messageId==a["c"].EventAgentLogout)cti.handleAgentEvent(202,e.reasonCode,e.deviceState),console.log(a["c"].EventAgentLogout,"坐席登出"),o["a"].setCCKoala(e);else if(e.messageId==a["c"].EventDialing)cti.handleVoiceEvent(54,e),console.log(a["c"].EventDialing,"外呼拨号"),o["a"].koalaOptLog(t.toJSON(e),"makeCall"),t("#saleOutCallForm #sessionId").val(e.callID),o["a"].setCCKoala(e);else if(e.messageId==a["c"].EventRinging)cti.handleVoiceEvent(56,e),console.log(a["c"].EventRinging,"振铃"),log("EventRinging: "+t.toJSON(e));else if(e.messageId==a["c"].EventEstablished)cti.handleVoiceEvent(55,e),console.log(a["c"].EventEstablished,"通话中"),o["a"].setCCKoala(e),o["a"].koalaOptLog(t.toJSON(e),"established");else if(e.messageId==a["c"].EventReleased)cti.handleVoiceEvent(2,e),console.log(a["c"].EventEstablished,"挂断"),o["a"].setCCKoala(e),o["a"].koalaOptLog(t.toJSON(e),"releaseCall");else if(e.messageId==a["c"].EventHeld)cti.handleVoiceEvent(57,e);else if(e.messageId==a["c"].EventRetrieved)cti.handleVoiceEvent(58,e);else if(e.messageId==a["c"].EventAbandoned)cti.handleVoiceEvent(2,e);else if(e.messageId==a["c"].EventAgentInfo){var s=document.getElementById("callIframeMonitor");s&&s.contentWindow.onAgentInfoEvent(e),log("EventAgentInfo: "+t.toJSON(e))}else if(e.messageId==a["c"].EventOutboundInfo){var r=document.getElementById("callIframeCampaignTarget");r&&r.contentWindow.onOutboundInfo(e),log("EventOutboundInfo: "+t.toJSON(e))}else if(e.messageId==a["c"].EventError){if(i["default"].$message({message:e.errorMessage,type:"error"}),"正在下载,请稍候!"==e.errorMessage){s=document.getElementById("callIframeRecord");s&&s.contentWindow.overDownLoad()}else if(112==e.errorCode){s=document.getElementById("callIframeCampaign");s&&s.contentWindow.showCampaign()}}else if(e.messageId==a["c"].EventLinkDisconnected)1==e.reason?alert("该坐席已经从其它地方登陆，请退出!"):console.log("与服务器的连接已断开");else if(e.messageId==a["c"].EventDialingStarted||e.messageId==a["c"].EventCampaignUnloaded||e.messageId==a["c"].EventDialingStopped){s=document.getElementById("callIframeCampaign");s&&(e.messageId==a["c"].EventDialingStarted?s.contentWindow.searchCampaign(CAMPAIGN.START):e.messageId==a["c"].EventDialingStopped?s.contentWindow.searchCampaign(CAMPAIGN.STOP):e.messageId==a["c"].EventCampaignUnloaded&&s.contentWindow.searchCampaign(CAMPAIGN.UNLOAD))}else if(e.messageId!=a["c"].EventError&&e.messageId==a["c"].RequestQueueState)showMessage("");else if(e.messageId==a["c"].EventAgentDailyStatistics){console.log(e),"daily"==e.statisticType?s=document.getElementById("callIframeAgentDailyStatistic"):"ivr"==e.statisticType?s=document.getElementById("callIframeIvrHistory"):"queue"==e.statisticType?s=document.getElementById("callIframeQueueHistory"):"agent"==e.statisticType&&(s=document.getElementById("callIframeAgentHistory")),log("EventAgentDailyStatistics: "+t.toJSON(e)),s&&s.contentWindow.initPagination(e)}else if(e.messageId==a["c"].EventCampaignLoaded){showMessage("重复号码"+e.repeatNumber+",错误号码"+e.errorNumber+",成功号码"+e.successNumber);s=document.getElementById("callIframeCampaign");s&&s.contentWindow.showCampaign()}else if(e.messageId==a["c"].EventCampaignDelete){s=document.getElementById("callIframeCampaign");s&&s.contentWindow.searchCampaign(e.state)}else if(e.messageId==a["c"].EventCampaignSearch){s=document.getElementById("callIframeCampaign");s&&s.contentWindow.refreshCampaign(e.campaigns)}else if(e.messageId==a["c"].EventRetrieveCampaign){s=document.getElementById("callIframeCampaign");s&&s.contentWindow.modify()}},c=window.setTimeout((function(){}),2e4)}))}).call(this,n("1157"),n("1157"))},"2b61":function(e,t,n){"use strict";n("99af");var a=n("83d6"),o={};o.local={has:function(e){return!!localStorage.getItem("".concat(a["a"].storagePrefix).concat(e))},get:function(e){return localStorage.getItem("".concat(a["a"].storagePrefix).concat(e))},set:function(e,t){localStorage.setItem("".concat(a["a"].storagePrefix).concat(e),t)},remove:function(e){localStorage.removeItem("".concat(a["a"].storagePrefix).concat(e))},clear:function(){localStorage.clear()}},o.session={has:function(e){return!!sessionStorage.getItem("".concat(a["a"].storagePrefix).concat(e))},get:function(e){return sessionStorage.getItem("".concat(a["a"].storagePrefix).concat(e))},set:function(e,t){sessionStorage.setItem("".concat(a["a"].storagePrefix).concat(e),t)},remove:function(e){sessionStorage.removeItem("".concat(a["a"].storagePrefix).concat(e))},clear:function(){sessionStorage.clear()}},t["a"]=o},"2d40":function(e,t,n){"use strict";n("9e88")},"2fa1":function(e,t,n){},"2fbd":function(e,t,n){},"34de":function(e,t,n){"use strict";n("d208")},"365c":function(e,t,n){"use strict";n.d(t,"a",(function(){return d}));n("d3b7");var a=n("bc3a"),o=n.n(a),i=(n("4328"),n("a18c")),c=n("4360"),s=n("5c96"),r=function(){i["a"].push({path:"/login",query:{redirect:i["a"].currentRoute.fullPath}})},d="/api/",l=o.a.create({baseURL:"/api/",timeout:6e5,responseType:"json"});l.interceptors.request.use((function(e){return e.headers["X-Requested-With"]="XMLHttpRequest",e.headers["Content-Type"]="application/json; charset=UTF-8",c["a"].getters["user/isLogin"]&&(e.headers["token"]=c["a"].state.user.token),"post"==e.method?e.data instanceof FormData?c["a"].getters["user/isLogin"]&&e.data.append("auth-token",c["a"].state.user.token):void 0==e.data&&(e.data={}):void 0==e.params&&(e.params={}),e})),l.interceptors.response.use((function(e){return"00000"!==e.data.status?("00003"==e.data.status&&c["a"].dispatch("user/logout").then((function(){localStorage.removeItem("fa_token"),localStorage.removeItem("fa_account"),localStorage.removeItem("fa_failure_time"),r()})),"没有空闲管家！"===e.data.message||"00003"===e.data.status||s["Message"].error(e.data.message),Promise.reject(e.data)):Promise.resolve(e.data)}),(function(e){return Promise.reject(e)})),t["b"]=l},"36ba":function(e){e.exports=JSON.parse('{"Buildings":{"home":["house","房子","家","主页"],"home-2":["house","房子","家","主页"],"home-3":["house","房子","家","主页"],"home-4":["house","房子","家","主页"],"home-5":["house","房子","家","主页"],"home-6":["house","房子","家","主页"],"home-7":["house","房子","家","主页"],"home-8":["house","房子","家","主页"],"home-gear":["house","房子","工厂"],"home-wifi":["smart home","房子","家具","智能家居"],"home-smile":["house","smart home","smile","房子","智能家居","微笑"],"home-smile-2":["house","smart home","smile","房子","智能家居","微笑"],"home-heart":["house","心","房子","家","主页","孤儿院"],"building":["city","office","enterprise","建筑","城市","楼","办公楼","写字楼","企业"],"building-2":["city","office","construction","enterprise","城市","建筑","楼","企业"],"building-3":["factory","plant","enterprise","工厂","建筑","楼","企业"],"building-4":["city","office","enterprise","建筑","城市","楼","办公楼","写字楼","企业"],"hotel":["building","hotel","office","enterprise","tavern","建筑","酒店","楼","办公楼","写字楼","企业"],"community":["building","hotel","社区","建筑","酒店"],"government":["building","政府","建筑","大会堂"],"bank":["bank","finance","savings","banking","银行","交易所"],"store":["shop","mall","supermarket","商店","超市","店铺","商家"],"store-2":["shop","mall","supermarket","商店","超市","店铺","商家"],"store-3":["shop","mall","supermarket","商店","超市","店铺","商家"],"hospital":["medical","health","医院"],"ancient-gate":["historical","genre","scenic","trip","travel","旅行","旅游","城门","古代","历史","景区"],"ancient-pavilion":["historical","genre","scenic","trip","travel","旅行","旅游","凉亭","古代","历史","景区"]},"Business":{"mail":["envelope","email","inbox","信封","邮箱","邮件","收件箱"],"mail-open":["envelope","email","inbox","信封","邮箱","邮件","收件箱"],"mail-send":["envelope","email","inbox","信封","邮箱","邮件","发送","发件箱"],"mail-unread":["envelope","email","inbox","信封","邮箱","邮件","未读"],"mail-add":["envelope","email","inbox","add","信封","邮箱","邮件","新增","添加"],"mail-check":["envelope","email","inbox","read","信封","邮箱","邮件","已读"],"mail-close":["envelope","email","inbox","failed","x","信封","邮箱","邮件","失败"],"mail-download":["envelope","email","inbox","download","信封","邮箱","邮件","下载"],"mail-forbid":["envelope","email","inbox","privacy","信封","邮箱","邮件","禁止"],"mail-lock":["envelope","email","inbox","lock","信封","邮箱","邮件","加密"],"mail-settings":["envelope","email","inbox","settings","信封","邮箱","邮件","设置"],"mail-star":["envelope","email","inbox","favorite","信封","邮箱","邮件","收藏","喜欢"],"mail-volume":["envelope","email","inbox","promotional email","email campaign","subscription","信封","邮箱","邮件","收件箱","推广","订阅"],"inbox":["收件箱"],"inbox-archive":["收件箱","归档","收纳"],"inbox-unarchive":["unzip","unpack","extract","收件箱","取消归档","还原","解压缩"],"cloud":["weather","云端"],"cloud-off":["offline-mode","connection-fail","slash","weather","云端","断网","无信号","连接失败"],"attachment":["annex","paperclip","附件","曲别针"],"profile":["id","档案","资料","身份证","证件"],"archive":["box","收纳","归档","存档","盒子","纸箱"],"archive-drawer":["night table","收纳","抽屉","归档","存档","床头柜"],"at":["@","mention","提到","在"],"award":["medal","achievement","badge","成就","奖牌","金牌","勋章"],"medal":["award","achievement","badge","成就","奖牌","金牌","勋章"],"medal-2":["award","achievement","badge","成就","奖牌","金牌","勋章"],"bar-chart":["statistics","rhythm","柱状图","统计","韵律","节奏"],"bar-chart-horizontal":["statistics","rhythm","柱状图","统计","韵律","节奏"],"bar-chart-2":["statistics","rhythm","柱状图","统计","排行","节奏"],"bar-chart-box":["statistics","rhythm","柱状图","统计","节奏"],"bar-chart-grouped":["statistics","rhythm","柱状图","统计","分组"],"bubble-chart":["data","analysis","statistics","气泡图","统计"],"pie-chart":["data","analysis","饼图","饼状图","数据","分析"],"pie-chart-2":["data","analysis","饼图","饼状图","数据","分析"],"pie-chart-box":["data","analysis","饼图","饼状图","数据","分析"],"donut-chart":["data","analysis","环形图","数据","分析"],"line-chart":["data","analysis","stats","折线图","数据","分析"],"bookmark":["tag","书签","标记"],"bookmark-2":["tag","书签","标记"],"bookmark-3":["tag","书签","标记","荣誉"],"briefcase":["bag","baggage","公文包","行李箱","旅行箱","皮包"],"briefcase-2":["bag","baggage","公文包","行李箱","旅行箱","皮包"],"briefcase-3":["bag","baggage","公文包","行李箱","旅行箱","皮包"],"briefcase-4":["bag","baggage","公文包","行李箱","旅行箱","皮包"],"briefcase-5":["bag","baggage","公文包","行李箱","旅行箱","皮包"],"calculator":["计算器","计算机"],"calendar":["date","plan","schedule","agenda","日历","日期","月份","计划","日程","时间表"],"calendar-2":["date","plan","schedule","agenda","日历","日期","月份","计划","日程","时间表"],"calendar-event":["date","plan","schedule","agenda","日历","日期","月份","计划","日程","时间表"],"calendar-todo":["date","plan","schedule","agenda","日历","日期","月份","计划","日程","时间表"],"calendar-check":["date","plan","schedule","agenda","check-in","punch","日历","日期","月份","计划","日程","时间表","签到","打卡"],"customer-service":["headset","客服","售后","耳机","耳麦"],"customer-service-2":["headset","客服","售后","耳机","耳麦"],"flag":["banner","pin","旗帜","旗子","国旗","标记"],"flag-2":["banner","pin","旗帜","旗子","国旗","标记"],"global":["earth","union","world","language","地球","联合","世界","全球","语言"],"honour":["honor","glory","锦旗","荣誉","荣耀","军衔"],"links":["connection","address","联系","链接","地址"],"printer":["打印机"],"printer-cloud":["打印机","云打印"],"record-mail":["voice mail","tape","录音","留言","语音信箱","磁带"],"reply":["forward","回复全部","回复所有"],"reply-all":["forward","回复","答复","留言","转发"],"send-plane":["发送","纸飞机"],"send-plane-2":["发送","纸飞机"],"projector":["projection","meeting","投影仪","会议室"],"projector-2":["projection","meeting","投影仪","会议室","极米"],"slideshow":["presentation","meeting","PPT","keynote","投影","放映","演示","演讲","幻灯片","会议室"],"slideshow-2":["presentation","meeting","投影","放映","演示","演讲","幻灯片","会议室"],"slideshow-3":["presentation","meeting","投影","放映","演示","演讲","视频会议","幻灯片","会议室"],"slideshow-4":["presentation","meeting","投影","放映","演示","演讲","可视对讲","幻灯片","会议室"],"window":["browser","program","web","窗口","浏览器","程序","网站"],"window-2":["browser","program","web","窗口","浏览器","程序","网站"],"stack":["layers","图层","叠加","堆栈"],"service":["heart","handshake","cooperation","服务","握手","心","合作"],"registered":["注册","商标"],"trademark":["注册","商标"],"advertisement":["ad","广告","推广"],"copyleft":["著佐权"],"copyright":["版权"],"creative-commons":["知识共享"],"creative-commons-by":["attribution","copyright","版权","知识共享","署名"],"creative-commons-nc":["noncommercial","copyright","版权","知识共享","非商业用途"],"creative-commons-nd":["no derivative works","copyright","版权","知识共享","禁止演绎"],"creative-commons-sa":["share alike","copyright","版权","知识共享","相同方式共享"],"creative-commons-zero":["cc0","copyright","版权","知识共享"]},"Communication":{"chat-1":["message","reply","comment","消息","聊天","回复","评论"],"chat-2":["message","reply","comment","消息","聊天","回复","评论"],"chat-3":["message","reply","comment","消息","聊天","回复","评论"],"chat-4":["message","reply","comment","消息","聊天","回复","评论"],"message":["chat","comment","reply","消息","聊天","回复","评论"],"message-2":["chat","reply","comment","消息","聊天","回复","评论"],"message-3":["chat","reply","comment","消息","聊天","回复","评论"],"chat-check":["message","reply","comment","消息","聊天","回复","评论","已阅"],"chat-delete":["message","comment","消息","聊天","回复","评论","清除","删除"],"chat-forward":["message","comment","消息","聊天","转发"],"chat-upload":["message","comment","消息","聊天","上传"],"chat-download":["message","comment","消息","下载"],"chat-new":["message","reply","comment","消息","聊天","回复","评论"],"chat-settings":["message","comment","消息","聊天","回复","评论","设置"],"chat-smile":["message","reply","comment","消息","聊天","回复","评论"],"chat-smile-2":["message","reply","comment","消息","聊天","回复","评论"],"chat-smile-3":["message","reply","comment","消息","聊天","回复","评论"],"chat-heart":["message","reply","comment","消息","聊天","回复","评论","心","点赞","收藏"],"chat-off":["message","reply","comment","slash","消息","聊天","回复","评论","禁止","关闭"],"feedback":["message","comment","消息","聊天","回复","评论","反馈"],"discuss":["message","reply","comment","消息","聊天","回复","评论","讨论","群聊"],"question-answer":["message","reply","comment","消息","聊天","回复","评论","讨论","群聊"],"questionnaire":["message","comment","help","消息","聊天","回复","评论","讨论","调查问卷","帮助"],"video-chat":["message","comment","消息","视频聊天"],"chat-voice":["message","comment","消息","语音消息"],"chat-quote":["message","reply","comment","消息","引用回复"],"chat-follow-up":["message","reply","comment","消息","+1","跟帖"],"chat-poll":["message","vote","questionnaire","消息","投票","问卷调查"],"chat-history":["message","历史消息","消息记录"],"chat-private":["message","私密消息","密聊"]},"Design":{"pencil":["edit","铅笔","编辑"],"edit":["pencil","铅笔","编辑"],"edit-2":["pencil","铅笔","编辑"],"ball-pen":["圆珠笔"],"quill-pen":["羽毛笔","钢笔","编辑"],"pen-nib":["钢笔","笔尖"],"ink-bottle":["墨水瓶"],"mark-pen":["马克笔"],"markup":["标记","马克"],"edit-box":["编辑"],"edit-circle":["编辑"],"sip":["吸管","取色器"],"brush":["笔刷","画笔","刷子"],"brush-2":["刷子"],"brush-3":["刷子"],"brush-4":["刷子"],"paint-brush":["填色","填充","刷子"],"contrast":["brightness","tonalit","对比度","亮度","色调"],"contrast-2":["moon","dark","brightness","tonalit","月亮","夜间","对比度","亮度","色调"],"drop":["water","blur","模糊","水","滴"],"blur-off":["water","drop","slash","模糊","水","滴","禁止","关闭"],"contrast-drop":["water","brightness","tonalit","水","对比度","亮度","色调","滴"],"contrast-drop-2":["water","brightness","tonalit","水","对比度","亮度","色调","滴"],"compasses":["圆规"],"compasses-2":["圆规"],"scissors":["剪刀","裁剪"],"scissors-cut":["剪刀","裁剪"],"scissors-2":["剪刀","裁剪","截屏"],"slice":["knife","切图","切片","刀"],"eraser":["remove formatting","橡皮","擦除","清除格式"],"ruler":["尺子"],"ruler-2":["尺子"],"pencil-ruler":["design","铅笔","尺子","文具","设计"],"pencil-ruler-2":["design","铅笔","尺子","文具","设计"],"t-box":["文字","字体","字号"],"input-method":["输入法","文字"],"artboard":["grid","crop","画板","裁切"],"artboard-2":["画板"],"crop":["裁切"],"crop-2":["裁切"],"screenshot":["capture","屏幕截图","截屏"],"screenshot-2":["capture","屏幕截图","截屏"],"drag-move":["arrow","拖拽","移动","箭头"],"drag-move-2":["arrow","拖拽","移动","箭头"],"focus":["aim","target","焦点","聚焦","目标","靶心"],"focus-2":["aim","target","bullseye","焦点","聚焦","目标","靶心"],"focus-3":["aim","target","bullseye","焦点","聚焦","目标","靶心"],"paint":["填色","填充","油漆桶"],"palette":["调色盘","色板"],"pantone":["色板","潘通色","色号"],"shape":["border","形状","描边","边框"],"shape-2":["border","形状","描边","边框"],"magic":["fantasy","magic stick","beautify","魔法棒","美化","幻想","魔幻"],"anticlockwise":["rotate","left","左翻转","左旋转"],"anticlockwise-2":["rotate","left","左翻转","左旋转"],"clockwise":["rotate","right","右翻转","右旋转"],"clockwise-2":["rotate","right","右翻转","右旋转"],"hammer":["锤子"],"tools":["settings","工具","设置"],"drag-drop":["drag and drop","mouse","拖拽","鼠标"],"table":["表格"],"table-alt":["表格"],"layout":["布局"],"layout-2":["collage","布局","拼贴画"],"layout-3":["collage","布局","拼贴画"],"layout-4":["collage","布局","拼贴画"],"layout-5":["collage","布局","拼贴画"],"layout-6":["collage","布局","拼贴画"],"layout-column":["左右布局"],"layout-row":["上下布局"],"layout-top":["顶部布局","顶部导航"],"layout-right":["右侧布局","右侧导航"],"layout-bottom":["底部布局","底部导航"],"layout-left":["左侧布局","左侧导航"],"layout-top-2":["顶部布局","顶部导航"],"layout-right-2":["右侧布局","右侧导航"],"layout-bottom-2":["底部布局","底部导航"],"layout-left-2":["左侧布局","左侧导航"],"layout-grid":["卡片布局","网格"],"layout-masonry":["collage","瀑布流布局","拼贴画"],"collage":["瀑布流布局","拼贴画"],"grid":["table","网格","表格"]},"Development":{"bug":["虫子"],"bug-2":["虫子"],"code":["代码","编程"],"code-s":["代码","编程"],"code-s-slash":["代码","编程"],"code-box":["代码","编程"],"terminal-box":["code","command line","终端","代码","命令行"],"terminal":["code","command line","终端","代码","命令行"],"terminal-window":["code","command line","终端","代码","命令行"],"parentheses":["code","math","小括号"],"brackets":["code","math","中括号"],"braces":["code","math","大括号","花括号"],"command":["apple key","花键","苹果键"],"cursor":["mouse","指针","鼠标"],"git-commit":["node","提交"],"git-pull-request":["合并申请"],"git-merge":["合并"],"git-branch":["分支"],"git-repository":["仓库"],"git-repository-commits":["仓库","提交"],"git-repository-private":["私密仓库","私人仓库"],"html5":["html","h5"],"css3":["css"]},"Device":{"tv":["电视"],"tv-2":["monitor","电视","显示器"],"computer":["monitor","电脑","显示器"],"mac":["monitor","显示器"],"macbook":["laptop","笔记本"],"cellphone":["手机","电话"],"smartphone":["mobile","手机"],"tablet":["平板电脑"],"device":["设备"],"phone":["电话"],"database":["storage","数据库","存储"],"database-2":["storage","数据库","存储"],"server":["服务器"],"hard-drive":["disc","storage","硬盘","存储"],"hard-drive-2":["disc","server","storage","硬盘","服务器","存储"],"install":["安装"],"uninstall":["卸载"],"save":["floppy","保存","软盘"],"save-2":["floppy","保存","软盘"],"save-3":["floppy","保存","软盘"],"sd-card":["内存卡"],"sd-card-mini":["内存卡"],"sim-card":["电话卡"],"sim-card-2":["电话卡"],"dual-sim-1":["sim card","电话卡","卡槽","双卡双待"],"dual-sim-2":["sim card","电话卡","卡槽","双卡双待"],"u-disk":["U盘","优盘"],"battery":["电池"],"battery-charge":["电池","充电"],"battery-low":["电池","低电量"],"battery-2":["电池"],"battery-2-charge":["电池","充电"],"battery-saver":["电池","省电模式"],"battery-share":["电池共享","共享电量"],"cast":["mirroring","投屏","无线","广播"],"airplay":["mirroring","投屏","无线"],"cpu":["中央处理器"],"gradienter":["水平仪"],"keyboard":["input","键盘","输入"],"keyboard-box":["input","键盘","输入"],"mouse":["鼠标"],"sensor":["capacitor","传感器","电容器"],"router":["wifi","signal tower","radio","station","路由器","信号塔","广播","基站","流量"],"radar":["satellite receiver","雷达","卫星接收器","锅"],"gamepad":["consoles","controller","游戏手柄"],"remote-control":["controller","遥控器"],"remote-control-2":["controller","遥控器"],"device-recover":["恢复出厂设置"],"hotspot":["手机热点"],"phone-find":["找回手机"],"phone-lock":["锁定手机"],"rotate-lock":["锁定旋转屏幕"],"restart":["reload","refresh","重启"],"shut-down":["power off","关机"],"fingerprint":["指纹"],"fingerprint-2":["指纹"],"barcode":["scan","扫码","条形码","条码"],"barcode-box":["scan","扫码","条形码","条码"],"qr-code":["二维码"],"qr-scan":["二维码","扫描"],"qr-scan-2":["二维码","扫描"],"scan":["扫描"],"scan-2":["扫描"],"rss":["feed","subscribe","订阅"],"gps":["signal","定位","信号"],"base-station":["wifi","signal tower","router","cast","基站","信号塔","路由器","广播","流量"],"bluetooth":["wireless","蓝牙","无线"],"bluetooth-connect":["wireless","蓝牙","连接","无线"],"wifi":["无线网"],"wifi-off":["slash","offline","connection-fail","无线网","关闭","断网","链接失败"],"signal-wifi":["cellular","strength","无线网","信号"],"signal-wifi-1":["cellular","strength","无线网","信号"],"signal-wifi-2":["cellular","strength","无线网","信号"],"signal-wifi-3":["cellular","strength","无线网","信号"],"signal-wifi-error":["cellular","offline","connection-fail","无线网","断网","链接失败","无信号"],"signal-wifi-off":["cellular","slash","offline","connection-fail","无线网","关闭","断网","链接失败"],"wireless-charging":["power","flash","无线充电","闪充"],"dashboard-2":["仪表盘"],"dashboard-3":["仪表盘"],"usb":["优盘"]},"Document":{"file":["new","paper","文件","文档","新建"],"file-2":["new","paper","文件","文档","新建"],"file-3":["new","paper","文件","文档","新建"],"file-4":["new","paper","文件","文档","新建"],"sticky-note":["new","paper","文件","文档","新建","便签纸","便利贴"],"sticky-note-2":["new","paper","文件","文档","新建","便签纸","便利贴"],"file-edit":["文件","文档","编辑"],"draft":["草稿箱","文件","文档","编辑"],"file-paper":["文件","文档","纸","谱"],"file-paper-2":["文件","文档","纸","谱"],"file-text":["文件","文档","文本"],"file-list":["清单","列表"],"file-list-2":["清单","列表"],"file-list-3":["newspaper","清单","列表","报纸"],"bill":["账单"],"file-copy":["duplicate","clone","复制","克隆"],"file-copy-2":["duplicate","clone","复制","克隆"],"clipboard":["copy","复制","剪切板"],"survey":["research","questionnaire","调查","问卷","调研"],"article":["newspaper","文章","报纸"],"newspaper":["报纸"],"file-zip":["7z","rar","压缩包"],"file-mark":["文件","文档","标记"],"task":["todo","任务","待办"],"todo":["待办"],"book":["read","dictionary","booklet","书","阅读","字典","小册子"],"book-mark":["read","dictionary","booklet","书","阅读","字典","小册子","书签"],"book-2":["read","dictionary","booklet","书","阅读","字典","小册子"],"book-3":["read","dictionary","booklet","书","阅读","字典","小册子"],"book-open":["read","booklet","magazine","书","阅读","小册子","杂志"],"book-read":["booklet","magazine","书","阅读","小册子","杂志"],"contacts-book":["通讯录","联系人"],"contacts-book-2":["通讯录","联系人"],"contacts-book-upload":["upload","通讯录","联系人","上传"],"booklet":["notebook","手册","笔记本","小册子"],"file-code":["config","文件","文档","代码","脚本","配置文件"],"file-pdf":["文件","文档"],"file-word":["文档"],"file-ppt":["文件","文档"],"file-excel":["文档","表单"],"file-word-2":["文档"],"file-ppt-2":["文件","文档"],"file-excel-2":["文档","表单"],"file-hwp":["文件","文档","hangul word processor"],"keynote":["演示文稿","幻灯片","讲演"],"numbers":["表格"],"pages":["文稿"],"file-search":["文件","文档","搜索"],"file-add":["new","文件","文档","新建"],"file-reduce":["文件","文档","减"],"file-settings":["文件","文档","设置"],"file-upload":["文件","文档","上传"],"file-transfer":["文件","文档","传输"],"file-download":["文件","文档","下载"],"file-lock":["文件","文档","锁"],"file-chart":["report","文件","文档","柱状图","报表"],"file-chart-2":["report","文件","文档","饼图","报表"],"file-music":["文件","文档","音乐"],"file-gif":["文件","文档","动图"],"file-forbid":["文件","文档","禁用"],"file-info":["文件","文档","信息"],"file-warning":["alert","文件","文档","警告","提醒"],"file-unknow":["文件","文档","未知","问号"],"file-user":["文件","文档","用户"],"file-shield":["protected","secured","文件","文档","盾牌","保护","安全"],"file-shield-2":["protected","secured","文件","文档","盾牌","保护","安全"],"file-damage":["breakdown","broken","文件","文档","损坏","破损","破裂"],"file-history":["record","文件","文档","记录","历史"],"file-shred":["shredder","shred","destroy","cut","文档","销毁","碎纸机","破裂","粉碎"],"file-cloud":["文件","文档","云"],"folder":["directory","file","文件夹","目录","文档"],"folder-2":["directory","file","文件夹","目录","文档"],"folder-3":["directory","file","文件夹","目录","文档"],"folder-4":["directory","file","文件夹","目录","文档"],"folder-5":["directory","file","文件夹","目录","文档"],"folders":["directory","file","文件夹","目录","文档","批量"],"folder-add":["directory","file","文件夹","目录","文档","添加"],"folder-reduce":["directory","file","文件夹","目录","文档","减"],"folder-settings":["directory","file","文件夹","目录","文档","设置"],"folder-upload":["directory","file","文件夹","目录","文档","上传"],"folder-transfer":["directory","file","文件夹","目录","文档","传输"],"folder-download":["directory","file","文件夹","目录","文档","下载"],"folder-lock":["directory","file","文件夹","目录","文档","锁"],"folder-chart":["report","文件夹","目录","文档","柱状图","报表"],"folder-chart-2":["report","文件夹","目录","文档","饼图","报表"],"folder-music":["directory","file","文件夹","目录","文档","音乐"],"folder-forbid":["directory","file","文件夹","目录","文档","禁用"],"folder-info":["directory","file","文件夹","目录","文档","信息"],"folder-warning":["alert","directory","file","文件夹","目录","文档","警告","提醒"],"folder-unknow":["directory","file","文件夹","目录","文档","未知"],"folder-user":["directory","file","文件夹","目录","文档","用户"],"folder-shield":["directory","file","protected","secured","文件夹","目录","文档","保护","盾牌","安全"],"folder-shield-2":["directory","file","protected","secured","文件夹","目录","文档","保护","盾牌","安全"],"folder-shared":["directory","file","文件夹","目录","文档","分享"],"folder-received":["directory","file","文件夹","目录","文档","接收"],"folder-open":["directory","file","文件夹","目录","文档","打开"],"folder-keyhole":["directory","encryption","file","文件夹","目录","文档","打开","加密文档"],"folder-zip":["directory","file","文件夹","目录","文档","打开","压缩"],"folder-history":["directory","file","record","文件夹","目录","文档","记录","历史"],"markdown":["arrow","箭头","下"]},"Editor":{"bold":["加粗"],"italic":["斜体"],"heading":["标题"],"text":["字体"],"font-color":["文字色"],"font-size":["字号","字体大小"],"font-size-2":["字号","字体大小"],"underline":["下划线"],"emphasis":["着重号"],"emphasis-cn":["着重号"],"strikethrough":["remove formatting","删除线"],"strikethrough-2":["remove formatting","删除线"],"format-clear":["remove formatting","清除格式"],"align-left":["左对齐"],"align-center":["居中对齐"],"align-right":["右对齐"],"align-justify":["排列对齐"],"align-top":["顶部对齐"],"align-vertically":["垂直对齐"],"align-bottom":["底部对齐"],"list-check":["check list","清单列表"],"list-check-2":["check list","清单列表"],"list-ordered":["number list","有序列表"],"list-unordered":["bullet list","无序列表"],"indent-decrease":["indent more","缩进"],"indent-increase":["indent less","缩进"],"line-height":["行高"],"text-spacing":["字间距"],"text-wrap":["文本换行"],"attachment-2":["annex","paperclip","附件","曲别针"],"link":["connection","address","联系","链接","地址"],"link-unlink":["connection","remove address","去除链接"],"link-m":["connection","address","联系","链接","地址"],"link-unlink-m":["connection","remove address","去除链接"],"separator":["分割线"],"space":["空格"],"page-separator":["insert","分页符","插入"],"code-view":["代码视图"],"double-quotes-l":["left","quotaion marks","双引号"],"double-quotes-r":["right","quotaion marks","双引号"],"single-quotes-l":["left","quotaion marks","单引号"],"single-quotes-r":["right","quotaion marks","单引号"],"table-2":["表格"],"subscript":["角标","下标","脚注"],"subscript-2":["角标","下标","脚注"],"superscript":["角标","上标"],"superscript-2":["角标","上标"],"paragraph":["段落"],"text-direction-l":["文本左对齐"],"text-direction-r":["文本左对齐"],"functions":["功能"],"omega":["Ω","特殊符号"],"hashtag":["#","井号"],"asterisk":["*","星号"],"question-mark":["问号"],"translate":["translator","翻译"],"translate-2":["translator","翻译"],"a-b":["a/b testing","ab testing","ab测试"],"english-input":["英文输入法"],"pinyin-input":["拼音输入法"],"wubi-input":["五笔输入法"],"input-cursor-move":["移动输入光标"],"number-1":["1","一","数字"],"number-2":["2","二","数字"],"number-3":["3","三","数字"],"number-4":["4","四","数字"],"number-5":["5","五","数字"],"number-6":["6","六","数字"],"number-7":["7","七","数字"],"number-8":["8","八","数字"],"number-9":["9","九","数字"],"number-0":["0","零","数字"],"sort-asc":["ranking","ordering","sorting","ascending","descending","升序排列","排序"],"sort-desc":["ranking","ordering","降序排列","排序"],"bring-forward":["arrange","层级","向上一层"],"send-backward":["arrange","层级","向下一层"],"bring-to-front":["arrange","层级","移到最前面"],"send-to-back":["arrange","层级","移到最后面"],"h-1":["heading","一级标题"],"h-2":["heading","一级标题"],"h-3":["heading","一级标题"],"h-4":["heading","一级标题"],"h-5":["heading","一级标题"],"h-6":["heading","一级标题"],"insert-column-left":["添加列","左侧"],"insert-column-right":["添加列","右侧"],"insert-row-top":["添加行","顶部"],"insert-row-bottom":["添加行","底部"],"delete-column":["删除列","底部"],"delete-row":["添加行","底部"],"merge-cells-horizontal":["合并单元格"],"merge-cells-vertical":["合并单元格"],"split-cells-horizontal":["拆分单元格"],"split-cells-vertical":["拆分单元格"],"flow-chart":["流程图"],"mind-map":["mindmap","脑图","思维导图"],"node-tree":["节点","层级关系图"],"organization-chart":["组织架构图","局域网"],"rounded-corner":["圆角"]},"Finance":{"wallet":["钱包","卡包"],"wallet-2":["钱包","卡包"],"wallet-3":["钱包","卡包"],"bank-card":["credit","purchase","payment","cc","银行卡","信用卡","购买","消费","支付"],"bank-card-2":["credit","purchase","payment","cc","银行卡","信用卡","购买","消费","支付"],"secure-payment":["credit","purchase","payment","cc","银行卡","信用卡","购买","消费","支付","安全"],"refund":["credit card","repayment","cc","银行卡","信用卡还款"],"refund-2":["credit card","repayment","cc","银行卡","信用卡还款"],"safe":["保险柜","保险箱"],"safe-2":["保险柜","保险箱"],"price-tag":["label","标签","价签"],"price-tag-2":["label","标签","价签"],"price-tag-3":["label","标签","价签"],"ticket":["coupon","票","优惠券","代金券"],"ticket-2":["coupon","票","优惠券","代金券"],"coupon":["ticket","票","优惠券","代金券"],"coupon-2":["ticket","票","优惠券","代金券"],"coupon-3":["ticket","票","优惠券","代金券"],"coupon-4":["优惠券","代金券"],"coupon-5":["优惠券","代金券"],"shopping-bag":["purse","购物袋","购买","消费","商城"],"shopping-bag-2":["购物袋","购买","消费","商城"],"shopping-bag-3":["购物袋","购买","消费","商城"],"shopping-basket":["购物篮","购买","消费","商城"],"shopping-basket-2":["购物篮","购买","消费","商城"],"shopping-cart":["购物车","购买","消费","商城"],"shopping-cart-2":["购物车","购买","消费","商城"],"vip":["会员"],"vip-crown":["king","queen","皇冠","会员","国王","女王","王后"],"vip-crown-2":["king","queen","皇冠","会员","国王","女王","王后"],"vip-diamond":["钻石","会员"],"trophy":["奖品","奖杯","金杯"],"exchange":["swap","交换","换算","兑换"],"exchange-box":["swap","交换","换算","兑换"],"swap":["exchange","交换","换算","兑换"],"swap-box":["exchange","交换","换算","兑换"],"exchange-dollar":["swap","transfer","交换","换算","兑换","美元","转账"],"exchange-cny":["swap","transfer","交换","换算","兑换","人民币","转账"],"exchange-funds":["swap","transfer","交换","换算","兑换","基金","股票","转账"],"increase-decrease":["计算器"],"percent":["百分之","百分比"],"copper-coin":["currency","payment","铜币","硬币","货币","钱","支付"],"copper-diamond":["currency","coins","金币","钻石","货币","钱","支付"],"money-cny-box":["currency","payment","货币","钱","支付","人民币"],"money-cny-circle":["currency","coins","金币","payment","货币","钱","支付","人民币"],"money-dollar-box":["currency","payment","货币","钱","支付","美元"],"money-dollar-circle":["currency","coins","金币","payment","cent","penny","货币","钱","支付","美元","美分","便士"],"money-euro-box":["currency","payment","货币","钱","支付","欧元"],"money-euro-circle":["currency","coins","金币","payment","货币","钱","支付","欧元"],"money-pound-box":["currency","payment","货币","钱","支付","英镑"],"money-pound-circle":["currency","coins","金币","payment","货币","钱","支付","英镑"],"bit-coin":["currency","payment","货币","钱","比特币"],"coin":["金币","硬币"],"coins":["金币","硬币"],"currency":["cash","payment","货币","钱"],"funds":["foundation","stock","基金","股票"],"funds-box":["foundation","stock","基金","股票"],"red-packet":["红包"],"water-flash":["水电费"],"stock":["股票"],"auction":["hammer","拍卖","锤子"],"gift":["present","礼物"],"gift-2":["present","礼物"],"hand-coin":["donate","business","捐赠"],"hand-heart":["help","donate","volunteer","welfare","帮助","爱心","捐赠","志愿者","公益"],"24-hours":["last","24小时营业"]},"Health & Medical":{"heart":["like","love","favorite","心","喜欢","爱","收藏"],"heart-2":["like","love","favorite","心","喜欢","爱","收藏"],"heart-3":["like","love","favorite","心","喜欢","爱","收藏"],"heart-add":["like","love","favorite","心","喜欢","爱","收藏"],"dislike":["like","love","remove favorite","心","不喜欢","取消收藏"],"hearts":["romance","爱情","浪漫","心"],"heart-pulse":["heart rate","脉搏","心率"],"pulse":["wave","heart rate","脉搏","心率","波"],"empathize":["care","heart","同理心","关爱","心"],"nurse":["doctors","医生","护士"],"dossier":["病例"],"health-book":["健康手册","医疗手册"],"first-aid-kit":["case","急救箱"],"capsule":["medicine"," 胶囊","药"],"medicine-bottle":["药瓶"],"flask":["testing","experimental","experiment","烧瓶","实验","试验"],"test-tube":["testing","experimental","experiment","试管","实验","试验"],"microscope":["testing","experimental","experiment","显微镜","实验","试验"],"hand-sanitizer":["alcohol","酒精消毒洗手液"],"mental-health":["心理健康"],"psychotherapy":["心理治疗"],"stethoscope":["听诊器"],"syringe":["注射器"],"thermometer":["体温计"],"infrared-thermometer":["红外线体温计","体温枪"],"surgical-mask":["外科医用口罩"],"virus":["病毒"],"lungs":["肺部"],"rest-time":["close","休息时间","打烊"],"zzz":["睡觉"]},"Logos":{"alipay":["zhifubao","支付宝"],"amazon":["亚马逊"],"android":["applications","安卓","应用"],"angularjs":["angular","programing framework"],"app-store":["applications","苹果应用商店"],"apple":["苹果"],"baidu":["du","claw","百度","爪"],"behance":["behance"],"bilibili":["哔哩哔哩"],"centos":["linux","system","系统"],"chrome":["谷歌浏览器"],"codepen":["代码笔"],"coreos":["linux","system","系统"],"dingding":["钉钉"],"discord":["game","chat"],"disqus":["comments"],"douban":["豆瓣"],"dribbble":["追波"],"drive":["google drive","谷歌云端硬盘"],"dropbox":["多宝箱"],"edge":["microsoft edge","edge浏览器"],"evernote":["印象笔记"],"facebook":["脸书"],"facebook-circle":["脸书"],"facebook-box":["脸书"],"finder":["macintosh","仿达"],"firefox":["火狐浏览器"],"flutter":["google"],"gatsby":["gatsby"],"github":["github"],"gitlab":["gitlab"],"google":["谷歌"],"google-play":["applications","谷歌应用商店"],"honor-of-kings":["game","王者荣耀"],"ie":["internet explorer","浏览器"],"instagram":["照片墙"],"invision":["invision"],"kakao-talk":["kakao talk","chat"],"line":["连我"],"linkedin":["领英"],"linkedin-box":["领英"],"mastercard":["bank card","银行卡"],"mastodon":["mastodon","长毛象"],"medium":["媒体"],"messenger":["facebook","脸书","信使"],"microsoft":["windows","窗户","微软"],"mini-program":["微信小程序"],"netease-cloud-music":["netease cloud music","网易云音乐"],"netflix":["网飞"],"npmjs":["npm","nodejs"],"open-source":["opensource","开源"],"opera":["欧朋浏览器"],"patreon":["donate","money","捐赠","打赏"],"paypal":["贝宝"],"pinterest":["拼趣"],"pixelfed":["photography","pixelfed"],"playstation":["ps"],"product-hunt":["product hunt"],"qq":["penguin","tencent","腾讯","企鹅"],"reactjs":["react","programing framework","facebook"],"reddit":["reddit"],"remixicon":["remix icon","图标"],"safari":["safari浏览器"],"skype":["skype"],"slack":["slack"],"snapchat":["ghost","色拉布","幽灵"],"soundcloud":["声云"],"spectrum":["spectrum"],"spotify":["music","音乐"],"stack-overflow":["stack overflow"],"stackshare":["share","分享","技术栈"],"steam":["game","store"],"switch":["nintendo","任天堂"],"taobao":["淘宝"],"telegram":["telegram"],"trello":["trello"],"tumblr":["汤博乐"],"twitch":["twitch"],"twitter":["推特"],"ubuntu":["linux","system","系统"],"unsplash":["photos"],"vimeo":["视频"],"visa":["bank card","银行卡"],"vuejs":["vue","programing framework"],"wechat":["微信"],"wechat-2":["微信"],"wechat-pay":["微信支付"],"weibo":["新浪微博"],"whatsapp":["瓦次艾普"],"windows":["microsoft","窗户","微软"],"xbox":["xbox"],"xing":["xing"],"youtube":["优兔","油管"],"zcool":["zcool","站酷"],"zhihu":["知乎"]},"Map":{"map-pin":["location","navigation","地图","坐标","定位","导航","位置"],"map-pin-2":["location","navigation","地图","坐标","定位","导航","位置"],"map-pin-3":["location","navigation","地图","坐标","定位","导航","位置"],"map-pin-4":["location","navigation","地图","坐标","定位","导航","位置"],"map-pin-5":["location","navigation","地图","坐标","定位","导航","位置"],"map-pin-add":["location","navigation","地图","坐标","定位","导航","位置","新增","添加"],"map-pin-range":["location","navigation","地图","坐标","定位","导航","位置","范围"],"map-pin-time":["location","navigation","地图","坐标","定位","导航","位置","时间"],"map-pin-user":["location","navigation","地图","坐标","定位","导航","位置","用户"],"pin-distance":["坐标","距离"],"pushpin":["图钉"],"pushpin-2":["图钉"],"compass":["navigation","safari","direction","discover","指南针","导航","方向","发现","探索"],"compass-2":["navigation","direction","discover","指南针","导航","方向","发现","探索"],"compass-3":["navigation","safari","direction","discover","指南针","导航","方向","发现","探索"],"compass-4":["navigation","direction","discover","指南针","导航","方向","发现","探索"],"compass-discover":["navigation","direction","指南针","导航","方向","发现","探索"],"anchor":["锚"],"china-railway":["中铁","铁路","火车"],"space-ship":["太空飞船"],"rocket":["火箭"],"rocket-2":["space ship","火箭","太空飞船"],"map":["navigation","travel","地图","导航","旅行"],"map-2":["location","navigation","travel","地图","定位","导航","旅行"],"treasure-map":["thriller","adventure","地图","藏宝图"],"road-map":["navigation","travel","地图","导航","旅行"],"earth":["global","union","world","language","地球","全球","联合","世界","语言"],"globe":["earth","地球仪"],"parking":["停车场"],"parking-box":["停车场"],"route":["path","路线"],"guide":["path","指引","路线"],"gas-station":["加气站","加油站"],"charging-pile":["充电桩"],"charging-pile-2":["充电桩"],"car":["汽车"],"car-washing":["汽车","洗车"],"roadster":["car","汽车","跑车"],"taxi":["car","出租车","汽车"],"taxi-wifi":["car","出租车","汽车"],"police-car":["汽车","警车"],"bus":["大巴","巴士"],"bus-2":["大巴","巴士"],"bus-wifi":["大巴","巴士"],"truck":["van","delivery","卡车","货车","运输"],"train":["火车"],"train-wifi":["火车"],"subway":["地铁"],"subway-wifi":["地铁"],"flight-takeoff":["airplane","plane","origin","起飞","出发","始发","起点","飞机"],"flight-land":["airplane","plane","destination","着陆","到达","抵达","终点","飞机"],"plane":["fight","飞机","航班"],"sailboat":["帆船"],"ship":["轮船","航海","海运"],"ship-2":["轮船"],"bike":["自行车"],"e-bike":["take out","takeaway","电动车","外卖"],"e-bike-2":["take out","takeaway","电动车","外卖"],"takeaway":["take out","takeaway","电动车","外卖"],"motorbike":["摩托车"],"caravan":["房车"],"walk":["步行"],"run":["奔跑","跑步"],"riding":["bike","骑行","自行车"],"barricade":["路障"],"footprint":["脚印","足迹"],"traffic-light":["交通","信号灯"],"signal-tower":["base station","antenna","信号塔","基站","天线"],"restaurant":["餐厅","饭店"],"restaurant-2":["餐厅","饭店"],"cup":["tea","coffee","杯子","咖啡","茶"],"goblet":["cup","wine glass","高脚杯","酒杯"],"hotel-bed":["酒店","床"],"navigation":["gps","导航"],"oil":["汽油","机油"],"direction":["right","方向","右转"],"steering":["drive","方向盘","驾车"],"steering-2":["drive","方向盘","驾车"],"lifebuoy":["life ring","救生圈"],"passport":["passports","护照"],"suitcase":["travel","旅行","行李箱"],"suitcase-2":["travel","旅行","行李箱","拉杆箱"],"suitcase-3":["travel","旅行","boarding case","行李箱","拉杆箱","登机箱"],"luggage-deposit":["consignment","行李箱","行李寄存","托运"],"luggage-cart":["行李车"]},"Media":{"image":["picture","photo","图片","照片"],"image-2":["picture","photo","图片","照片"],"image-add":["picture","photo","图片","照片","添加"],"image-edit":["picture","photo","图片","照片","编辑"],"landscape":["picture","image","photo","图片","照片"],"gallery":["picture","image","图片","相册"],"gallery-upload":["picture","image","图片","相册","上传"],"video":["视频"],"movie":["film","video","电影","硬盘","视频"],"movie-2":["film","video","电影","硬盘","视频"],"film":["movie","video","影片","电影","视频"],"clapperboard":["movie","film","场记板","电影"],"vidicon":["video","camera","摄像机","摄影机","视频"],"vidicon-2":["camera","摄像机","摄影机"],"live":["video","camera","摄像机","摄影机","视频","直播"],"video-add":["camera","摄像机","摄影机","视频","添加"],"video-upload":["camera","摄像机","摄影机","视频","上传"],"video-download":["camera","摄像机","摄影机","视频","下载"],"dv":["vidicon","camera","摄像机","摄影机"],"camera":["photo","照相机","拍照","照片"],"camera-off":["photo","slash","照相机","拍照","照片","禁止","关闭"],"camera-2":["photo","照相机","拍照","照片"],"camera-3":["photo","照相机","拍照","照片"],"camera-lens":["aperture","photo","照相机","拍照","照片","朋友圈"],"camera-switch":["照相机","拍照","翻转"],"polaroid":["camera","相机","宝丽来"],"polaroid-2":["camera","相机","宝丽来"],"phone-camera":["手机相机","手机摄像头"],"webcam":["摄像头"],"mv":["music video","音乐"],"music":["音乐"],"music-2":["音乐"],"disc":["music","album","音乐","唱片"],"album":["music","唱片","音乐"],"dvd":["cd","dvd","record","光盘","刻录"],"headphone":["music","headset","耳机","音乐"],"radio":["收音机","电台"],"radio-2":["收音机","电台"],"tape":["录音","磁带"],"mic":["record","voice","话筒","语音","录音","声音"],"mic-2":["record","voice","话筒","语音","录音","声音"],"mic-off":["record","voice","slash","关闭话筒","关闭语音","录音","关闭声音","静音","禁止"],"volume-down":["trumpet","sound","speaker","音量低","喇叭","声音","扬声器"],"volume-mute":["trumpet","sound","off","音量低","喇叭","声音","静音"],"volume-up":["trumpet","sound","speaker","音量高","喇叭","声音","扬声器"],"volume-vibrate":["trumpet","sound","speaker","喇叭","声音","扬声器","震动模式"],"volume-off-vibrate":["trumpet","sound","speaker","静音","喇叭","声音","扬声器","静音模式"],"speaker":["音响"],"speaker-2":["音响"],"speaker-3":["音响"],"surround-sound":["环绕立体声"],"broadcast":["广播"],"notification":["bell","alarm","通知","铃铛","提醒"],"notification-2":["bell","alarm","通知","铃铛","提醒"],"notification-3":["bell","alarm","通知","铃铛","提醒"],"notification-4":["bell","alarm","通知","铃铛","提醒"],"notification-off":["bell","alarm","silent","slash","通知","铃铛","提醒","免打扰","静音","关闭","禁止"],"play-circle":["start","播放","开始"],"pause-circle":["暂停"],"record-circle":["录音"],"stop-circle":["停止"],"eject":["推出"],"play":["start","播放","开始"],"pause":["暂停"],"stop":["停止"],"rewind":["fast","快退"],"speed":["fast","快进"],"skip-back":["上一曲"],"skip-forward":["下一曲"],"play-mini":["播放"],"pause-mini":["暂停"],"stop-mini":["停止"],"rewind-mini":["fast","快退"],"speed-mini":["fast","快进"],"skip-back-mini":["上一曲"],"skip-forward-mini":["下一曲"],"repeat":["循环播放"],"repeat-2":["循环播放"],"repeat-one":["单曲循环"],"order-play":["顺序播放"],"shuffle":["随机播放"],"play-list":["播放列表"],"play-list-2":["播放列表"],"play-list-add":["列表","添加"],"fullscreen":["maximize","全屏","最大化"],"fullscreen-exit":["minimize","退出全屏","最小化"],"equalizer":["sliders","controls","settings","filter","均衡器","控制器","设置","筛选"],"sound-module":["sliders","controls","settings","filter","均衡器","控制器","设置","筛选"],"rhythm":["节奏","韵律"],"voiceprint":["声纹"],"hq":["high quality","高质量","高品质"],"hd":["high definition","高清晰度"],"4k":["high definition","high quality","高清晰度","高品质","超清"],"closed-captioning":["隐藏字幕"],"aspect-ratio":["宽高比","比例"],"picture-in-picture":["画中画","小窗"],"picture-in-picture-2":["画中画","小窗"],"picture-in-picture-exit":["退出画中画","退出小窗"]},"System":{"apps":["应用"],"apps-2":["应用"],"function":["layout","功能","应用","卡片布局"],"dashboard":["仪表盘"],"menu":["navigation","hamburger","导航","菜单","汉堡包"],"menu-2":["navigation","hamburger","导航","菜单","汉堡包"],"menu-3":["navigation","hamburger","导航","菜单","汉堡包"],"menu-4":["navigation","hamburger","导航","菜单","汉堡包"],"menu-5":["navigation","hamburger","导航","菜单","汉堡包"],"menu-add":["navigation","hamburger","导航","菜单","汉堡包","添加"],"menu-fold":["navigation","hamburger","导航","菜单","汉堡包","展开"],"more":["ellipsis","更多","省略"],"more-2":["ellipsis","更多","省略"],"star":["favorite","like","mark","星星","星标","喜欢"],"star-s":["favorite","like","mark","星星","星标","喜欢","半星"],"star-half":["favorite","like","mark","星星","星标","喜欢"],"star-half-s":["favorite","like","mark","星星","星标","喜欢","半星"],"settings":["edit","gear","preferences","偏好设置","编辑","齿轮"],"settings-2":["edit","gear","preferences","偏好设置","编辑","齿轮"],"settings-3":["edit","gear","preferences","偏好设置","编辑","齿轮"],"settings-4":["edit","gear","preferences","偏好设置","编辑","齿轮"],"settings-5":["edit","gear","preferences","偏好设置","编辑","齿轮"],"settings-6":["edit","gear","preferences","偏好设置","编辑","齿轮"],"list-settings":["列表","设置"],"forbid":["slash","ban","禁止","禁用"],"forbid-2":["slash","ban","禁止","禁用"],"information":["信息"],"error-warning":["alert","警告","错误"],"question":["help","问号","帮助"],"alert":["提醒","警告"],"spam":["alert","垃圾邮件","警告"],"spam-2":["alert","垃圾邮件","警告"],"spam-3":["alert","垃圾邮件","警告"],"checkbox-blank":["复选框","空"],"checkbox":["复选框"],"checkbox-indeterminate":["复选框"],"add-box":["plus","new","复选框","添加","加号","新增"],"checkbox-blank-circle":["复选框","空"],"checkbox-circle":["复选框"],"indeterminate-circle":["slash","ban","复选框","禁"],"add-circle":["plus","new","复选框","添加","加号","新增"],"close-circle":["cancel","remove","delete","empty","x","关闭","取消","移除","删除","清空"],"radio-button":["单选框"],"checkbox-multiple-blank":["复选框","空"],"checkbox-multiple":["复选框","空"],"check":["对勾"],"check-double":["read","done","double-tick","双对勾","已读"],"close":["cancel","remove","delete","empty","x","关闭","取消","移除","删除","清空"],"add":["plus","new","添加","新增","加号"],"subtract":["减"],"divide":["除以"],"arrow-left-up":["corner","左上"],"arrow-up":["箭头","向上"],"arrow-right-up":["corner","右上"],"arrow-right":["forward","箭头","向右"],"arrow-right-down":["corner","右下"],"arrow-down":["箭头","向下"],"arrow-left-down":["corner","箭头","左下"],"arrow-left":["backward","箭头","向左","返回"],"arrow-up-circle":["箭头","向上"],"arrow-right-circle":["forward","箭头","向右"],"arrow-down-circle":["箭头","向下","下载"],"arrow-left-circle":["backward","箭头","向左","返回"],"arrow-up-s":["箭头","向上"],"arrow-down-s":["箭头","向下"],"arrow-right-s":["forward","箭头","向右"],"arrow-left-s":["backward","箭头","向左","返回"],"arrow-drop-up":["箭头","向上"],"arrow-drop-right":["forward","箭头","向右"],"arrow-drop-down":["箭头","向下"],"arrow-drop-left":["backward","箭头","向左","返回"],"arrow-left-right":["exchange","swap","箭头","左右","交换","换算","兑换"],"arrow-up-down":["exchange","swap","箭头","上下","交换","换算","兑换"],"arrow-go-back":["undo","箭头","返回","撤销","撤回"],"arrow-go-forward":["redo","箭头","重做","撤回"],"download":["下载"],"upload":["上传"],"download-2":["下载"],"upload-2":["上传"],"download-cloud":["下载","云"],"download-cloud-2":["下载","云"],"upload-cloud":["上传","云"],"upload-cloud-2":["上传","云"],"login-box":["sign in","登录"],"logout-box":["sign out","登出","注销"],"logout-box-r":["sign out","登出","注销"],"login-circle":["sign in","登录"],"logout-circle":["sign out","登出","注销"],"logout-circle-r":["sign out","登出","注销"],"refresh":["synchronization","reload","restart","spinner","loader","ajax","update","刷新","同步"],"shield":["safety","protect","盾牌","卫士","安全","防御"],"shield-cross":["safety","protect","盾牌","卫士","安全","防御","闪电"],"shield-flash":["safety","protect","盾牌","卫士","安全","防御"],"shield-star":["safety","protect","盾牌","卫士","安全","防御","星星"],"shield-user":["safety","protect","user protected","guarantor","盾牌","卫士","安全","防御","用户"],"shield-keyhole":["safety","protect","guarantor","盾牌","卫士","安全","防御","钥匙孔"],"shield-check":["safety","protect","盾牌","卫士","安全","防御"],"delete-back":["backspace","删除","退格"],"delete-back-2":["backspace","删除","退格"],"delete-bin":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"delete-bin-2":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"delete-bin-3":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"delete-bin-4":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"delete-bin-5":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"delete-bin-6":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"delete-bin-7":["trash","remove","ash-bin","garbage","dustbin","uninstall","卸载","删除","垃圾桶"],"lock":["security","password","锁子","安全","密码"],"lock-2":["security","password","锁子","安全","密码"],"lock-password":["security","锁子","安全","密码"],"lock-unlock":["security","password","锁子","安全","密码"],"eye":["watch","view","眼睛","查看"],"eye-off":["slash","眼睛","不可见","关闭","禁止"],"eye-2":["watch","view","眼睛","查看"],"eye-close":["x","闭眼"],"search":["搜索","放大镜"],"search-2":["搜索","放大镜"],"search-eye":["搜索","放大镜","眼睛"],"zoom-in":["放大","放大镜"],"zoom-out":["缩小","放大镜"],"find-replace":["查找","搜索","替换"],"share":["分享","转发"],"share-box":["分享","转发"],"share-circle":["分享","转发"],"share-forward":["分享","转发"],"share-forward-2":["分享","转发"],"share-forward-box":["分享","转发"],"side-bar":["侧边栏"],"time":["clock","时间","时钟","钟表"],"timer":["chronograph","stopwatch","秒表","计时器"],"timer-2":["chronograph","stopwatch","秒表","计时器"],"timer-flash":["chronograph","stopwatch","秒表","计时器","闪电"],"alarm":["闹钟"],"history":["record","recent","time machine","历史记录","最近"],"thumb-down":["dislike","bad","不喜欢","不好"],"thumb-up":["like","good","喜欢","好"],"alarm-warning":["alert","report","police light","告警","举报","警灯"],"notification-badge":["red dot","通知","小红点"],"toggle":["switch","开关","触发器"],"filter":["filtration","筛选","过滤"],"filter-2":["filtration","筛选","过滤"],"filter-3":["filtration","筛选","过滤"],"filter-off":["filtration","clear-filter","筛选","过滤"],"loader":["loader","spinner","ajax","waiting","delay","加载中","载入中","正在加载"],"loader-2":["loader","spinner","ajax","waiting","delay","加载中","载入中","正在加载"],"loader-3":["loader","spinner","ajax","waiting","delay","加载中","载入中","正在加载"],"loader-4":["loader","spinner","ajax","waiting","delay","加载中","载入中","正在加载"],"loader-5":["loader","spinner","ajax","waiting","delay","加载中","载入中","正在加载"],"external-link":["外链"]},"User&Faces":{"user":["用户"],"user-2":["用户"],"user-3":["用户"],"user-4":["用户"],"user-5":["用户"],"user-6":["用户"],"user-smile":["用户","微笑"],"account-box":["用户","账户"],"account-circle":["用户","账户"],"account-pin-box":["用户","账户"],"account-pin-circle":["用户","账户"],"user-add":["用户","添加","新增"],"user-follow":["关注"],"user-unfollow":["用户","取消关注"],"user-shared":["transfer","用户","我分享的","发送"],"user-shared-2":["transfer","用户","我分享的","发送"],"user-received":["用户","我接收的","收取"],"user-received-2":["用户","我接收的","收取"],"user-location":["用户","定位"],"user-search":["用户","查找"],"user-settings":["admin","用户","设置","管理员"],"user-star":["用户","关注"],"user-heart":["用户","关注"],"admin":["admin","用户","管理员"],"contacts":["联系人"],"group":["team","团队","群组"],"group-2":["team","团队","群组"],"team":["团队","小组","群主"],"user-voice":["用户","录音","演讲"],"emotion":["表情","笑脸"],"emotion-2":["表情","笑脸"],"emotion-happy":["表情","开心"],"emotion-normal":["表情","一般"],"emotion-unhappy":["表情","不开心"],"emotion-laugh":["comedy","happy","表情","大笑","笑脸","开心","喜剧"],"emotion-sad":["drama","tears","悲剧","哭泣","泪"],"skull":["ghost","骷髅","鬼怪"],"skull-2":["ghost","horror","thriller","骷髅","鬼怪","恐惧","恐怖"],"men":["gender","man","male","男人","男性"],"women":["gender","woman","female","女人","女性"],"travesti":["女人","女性"],"genderless":["女人","女性"],"open-arm":["张开双臂"],"body-scan":["gesture recognition","body","扫描身体","体态识别","动作之别","手势识别"],"parent":["patriarch","父母","亲子","家长"],"robot":["mechanic","机器人"],"aliens":["science fiction","ET","外星人","科幻小说"],"bear-smile":["cartoon","anime","cartoon","小熊","微笑","儿童","动画片","卡通","动漫"],"mickey":["cartoon","disney","迪士尼","米老鼠","微笑","儿童","动画片"],"criminal":["horror","thriller","罪犯","犯罪","恐怖"],"ghost":["horror","thriller","鬼怪","恐怖","恐惧"],"ghost-2":["horror","鬼怪","恐怖","恐惧"],"ghost-smile":["鬼怪","笑"],"star-smile":["animation","动画","微笑","星星"],"spy":["incognito mode","detective","secret","间谍","侦探","无痕模式","隐私模式"]},"Weather":{"sun":["light mode","sunny","太阳","白天模式","晴天"],"moon":["dark mode","night","月亮","夜间模式","月牙"],"flashlight":["闪电"],"cloudy":["多云"],"cloudy-2":["多云"],"mist":["雾气","雾霾"],"foggy":["大雾"],"cloud-windy":["风"],"windy":["大风","刮风"],"rainy":["下雨","雨天"],"drizzle":["小雨"],"showers":["中雨"],"heavy-showers":["大雨"],"thunderstorms":["雷暴","雷阵雨"],"hail":["冰雹"],"snowy":["下雪","雪天"],"sun-cloudy":["晴转多云"],"moon-cloudy":["夜间多云"],"tornado":["龙卷风"],"typhoon":["cyclone","tornado","龙卷风","旋风","台风"],"haze":["阴霾","薄雾"],"haze-2":["阴霾","薄雾"],"sun-foggy":["薄雾"],"moon-foggy":["薄雾"],"moon-clear":["夜间模式","夜间无云"],"temp-hot":["temperature","温度","高温","热"],"temp-cold":["temperature","温度","低温","冷"],"celsius":["temperature","温度","摄氏度"],"fahrenheit":["temperature","温度","华氏度"],"fire":["hot","火","热门"],"blaze":["火灾"],"earthquake":["地震"],"flood":["洪水"],"meteor":["流星","陨石"],"rainbow":["彩虹"]},"Others":{"basketball":["sports","运动","篮球"],"bell":["cartoon","anime","doraemon","铃铛","哆啦A梦","卡通","动漫"],"billiards":["sports","运动","台球","8"],"boxing":["sports","运动","拳击"],"cake":["anniversary","蛋糕"],"cake-2":["anniversary","蛋糕"],"cake-3":["蛋糕"],"door-lock":["门锁"],"door-lock-box":["门锁"],"football":["sports","运动","足球"],"game":["pac man","游戏","吃豆人"],"handbag":["fashion","时尚","手提包","女包"],"key":["password","钥匙","密码"],"key-2":["password","钥匙","密码"],"knife":["刀"],"knife-blood":["crime","刀","犯罪","血","杀人"],"lightbulb":["energy","creativity","灯泡","能源"],"lightbulb-flash":["energy","creativity","灯泡","能源","闪电"],"outlet":["插座"],"outlet-2":["插座"],"ping-pong":["sports","table tennis","运动","乒乓球"],"plug":["二脚插头"],"plug-2":["三脚插头"],"reserved":["已预定"],"shirt":["clothes","衬衫","衣服"],"sword":["war","刀剑","战争","战斗","玄幻"],"t-shirt":["skin","theme","T恤","皮肤","主题"],"t-shirt-2":["skin","theme","T恤","皮肤","主题"],"t-shirt-air":["dry","T恤","风干","烘干"],"umbrella":["protect","雨伞","保护"],"character-recognition":["ocr","文字识别"],"voice-recognition":["asr","语音识别"],"leaf":["energy","ecology","树叶","节能","环保","语音识别"],"plant":["植物"],"seedling":["树苗","植物"],"recycle":["recyclable","可回收"],"scales":["balance","称","天平","天秤"],"scales-2":["厨房称"],"scales-3":["balance","称","天平","天秤"],"fridge":["refrigerator","电冰箱"],"wheelchair":["accessbility","轮椅","可访问性","辅助功能"],"cactus":["desertr","仙人掌","沙漠"],"door":["门"],"door-open":["开门"],"door-closed":["关门"]}}')},3781:function(e,t,n){},"395f":function(e,t,n){"use strict";n("7e11")},"3a6c":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-image-load-fail",use:"icon-image-load-fail-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-image-load-fail"><defs><style type="text/css"></style></defs><path d="M776 328m-72 0a72 72 0 1 0 144 0 72 72 0 1 0-144 0Z" p-id="1593" /><path d="M999.904 116.608a32 32 0 0 0-21.952-10.912l-456.192-31.904a31.552 31.552 0 0 0-27.2 11.904l-92.192 114.848a32 32 0 0 0 0.672 40.896l146.144 169.952-147.456 194.656 36.48-173.376a32 32 0 0 0-11.136-31.424L235.616 245.504l79.616-125.696a32 32 0 0 0-29.28-49.024l-240.192 16.768a32 32 0 0 0-29.696 34.176l55.808 798.016a32.064 32.064 0 0 0 34.304 29.696l176.512-13.184c17.632-1.312 30.848-16.672 29.504-34.272s-16.576-31.04-34.304-29.536l-144.448 10.784-6.432-92.512 125.312-12.576a32 32 0 0 0 28.672-35.04 32.16 32.16 0 0 0-35.04-28.672l-123.392 12.416L82.144 149.184l145.152-10.144-60.96 96.224a32 32 0 0 0 6.848 41.952l198.4 161.344-58.752 279.296a30.912 30.912 0 0 0 0.736 14.752 31.68 31.68 0 0 0 1.408 11.04l51.52 154.56a31.968 31.968 0 0 0 27.456 21.76l523.104 47.552a32.064 32.064 0 0 0 34.848-29.632L1007.68 139.84a32.064 32.064 0 0 0-7.776-23.232z m-98.912 630.848l-412.576-39.648a31.52 31.52 0 0 0-34.912 28.768 32 32 0 0 0 28.8 34.912l414.24 39.808-6.272 89.536-469.728-42.72-39.584-118.72 234.816-310.016a31.936 31.936 0 0 0-1.248-40.192L468.896 219.84l65.088-81.056 407.584 28.48-40.576 580.192z" p-id="1594" /></symbol>'});c.a.add(s);t["default"]=s},"3eac":function(e,t,n){"use strict";n("767c")},"3f1d":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-ball-triangle",use:"icon-loading-ball-triangle-usage",viewBox:"0 0 57 57",content:'<symbol viewBox="0 0 57 57" xmlns="http://www.w3.org/2000/svg" stroke="#fff" id="icon-loading-ball-triangle">\r\n    <g fill="none" fill-rule="evenodd">\r\n        <g transform="translate(1 1)" stroke-width="2">\r\n            <circle cx="5" cy="50" r="5">\r\n                <animate attributeName="cy" begin="0s" dur="2.2s" values="50;5;50;50" calcMode="linear" repeatCount="indefinite"></animate>\r\n                <animate attributeName="cx" begin="0s" dur="2.2s" values="5;27;49;5" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="27" cy="5" r="5">\r\n                <animate attributeName="cy" begin="0s" dur="2.2s" from="5" to="5" values="5;50;50;5" calcMode="linear" repeatCount="indefinite"></animate>\r\n                <animate attributeName="cx" begin="0s" dur="2.2s" from="27" to="27" values="27;49;5;27" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n            <circle cx="49" cy="50" r="5">\r\n                <animate attributeName="cy" begin="0s" dur="2.2s" values="50;50;5;50" calcMode="linear" repeatCount="indefinite"></animate>\r\n                <animate attributeName="cx" from="49" to="49" begin="0s" dur="2.2s" values="49;5;27;49" calcMode="linear" repeatCount="indefinite"></animate>\r\n            </circle>\r\n        </g>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},"407b":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"scrolling-list-"+this.key}},[n("div",{staticClass:"scrolling-wrap",style:e.wrapStyle},[n("div",{ref:"scrollList",staticClass:"scrolling-list-body"},e._l(e.data,(function(t,a){return n("div",{key:a,staticClass:"scrolling-list-row flex-row justify-between"},[e._t("item",null,{item:t})],2)})),0)])])},o=[],i=(n("a9e3"),{name:"ScrollingList",props:{data:{type:Array,default:!0},itemHeight:{type:Number,default:1.4},rowNum:{type:Number,default:5}},data:function(){return{scrollInterval:null,currentIndex:0,key:Math.round(1e4*Math.random())}},mounted:function(){var e=this;this.$nextTick((function(){e.startScrolling()}))},watch:{data:function(e,t){this.data=e,this.restartScrolling()}},beforeDestroy:function(){this.stopScrolling()},computed:{wrapStyle:function(){return{height:"".concat(this.rowNum*this.itemHeight,"rem")}}},methods:{startScrolling:function(){var e=this,t=this.$refs.scrollList;0!=t.length?this.data.length<=this.rowNum?console.log("数据未超过显示区域，不进行滚动"):this.scrollInterval=setInterval((function(){e.currentIndex++,e.data.length-e.rowNum+1==e.currentIndex?(e.currentIndex=0,t.style.transition="none",t.style.transform="translateY(0)",requestAnimationFrame((function(){t.style.transition="",t.style.transform="translateY(-".concat(e.itemHeight*e.currentIndex,"rem)")}))):t.style.transform="translateY(-".concat(e.itemHeight*e.currentIndex,"rem)")}),2e3):console.error("list 为空")},stopScrolling:function(){this.scrollInterval&&clearInterval(this.scrollInterval)},restartScrolling:function(){this.stopScrolling(),this.startScrolling()}}}),c=i,s=(n("6aec"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"0d83df70",null);t["default"]=r.exports},4080:function(e,t,n){},4095:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-popover",{attrs:{placement:"bottom",trigger:"hover",width:"250"},on:{show:e.handlePopoverShow}},[n("div",{staticClass:"picker-container",attrs:{slot:"reference"},slot:"reference"},[""!=e.myValue?n("svg-icon",{attrs:{name:e.myValue}}):n("i",{staticClass:"el-icon-plus"})],1),n("el-tabs",{ref:"tabs",model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[n("el-tab-pane",{attrs:{label:"Element Icon",name:"element"}},[n("el-input",{attrs:{size:"mini",placeholder:"请输入搜索关键词","prefix-icon":"el-icon-search",clearable:""},model:{value:e.element.search,callback:function(t){e.$set(e.element,"search",t)},expression:"element.search"}}),n("div",{staticClass:"list-icon"},e._l(e.elementIconCurrentList,(function(t,a){return n("span",{key:a,staticClass:"list-icon-item",on:{click:function(n){return e.choose("el-icon-"+t)}}},[n("svg-icon",{attrs:{name:"el-icon-"+t}})],1)})),0),n("el-pagination",{attrs:{small:"",layout:"prev, pager, next","current-page":e.element.currentPage,"page-size":e.element.pageSize,total:e.elementIconList.length,"pager-count":5},on:{"update:currentPage":function(t){return e.$set(e.element,"currentPage",t)},"update:current-page":function(t){return e.$set(e.element,"currentPage",t)}}})],1),n("el-tab-pane",{attrs:{label:"Remix Icon",name:"remix"}},[n("el-input",{attrs:{size:"mini",placeholder:"请输入搜索关键词","prefix-icon":"el-icon-search",clearable:""},model:{value:e.remix.search,callback:function(t){e.$set(e.remix,"search",t)},expression:"remix.search"}}),n("div",{staticClass:"list-icon"},e._l(e.remixIconCurrentList,(function(t,a){return n("span",{key:a,staticClass:"list-icon-item",on:{click:function(n){return e.choose("Editor"==t.type?"ri-"+t.name:"ri-"+t.name+"-"+e.remix.style)}}},[n("svg-icon",{attrs:{name:"Editor"==t.type?"ri-"+t.name:"ri-"+t.name+"-"+e.remix.style}})],1)})),0),n("div",{staticClass:"style-choose"},[n("el-radio-group",{attrs:{size:"mini"},model:{value:e.remix.style,callback:function(t){e.$set(e.remix,"style",t)},expression:"remix.style"}},[n("el-radio-button",{attrs:{label:"line"}},[e._v("线条")]),n("el-radio-button",{attrs:{label:"fill"}},[e._v("填充")])],1)],1),n("el-pagination",{attrs:{small:"",layout:"prev, pager, next","current-page":e.remix.currentPage,"page-size":e.remix.pageSize,total:e.remixIconList.length,"pager-count":5},on:{"update:currentPage":function(t){return e.$set(e.remix,"currentPage",t)},"update:current-page":function(t){return e.$set(e.remix,"currentPage",t)}}})],1)],1)],1)},o=[],i=(n("ac1f"),n("841c"),n("4de4"),n("fb6a"),n("caad"),n("2532"),n("b0c0"),n("e4c7")),c=n("36ba"),s={name:"IconPicker",props:{value:{type:String,default:""}},data:function(){return{activeTab:"element",element:{search:"",icons:i,pageSize:20,currentPage:1},remix:{search:"",icons:[],style:"line",pageSize:20,currentPage:1},myValue:""}},computed:{elementIconList:function(){var e=this,t=this.element.icons;return""!=this.element.search&&(t=t.filter((function(t){return t.indexOf(e.element.search)>=0}))),t},elementIconCurrentList:function(){return this.elementIconList.slice((this.element.currentPage-1)*this.element.pageSize,(this.element.currentPage-1)*this.element.pageSize+this.element.pageSize)},remixIconList:function(){var e=this,t=this.remix.icons;return""!=this.remix.search&&(t=t.filter((function(t){return t.keyword.includes(e.remix.search)||t.name.indexOf(e.remix.search)>=0}))),t},remixIconCurrentList:function(){return this.remixIconList.slice((this.remix.currentPage-1)*this.remix.pageSize,(this.remix.currentPage-1)*this.remix.pageSize+this.remix.pageSize)}},watch:{value:{handler:function(e){this.myValue=e},immediate:!0},myValue:function(e){this.$emit("input",e)}},mounted:function(){var e=[];for(var t in c)for(var n in c[t])e.push({name:n,type:t,keyword:c[t][n]});this.remix.icons=e},methods:{handlePopoverShow:function(){this.$refs.tabs.calcPaneInstances(!0)},choose:function(e){this.myValue=e}}},r=s,d=(n("11ef"),n("2877")),l=Object(d["a"])(r,a,o,!1,null,"6b8badf7",null);t["default"]=l.exports},4242:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{"vertical-line":"vertical"===e.direction,"horizontal-line":"horizontal"===e.direction},style:e.lineStyle})},o=[],i={name:"LineDivider",props:{direction:{type:String,default:"horizontal"},lineWidth:{type:String,default:"1px"},lineColor:{type:String,default:"#000"},lineLength:{type:String,default:"100%"}},computed:{lineStyle:function(){return{width:"horizontal"===this.direction?this.lineLength:this.lineWidth,height:"vertical"===this.direction?this.lineLength:this.lineWidth,backgroundColor:this.lineColor}}}},c=i,s=(n("bdf9"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"f8584a96",null);t["default"]=r.exports},4260:function(e,t,n){"use strict";n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return s})),n.d(t,"b",(function(){return r}));var a=n("53ca"),o=(n("4d63"),n("ac1f"),n("25f0"),n("4360"));function i(e){var t;if("object"===Object(a["a"])(e))if(Array.isArray(e))for(var n in t=[],e)t.push(i(e[n]));else if(null===e)t=null;else if(e.constructor===RegExp)t=e;else for(var o in t={},e)t[o]=i(e[o]);else t=e;return t}function c(e){return!o["a"].state.settings.openPermission||o["a"].state.user.permissions.some((function(t){return t===e}))}function s(e){var t;return t="string"===typeof e?c(e):e.some((function(e){return c(e)})),t}function r(e){var t=e.every((function(e){return c(e)}));return t}},"427e":function(e,t,n){"use strict";n("5fa5")},"42f0":function(e,t,n){},4360:function(e,t,n){"use strict";n("159b"),n("d3b7"),n("ddb0"),n("fb6a");var a=n("2b0e"),o=n("2f62");a["default"].use(o["a"]);var i={},c=n("e2f4");c.keys().forEach((function(e){i[e.slice(2,-3)]=c(e).default})),t["a"]=new o["a"].Store({modules:i,strict:!1})},"43ee":function(e,t,n){"use strict";(function(e,t){var a=n("2a1d");e.namespace("cti");var o=new a["b"];cti.TimerObj=function(){this.timerRunning=!1,this.second=0,this.minute=0,this.hour=0,this.timeoutObj=null},cti.showTimer=function(e){var n=o.get(e);null!=n&&void 0!=n||(n=new cti.TimerObj),n.second++,60==n.second&&(n.second=0,n.minute+=1),60==n.minute&&(n.minute=0,n.hour+=1),n.timeValue=(n.minute<10?"0":"")+n.minute,n.timeValue+=(n.second<10?":0":":")+n.second,n.hour>0&&(n.timeValue=(n.hour<10?"0":"")+n.hour+":"+n.timeValue),t("#"+e+"Timer").html(n.timeValue),n.timeoutObj=setTimeout("cti.showTimer('"+e+"')",1e3),n.timerRunning=!0,o.put(e,n)},cti.stopTimer=function(e){var n=o.get(e);null!=n&&n.timerRunning&&(n.timerRunning=!1,clearTimeout(n.timeoutObj),"agentState"!=e&&"line1"!=e&&"line2"!=e||(n.second=0,n.minute=0,n.hour=0,o.put(e,n))),n=null,t("#"+e+"Timer").html("")},cti.startTimer=function(e){if("agentState"==e)cti.stopTimer(e),cti.showTimer(e);else{var t=o.get(e);null!=t&&0!=t.timerRunning||(cti.stopTimer(e),cti.showTimer(e))}},cti.Agent=function(){this.state="Logout",this.choosedState="Logout",this.reason=-1,this.workingLineNumber=0,this.thisDN="",this.pstnDN="",this.agentID="",this.thisQueues=new Array},cti.Agent.instance=null,cti.Agent.getInstance=function(){return null==cti.Agent.instance&&(cti.Agent.instance=new cti.Agent),cti.Agent.instance},cti.Agent.prototype.init=function(e,t,n,a){this.thisDN=e,this.pstnDN=t,this.agentID=n,this.thisQueues=a},cti.Agent.prototype.setState=function(e){this.state=e},cti.Agent.prototype.getState=function(){return this.state},cti.Agent.prototype.setChoosedState=function(e){this.choosedState=e},cti.Agent.prototype.getChoosedState=function(){return this.choosedState},cti.Agent.prototype.setReason=function(e){this.reason=e},cti.Agent.prototype.getReason=function(){return this.reason},cti.Agent.prototype.getWorkingLineNumber=function(){return this.workingLineNumber},cti.Agent.prototype.getThisDN=function(){return this.thisDN},cti.Agent.prototype.getPstnDN=function(){return this.pstnDN},cti.Agent.prototype.getAgentID=function(){return this.agentID},cti.Agent.prototype.getThisQueues=function(){return this.thisQueues},cti.Agent.prototype.updateAgentState=function(e,t){this.setState(e),"NotReady"!=e&&this.setReason(t)},cti.Agent.prototype.changeAgentState=function(e,n){"Ready"==e?(0==this.workingLineNumber&&(t("#agentstatDiv").attr("class","agentStat_ready"),cti.updateContent("agentState","就绪"),this.getChoosedState()!=e&&(this.setChoosedState(e),cti.startTimer("agentState"))),this.setReason(-1)):"NotReady"==e?0==this.workingLineNumber&&(t("#agentstatDiv").attr("class","agentStat_notReady"),3==n?cti.updateContent("agentState","示忙"):5==n?cti.updateContent("agentState","休息"):0==n?cti.updateContent("agentState","整理"):(this.setReason(3),cti.updateContent("agentState","示忙")),this.getChoosedState()!=e&&(this.setChoosedState(e),cti.startTimer("agentState")),this.getReason()!=n&&(this.setReason(n),cti.startTimer("agentState"))):"Busy"==e?(this.workingLineNumber+=1,t("#agentstatDiv").attr("class","agentStat_busy"),cti.updateContent("agentState","忙碌"),this.getChoosedState()!=e&&(this.setChoosedState(e),cti.startTimer("agentState"))):"Idle"==e?(this.workingLineNumber-=1,0==this.workingLineNumber&&("Ready"==this.getState()?(t("#agentstatDiv").attr("class","agentStat_ready"),cti.updateContent("agentState","就绪"),this.getChoosedState()!=e&&(this.setChoosedState(e),cti.startTimer("agentState"))):"NotReady"==this.getState()&&(t("#agentstatDiv").attr("class","agentStat_notReady"),3==n?cti.updateContent("agentState","示忙"):5==n?cti.updateContent("agentState","休息"):0==n?cti.updateContent("agentState","整理"):(this.setReason(3),cti.updateContent("agentState","示忙")),this.getChoosedState()!=e&&(this.setChoosedState(e),cti.startTimer("agentState"))))):"Logout"==e&&(t("#agentstatDiv").attr("class","agentStat_offLine"),cti.updateContent("agentState","登出"),this.getChoosedState()!=e&&(this.setChoosedState(e),cti.startTimer("agentState")))},cti.LineData=function(){this.stateImage="",this.lineState="",this.phoneNumber="",this.callType=-1,this.callId="",this.parties=new Array},cti.Line=function(){this.MAX_LINES=2,this.lineId=0,this.lineDatas=new Array(this.MAX_LINES)},cti.Line.instance=null,cti.Line.getInstance=function(){return null==cti.Line.instance&&(cti.Line.instance=new cti.Line),cti.Line.instance},cti.Line.prototype.init=function(){for(var e=0;e<this.MAX_LINES;e++)this.lineDatas[e]=new cti.LineData},cti.Line.prototype.getLineData=function(e){return this.lineDatas[e]},cti.Line.prototype.getFreeLine=function(){for(var e=-1,t=0;t<this.MAX_LINES;t++)if(""==this.lineDatas[t].callId){e=t;break}return e==this.MAX_LINES&&showMessage("没有空闲线路"),e},cti.Line.prototype.getTalkingLine=function(){for(var e=-1,t=0;t<this.MAX_LINES;t++)if("Talking"==this.lineDatas[t].lineState){e=t;break}return e==this.MAX_LINES&&showMessage("没有通话线路"),e},cti.Line.prototype.getLineByCallId=function(e){for(var t=0;t<this.MAX_LINES;t++)if(this.lineDatas[t].callId==e)return t},cti.Line.prototype.getLineKey=function(e){var t="";switch(e){case 0:t="line1";break;case 1:t="line2";break}return t},cti.Line.prototype.setCurrentLineId=function(e){this.lineId=e},cti.Line.prototype.getCurrentLineId=function(){return this.lineId},cti.Line.prototype.stateChange=function(e,n,a,o,i,c){switch(i=c.otherDN,e){case STATUS.IDLE:cti.Agent.getInstance().changeAgentState("Idle"),this.lineDatas[n].lineState="",this.lineDatas[n].phoneNumber="",this.lineDatas[n].callType=-1,this.lineDatas[n].callId="",this.lineDatas[n].stateImage="images/softphone/phone_idle.gif";break;case STATUS.DIALING:cti.Agent.getInstance().changeAgentState("Busy"),this.lineDatas[n].lineState="Dialing",this.lineDatas[n].phoneNumber=null!=i?i:"",this.lineDatas[n].callType=a,this.lineDatas[n].callId=o,this.lineDatas[n].stateImage="images/softphone/phone_busy.gif";break;case STATUS.ESTABLISHED:if(""==o||null==o)break;this.lineDatas[n].parties=new Array,this.lineDatas[n].lineState="Talking",this.lineDatas[n].callType=a,this.lineDatas[n].callId=o,this.lineDatas[n].parties.push(i),this.lineDatas[n].phoneNumber=null!=i&&"Unknown"!=i?i:"",c.otherRole==PARTYROLE.OBSERVABLE&&(this.lineDatas[n].lineState="Monitoring");break;case STATUS.RINGING:cti.Agent.getInstance().changeAgentState("Busy"),this.lineDatas[n].lineState="Ringing",this.lineDatas[n].callType=a,this.lineDatas[n].callId=o,this.lineDatas[n].phoneNumber=null!=i&&"Unknown"!=i?i:"",this.lineDatas[n].stateImage="images/softphone/phone_busy.gif";break;case STATUS.HELD:this.lineDatas[n].lineState="Hold";break;case STATUS.RETRIEVED:this.lineDatas[n].lineState="Talking";break;default:break}n==this.getCurrentLineId()&&(t("#phoneNumber").attr("value",this.lineDatas[n].phoneNumber),t("#lineState").html(this.lineDatas[n].lineState),t("#stateImage").attr("src",this.lineDatas[n].stateImage))},cti.Line.prototype.isPhoneIdle=function(){for(var e=!0,t=0;t<this.MAX_LINES;t++)if(""!=this.lineDatas[t].lineState){e=!1;break}return e},cti.updateContent=function(e,n){t("#"+e).html(n)},cti.indexOf=function(e,t){if(e.length<1)return-1;for(var n=0;n<e.length;n++)if(e[n]==t)return n},t(document).ready((function(){cti.Line.getInstance().init()}))}).call(this,n("1157"),n("1157"))},"45c5":function(e,t,n){"use strict";n("9b62")},"491a":function(e,t,n){"use strict";n("c50b")},"4a62":function(e,t,n){},"4afc":function(e,t,n){"use strict";n("57ea")},"4b51":function(e,t,n){"use strict";(function(e){var t=n("53ca"),a=(n("ac1f"),n("466d"),n("bf19"),n("5319"),n("a4d3"),n("e01a"),n("1276"),n("fb6a"),n("d3b7"),n("25f0"),n("841c"),n("a434"),n("4d63"),n("9674"));window.MozWebSocket&&(window.WebSocket=window.MozWebSocket);var o={WS_OPEN:1,WS_CLOSING:2,WS_CLOSED:3,MSG_WS_NOT_SUPPORTED:"Unfortunately your browser does neither natively support WebSockets\nnor you have the Adobe Flash-PlugIn 10+ installed.\nPlease download the last recent Adobe Flash Player at http://get.adobe.com/flashplayer.",WS_SERVER_SCHEMA:"ws",WS_SERVER_HOST:self.location.hostname?self.location.hostname:"127.0.0.1",WS_SERVER_PORT:8787,WS_SERVER_CONTEXT:"/webSocket",WS_SERVER_URL:"ws://"+(self.location.hostname?self.location.hostname:"127.0.0.1")+":8787/webSocket",BT_UNKNOWN:0,BT_FIREFOX:1,BT_NETSCAPE:2,BT_OPERA:3,BT_IEXPLORER:4,BT_SAFARI:5,BT_CHROME:6,BROWSER_NAMES:["Unknown","Firefox","Netscape","Opera","Internet Explorer","Safari","Chrome"],$:function(e){return document.getElementById(e)},getServerURL:function(e,t,n,a){var o=e+"://"+t+(n?":"+n:"");return a&&a.length>0&&(o+=a),o},browserSupportsWebSockets:function(){return null!==window.WebSocket&&void 0!==window.WebSocket},browserSupportsNativeWebSockets:function(){return null!==window.WebSocket&&void 0!==window.WebSocket}(),browserSupportsJSON:function(){return null!==window.JSON&&void 0!==window.JSON},browserSupportsNativeJSON:function(){return null!==window.JSON&&void 0!==window.JSON}(),browserSupportsWebWorkers:function(){return null!==window.Worker&&void 0!==window.Worker}(),isIE:function(){var e=navigator.userAgent,t=e.indexOf("MSIE");return t>=0}(),getBrowserName:function(){return this.fBrowserName},getBrowserVersion:function(){return this.fBrowserVerNo},getBrowserVersionString:function(){return this.fBrowserVerStr},isFirefox:function(){return this.fIsFirefox},isOpera:function(){return this.fIsOpera},isChrome:function(){return this.fIsChrome},isIExplorer:function(){return this.fIsIExplorer},isIE_LE6:function(){return this.isIExplorer()&&this.getBrowserVersion()<7},isIE_LE7:function(){return this.isIExplorer()&&this.getBrowserVersion()<8},isIE_GE8:function(){return this.isIExplorer()&&this.getBrowserVersion()>=8},isSafari:function(){return this.fIsSafari},isNetscape:function(){return this.fIsNetscape},isPocketIE:function(){return this.fIsPocketIE},isConnected:function(){return this.isOpened()},isOpened:function(){return void 0!=o.webSocketBaseClient.prototype.fConn&&null!=o.webSocketBaseClient.prototype.fConn&&o.webSocketBaseClient.prototype.fConn.readyState==o.WS_OPEN}};if(self["ws"]=o,function(){o.fBrowserName="unknown",o.fBrowserType=o.BT_UNKNOWN,o.fBrowserVerNo=void 0,o.fIsIExplorer=!1,o.fIsFirefox=!1,o.fIsChrome=!1;var e,t,n,a,i,c=navigator.userAgent;if(o.fIsChrome=c.indexOf("Chrome")>=0,o.fIsChrome?o.fBrowserType=o.BT_CHROME:(o.fIsFirefox="Netscape"==navigator.appName,o.fIsFirefox?o.fBrowserType=o.BT_FIREFOX:(o.fIsIExplorer="Microsoft Internet Explorer"==navigator.appName,o.fIsIExplorer&&(o.fBrowserType=o.BT_IEXPLORER))),o.fIsIExplorer)o.fBrowserName=o.BROWSER_NAMES[o.BT_IEXPLORER],i=c.match(/MSIE.*/i),i&&(n=i[0].substr(5),e=n.indexOf(";"),o.fBrowserVerStr=e>0?n.substr(0,e):n,o.fBrowserVerNo=parseFloat(o.fBrowserVerStr));else if(o.fIsFirefox){if(o.fBrowserName=o.BROWSER_NAMES[o.BT_FIREFOX],i=c.match(/Firefox\/.*/i),i){n=i[0].substr(8),e=n.indexOf(" "),o.fBrowserVerStr=e>0?n.substring(0,e):n,a=0,t=0;while(t<n.length){if("."==n.charAt(t)&&a++,a>=2)break;t++}n=n.substring(0,t),o.fBrowserVerNo=parseFloat(n)}}else o.fIsChrome&&(o.fBrowserName=o.BROWSER_NAMES[o.BT_CHROME],i=c.match(/Chrome\/.*/i),i&&(n=i[0].substr(7),e=n.indexOf(" "),o.fBrowserVerStr=e>0?n.substr(0,e):n,o.fBrowserVerNo=parseFloat(n)))}(),o.oop={},o.oop.declareClass=function(e,t,n,a){var o=self[e];o||(self[e]={});var i,c=function(){this.create&&this.create.apply(this,arguments)};for(i in o[t]=c,a)c.prototype[i]=a[i];if(null!=n)for(i in n.descendants||(n.descendants=[]),n.descendants.push(c),n.prototype){var s=n.prototype[i];"function"==typeof s&&(c.prototype[i]?c.prototype[i].inherited=s:c.prototype[i]=s,c.prototype[i].superClass=n)}},o.oop.addPlugIn=function(e,t){for(var n in e.fPlugIns||(e.fPlugIns=[]),e.fPlugIns.push(t),t)e.prototype[n]||(e.prototype[n]=t[n]);if(e.descendants)for(var a=0,i=e.descendants.length;a<i;a++)o.oop.addPlugIn(e.descendants[a],t)},o.oop.declareClass("ws","webSocketBaseClient",null,{open:function(e,t){if(t||(t={}),!self.WebSocket)throw new Error("WebSockets not supported by browser");if(o.webSocketBaseClient.prototype.fConn)throw new Error("Already connected");var n=this,i=null;o.webSocketBaseClient.prototype.fConn=new a["a"](e),this.fURL=e,o.webSocketBaseClient.prototype.fConn.onopen=function(e){n.fStatus=o.WS_OPEN,t.OnOpen&&t.OnOpen(e),n.processQueue()},o.webSocketBaseClient.prototype.fConn.onmessage=function(e){n.processToken(e),t.OnMessage&&t.OnMessage(e,i)},o.webSocketBaseClient.prototype.fConn.onclose=function(e){n.fStatus=o.WS_CLOSED,t.OnClose&&t.OnClose(e)}},processQueue:function(){this.sendToken({type:"welcome",thisDN:"",message:""})},processToken:function(t){var n=self["ws"].webSocketClient.fOnWelcome,a=e.parseJSON(t.data);2==a.messageId&&n&&n(a)},sendToken:function(t){o.webSocketBaseClient.prototype.fConn.send(e.toJSON(t))},connect:function(e,t){return this.open(e,t)},isOpened:function(){return void 0!=o.webSocketBaseClient.prototype.fConn&&null!=o.webSocketBaseClient.prototype.fConn&&o.webSocketBaseClient.prototype.fConn.readyState==o.WS_OPEN},getURL:function(){return this.fURL},isConnected:function(){return this.isOpened()},forceClose:function(e){},close:function(e){},disconnect:function(e){return this.close(e)},createDefaultResult:function(){return{code:0,msg:"ok"}},addPlugIn:function(e,t){this.fPlugIns||(this.fPlugIns=[]),this.fPlugIns.push(e),t||(t=e.ID),t&&(e.conn=this)}}),o.oop.declareClass("ws","webSocketClient",o.webSocketBaseClient,{open:function(e,t){var n=this.createDefaultResult();try{t&&t.OnWelcome&&"function"==typeof t.OnWelcome&&(o.webSocketClient.fOnWelcome=t.OnWelcome),o.webSocketBaseClient.prototype.open(e,t)}catch(a){n.code=-1,n.msg="Exception on open: "+a.message}return n},connect:function(e,t){return this.open(e,t)},close:function(e){},disconnect:function(e){return this.close(e)}}),o.SystemClientPlugIn={login:function(e,t,n){var a=this.createDefaultResult();return this.isOpened()?this.sendToken({type:"login",thisDN:e,message:t}):(a.code=-1,a.msg="Not connected."),a},logon:function(e,t,n,a){var o=this.createDefaultResult();if(a||(a={}),this.isOpened())this.login(t,n,a);else{var i=a.OnWelcome,c=this;a.OnWelcome=function(e){i&&i.call(c,e),c.login(t,n,a)},this.open(e,a)}return o},ping:function(){var e=this.createDefaultResult();return this.isOpened()?this.sendToken({type:"ping",thisDN:"",message:""}):(e.code=-1,e.msg="Not connected."),e},startKeepAlive:function(e){if(this.hKeepAlive&&stopKeepAlive(),this.isOpened()){var t=1e4,n=!0;e&&(void 0!=e.interval&&(t=e.interval),void 0!=e.immediate&&(n=e.immediate)),n&&this.ping();var a=this;this.hKeepAlive=setInterval((function(){a.isOpened()?a.ping():a.stopKeepAlive()}),t)}},stopKeepAlive:function(){this.hKeepAlive&&(clearInterval(this.hKeepAlive),this.hKeepAlive=null)}},o.oop.addPlugIn(o.webSocketClient,o.SystemClientPlugIn),!o.browserSupportsNativeWebSockets){var i=function(){var e,n,a,o,c,s,r="undefined",d="object",l="Shockwave Flash",m="ShockwaveFlash.ShockwaveFlash",u="application/x-shockwave-flash",h="SWFObjectExprInst",f="onreadystatechange",p=window,g=document,b=navigator,v=!1,y=[E],w=[],k=[],C=[],S=!1,x=!1,_=!0,I=function(){var e=Object(t["a"])(g.getElementById)!=r&&Object(t["a"])(g.getElementsByTagName)!=r&&Object(t["a"])(g.createElement)!=r,n=b.userAgent.toLowerCase(),a=b.platform.toLowerCase(),o=/win/.test(a||n),i=/mac/.test(a||n),c=!!/webkit/.test(n)&&parseFloat(n.replace(/^.*webkit\/(\d+(\.\d+)?).*$/,"$1")),s=!1,h=[0,0,0],f=null;if(Object(t["a"])(b.plugins)!=r&&Object(t["a"])(b.plugins[l])==d)f=b.plugins[l].description,!f||Object(t["a"])(b.mimeTypes)!=r&&b.mimeTypes[u]&&!b.mimeTypes[u].enabledPlugin||(v=!0,s=!1,f=f.replace(/^.*\s+(\S+\s+\S+$)/,"$1"),h[0]=parseInt(f.replace(/^(.*)\..*$/,"$1"),10),h[1]=parseInt(f.replace(/^.*\.(.*)\s.*$/,"$1"),10),h[2]=/[a-zA-Z]/.test(f)?parseInt(f.replace(/^.*[a-zA-Z]+(.*)$/,"$1"),10):0);else if(Object(t["a"])(p.ActiveXObject)!=r)try{var y=new ActiveXObject(m);y&&(f=y.GetVariable("$version"),f&&(s=!0,f=f.split(" ")[1].split(","),h=[parseInt(f[0],10),parseInt(f[1],10),parseInt(f[2],10)]))}catch(w){}return{w3:e,pv:h,wk:c,ie:s,win:o,mac:i}}();(function(){I.w3&&((Object(t["a"])(g.readyState)!=r&&"complete"==g.readyState||Object(t["a"])(g.readyState)==r&&(g.getElementsByTagName("body")[0]||g.body))&&D(),S||(Object(t["a"])(g.addEventListener)!=r&&g.addEventListener("DOMContentLoaded",D,!1),I.ie&&I.win&&(g.attachEvent(f,(function(){"complete"==g.readyState&&(g.detachEvent(f,arguments.callee),D())})),p==top&&function(){if(!S){try{g.documentElement.doScroll("left")}catch(e){return void setTimeout(arguments.callee,0)}D()}}()),I.wk&&function(){S||(/loaded|complete/.test(g.readyState)?D():setTimeout(arguments.callee,0))}(),O(D)))})();function D(){if(!S){try{var e=g.getElementsByTagName("body")[0].appendChild(U("span"));e.parentNode.removeChild(e)}catch(a){return}S=!0;for(var t=y.length,n=0;n<t;n++)y[n]()}}function T(e){S?e():y[y.length]=e}function O(e){if(Object(t["a"])(p.addEventListener)!=r)p.addEventListener("load",e,!1);else if(Object(t["a"])(g.addEventListener)!=r)g.addEventListener("load",e,!1);else if(Object(t["a"])(p.attachEvent)!=r)H(p,"onload",e);else if("function"==typeof p.onload){var n=p.onload;p.onload=function(){n(),e()}}else p.onload=e}function E(){v?A():L()}function A(){var e=g.getElementsByTagName("body")[0],n=U(d);n.setAttribute("type",u);var a=e.appendChild(n);if(a){var o=0;(function(){if(Object(t["a"])(a.GetVariable)!=r){var i=a.GetVariable("$version");i&&(i=i.split(" ")[1].split(","),I.pv=[parseInt(i[0],10),parseInt(i[1],10),parseInt(i[2],10)])}else if(o<10)return o++,void setTimeout(arguments.callee,10);e.removeChild(n),a=null,L()})()}else L()}function L(){var e=w.length;if(e>0)for(var n=0;n<e;n++){var a=w[n].id,o=w[n].callbackFn,i={success:!1,id:a};if(I.pv[0]>0){var c=W(a);if(c)if(!q(w[n].swfVersion)||I.wk&&I.wk<312)if(w[n].expressInstall&&$()){var s={};s.data=w[n].expressInstall,s.width=c.getAttribute("width")||"0",s.height=c.getAttribute("height")||"0",c.getAttribute("class")&&(s.styleclass=c.getAttribute("class")),c.getAttribute("align")&&(s.align=c.getAttribute("align"));for(var d={},l=c.getElementsByTagName("param"),m=l.length,u=0;u<m;u++)"movie"!=l[u].getAttribute("name").toLowerCase()&&(d[l[u].getAttribute("name")]=l[u].getAttribute("value"));M(s,d,a,o)}else B(c),o&&o(i);else G(a,!0),o&&(i.success=!0,i.ref=N(a),o(i))}else if(G(a,!0),o){var h=N(a);h&&Object(t["a"])(h.SetVariable)!=r&&(i.success=!0,i.ref=h),o(i)}}}function N(e){var n=null,a=W(e);if(a&&"OBJECT"==a.nodeName)if(Object(t["a"])(a.SetVariable)!=r)n=a;else{var o=a.getElementsByTagName(d)[0];o&&(n=o)}return n}function $(){return!x&&q("6.0.65")&&(I.win||I.mac)&&!(I.wk&&I.wk<312)}function M(i,c,s,d){x=!0,a=d||null,o={success:!1,id:s};var l=W(s);if(l){"OBJECT"==l.nodeName?(e=R(l),n=null):(e=l,n=s),i.id=h,(Object(t["a"])(i.width)==r||!/%$/.test(i.width)&&parseInt(i.width,10)<310)&&(i.width="310"),(Object(t["a"])(i.height)==r||!/%$/.test(i.height)&&parseInt(i.height,10)<137)&&(i.height="137"),g.title=g.title.slice(0,47)+" - Flash Player Installation";var m=I.ie&&I.win?"ActiveX":"PlugIn",u="MMredirectURL="+p.location.toString().replace(/&/g,"%26")+"&MMplayerType="+m+"&MMdoctitle="+g.title;if(Object(t["a"])(c.flashvars)!=r?c.flashvars+="&"+u:c.flashvars=u,I.ie&&I.win&&4!=l.readyState){var f=U("div");s+="SWFObjectNew",f.setAttribute("id",s),l.parentNode.insertBefore(f,l),l.style.display="none",function(){4==l.readyState?l.parentNode.removeChild(l):setTimeout(arguments.callee,10)}()}P(i,c,s)}}function B(e){if(I.ie&&I.win&&4!=e.readyState){var t=U("div");e.parentNode.insertBefore(t,e),t.parentNode.replaceChild(R(e),t),e.style.display="none",function(){4==e.readyState?e.parentNode.removeChild(e):setTimeout(arguments.callee,10)}()}else e.parentNode.replaceChild(R(e),e)}function R(e){var t=U("div");if(I.win&&I.ie)t.innerHTML=e.innerHTML;else{var n=e.getElementsByTagName(d)[0];if(n){var a=n.childNodes;if(a)for(var o=a.length,i=0;i<o;i++)1==a[i].nodeType&&"PARAM"==a[i].nodeName||8==a[i].nodeType||t.appendChild(a[i].cloneNode(!0))}}return t}function P(e,n,a){var o,i=W(a);if(I.wk&&I.wk<312)return o;if(i)if(Object(t["a"])(e.id)==r&&(e.id=a),I.ie&&I.win){var c="";for(var s in e)e[s]!=Object.prototype[s]&&("data"==s.toLowerCase()?n.movie=e[s]:"styleclass"==s.toLowerCase()?c+=' class="'+e[s]+'"':"classid"!=s.toLowerCase()&&(c+=" "+s+'="'+e[s]+'"'));var l="";for(var m in n)n[m]!=Object.prototype[m]&&(l+='<param name="'+m+'" value="'+n[m]+'" />');i.outerHTML='<object classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000"'+c+">"+l+"</object>",k[k.length]=e.id,o=W(e.id)}else{var h=U(d);for(var f in h.setAttribute("type",u),e)e[f]!=Object.prototype[f]&&("styleclass"==f.toLowerCase()?h.setAttribute("class",e[f]):"classid"!=f.toLowerCase()&&h.setAttribute(f,e[f]));for(var p in n)n[p]!=Object.prototype[p]&&"movie"!=p.toLowerCase()&&j(h,p,n[p]);i.parentNode.replaceChild(h,i),o=h}return o}function j(e,t,n){var a=U("param");a.setAttribute("name",t),a.setAttribute("value",n),e.appendChild(a)}function z(e){var t=W(e);t&&"OBJECT"==t.nodeName&&(I.ie&&I.win?(t.style.display="none",function(){4==t.readyState?F(e):setTimeout(arguments.callee,10)}()):t.parentNode.removeChild(t))}function F(e){var t=W(e);if(t){for(var n in t)"function"==typeof t[n]&&(t[n]=null);t.parentNode.removeChild(t)}}function W(e){var t=null;try{t=g.getElementById(e)}catch(n){}return t}function U(e){return g.createElement(e)}function H(e,t,n){e.attachEvent(t,n),C[C.length]=[e,t,n]}function q(e){var t=I.pv,n=e.split(".");return n[0]=parseInt(n[0],10),n[1]=parseInt(n[1],10)||0,n[2]=parseInt(n[2],10)||0,!!(t[0]>n[0]||t[0]==n[0]&&t[1]>n[1]||t[0]==n[0]&&t[1]==n[1]&&t[2]>=n[2])}function V(e,n,a,o){if(!I.ie||!I.mac){var i=g.getElementsByTagName("head")[0];if(i){var l=a&&"string"==typeof a?a:"screen";if(o&&(c=null,s=null),!c||s!=l){var m=U("style");m.setAttribute("type","text/css"),m.setAttribute("media",l),c=i.appendChild(m),I.ie&&I.win&&Object(t["a"])(g.styleSheets)!=r&&g.styleSheets.length>0&&(c=g.styleSheets[g.styleSheets.length-1]),s=l}I.ie&&I.win?c&&Object(t["a"])(c.addRule)==d&&c.addRule(e,n):c&&Object(t["a"])(g.createTextNode)!=r&&c.appendChild(g.createTextNode(e+" {"+n+"}"))}}}function G(e,t){if(_){var n=t?"visible":"hidden";S&&W(e)?W(e).style.visibility=n:V("#"+e,"visibility:"+n)}}function J(e){var n=/[\\\"<>\.;]/,a=null!=n.exec(e);return a&&("undefined"===typeof encodeURIComponent?"undefined":Object(t["a"])(encodeURIComponent))!=r?encodeURIComponent(e):e}(function(){I.ie&&I.win&&window.attachEvent("onunload",(function(){for(var e=C.length,t=0;t<e;t++)C[t][0].detachEvent(C[t][1],C[t][2]);for(var n=k.length,a=0;a<n;a++)z(k[a]);for(var o in I)I[o]=null;for(var c in I=null,i)i[c]=null;i=null}))})();return{registerObject:function(e,t,n,a){if(I.w3&&e&&t){var o={};o.id=e,o.swfVersion=t,o.expressInstall=n,o.callbackFn=a,w[w.length]=o,G(e,!1)}else a&&a({success:!1,id:e})},getObjectById:function(e){if(I.w3)return N(e)},embedSWF:function(e,n,a,o,i,c,s,l,m,u){var h={success:!1,id:n};I.w3&&!(I.wk&&I.wk<312)&&e&&n&&a&&o&&i?(G(n,!1),T((function(){a+="",o+="";var f={};if(m&&Object(t["a"])(m)===d)for(var p in m)f[p]=m[p];f.data=e,f.width=a,f.height=o;var g={};if(l&&Object(t["a"])(l)===d)for(var b in l)g[b]=l[b];if(s&&Object(t["a"])(s)===d)for(var v in s)Object(t["a"])(g.flashvars)!=r?g.flashvars+="&"+v+"="+s[v]:g.flashvars=v+"="+s[v];if(q(i)){var y=P(f,g,n);f.id==n&&G(n,!0),h.success=!0,h.ref=y}else{if(c&&$())return f.data=c,void M(f,g,n,u);G(n,!0)}u&&u(h)}))):u&&u(h)},switchOffAutoHideShow:function(){_=!1},ua:I,getFlashPlayerVersion:function(){return{major:I.pv[0],minor:I.pv[1],release:I.pv[2]}},hasFlashPlayerVersion:q,createSWF:function(e,t,n){return I.w3?P(e,t,n):void 0},showExpressInstall:function(e,t,n,a){I.w3&&$()&&M(e,t,n,a)},removeSWF:function(e){I.w3&&z(e)},createCSS:function(e,t,n,a){I.w3&&V(e,t,n,a)},addDomLoadEvent:T,addLoadEvent:O,getQueryParamValue:function(e){var t=g.location.search||g.location.hash;if(t){if(/\?/.test(t)&&(t=t.split("?")[1]),null==e)return J(t);for(var n=t.split("&"),a=0;a<n.length;a++)if(n[a].substring(0,n[a].indexOf("="))==e)return J(n[a].substring(n[a].indexOf("=")+1))}return""},expressInstallCallback:function(){if(x){var t=W(h);t&&e&&(t.parentNode.replaceChild(e,t),n&&(G(n,!0),I.ie&&I.win&&(e.style.display="block")),a&&a(o)),x=!1}}}}();i.hasFlashPlayerVersion("10.0.0")?(WEB_SOCKET_DEBUG=!0,function(){for(var e=document.getElementsByTagName("script"),t=0,n=e.length;t<n;t++){var a=e[t],i=a.src;if(i||(i=a.getAttribute("src")),i){var c=i.lastIndexOf("websocket.js");if(c>0){window.WEB_SOCKET_SWF_LOCATION=i.substr(0,c)+"flash-bridge/WebSocketMain.swf",o.WS_FLASHBRIDGE=window.WEB_SOCKET_SWF_LOCATION;break}}}}(),window.WEB_SOCKET_SWF_LOCATION&&function(){if(window.WEB_SOCKET_FORCE_FLASH);else{if(window.WebSocket)return;if(window.MozWebSocket)return void(window.WebSocket=MozWebSocket)}var e;e=window.WEB_SOCKET_LOGGER?WEB_SOCKET_LOGGER:window.console&&window.console.log&&window.console.error?window.console:{log:function(){},error:function(){}},i.getFlashPlayerVersion().major<10?e.error("Flash Player >= 10.0.0 is required."):("file:"==location.protocol&&e.error("WARNING: web-socket-js doesn't work in file:///... URL unless you set Flash Security Settings properly. Open the page via Web server i.e. http://..."),window.WebSocket=function(e,t,n,a,o){var i=this;i.__id=WebSocket.__nextId++,WebSocket.__instances[i.__id]=i,i.readyState=WebSocket.CONNECTING,i.bufferedAmount=0,i.__events={},t?"string"==typeof t&&(t=[t]):t=[],i.__createTask=setTimeout((function(){WebSocket.__addTask((function(){i.__createTask=null,WebSocket.__flash.create(i.__id,e,t,n||null,a||0,o||null)}))}),0)},WebSocket.prototype.send=function(e){if(this.readyState==WebSocket.CONNECTING)throw"INVALID_STATE_ERR: Web Socket connection has not been established";var t=WebSocket.__flash.send(this.__id,encodeURIComponent(e));return t<0||(this.bufferedAmount+=t,!1)},WebSocket.prototype.close=function(){if(this.__createTask)return clearTimeout(this.__createTask),this.__createTask=null,void(this.readyState=WebSocket.CLOSED);this.readyState!=WebSocket.CLOSED&&this.readyState!=WebSocket.CLOSING&&(this.readyState=WebSocket.CLOSING,WebSocket.__flash.close(this.__id))},WebSocket.prototype.addEventListener=function(e,t,n){e in this.__events||(this.__events[e]=[]),this.__events[e].push(t)},WebSocket.prototype.removeEventListener=function(e,t,n){if(e in this.__events)for(var a=this.__events[e],o=a.length-1;o>=0;--o)if(a[o]===t){a.splice(o,1);break}},WebSocket.prototype.dispatchEvent=function(e){for(var t=this.__events[e.type]||[],n=0;n<t.length;++n)t[n](e);var a=this["on"+e.type];a&&a.apply(this,[e])},WebSocket.prototype.__handleEvent=function(e){var t;if("readyState"in e&&(this.readyState=e.readyState),"protocol"in e&&(this.protocol=e.protocol),"open"==e.type||"error"==e.type)t=this.__createSimpleEvent(e.type);else if("close"==e.type)t=this.__createSimpleEvent("close"),t.wasClean=!!e.wasClean,t.code=e.code,t.reason=e.reason;else{if("message"!=e.type)throw"unknown event type: "+e.type;var n=decodeURIComponent(e.message);t=this.__createMessageEvent("message",n)}this.dispatchEvent(t)},WebSocket.prototype.__createSimpleEvent=function(e){if(document.createEvent&&window.Event){var t=document.createEvent("Event");return t.initEvent(e,!1,!1),t}return{type:e,bubbles:!1,cancelable:!1}},WebSocket.prototype.__createMessageEvent=function(e,t){if(document.createEvent&&window.MessageEvent&&!window.opera){var n=document.createEvent("MessageEvent");return n.initMessageEvent("message",!1,!1,t,null,null,window,null),n}return{type:e,data:t,bubbles:!1,cancelable:!1}},WebSocket.CONNECTING=0,WebSocket.OPEN=1,WebSocket.CLOSING=2,WebSocket.CLOSED=3,WebSocket.__initialized=!1,WebSocket.__flash=null,WebSocket.__instances={},WebSocket.__tasks=[],WebSocket.__nextId=0,WebSocket.loadFlashPolicyFile=function(e){WebSocket.__addTask((function(){WebSocket.__flash.loadManualPolicyFile(e)}))},WebSocket.__initialize=function(){if(!WebSocket.__initialized)if(WebSocket.__initialized=!0,WebSocket.__swfLocation&&(window.WEB_SOCKET_SWF_LOCATION=WebSocket.__swfLocation),window.WEB_SOCKET_SWF_LOCATION){if(!window.WEB_SOCKET_SUPPRESS_CROSS_DOMAIN_SWF_ERROR&&!WEB_SOCKET_SWF_LOCATION.match(/(^|\/)WebSocketMainInsecure\.swf(\?.*)?$/)&&WEB_SOCKET_SWF_LOCATION.match(/^\w+:\/\/([^\/]+)/)){var t=RegExp.$1;location.host!=t&&e.error("[WebSocket] You must host HTML and WebSocketMain.swf in the same host ('"+location.host+"' != '"+t+"'). See also 'How to host HTML file and SWF file in different domains' section in README.md. If you use WebSocketMainInsecure.swf, you can suppress this message by WEB_SOCKET_SUPPRESS_CROSS_DOMAIN_SWF_ERROR = true;")}var n=document.createElement("div");n.id="webSocketContainer",n.style.position="absolute",WebSocket.__isFlashLite()?(n.style.left="0px",n.style.top="0px"):(n.style.left="-100px",n.style.top="-100px");var a=document.createElement("div");a.id="webSocketFlash",n.appendChild(a),document.body.appendChild(n),i.embedSWF(WEB_SOCKET_SWF_LOCATION,"webSocketFlash","1","1","10.0.0",null,null,{hasPriority:!0,swliveconnect:!0,allowScriptAccess:"always"},null,(function(t){t.success||e.error("[WebSocket] swfobject.embedSWF failed")}))}else e.error("[WebSocket] set WEB_SOCKET_SWF_LOCATION to location of WebSocketMain.swf")},WebSocket.__onFlashInitialized=function(){setTimeout((function(){WebSocket.__flash=document.getElementById("webSocketFlash"),WebSocket.__flash.setCallerUrl(location.href),WebSocket.__flash.setDebug(!!window.WEB_SOCKET_DEBUG);for(var e=0;e<WebSocket.__tasks.length;++e)WebSocket.__tasks[e]();WebSocket.__tasks=[]}),0)},WebSocket.__onFlashEvent=function(){return setTimeout((function(){try{for(var t=WebSocket.__flash.receiveEvents(),n=0;n<t.length;++n)WebSocket.__instances[t[n].webSocketId].__handleEvent(t[n])}catch(a){e.error(a)}}),0),!0},WebSocket.__log=function(t){e.log(decodeURIComponent(t))},WebSocket.__error=function(t){e.error(decodeURIComponent(t))},WebSocket.__addTask=function(e){WebSocket.__flash?e():WebSocket.__tasks.push(e)},WebSocket.__isFlashLite=function(){if(!window.navigator||!window.navigator.mimeTypes)return!1;var e=window.navigator.mimeTypes["application/x-shockwave-flash"];return!!(e&&e.enabledPlugin&&e.enabledPlugin.filename)&&!!e.enabledPlugin.filename.match(/flashlite/i)},window.WEB_SOCKET_DISABLE_AUTO_INITIALIZATION||i.addDomLoadEvent((function(){WebSocket.__initialize()})))}()):WebSocket=null}}).call(this,n("1157"))},"4c2b":function(e,t,n){"use strict";n("4080")},5070:function(e,t,n){},"549e":function(e,t,n){"use strict";n.r(t);n("b0c0");var a,o=n("2b0e"),i=n("6436").default,c=o["default"].extend(i),s=function(e){return e=e||{},a=new c({data:e}),a.vm=a.$mount(),a.dom=a.vm.$el,document.body.appendChild(a.dom),a.show=!0,a.vm},r=function(){a.show=!1};t["default"]={install:function(e){e.prototype["$".concat(i.name)]=s,e.prototype["$".concat(i.name)].close=r}}},5531:function(e,t,n){"use strict";n("e9c1")},5546:function(e,t,n){(function(e){var t=n("7037").default;n("ac1f"),n("fb6a"),n("466d"),n("5319"),n("d3b7"),n("ddb0"),n("1276"),n("a15b"),n("841c"),new function(n){var a=n.separator||"&",o=!1!==n.spaces,i=(n.suffix,!1!==n.prefix),c=i?!0===n.hash?"#":"?":"";e.query=new function(){var n=function(e,t){return void 0!=e&&null!==e&&(!t||e.constructor==t)},i=function(e){var t,n=/\[([^[]*)\]/g,a=/^(\S+?)(\[\S*\])?$/.exec(e),o=a[1],i=[];while(t=n.exec(a[2]))i.push(t[1]);return[o,i]},s=function e(a,o,i){var c=o.shift();if("object"!=t(a)&&(a=null),""===c)if(a||(a=[]),n(a,Array))a.push(0==o.length?i:e(null,o.slice(0),i));else if(n(a,Object)){var s=0;while(null!=a[s++]);a[--s]=0==o.length?i:e(a[s],o.slice(0),i)}else a=[],a.push(0==o.length?i:e(null,o.slice(0),i));else if(c&&c.match(/^\s*[0-9]+\s*$/)){var r=parseInt(c,10);a||(a=[]),a[r]=0==o.length?i:e(a[r],o.slice(0),i)}else{if(!c)return i;var d=c.replace(/^\s*|\s*$/g,"");if(a||(a={}),n(a,Array)){for(var l={},m=0;m<a.length;++m)l[m]=a[m];a=l}a[d]=0==o.length?i:e(a[d],o.slice(0),i)}return a},r=function(t){var n=this;return n.keys={},t.queryObject?e.each(t.get(),(function(e,t){n.SET(e,t)})):e.each(arguments,(function(){var t=""+this;t=t.replace(/^[?#]/,""),t=t.replace(/[;&]$/,""),o&&(t=t.replace(/[+]/g," ")),e.each(t.split(/[&;]/),(function(){var e=this.split("=")[0],t=this.split("=")[1];e&&(/^[+-]?[0-9]+\.[0-9]*$/.test(t)?t=parseFloat(t):/^[+-]?[0-9]+$/.test(t)&&(t=parseInt(t,10)),t=!t&&0!==t||t,!1!==t&&!0!==t&&"number"!=typeof t&&(t=decodeURIComponent(t)),n.SET(e,t))}))})),n};return r.prototype={queryObject:!0,has:function(e,t){var a=this.get(e);return n(a,t)},GET:function(e){if(!n(e))return this.keys;var t=i(e),a=t[0],o=t[1],c=this.keys[a];while(null!=c&&0!=o.length)c=c[o.shift()];return c||""},get:function(t){var a=this.GET(t);return n(a,Object)?e.extend(!0,{},a):n(a,Array)?a.slice(0):a},SET:function(e,t){var a=n(t)?t:null,o=i(e),c=o[0],r=o[1],d=this.keys[c];return this.keys[c]=s(d,r.slice(0),a),this},set:function(e,t){return this.copy().SET(e,t)},REMOVE:function(e){return this.SET(e,null).COMPACT()},remove:function(e){return this.copy().REMOVE(e)},EMPTY:function(){var t=this;return e.each(t.keys,(function(e,n){delete t.keys[e]})),t},empty:function(){return this.copy().EMPTY()},copy:function(){return new r(this)},COMPACT:function(){function a(o){var i="object"==t(o)?n(o,Array)?[]:{}:o;if("object"==t(o)){var c=function(e,t,a){n(e,Array)?e.push(a):e[t]=a};e.each(o,(function(e,t){if(!n(t))return!0;c(i,e,a(t))}))}return i}return this.keys=a(this.keys),this},compact:function(){return this.copy().COMPACT()},toString:function(){var o=[],i=[],s=function(e,t,a){if(n(a)&&!1!==a){var o=[t];!0!==a&&(o.push("="),o.push(encodeURIComponent(a))),e.push(o.join(""))}},r=function n(a,o){var c=function(e){return o&&""!=o?[o,"[",e,"]"].join(""):[e].join("")};e.each(a,(function(e,a){"object"==t(a)?n(a,c(e)):s(i,c(e),a)}))};return r(this.keys),i.length>0&&o.push(c),o.push(i.join(a)),o.join("")}},new r(location.search,location.hash)}}(e.query||{})}).call(this,n("1157"))},"56d7":function(e,t,n){"use strict";n.r(t);n("e260"),n("e6cf"),n("cca6"),n("a79d"),n("d81d"),n("d3b7"),n("ddb0");var a=n("2b0e"),o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"app"}},[n("RouterView")],1)},i=[];n("99af"),n("ac1f"),n("5319"),n("1276"),n("b0c0");window.addEventListener("beforeunload",(function(e){window.scroll(0,0)}));var c={provide:function(){return{generateI18nTitle:this.generateI18nTitle}},data:function(){return{path:"",socket:void 0,confirmMsg:void 0}},watch:{$route:{handler:"routeChange",immediate:!0},"$store.state.keepAlive.list":function(e){},"$store.state.settings.mode":{handler:function(){"pc"==this.$store.state.settings.mode?this.$store.commit("settings/updateThemeSetting",{sidebarCollapse:this.$store.state.settings.sidebarCollapseLastStatus}):"mobile"==this.$store.state.settings.mode&&this.$store.commit("settings/updateThemeSetting",{sidebarCollapse:!0}),document.body.setAttribute("data-mode",this.$store.state.settings.mode)},immediate:!0},"$store.state.settings.layout":{handler:function(){document.body.setAttribute("data-layout",this.$store.state.settings.layout)},immediate:!0},"$store.state.settings.theme":{handler:function(){document.body.setAttribute("data-theme",this.$store.state.settings.theme)},immediate:!0},"$store.state.settings.showHeader":{handler:function(){document.body.removeAttribute("data-no-main-sidebar"),(this.$store.state.settings.showHeader||this.$store.state.menu.routes.length<=1&&!this.$store.state.settings.alwaysShowMainSidebar)&&document.body.setAttribute("data-no-main-sidebar","")},immediate:!0},"$store.state.menu.routes":{handler:function(){document.body.removeAttribute("data-no-main-sidebar"),(this.$store.state.settings.showHeader||this.$store.state.menu.routes.length<=1&&!this.$store.state.settings.alwaysShowMainSidebar)&&document.body.setAttribute("data-no-main-sidebar","")},immediate:!0,deep:!0},"$store.state.settings.sidebarCollapse":{handler:function(){document.body.removeAttribute("data-sidebar-no-collapse"),document.body.removeAttribute("data-sidebar-collapse"),this.$store.state.settings.sidebarCollapse?document.body.setAttribute("data-sidebar-collapse",""):document.body.setAttribute("data-sidebar-no-collapse","")},immediate:!0}},beforeDestroy:function(){this.socket&&this.socket.close()},mounted:function(){var e=this;this.path="".concat("wss://boe.rfcare.cn","/bms/order/ws?token=").concat(this.$store.state.user.token),window.onresize=function(){e.$store.commit("settings/setMode",document.body.clientWidth)},window.onresize();var t=this.$route.path;console.log("currentURL:"+window.location.href),console.log("path:"+this.$route.path),"/mp"!==t&&"/"!==t&&"/login"!==t&&this.init()},methods:{generateI18nTitle:function(e,t){var n;return n=this.$te(e)?this.$t(e):t,n},routeChange:function(){this.$route.meta.title&&this.$store.commit("settings/setTitle",this.generateI18nTitle(this.$route.meta.i18n,this.$route.meta.title));var e=this.$route.path;console.log("currentURL:"+window.location.href),console.log("path:"+this.$route.path),"/mp"!==e&&"/"!==e&&"/login"!==e&&this.fetchNotHandleCount()},fetchNotHandleCount:function(){var e=this;console.log("token:"+this.$store.state.user.token),this.$store.state.user.token&&this.$api.get("/bms/event/not-handle/getCount",{}).then((function(t){if("00000"===t.status&&(e.$store.commit("menuBadge/setUnprocessedNumber",t.data||0),e.$route.query.needTip&&t.data>0)){var n=JSON.parse(JSON.stringify(e.$route.query));delete n.needTip,e.$router.replace({query:n}),e.$confirm("您有".concat(t.data||0,"个未处理告警事件?"),"",{confirmButtonText:"我知道了",showCancelButton:!1,type:"error"}).then((function(){}))}}))},init:function(){void 0===WebSocket?alert("您的浏览器不支持socket"):(this.socket=new WebSocket(this.path),this.socket.onopen=this.open,this.socket.onerror=this.error,this.socket.onmessage=this.getMessage,this.socket.onclose=this.close)},open:function(){console.log("socket连接成功")},error:function(e){console.log("连接错误",e)},getMessage:function(e){var t=this;console.log("msg.data",e);var n=e.data;if(n)if(this.$api.get("/bms/event/not-handle/getCount",{}).then((function(e){"00000"===e.status&&t.$store.commit("menuBadge/setUnprocessedNumber",e.data||0)})),-1!=n.indexOf("rt_event_add_happen@")){this.$eventBus.$emit("ServicePlatformUnProcessed-Refresh");var a=n.split("@");this.confirmMsg&&(this.$msgbox.close(),this.confirmMsg=void 0),"servicePlatformEventDetail"===this.$route.name&&(this.confirmMsg=this.$confirm("您有一条新的未处理告警事件?","事件提醒",{confirmButtonText:"立即处理",cancelButtonText:"一会儿处理",type:"error"}).then((function(){t.$router.push({name:"servicePlatformEventDetail",params:{id:a[1]}})})).catch((function(){})))}else if(-1!=n.indexOf("rt_event_update_happen@")){this.$eventBus.$emit("ServicePlatformUnProcessed-Refresh");var o=n.split("@"),i="servicePlatformEventDetail"===this.$route.name&&this.$route.params.id&&this.$route.params.id+""===o[1]+"";console.log(n,n.split("@"),i,this.$route.name,this.$route.params.id,o[1],this.$route.params.id+""===o[1]+""),this.$message({type:"warning",message:i?"当前事件有新的告警更新":"有新的告警",duration:2500}),i&&this.$eventBus.$emit("ServicePlatformEventDetail-HisRefresh")}else-1!=n.indexOf("rt_event_handle_happen@")&&console.log("处理事件, 刷新状态")},close:function(){console.log("socket已经关闭")}},metaInfo:function(){var e=this;return{title:this.$store.state.settings.enableDynamicTitle&&this.$store.state.settings.title,titleTemplate:function(t){var n=t?"".concat(t," - ").concat("BOE 服务管理系统"):"BOE 服务管理系统";if("2"==e.$store.state.user.member.systemVersion)return n?n+" - 高级版":"BOE 服务管理系统 - 高级版"}}}},s=c,r=(n("8c5e"),n("5c64"),n("2877")),d=Object(r["a"])(s,o,i,!1,null,"3425af28",null),l=d.exports,m=n("a18c"),u=n("4360"),h=n("5530"),f=(n("b64b"),n("caad"),n("2532"),n("a925")),p=n("f0d9"),g=n.n(p),b=n("c87b"),v=n.n(b),y=n("b2d6"),w=n.n(y),k={app:{profile:"个人设置",login:"登 录",logout:"退出登录",account:"用户名",password:"密码",captcha:"验证码",sendCaptcha:"发送验证码",newPassword:"新密码",goLogin:"去登录",check:"确 认"},route:{login:"登录",dashboard:"控制台",personal:{setting:"个人设置",editpassword:"修改密码"},i18n:"国际化"}},C={app:{profile:"我的資料",login:"登 錄",logout:"退出登錄",account:"賬號",password:"密碼",captcha:"驗證碼",sendCaptcha:"發送驗證碼",newPassword:"新密碼",goLogin:"去登錄",check:"確 認"},route:{login:"登錄",dashboard:"歡迎頁",personal:{setting:"我的設置",editpassword:"修改密碼"},i18n:"國際化"}},S={app:{profile:"Profile",login:"Login",logout:"Logout",account:"account",password:"password",captcha:"captcha",sendCaptcha:"send captcha",newPassword:"new password",goLogin:"go login",check:"Submit"},route:{login:"Login",dashboard:"Dashboard",personal:{setting:"Personal Setting",editpassword:"Edit Password"},i18n:"I18N"}};a["default"].use(f["a"]);var x={"zh-CN":Object(h["a"])(Object(h["a"])({},g.a),k),"zh-TW":Object(h["a"])(Object(h["a"])({},v.a),C),en:Object(h["a"])(Object(h["a"])({},w.a),S)};if(!u["a"].state.settings.defaultLang){var _=navigator.language||navigator.browserLanguage;Object.keys(x).map((function(e){_.includes(e)&&u["a"].commit("settings/setDefaultLang",e)}))}var I=new f["a"]({silentTranslationWarn:!0,fallbackLocale:"zh-CN",locale:u["a"].state.settings.defaultLang,messages:x}),D=I,T=n("365c"),O=n("5a0c"),E=n.n(O),A=n("4260"),L={install:function(e){e.prototype.$auth=A["a"],e.prototype.$authAll=A["b"],e.directive("auth",{inserted:function(e,t){Object(A["a"])(t.value)||e.remove()}}),e.directive("auth-all",{inserted:function(e,t){Object(A["b"])(t.value)||e.remove()}})}},N=(n("7db0"),{install:function(e){e.prototype.$tabbarClose=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:m["a"].currentRoute.meta.activeMenu||m["a"].currentRoute.fullPath,t=m["a"].currentRoute.meta.activeMenu||m["a"].currentRoute.fullPath;if(e==t&&u["a"].state.tabbar.list.length>1){var n=~~Object.keys(u["a"].state.tabbar.list).find((function(t){return u["a"].state.tabbar.list[t].path==e}));n<u["a"].state.tabbar.list.length-1?this.$router.push(u["a"].state.tabbar.list[n+1].path):this.$router.push(u["a"].state.tabbar.list[n-1].path)}u["a"].dispatch("tabbar/remove",e)}}}),$=n("2b27"),M=n.n($),B=n("58ca"),R=n("5c96"),P=n.n(R),j=(n("0fae"),n("9b6a")),z=n("e5d9"),F=(n("159b"),n("8326"));F.keys().forEach((function(e){var t=F(e).default;/.vue$/.test(e)?a["default"].component(t.name,t):a["default"].use(t)}));n("ab05"),n("8190"),n("7dd0a"),n("be35");u["a"].state.settings.enableErrorLog&&(a["default"].config.errorHandler=function(e,t,n){var a={url:location.href,err:{message:e.message,stack:e.stack},info:n,datetime:t.$dayjs().format("YYYY-MM-DD HH:mm:ss")};a=JSON.stringify(a),sessionStorage.setItem("errorLog",a)});var W=n("bd0c"),U=n.n(W),H=n("4eb5"),q=n.n(H);a["default"].prototype.$api=T["b"],a["default"].prototype.$dayjs=E.a,a["default"].use(L),a["default"].use(N),a["default"].use(M.a),a["default"].use(B["a"]),a["default"].prototype.$ELEMENT=P.a,a["default"].use(P.a,{size:u["a"].state.settings.elementSize,i18n:function(e,t){return D.t(e,t)}}),a["default"].prototype.$hotkeys=j["a"],a["default"].use(z["a"]);var V=n("a244"),G=function(e){return e.keys().map(e)};G(V),a["default"].use(U.a,{ak:"eiGKuUzrdbcGMssUWfbN0UC2tsFQYuzH"}),a["default"].use(q.a),a["default"].config.productionTip=!1;var J=new a["default"]({router:m["a"],store:u["a"],i18n:D,render:function(e){return e(l)}}).$mount("#app");a["default"].prototype.$eventBus=J;t["default"]=J},"576c":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-tail-spin",use:"icon-loading-tail-spin-usage",viewBox:"0 0 38 38",content:'<symbol viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" id="icon-loading-tail-spin">\r\n    <defs>\r\n        <linearGradient x1="8.042%" y1="0%" x2="65.682%" y2="23.865%" id="icon-loading-tail-spin_a">\r\n            <stop stop-color="#fff" stop-opacity="0" offset="0%" />\r\n            <stop stop-color="#fff" stop-opacity=".631" offset="63.146%" />\r\n            <stop stop-color="#fff" offset="100%" />\r\n        </linearGradient>\r\n    </defs>\r\n    <g fill="none" fill-rule="evenodd">\r\n        <g transform="translate(1 1)">\r\n            <path d="M36 18c0-9.94-8.06-18-18-18" id="icon-loading-tail-spin_Oval-2" stroke="url(#icon-loading-tail-spin_a)" stroke-width="2">\r\n                <animateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="0.9s" repeatCount="indefinite"></animateTransform>\r\n            </path>\r\n            <circle fill="#fff" cx="36" cy="18" r="1">\r\n                <animateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="0.9s" repeatCount="indefinite"></animateTransform>\r\n            </circle>\r\n        </g>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},"57ea":function(e,t,n){},5966:function(e,t,n){"use strict";n("f079")},"59c9":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d81d"),n("b0c0"),n("d3b7"),n("ddb0"),n("313e")),c={name:"ChartArea",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=i.init(this.$refs.chart);var e=this.data.series.map((function(e){return{name:e.name,type:"line",smooth:!0,showAllSymbol:!0,symbol:"circle",symbolSize:8,lineStyle:{normal:{color:"#49DBFF",shadowColor:"rgba(0, 0, 0, .3)",shadowBlur:0,shadowOffsetY:5,shadowOffsetX:5}},label:{show:!0,position:"top",textStyle:{color:"#00b3f4"}},itemStyle:{color:"#49DBFF",borderColor:"#fff",borderWidth:1,shadowColor:"#49DBFF",shadowBlur:10},tooltip:{show:!1},data:e.values}})),t={legend:{show:!0,icon:"rect",itemWidth:20,right:30,top:"0%",textStyle:{color:"#fff"},data:this.data.series.map((function(e){return e.name}))},tooltip:{trigger:"axis",axisPointer:{lineStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(0, 255, 233,0)"},{offset:.5,color:"rgba(255, 255, 255,1)"},{offset:1,color:"rgba(0, 255, 233,0)"}],global:!1}}}},grid:{top:"50",left:"60",right:"5%",bottom:"15%"},xAxis:[{type:"category",axisLine:{show:!0,lineStyle:{color:"#49DBFF",width:2}},axisLabel:{margin:10,textStyle:{color:"#49DBFF"}},splitLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.1)"}},boundaryGap:!1,data:["11/09","11/08","11/07","11/06","11/05","11/04"]}],yAxis:[{type:"value",min:0,splitNumber:7,splitLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.1)"}},axisLine:{show:!0,lineStyle:{color:"#26D9FF",width:2}},axisLabel:{show:!0,margin:10,textStyle:{color:"#49DBFF",fontSize:12}},axisTick:{show:!0}}],series:e};this.chart.setOption(t)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},"5a04":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"chip",class:[e.type?"chip--"+e.type:""]},[n("div",{staticClass:"content"},[e._t("default"),e.closable?n("span",{staticClass:"closable",on:{click:e.handleClose}},[n("i",{staticClass:"el-icon-close"})]):e._e()],2)])},o=[],i={name:"Chip",props:{type:{type:String,default:""},closable:{type:Boolean,default:!1}},methods:{handleClose:function(){this.$emit("close")}}},c=i,s=(n("13ff"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"6c8bbf9e",null);t["default"]=r.exports},"5a88":function(e,t,n){(function(e){n("a4d3"),n("e01a"),e.namespace("cti"),cti.handleAgentEvent=function(e,t,n){console.log(e);try{200==e?(cti.UIManager.getInstance().changeButtonWhenReady(),cti.Agent.getInstance().updateAgentState("Ready",t),cti.Agent.getInstance().changeAgentState("Ready")):201==e?(cti.Agent.getInstance().updateAgentState("NotReady",t),cti.Agent.getInstance().changeAgentState("NotReady",t)):202==e&&(cti.UIManager.getInstance().changeButtonWhenLogout(),cti.Agent.getInstance().updateAgentState("Logout",t),cti.Agent.getInstance().changeAgentState("Logout")),2==n&&alert("SIP话机不可用")}catch(a){window.console&&window.console.log&&window.console.log(a.description)}},cti.handleVoiceEvent=function(e,t){var n=-1,a=t.callType,o=t.callID,i=t.otherDN,c=null;t.attachDatas;try{switch(e){case STATUS.DIALING:c=t.creationTime,n=cti.Line.getInstance().getCurrentLineId(),cti.Line.getInstance().stateChange(e,n,a,o,i,t);var s=cti.getLineNameById(n);cti.Line.getInstance().getCurrentLineId()==n?(cti.UIManager.getInstance().changeLineButton(s,"_on","_dialing"),cti.UIManager.getInstance().changeButtonWhenDialing()):cti.UIManager.getInstance().changeLineButton(s,"_off","_dialing");break;case STATUS.RINGING:null==c&&(c=t.creationTime),n=cti.Line.getInstance().getFreeLine(),cti.Line.getInstance().stateChange(e,n,a,o,i,t);s=cti.getLineNameById(n);cti.Line.getInstance().getCurrentLineId()==n?(cti.UIManager.getInstance().changeLineButton(s,"_on","_ringing"),cti.UIManager.getInstance().changeButtonWhenRinging()):cti.UIManager.getInstance().changeLineButton(s,"_off","_ringing");break;case STATUS.ESTABLISHED:case STATUS.RETRIEVED:n=cti.Line.getInstance().getLineByCallId(o),cti.Line.getInstance().stateChange(e,n,a,o,i,t);s=cti.getLineNameById(n);if(cti.Line.getInstance().getCurrentLineId()==n?(cti.UIManager.getInstance().changeLineButton(s,"_on","_talking"),cti.UIManager.getInstance().changeButtonWhenTalking()):cti.UIManager.getInstance().changeLineButton(s,"_off","_talking"),e==STATUS.ESTABLISHED){a!=CALLTYPE.INBOUND&&a!=CALLTYPE.OUTBOUND||cti.agentNotReady(1);var r=cti.Line.getInstance().getLineKey(n);cti.startTimer(r)}break;case STATUS.HELD:n=cti.Line.getInstance().getLineByCallId(o),cti.Line.getInstance().stateChange(e,n,a,o,i,t),cti.Line.getInstance().getCurrentLineId()==n?(cti.UIManager.getInstance().changeLineButton(s,"_on","_hold"),cti.UIManager.getInstance().changeButtonWhenHold()):cti.UIManager.getInstance().changeLineButton(s,"_off","_hold");break;case STATUS.IDLE:n=cti.Line.getInstance().getLineByCallId(o),cti.Line.getInstance().stateChange(e,n,a,o,i,t);s=cti.getLineNameById(n);cti.Line.getInstance().getCurrentLineId()==n?(cti.UIManager.getInstance().changeLineButton(s,"_on","_idle"),cti.UIManager.getInstance().changeButtonWhenIdle()):cti.UIManager.getInstance().changeLineButton(s,"_off","_idle"),a!=CALLTYPE.INBOUND&&a!=CALLTYPE.OUTBOUND||cti.agentNotReady(0);r=cti.Line.getInstance().getLineKey(n);cti.stopTimer(r),auto_save();break;default:break}}catch(d){window.console&&window.console.log&&window.console.log(d.description)}try{t.thisRole}catch(d){window.console&&window.console.log&&window.console.log(d.description)}}}).call(this,n("1157"))},"5ba9":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=n("5530"),c=n("2909"),s=(n("d81d"),n("d3b7"),n("ddb0"),n("313e")),r={name:"ChartRadar",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=s.init(this.$refs.chart);var e=[{itemStyle:{normal:{color:"rgba(19, 173, 255, 1)",borderColor:"rgba(19, 173, 255, 0.4)",borderWidth:1}},areaStyle:{normal:{color:"rgba(19, 173, 255, 0.5)"}},lineStyle:{normal:{color:"rgba(19, 173, 255, 1)",width:2,type:"dashed"}}},{itemStyle:{normal:{color:"rgba(245, 166, 35, 1)",borderColor:"rgba(245, 166, 35, 0.3)",borderWidth:1}},areaStyle:{normal:{color:"rgba(245, 166, 35, 0.4)"}},lineStyle:{normal:{color:"rgba(245, 166, 35, 1)",width:2,type:"dashed"}}},{itemStyle:{normal:{color:"rgba(1, 247, 255, 1)",borderColor:"rgba(1, 247, 255, 0.3)",borderWidth:1}},areaStyle:{normal:{color:"rgba(1, 247, 255, 0.4)"}},lineStyle:{normal:{color:"rgba(1, 247, 255, 1)",width:2,type:"dashed"}}}],t=[],n=[];this.data.seriesData.map((function(a,o){t.push(Math.max.apply(Math,Object(c["a"])(a.values))),n.push(Object(i["a"])(Object(i["a"])({},{type:"radar",symbol:"circle",symbolSize:5,data:[a.values]}),e[o<e.length?o:0]))}));var a=Math.max.apply(Math,t)||100,o={indicator:[{name:"0时",max:a},{name:"1时",max:a},{name:"2时",max:a},{name:"3时",max:a},{name:"4时",max:a},{name:"5时",max:a},{name:"6时",max:a},{name:"7时",max:a},{name:"8时",max:a},{name:"9时",max:a},{name:"10时",max:a},{name:"11时",max:a},{name:"12时",max:a},{name:"13时",max:a},{name:"14时",max:a},{name:"15时",max:a},{name:"16时",max:a},{name:"17时",max:a},{name:"18时",max:a},{name:"19时",max:a},{name:"20时",max:a},{name:"21时",max:a},{name:"22时",max:a},{name:"23时",max:a}]},r={radar:{name:{formatter:"{value}",textStyle:{fontSize:14,color:"#49DBFF"}},center:["50%","50%"],radius:"70%",startAngle:90,splitNumber:4,shape:"circle",splitArea:{areaStyle:{color:["transparent"]}},axisLine:{show:!0,lineStyle:{color:"#336ae4"}},splitLine:{show:!0,lineStyle:{color:"#336ae4"}},indicator:o.indicator},series:n};this.chart.setOption(r)}}},d=r,l=n("2877"),m=Object(l["a"])(d,a,o,!1,null,null,null);t["default"]=m.exports},"5c64":function(e,t,n){"use strict";n("9620")},"5e10":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=n("2909"),c=(n("a9e3"),n("d3b7"),n("ddb0"),n("ac1f"),n("1276"),n("cb29"),n("a15b"),n("b0c0"),n("313e")),s={name:"ChartHorizontalBar",props:{data:{type:Object,default:function(){return{}}},showLineBg:{type:Boolean,default:!1},colors:{type:Array,default:function(){return[]}},colorIndex:{type:Number,default:0},borderRadius:{type:Number,default:4}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=this.chart||c.init(this.$refs.chart);var e=[{name:"数量",type:"bar",zlevel:2,itemStyle:{normal:{barBorderRadius:this.borderRadius,color:new c.graphic.LinearGradient(0,0,1,0,[{offset:0,color:this.colors[0]},{offset:1,color:this.colors[1]}])}},barWidth:14,data:this.data.values}],t=Math.max.apply(Math,Object(i["a"])(this.data.values)),n=[];if(t>10){var a=(t+"").split(""),o=parseInt(a[0]);a.fill(0),a[0]=o;var s=parseInt(a.join(""));t>s&&(a[0]=o+1),n=parseInt(a.join(""))}!0===this.showLineBg&&(e.push({name:"背景",type:"bar",zlevel:1,barGap:"-100%",itemStyle:{normal:{color:"rgba(14, 59, 110, 1)",barBorderRadius:this.borderRadius}},barWidth:14,data:Object(i["a"])(this.data.values).fill(n)}),console.log(e));var r={grid:{left:20,right:20,bottom:10,top:0,containLabel:!0},tooltip:{trigger:"axis",axisPointer:{type:"none"},formatter:function(e){return e[0].name+"<br/><span style='display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:rgba(36,207,233,0.9)'></span>"+e[0].seriesName+" : "+e[0].value+" 次<br/>"}},xAxis:{show:!1,type:"value"},yAxis:[{type:"category",inverse:!0,axisLabel:{show:!0,width:300,textStyle:{color:"#fff"}},splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!1},data:this.data.names},{type:"category",inverse:!0,axisTick:"none",axisLine:"none",show:!0,axisLabel:{textStyle:{color:this.colors[this.colorIndex||0],fontSize:"12"},formatter:function(e){return e}},data:Object(i["a"])(this.data.values)}],series:e};this.chart.setOption(r,!0)}}},r=s,d=n("2877"),l=Object(d["a"])(r,a,o,!1,null,null,null);t["default"]=l.exports},"5e7a":function(e,t,n){"use strict";n("2aaa")},"5fa5":function(e,t,n){},6436:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("transition",{attrs:{name:"spinkit-transition"}},[e.show?n("div",{staticClass:"spinkit-container"},[n("div",{staticClass:"spinkit",style:{"--sk-size":e.size+"px","--sk-color":e.color}},["plane"==e.type?n("div",{staticClass:"sk-plane"}):e._e(),"chase"==e.type?n("div",{staticClass:"sk-chase"},[n("div",{staticClass:"sk-chase-dot"}),n("div",{staticClass:"sk-chase-dot"}),n("div",{staticClass:"sk-chase-dot"}),n("div",{staticClass:"sk-chase-dot"}),n("div",{staticClass:"sk-chase-dot"}),n("div",{staticClass:"sk-chase-dot"})]):e._e(),"bounce"==e.type?n("div",{staticClass:"sk-bounce"},[n("div",{staticClass:"sk-bounce-dot"}),n("div",{staticClass:"sk-bounce-dot"})]):e._e(),"wave"==e.type?n("div",{staticClass:"sk-wave"},[n("div",{staticClass:"sk-wave-rect"}),n("div",{staticClass:"sk-wave-rect"}),n("div",{staticClass:"sk-wave-rect"}),n("div",{staticClass:"sk-wave-rect"}),n("div",{staticClass:"sk-wave-rect"})]):e._e(),"pulse"==e.type?n("div",{staticClass:"sk-pulse"}):e._e(),"flow"==e.type?n("div",{staticClass:"sk-flow"},[n("div",{staticClass:"sk-flow-dot"}),n("div",{staticClass:"sk-flow-dot"}),n("div",{staticClass:"sk-flow-dot"})]):e._e(),"swing"==e.type?n("div",{staticClass:"sk-swing"},[n("div",{staticClass:"sk-swing-dot"}),n("div",{staticClass:"sk-swing-dot"})]):e._e(),"circle"==e.type?n("div",{staticClass:"sk-circle"},[n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"}),n("div",{staticClass:"sk-circle-dot"})]):e._e(),"circle-fade"==e.type?n("div",{staticClass:"sk-circle-fade"},[n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"}),n("div",{staticClass:"sk-circle-fade-dot"})]):e._e(),"grid"==e.type?n("div",{staticClass:"sk-grid"},[n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"}),n("div",{staticClass:"sk-grid-cube"})]):e._e(),"fold"==e.type?n("div",{staticClass:"sk-fold"},[n("div",{staticClass:"sk-fold-cube"}),n("div",{staticClass:"sk-fold-cube"}),n("div",{staticClass:"sk-fold-cube"}),n("div",{staticClass:"sk-fold-cube"})]):e._e(),"wander"==e.type?n("div",{staticClass:"sk-wander"},[n("div",{staticClass:"sk-wander-cube"}),n("div",{staticClass:"sk-wander-cube"}),n("div",{staticClass:"sk-wander-cube"})]):e._e()])]):e._e()])},o=[],i=(n("9adb"),{name:"SpinkitLoading",data:function(){return{show:!1,type:"plane",size:50,color:"#fff"}}}),c=i,s=(n("8ab0"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"07632bf4",null);t["default"]=r.exports},"665e":function(e,t,n){},"686b":function(e,t,n){},6888:function(e,t,n){},6920:function(e,t,n){},"6ae2":function(e,t,n){"use strict";n("18e7")},"6aec":function(e,t,n){"use strict";n("f034")},"6aed":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-user",use:"icon-user-usage",viewBox:"0 0 130 130",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 130 130" id="icon-user"><path d="M63.444 64.996c20.633 0 37.359-14.308 37.359-31.953 0-17.649-16.726-31.952-37.359-31.952-20.631 0-37.36 14.303-37.358 31.952 0 17.645 16.727 31.953 37.359 31.953zM80.57 75.65H49.434c-26.652 0-48.26 18.477-48.26 41.27v2.664c0 9.316 21.608 9.325 48.26 9.325H80.57c26.649 0 48.256-.344 48.256-9.325v-2.663c0-22.794-21.605-41.271-48.256-41.271z" stroke="#979797" /></symbol>'});c.a.add(s);t["default"]=s},"6b0d":function(e,t,n){},"6e4c":function(e,t,n){"use strict";n("7ec8")},"6e89":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-puff",use:"icon-loading-puff-usage",viewBox:"0 0 44 44",content:'<symbol viewBox="0 0 44 44" xmlns="http://www.w3.org/2000/svg" stroke="#fff" id="icon-loading-puff">\r\n    <g fill="none" fill-rule="evenodd" stroke-width="2">\r\n        <circle cx="22" cy="22" r="1">\r\n            <animate attributeName="r" begin="0s" dur="1.8s" values="1; 20" calcMode="spline" keyTimes="0; 1" keySplines="0.165, 0.84, 0.44, 1" repeatCount="indefinite"></animate>\r\n            <animate attributeName="stroke-opacity" begin="0s" dur="1.8s" values="1; 0" calcMode="spline" keyTimes="0; 1" keySplines="0.3, 0.61, 0.355, 1" repeatCount="indefinite"></animate>\r\n        </circle>\r\n        <circle cx="22" cy="22" r="1">\r\n            <animate attributeName="r" begin="-0.9s" dur="1.8s" values="1; 20" calcMode="spline" keyTimes="0; 1" keySplines="0.165, 0.84, 0.44, 1" repeatCount="indefinite"></animate>\r\n            <animate attributeName="stroke-opacity" begin="-0.9s" dur="1.8s" values="1; 0" calcMode="spline" keyTimes="0; 1" keySplines="0.3, 0.61, 0.355, 1" repeatCount="indefinite"></animate>\r\n        </circle>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},"6ece":function(e,t,n){},"6fb5":function(e,t,n){"use strict";n("d3a3")},7146:function(e,t,n){"use strict";(function(e,t){n("498a"),n("bf19");var a=n("2a1d");e.namespace("cti"),cti.agentLogin=function(){var e=cti.Agent.getInstance().getThisDN(),t=cti.Agent.getInstance().getAgentID(),n=cti.Agent.getInstance().getThisQueues(),a={messageId:100,thisDN:e,agentID:t,thisQueues:n};cti.send(a),console.log("登录参数",a)},cti.agentLogout=function(){var e=cti.Agent.getInstance().getThisDN(),t=cti.Agent.getInstance().getAgentID(),n={messageId:103,thisDN:e,agentID:t};cti.send(n)},cti.agentNotReady=function(e){var t=cti.Agent.getInstance().getThisDN(),n=cti.Agent.getInstance().getAgentID(),a={messageId:102,thisDN:t,agentID:n,reasonCode:e};cti.send(a)},cti.agentReady=function(){var e=cti.Agent.getInstance().getThisDN(),t=cti.Agent.getInstance().getAgentID(),n={messageId:101,thisDN:e,agentID:t};cti.send(n)},cti.makeCall=function(e,n,o){if(console.log("呼叫makeCall："+e),Object(a["d"])(e)){e=t.trim(e);var i=cti.Agent.getInstance().getThisDN(),c=Object(a["e"])(i);if(null!=c){var s={messageId:200,thisDN:i,otherDN:e,attachDatas:{id:n,type:o,"cti-tenant-id":c}};console.log(s),cti.send(s)}}},cti.answerCall=function(e){checkLineId(e);var t=cti.Agent.getInstance().getThisDN(),n=cti.Line.getInstance().getLineData(e),a=n.callId,o={messageId:201,thisDN:t,callID:a};cti.send(o)},cti.holdCall=function(e){checkLineId(e);var t=cti.Agent.getInstance().getThisDN(),n=cti.Line.getInstance().getLineData(e),a=n.callId,o={messageId:204,thisDN:t,callID:a};cti.send(o)},cti.retrieveCall=function(e){checkLineId(e);var t=cti.Line.getInstance().getLineData(e);if("Hold"==t.lineState){var n=cti.Agent.getInstance().getThisDN(),a=t.callId,o={messageId:217,thisDN:n,callID:a};cti.send(o)}else showMessage("Line "+(e+1)+" can't be retrieved.")},cti.releaseCall=function(e){checkLineId(e);var t=cti.Agent.getInstance().getThisDN(),n=cti.Line.getInstance().getLineData(e),a=n.callId,o={messageId:203,thisDN:t,callID:a};cti.send(o)},cti.forceReleaseCall=function(){var e=cti.Line.getInstance().getTalkingLine();checkLineId(e);var n=cti.Agent.getInstance().getThisDN(),a=cti.Line.getInstance().getLineData(e),o=a.callId,i=a.callType,c={messageId:515,thisDN:n,callID:o,callType:i};cti.handleVoiceEvent(2,t.toJSON(c))},cti.redirectCall=function(e,n){if(checkLineId(e),Object(a["d"])(n)){n=t.trim(n);var o=cti.Agent.getInstance().getThisDN(),i=cti.Line.getInstance().getLineData(e),c=i.callId,s={messageId:212,thisDN:o,callID:c,otherDN:n};cti.send(s)}},cti.initiateTransfer=function(e,n){if(checkLineId(e),Object(a["d"])(n)){n=t.trim(n);var o=cti.Agent.getInstance().getThisDN(),i=cti.Line.getInstance().getLineData(e),c=i.callId,s={messageId:221,thisDN:o,callID:c,otherDN:n};cti.send(s)}},cti.completeTransfer=function(){var e="",t="",n={messageId:223,thisDN:thisDN,callID:e,consultCallID:t};cti.send(n)},cti.singleStepTransfer=function(e,n){if(checkLineId(e),Object(a["d"])(n)){n=t.trim(n);var o=cti.Agent.getInstance().getThisDN(),i=cti.Line.getInstance().getLineData(e),c=i.callId,s=i.phoneNumber,r={messageId:215,thisDN:o,callID:c,otherDN:n,phoneNumber:s};cti.send(r)}},cti.initiateConference=function(e,t){checkLineId(e);var n=cti.Agent.getInstance().getThisDN(),a=cti.Line.getInstance().getLineData(e),o=a.callId,i={messageId:220,thisDN:n,callID:o,otherDN:t};cti.send(i)},cti.completeConference=function(){var e="",t="",n={messageId:222,thisDN:thisDN,callID:e,consultCallID:t};cti.send(n)},cti.singleStepConference=function(e,n){if(checkLineId(e),Object(a["d"])(n)){n=t.trim(n);var o=cti.Agent.getInstance().getThisDN(),i=cti.Line.getInstance().getLineData(e),c=i.callId,s={messageId:214,thisDN:o,callID:c,otherDN:n};cti.send(s)}},cti.transferToIVR=function(e,t,n){checkLineId(e);var a=cti.Agent.getInstance().getThisDN(),o=cti.Line.getInstance().getLineData(e),i=o.callId,c={messageId:224,thisDN:a,callID:i,ivrContext:t,ivrExtension:n};cti.send(c)},cti.sendDTMF=function(e,t){checkLineId(e);var n=cti.Agent.getInstance().getThisDN(),a=cti.Line.getInstance().getLineData(e),o=a.callId,i={messageId:250,thisDN:n,callID:o,dtmfDigit:t};cti.send(i)},cti.updateUserData=function(e){checkLineId(e);var t=cti.Agent.getInstance().getThisDN(),n=cti.Line.getInstance().getLineData(e),a=n.callId,o="",i={messageId:232,thisDN:t,callID:a,userData:o};cti.send(i)},cti.deleteUserData=function(e,t){checkLineId(e);var n=cti.Agent.getInstance().getThisDN(),a=cti.Line.getInstance().getLineData(e),o=a.callId,i={messageId:231,thisDN:n,callID:o,key:t};cti.send(i)},cti.getUserData=function(){},cti.startAgentsMonitoring=function(e){var t=cti.Agent.getInstance().getThisDN(),n={messageId:266,thisDN:t,agentDNS:e};cti.send(n)},cti.stopAgentsMonitoring=function(e){var t=cti.Agent.getInstance().getThisDN(),n={messageId:267,thisDN:t,agentDNS:e};cti.send(n)},cti.monitorCall=function(e,t){var n=cti.Agent.getInstance().getThisDN(),a={messageId:265,thisDN:n,callID:e,otherDN:t};cti.send(a)},cti.changeParty=function(e,t,n){var a=cti.Line.getInstance().getCurrentLineId(),o=cti.Line.getInstance().getLineData(a),i=cti.Agent.getInstance().getThisDN();if(""!=o.lineState)showMessage("当前状态不能强拆,请先挂断电话！");else{var c={messageId:215,thisDN:t,otherDN:i,callID:e,phoneNumber:n};cti.send(c)}},cti.releaseAgentCall=function(e,t){var n={messageId:203,thisDN:t,callID:e};cti.send(n)},cti.startAgentsMonitoring=function(e){var t=cti.Agent.getInstance().getThisDN(),n={messageId:266,thisDN:t,agentDNS:e};cti.send(n)},cti.stopAgentsMonitoring=function(e){var t=cti.Agent.getInstance().getThisDN(),n={messageId:267,thisDN:t,agentDNS:e};cti.send(n)},cti.startDialing=function(e,t,n){var a=cti.Agent.getInstance().getThisDN(),o={messageId:1001,thisDN:a,tenantId:e,campaignId:t,dialMode:n};cti.send(o)},cti.stopDialing=function(e,t,n){var a=cti.Agent.getInstance().getThisDN(),o={messageId:1002,thisDN:a,tenantId:e,campaignId:t,dialMode:n};cti.send(o)},cti.loadCampaign=function(e,t,n,a,o,i,c){var s=cti.Agent.getInstance().getThisDN(),r={messageId:1014,thisDN:s,tenantId:e,dialMode:t,name:n,agentDNs:a,queue:o,contacts:i,ratio:c};cti.send(r)},cti.searchCampaign=function(e,t){var n=cti.Agent.getInstance().getThisDN(),a={messageId:1007,thisDN:n,tenantId:e,state:t};cti.send(a)},cti.deleteCampaign=function(e,t,n){var a=cti.Agent.getInstance().getThisDN(),o={messageId:1008,thisDN:a,tenantId:e,campaignId:t,state:n};cti.send(o)},cti.unloadCampaign=function(e,t,n){var a=cti.Agent.getInstance().getThisDN(),o={messageId:1003,thisDN:a,tenantId:e,campaignId:t,dialMode:n};cti.send(o)},cti.updateCampaignConfig=function(e,t){var n=cti.Agent.getInstance().getThisDN(),a={messageId:1004,thisDN:n,tenantId:e,campaignId:t};cti.send(a)},cti.customerCampaign=function(e,t,n){var a=cti.Agent.getInstance().getThisDN(),o={messageId:1006,thisDN:a,tenantId:e,ccId:t,state:n};cti.send(o)},cti.updateQueue=function(e,t){var n=cti.Agent.getInstance().getThisDN(),a={messageId:270,thisDN:n,queue:e,method:t};cti.send(a)},cti.getStatisticAgent=function(e,t,n,a,o){var i=cti.Agent.getInstance().getThisDN(),c={messageId:2e3,thisDN:i,tenantId:e,statisticMetric:{filter:o,statisticType:t,timeRange:n,timeRange2:a},statisticObject:{}};cti.send(c)},cti.getStatisticForOutbound=function(e,t,n,a,o){var i=cti.Agent.getInstance().getThisDN(),c={messageId:1011,referenceID:1e3,statisticMetric:{filter:a,statisticType:o,timeProfile:"year",timeRange:t,timeRange2:n},statisticObject:{objectId:"kpi",statisticObjectType:o,tenantName:"'"+e+"'",tenantPassword:""},tenantId:e,campaignId:0,thisDN:i};cti.send(c)},cti.getStatisticHistoryDetail=function(e,t,n,a,o,i,c){var s=cti.Agent.getInstance().getThisDN(),r={messageId:2001,thisDN:s,tenantId:e,statisticMetric:{filter:o,statisticType:t,timeRange:n,timeRange2:a,pageSize:i,currentPage:c},statisticObject:{}};cti.send(r)},cti.campaignLoadByFileName=function(e,t){var n=cti.Agent.getInstance().getThisDN(),a={messageId:1012,thisDN:n,tenantId:e,campaignId:t};cti.send(a)},cti.retrieveCampaign=function(e,t,n){var a=cti.Agent.getInstance().getThisDN(),o={messageId:1013,thisDN:a,tenantId:e,campaignId:t,retrieveCampaignId:n};cti.send(o)},cti.downRecord=function(e){var t=cti.Agent.getInstance().getThisDN(),n=Object(a["e"])(t),o={messageId:3e3,thisDN:t,tenantId:n,attachDatas:e};cti.send(o)},cti.getRecord=function(e){var t=cti.Agent.getInstance().getThisDN(),n=Object(a["e"])(t),o={messageId:3001,thisDN:t,tenantId:n,attachDatas:e};cti.send(o)},cti.getRar=function(e){var t=cti.Agent.getInstance().getThisDN(),n=Object(a["e"])(t),o={messageId:3002,thisDN:t,tenantId:n,attachDatas:e};cti.send(o)}}).call(this,n("1157"),n("1157"))},"727e":function(e,t,n){"use strict";n("089a")},7364:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-eye",use:"icon-eye-usage",viewBox:"0 0 128 64",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 64" id="icon-eye"><path d="M127.072 7.994c1.37-2.208.914-5.152-.914-6.87-2.056-1.717-4.797-1.226-6.396.982-.229.245-25.586 32.382-55.74 32.382-29.24 0-55.74-32.382-55.968-32.627-1.6-1.963-4.57-2.208-6.397-.49C-.17 3.086-.399 6.275 1.2 8.238c.457.736 5.94 7.36 14.62 14.72L4.17 35.96c-1.828 1.963-1.6 5.152.228 6.87.457.98 1.6 1.471 2.742 1.471s2.284-.49 3.198-1.472l12.564-13.983c5.94 4.416 13.021 8.587 20.788 11.53l-4.797 17.418c-.685 2.699.686 5.397 3.198 6.133h1.37c2.057 0 3.884-1.472 4.341-3.68L52.6 42.83c3.655.736 7.538 1.227 11.422 1.227 3.883 0 7.767-.49 11.422-1.227l4.797 17.173c.457 2.208 2.513 3.68 4.34 3.68.457 0 .914 0 1.143-.246 2.513-.736 3.883-3.434 3.198-6.133l-4.797-17.172c7.767-2.944 14.848-7.114 20.788-11.53l12.336 13.738c.913.981 2.056 1.472 3.198 1.472s2.284-.49 3.198-1.472c1.828-1.963 1.828-4.906.228-6.87l-11.65-13.001c9.366-7.36 14.849-14.474 14.849-14.474z" /></symbol>'});c.a.add(s);t["default"]=s},7366:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("bm-overlay",{ref:"eventCustomSelfOverlay",staticClass:"event-custom-self-overlay",class:{sample:!0},style:e.pointColor,attrs:{pane:"labelPane"},on:{draw:e.draw}},[n("div",{staticClass:"infos un-infos"},[n("div",{staticClass:"avatar"},[n("img",{attrs:{src:"/images/event/icon-map-self-avatar.png"}})]),e.content.title?n("div",{staticClass:"event-name"},[n("span",[e._v(e._s(e.content.title)+" - "+e._s(e.content.orderType))])]):n("div",{staticClass:"event-name"},[n("span",[e._v(e._s(e.content.contactName)+" - "+e._s(e.content.orderStausDesc))])]),n("div",{staticClass:"operate-btns"})]),n("div",{staticClass:"arrow-wrap un-arrow-wrap"})])},o=[],i=(n("d81d"),n("b0c0"),n("bd0c")),c={name:"BMapEventSelfDot",components:{BmOverlay:i["BmOverlay"]},props:["content","position","color"],data:function(){return{pointColor:""}},watch:{position:{handler:function(){this.$refs.eventCustomSelfOverlay.reload()},deep:!0}},mounted:function(){this.pointColor=this.color},methods:{draw:function(e){var t=e.el,n=e.BMap,a=e.map,o=this.position,i=o.lng,c=o.lat,s=a.pointToOverlayPixel(new n.Point(i,c));t.style.left=s.x-105+"px",t.style.top=s.y-142+"px"},openCallDialog:function(){if("2"!==this.content.voStatus){var e=JSON.parse(JSON.stringify(this.content));e.contactName=e.name,console.log(e,"older"),this.$store.dispatch("custom/setCallDialogShow",{status:!0,vo:e,type:"older"})}}}},s=c,r=(n("6e4c"),n("184d"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"32ae6cba",null);t["default"]=d.exports},"73ba":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("bm-overlay",{ref:"eventCustomOverlay",class:{sample:!0},style:e.pointColor,attrs:{pane:"labelPane"},on:{draw:e.draw}},[n("div",{staticClass:"infos",class:{"un-infos":!e.isConnected}},[e.keyAuths&&!0===e.keyAuths[e.content.contactId]?n("img",{staticClass:"auth-key",attrs:{src:"/images/event/icon-card-key.png"}}):e._e(),n("div",{staticClass:"name"},[n("span",[e._v(e._s(e.content.contactName)+"("+e._s(e.content.relationTypeDesc)+")")])]),n("div",{staticClass:"msg"},[n("span",[e._v(e._s(e.content.statusDesc))])]),n("div",{staticClass:"operate-btns",class:{"un-operate-btns":!e.isConnected}},[n("el-row",[n("el-col",{staticStyle:{margin:"0","border-right":"1px solid #1275d4"},attrs:{span:13}},[n("img",{attrs:{src:"/images/event/icon-map-sms.png"},on:{click:e.handleOpenSendSms}})]),n("el-col",{staticStyle:{margin:"0"},attrs:{span:11}},[n("img",{attrs:{src:"/images/event/icon-map-edit.png"},on:{click:e.handleOpenResultModify}})])],1)],1)]),n("div",{staticClass:"arrow-wrap",class:{"un-arrow-wrap":!e.isConnected}})])},o=[],i=(n("d81d"),n("bd0c")),c={name:"BMapEventDot",components:{BmOverlay:i["BmOverlay"]},props:["content","position","color","keyAuths","contactMapping"],data:function(){return{pointColor:""}},watch:{position:{handler:function(){this.$refs.eventCustomOverlay.reload()},deep:!0}},computed:{isConnected:function(){return!(!this.content||!this.content.status)&&("1"!==this.content.status&&"2"!==this.content.status)}},mounted:function(){this.pointColor=this.color},methods:{draw:function(e){var t=e.el,n=e.BMap,a=e.map,o=this.position,i=o.lng,c=o.lat,s=a.pointToOverlayPixel(new n.Point(i,c));t.style.left=s.x-95+"px",t.style.top=s.y-123+"px"},openCallDialog:function(){"2"!==this.content.voStatus&&(console.log("this.content",this.content),this.$store.dispatch("custom/setCallDialogShow",{status:!0,vo:this.content,type:"contact"}))},handleOpenSendSms:function(){"2"!==this.content.voStatus?this.content&&this.content.id&&this.$store.dispatch("custom/setSendSmsDialogShow",{status:!0,vo:this.content}):this.$message({type:"error",message:"请先联系后进行操作!"})},handleOpenResultModify:function(){"2"!==this.content.voStatus?this.$store.dispatch("custom/setResultModifyDialogShow",{status:!0,vo:this.content}):this.$message({type:"error",message:"请先联系后进行操作!"})}}},s=c,r=(n("ab7c"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"dfb376ea",null);t["default"]=d.exports},7633:function(e,t,n){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},"767c":function(e,t,n){},"770f":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-example-crown",use:"icon-example-crown-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-example-crown"><defs><style type="text/css"></style></defs><path d="M136.533333 273.066667l669.047467 422.058666c10.6496 6.724267 15.837867 20.48 12.6976 33.5872-3.106133 13.073067-13.824 22.186667-26.077867 22.2208H238.421333c-27.306667 0-50.346667-22.4256-53.725866-52.3264L136.533333 273.066667z" fill="#DCA54F" p-id="1009" /><path d="M834.218667 698.606933c-3.345067 29.9008-26.385067 52.3264-53.691734 52.3264H245.794133c-12.288 0-41.984-9.1136-45.124266-22.186666-3.1744-13.073067 2.048-26.862933 12.6976-33.5872L882.346667 273.066667l-48.128 425.506133z" fill="#DCA54F" p-id="1010" /><path d="M512 170.666667l298.427733 490.5984a61.44 61.44 0 0 1 2.184534 59.323733c-9.489067 18.705067-27.818667 30.344533-47.7184 30.344533H512V170.666667z" fill="#E7B15C" p-id="1011" /><path d="M512 170.666667L196.061867 666.0096a61.44 61.44 0 0 0-2.184534 59.323733c9.489067 18.705067 27.3408 25.6 47.240534 25.6H512V170.666667z" fill="#F2D59C" p-id="1012" /><path d="M459.776 153.326933c0 18.193067 9.966933 34.986667 26.112 44.100267a53.3504 53.3504 0 0 0 52.224 0c16.145067-9.1136 26.112-25.941333 26.112-44.100267C564.224 125.201067 540.842667 102.4 512 102.4s-52.224 22.801067-52.224 50.926933z m391.543467 101.853867c-0.4096 18.432 9.454933 35.669333 25.770666 45.021867 16.315733 9.3184 36.522667 9.3184 52.872534 0 16.315733-9.352533 26.180267-26.589867 25.770666-45.056-0.6144-27.648-23.825067-49.800533-52.224-49.800534-28.3648 0-51.541333 22.152533-52.189866 49.834667z m-783.018667 0c-0.443733 18.432 9.4208 35.669333 25.736533 45.021867 16.315733 9.3184 36.522667 9.3184 52.872534 0 16.315733-9.352533 26.180267-26.589867 25.770666-45.056-0.6144-27.648-23.825067-49.800533-52.224-49.800534-28.3648 0-51.541333 22.152533-52.189866 49.834667z" fill="#E7B15C" p-id="1013" /><path d="M204.8 819.2m34.133333 0l546.133334 0q34.133333 0 34.133333 34.133333l0 0q0 34.133333-34.133333 34.133334l-546.133334 0q-34.133333 0-34.133333-34.133334l0 0q0-34.133333 34.133333-34.133333Z" fill="#DCA54F" p-id="1014" /></symbol>'});c.a.add(s);t["default"]=s},"79df":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-rings",use:"icon-loading-rings-usage",viewBox:"0 0 45 45",content:'<symbol viewBox="0 0 45 45" xmlns="http://www.w3.org/2000/svg" stroke="#fff" id="icon-loading-rings">\r\n    <g fill="none" fill-rule="evenodd" transform="translate(1 1)" stroke-width="2">\r\n        <circle cx="22" cy="22" r="6" stroke-opacity="0">\r\n            <animate attributeName="r" begin="1.5s" dur="3s" values="6;22" calcMode="linear" repeatCount="indefinite"></animate>\r\n            <animate attributeName="stroke-opacity" begin="1.5s" dur="3s" values="1;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            <animate attributeName="stroke-width" begin="1.5s" dur="3s" values="2;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </circle>\r\n        <circle cx="22" cy="22" r="6" stroke-opacity="0">\r\n            <animate attributeName="r" begin="3s" dur="3s" values="6;22" calcMode="linear" repeatCount="indefinite"></animate>\r\n            <animate attributeName="stroke-opacity" begin="3s" dur="3s" values="1;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n            <animate attributeName="stroke-width" begin="3s" dur="3s" values="2;0" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </circle>\r\n        <circle cx="22" cy="22" r="8">\r\n            <animate attributeName="r" begin="0s" dur="1.5s" values="6;1;2;3;4;5;6" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </circle>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},"7a13":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"result"},["success"===e.type?n("div",{staticClass:"icon icon-success"},[n("i",{staticClass:"el-icon-success"})]):"warning"===e.type?n("div",{staticClass:"icon icon-warning"},[n("i",{staticClass:"el-icon-warning"})]):n("div",{staticClass:"icon icon-error"},[n("i",{staticClass:"el-icon-error"})]),n("h1",[e._v(e._s(e.title))]),e.desc?n("div",{staticClass:"desc"},[e._v(e._s(e.desc))]):e._e(),e.$slots.extra?n("div",{staticClass:"extra"},[e._t("extra")],2):e._e(),e.$slots.default?n("div",{staticClass:"actions"},[e._t("default")],2):e._e()])},o=[],i=(n("caad"),{name:"Result",props:{type:{type:String,validator:function(e){return["success","warning","error"].includes(e)},required:!0},title:{type:String,required:!0},desc:{type:String,default:""}}}),c=i,s=(n("c91a"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"4ae6664a",null);t["default"]=r.exports},"7b68":function(e,t,n){"use strict";n("c435")},"7c25":function(e,t,n){"use strict";n("5070")},"7dca":function(e,t,n){"use strict";n.r(t);n("d3b7"),n("d81d"),n("b0c0"),n("7db0"),n("b64b"),n("4de4"),n("a434");var a=n("2b61"),o={list:[]},i={},c={add:function(e,t){var n=e.rootState,a=e.commit;return new Promise((function(e){var o=[];t.matched.map((function(e,t){t>0&&e.components.default.name&&o.push(e.components.default.name)})),a("add",{route:t,name:o}),a("updateStorage",n.user.account),a("keepAlive/add",o,{root:!0}),e()}))},remove:function(e,t){var n,a=e.rootState,o=e.state,i=e.commit;o.list.map((function(e){e.path==t&&(n=e.name)})),i("remove",t),i("updateStorage",a.user.account),n&&i("keepAlive/remove",n,{root:!0})},removeOtherSide:function(e,t){var n=e.rootState,a=e.state,o=e.commit,i=[];a.list.map((function(e){e.path==t||e.isPin||i.push(e.name)})),o("removeOtherSide",t),o("updateStorage",n.user.account),i.map((function(e){o("keepAlive/remove",e,{root:!0})}))},removeLeftSide:function(e,t){var n=e.rootState,a=e.state,o=e.commit,i=~~Object.keys(a.list).find((function(e){return a.list[e].path==t})),c=[];a.list.map((function(e,t){t<i&&!e.isPin&&c.push(e.name)})),o("removeLeftSide",t),o("updateStorage",n.user.account),c.map((function(e){o("keepAlive/remove",e,{root:!0})}))},removeRightSide:function(e,t){var n=e.rootState,a=e.commit,i=~~Object.keys(o.list).find((function(e){return o.list[e].path==t})),c=[];o.list.map((function(e,t){t>i&&!e.isPin&&c.push(e.name)})),a("removeRightSide",t),a("updateStorage",n.user.account),c.map((function(e){a("keepAlive/remove",e,{root:!0})}))},pin:function(e,t){var n=e.rootState,a=e.commit;a("pin",t),a("updateStorage",n.user.account)},unPin:function(e,t){var n=e.rootState,a=e.commit;a("unPin",t),a("updateStorage",n.user.account)}},s={recoveryStorage:function(e,t){null!=a["a"].local.get("tabbarPinData")&&(e.list=JSON.parse(a["a"].local.get("tabbarPinData"))[t]||[])},updateStorage:function(e,t){var n=JSON.parse(a["a"].local.get("tabbarPinData"))||{};n[t]=e.list.filter((function(e){return e.isPin})),a["a"].local.set("tabbarPinData",JSON.stringify(n))},clean:function(e){e.list=[]},add:function(e,t){"reload"!=t.route.name&&(e.list.some((function(e){return e.path==(t.route.meta.activeMenu||t.route.fullPath)}))||e.list.push({path:t.route.meta.activeMenu||t.route.fullPath,title:t.route.meta.title,i18n:t.route.meta.i18n,name:t.name,isPin:!1}))},remove:function(e,t){e.list=e.list.filter((function(e){return e.path!=t}))},removeOtherSide:function(e,t){e.list=e.list.filter((function(e){return e.path==t||e.isPin}))},removeLeftSide:function(e,t){var n=~~Object.keys(e.list).find((function(n){return e.list[n].path==t}));e.list=e.list.filter((function(e,t){return t>=n||e.isPin}))},removeRightSide:function(e,t){var n=~~Object.keys(e.list).find((function(n){return e.list[n].path==t}));e.list=e.list.filter((function(e,t){return t<=n||e.isPin}))},pin:function(e,t){var n=~~Object.keys(e.list).find((function(n){return e.list[n].path==t})),a=-1;e.list.map((function(e,t){e.isPin&&(a=t)})),e.list.splice(a+1,0,e.list[n]),e.list.splice(n+1,1),e.list.map((function(e){e.path==t&&(e.isPin=!0)}))},unPin:function(e,t){var n=Object.keys(e.list).find((function(n){return e.list[n].path==t}));n=~~n;var a=-1;e.list.map((function(e,t){e.isPin&&(a=t)})),e.list.splice(a+1,0,e.list[n]),e.list.splice(n,1),e.list.map((function(e){e.path==t&&(e.isPin=!1)}))},sort:function(e,t){e.list=t}};t["default"]={namespaced:!0,state:o,actions:c,getters:i,mutations:s}},"7dd0a":function(e,t,n){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},"7e11":function(e,t,n){},"7e15":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=n("2909"),c=(n("d81d"),n("b0c0"),n("cb29"),n("313e")),s={name:"ChartBar",props:{title:{type:String,default:""},datas:{type:Array,default:function(){return[]}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=c.init(this.$refs.chart);var e=[],t=[],n=0,a=Math.max.apply(Math,Object(i["a"])(this.datas.map((function(e){return e.value}))));this.datas.map((function(a){e.push(a.name),0===a.value?t.push(a.value+n):t.push(a.value)}));var o={color:["#3398DB"],tooltip:{trigger:"axis",axisPointer:{type:"line",lineStyle:{opacity:0}},formatter:function(e){return e[0].data===n?e[0].name+"：0":e[0].name+"："+e[0].data}},grid:{left:"0%",right:20,bottom:10,top:20,containLabel:!0,z:22},xAxis:[{type:"category",gridIndex:0,data:e,axisTick:{alignWithLabel:!0},axisLine:{lineStyle:{color:"#0c3b71"}},axisLabel:{show:!0,color:"#49DBFF",fontSize:14,padding:[0,0,0,-10]}}],yAxis:[{type:"value",gridIndex:0,splitLine:{show:!1},axisTick:{show:!1},min:n,max:a,axisLine:{lineStyle:{color:"#0c3b71"}},axisLabel:{color:"#49DBFF",formatter:"{value}"}},{type:"value",gridIndex:0,min:n,max:a,splitNumber:12,splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.0)","rgba(250,250,250,0.05)"]}}}],series:[{name:"-",type:"bar",barWidth:"30%",xAxisIndex:0,yAxisIndex:0,itemStyle:{normal:{barBorderRadius:2,color:new c.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"#00feff"},{offset:.5,color:"#027eff"},{offset:1,color:"#0286ff"}])}},data:t,zlevel:11},{name:"背景",type:"bar",barWidth:"50%",xAxisIndex:0,yAxisIndex:1,barGap:"-135%",data:Object(i["a"])(this.datas).fill(a),itemStyle:{normal:{color:"rgba(255,255,255,0.1)"}},zlevel:9}]};this.chart.setOption(o)}}},r=s,d=n("2877"),l=Object(d["a"])(r,a,o,!1,null,null,null);t["default"]=l.exports},"7ec8":function(e,t,n){},"7ed4":function(e,t,n){},"7f39":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-toolbar-collapse",use:"icon-toolbar-collapse-usage",viewBox:"0 0 1028 1024",content:'<symbol class="icon" viewBox="0 0 1028 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-toolbar-collapse"><defs><style type="text/css"></style></defs><path d="M989.866667 234.666667h-490.666667c-17.066667 0-34.133333-21.333333-34.133333-42.666667 0-25.6 12.8-42.666667 34.133333-42.666667h490.666667c17.066667 0 34.133333 17.066667 34.133333 42.666667 0 21.333333-12.8 42.666667-34.133333 42.666667z m-473.6 128h465.066666c25.6 0 46.933333 21.333333 46.933334 42.666666 0 25.6-21.333333 42.666667-46.933334 42.666667H516.266667c-25.6 0-46.933333-17.066667-46.933334-42.666667s21.333333-42.666667 46.933334-42.666666z m0 298.666666c-25.6 0-46.933333-21.333333-46.933334-42.666666 0-25.6 21.333333-42.666667 46.933334-42.666667h465.066666c25.6 0 46.933333 17.066667 46.933334 42.666667s-21.333333 42.666667-46.933334 42.666666H516.266667z m4.266666 128h452.266667c29.866667 0 51.2 17.066667 51.2 42.666667s-21.333333 42.666667-51.2 42.666667h-452.266667c-29.866667 0-51.2-17.066667-51.2-42.666667s21.333333-42.666667 51.2-42.666667z m-192 25.6c-17.066667 17.066667-46.933333 17.066667-64 0l-251.733333-273.066666c-17.066667-17.066667-17.066667-51.2 0-68.266667l251.733333-273.066667c17.066667-17.066667 46.933333-17.066667 64 0s17.066667 51.2 0 68.266667l-221.866666 238.933333 221.866666 238.933334c17.066667 21.333333 17.066667 51.2 0 68.266666z" p-id="8812" /></symbol>'});c.a.add(s);t["default"]=s},"812b":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=n("53ca"),c=n("313e"),s={name:"ChartPie1",props:{subTitle:{type:String,default:""},data:{type:Object,default:function(){return{}}},colors:{type:Array,default:function(){return[]}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=this.chart||c.init(this.$refs.chart),console.log("this.data.value",Object(i["a"])(this.data.value));var e={title:[{text:this.data.value+"%",subtext:this.subTitle,x:"center",top:"40%",textStyle:{fontSize:"20",color:"#FFFFFF",fontFamily:"DINAlternate-Bold, DINAlternate",foontWeight:"600"},subtextStyle:{fontSize:"12",color:"#919193",fontFamily:"DINAlternate-Bold, DINAlternate",lineHeight:4}}],polar:{radius:["42%","56%"],center:["50%","50%"]},angleAxis:{max:100,show:!1},radiusAxis:{type:"category",show:!0,axisLabel:{show:!1},axisLine:{show:!1},axisTick:{show:!1}},series:[{name:"",type:"bar",roundCap:!0,barWidth:90,showBackground:!0,data:[this.data.value],coordinateSystem:"polar",itemStyle:{normal:{color:new c.graphic.LinearGradient(0,0,1,0,[{offset:1,color:this.colors&&this.colors.length?this.colors[0]:"#16CEB9"},{offset:.5,color:this.colors&&this.colors.length?this.colors[1]:"#16CEB9"},{offset:0,color:this.colors&&this.colors.length?this.colors[2]:"#6648FF"}])}}},{name:"",type:"pie",startAngle:80,radius:["60%"],hoverAnimation:!1,center:["50%","50%"],itemStyle:{color:"rgba(66, 66, 66, .1)",borderWidth:1,borderColor:"#344979"},data:[100]},{name:"",type:"pie",startAngle:80,radius:["38%"],hoverAnimation:!1,center:["50%","50%"],itemStyle:{color:"rgba(66, 66, 66, .1)",borderWidth:1,borderColor:"#344979"},data:[100]}]};this.chart.setOption(e,!0)}}},r=s,d=n("2877"),l=Object(d["a"])(r,a,o,!1,null,null,null);t["default"]=l.exports},8190:function(e,t,n){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},8326:function(e,t,n){var a={"./AbsoluteContainer/index.vue":"fbc6","./AmapMarker/index.vue":"aae7","./Auth/index.vue":"c119","./AuthAll/index.vue":"8be5","./BatchActionBar/index.vue":"d2d5","./CascaderArea/index.vue":"220d","./Charts/Area/index.vue":"59c9","./Charts/Bar/index.vue":"7e15","./Charts/BarLine/index.vue":"e961","./Charts/HorizontalBar/index.vue":"5e10","./Charts/Line/index.vue":"b963","./Charts/LiquidFill/index.vue":"d82b","./Charts/MutiBarLine/index.vue":"c3d0","./Charts/Pie/index.vue":"c78e","./Charts/Pie1/index.vue":"812b","./Charts/Radar/index.vue":"5ba9","./Charts/Rose/index.vue":"9469","./Chip/index.vue":"5a04","./ColorfulCard/index.vue":"87ef","./Copyright/index.vue":"0f68","./DictTableColumn/index.vue":"cad4","./DynamicForm/index.vue":"2a06","./Editor/index.vue":"095c","./FileUpload/index.vue":"2a75","./FixedActionBar/index.vue":"0d07","./IconPicker/index.vue":"4095","./ImagePreview/index.vue":"9a2d","./ImageUpload/index.vue":"0835","./ImagesUpload/index.vue":"2968","./LineChart/index.vue":"e702","./LineDivider/index.vue":"4242","./MapDot/index.vue":"0ef0","./MapEventDot/index.vue":"73ba","./MapEventSelfDot/index.vue":"7366","./PageHeader/index.vue":"af20","./PageMain/index.vue":"08ba","./RectChart/index.vue":"f9e6","./ReportDatePicker/index.vue":"f10c","./Result/index.vue":"7a13","./ScrollingList/index.vue":"407b","./ScrollingTable/index.vue":"d193","./SearchBar/index.vue":"de71","./SpinkitLoading/index.js":"549e","./StationMapDot/index.vue":"a497","./SvgIcon/index.vue":"c00a","./Trend/index.vue":"cc5f"};function o(e){var t=i(e);return n(t)}function i(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}o.keys=function(){return Object.keys(a)},o.resolve=i,e.exports=o,o.id="8326"},"83d6":function(e,t,n){"use strict";var a={openPermission:!0,layout:"adaption",theme:"default",elementSize:"large",showHeader:!1,alwaysShowMainSidebar:!1,enableSidebarCollapse:!0,sidebarCollapse:!1,switchSidebarAndPageJump:!1,enableTabbar:!1,topbarFixed:!0,switchTabbarAndTopbar:!1,enableBreadcrumb:!0,showCopyright:!1,copyrightDates:"2021",copyrightCompany:"与安服务管理系统",copyrightWebsite:"",enableNavSearch:!1,enableNotification:!1,enableI18n:!1,defaultLang:"",enableMobileAdaptation:!0,enableFullscreen:!1,enablePageReload:!1,enableProgress:!0,enableDynamicTitle:!1,enableDashboard:!0,dashboardTitle:"控制台",storagePrefix:"fa_",enableWatermark:!1,enableFlatRoutes:!1,enableErrorLog:!1,enableThemeSetting:!1};t["a"]=a},"86a3":function(e,t,n){},"87ef":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-card",{staticClass:"mini-card",style:{background:"linear-gradient(50deg, "+e.colorFrom+", "+e.colorTo+")"},attrs:{shadow:"hover"}},[n("div",{attrs:{slot:"header"},slot:"header"},[e._v(e._s(e.header))]),n("div",{staticClass:"num"},[e._v(e._s(e.num))]),n("div",{staticClass:"tip"},[e._v(e._s(e.tip))]),e.icon?n("svg-icon",{attrs:{name:e.icon}}):e._e()],1)},o=[],i=(n("a9e3"),{name:"ColorfulCard",props:{colorFrom:{type:String,default:"#843cf6"},colorTo:{type:String,default:"#759bff"},header:{type:String,default:""},num:{type:Number,default:0},tip:{type:String,default:""},icon:{type:String,default:""}}}),c=i,s=(n("45c5"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"4c726e52",null);t["default"]=r.exports},"8ab0":function(e,t,n){"use strict";n("9e15")},"8b84":function(e,t,n){},"8be5":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.check()?e._t("default"):e._t("no-auth")],2)},o=[],i=n("4260"),c={name:"AuthAll",props:{value:{type:Array,default:function(){return[]}}},methods:{check:function(){return Object(i["b"])(this.value)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},"8c5e":function(e,t,n){"use strict";n("6888")},"8e92":function(e,t,n){"use strict";n("171d")},"90d5":function(e,t,n){"use strict";n("6ece")},9469:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d81d"),n("b0c0"),n("a15b"),n("313e")),c={name:"ChartRose",props:{datas:{type:Array,default:function(){return[]}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=i.init(this.$refs.chart);var e=["#218de0","#01cbb3","#85e647","#5d5cda","#05c5b0","#c29927"],t=["rgba(60,170,211,0.05)","rgba(1,203,179,0.05)","rgba(133,230,71,0.05)","rgba(93,92,218,0.05)","rgba(5,197,176,0.05)","rgba(194,153,39,0.05)"],n=[];this.datas.map((function(a,o){n.push({value:a.value,name:a.name,itemStyle:{borderColor:e[o<t.length?o:0],borderWidth:2,shadowBlur:20,shadowColor:"#41a8f8",shadowOffsetx:25,shadowOffsety:20,color:t[o<t.length?o:0]}})}));var a={grid:{left:-100,top:70,bottom:20,right:10,containLabel:!0},tooltip:{trigger:"item",formatter:"{b} : {c} ({d}%)"},legend:{show:!1},polar:{},angleAxis:{interval:1,type:"category",data:[],z:10,axisLine:{show:!1,lineStyle:{color:"#0B4A6B",width:1,type:"solid"}},axisLabel:{interval:0,show:!0,color:"#0B4A6B",margin:8,fontSize:16}},radiusAxis:{min:20,max:120,interval:20,axisLine:{show:!1,lineStyle:{color:"#0B3E5E",width:1,type:"solid"}},axisLabel:{formatter:"{value} %",show:!1,padding:[0,0,20,0],color:"#0B3E5E",fontSize:16},splitLine:{lineStyle:{color:"#07385e",width:2,type:"dashed"}}},calculable:!0,series:[{stack:"a",type:"pie",radius:"80%",roseType:"radius",zlevel:10,startAngle:100,label:{normal:{formatter:["{b|{b}}","{d|{d}%}"].join("\n"),rich:{b:{color:"#3bd2fe",fontSize:14,lineHeight:20},d:{color:"#d0fffc",fontSize:14,height:20}}}},labelLine:{normal:{show:!0,length:10,length2:45,lineStyle:{color:"#0096b1"}},emphasis:{show:!1}},data:n}]};this.chart.setOption(a)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},9620:function(e,t,n){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},9674:function(e,t,n){"use strict";function a(e){function t(s){n=new WebSocket(e),(o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","attempt-connect",e);var r=n,d=setTimeout((function(){(o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","connection-timeout",e),c=!0,r.close(),c=!1}),o.timeoutInterval);n.onopen=function(t){clearTimeout(d),(o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","onopen",e),o.readyState=WebSocket.OPEN,s=!1,o.onopen(t)},n.onclose=function(r){clearTimeout(d),n=null,i?(o.readyState=WebSocket.CLOSED,o.onclose(r)):(o.readyState=WebSocket.CONNECTING,s||c||((o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","onclose",e),o.onclose(r)),setTimeout((function(){t(!0)}),o.reconnectInterval))},n.onmessage=function(t){(o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","onmessage",e,t.data),o.onmessage(t)},n.onerror=function(n){(o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","onerror",e,n),o.readyState==WebSocket.CONNECTING&&"Netscape"!=navigator.appName&&(clearTimeout(d),n=null,i?(o.readyState=WebSocket.CLOSED,o.onclose(r)):(o.readyState=WebSocket.CONNECTING,s||c||((o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","onclose",e),o.onclose(r)),setTimeout((function(){t(!0)}),o.reconnectInterval)))}}this.debug=!1,this.reconnectInterval=1e3,this.timeoutInterval=2e3;var n,o=this,i=!1,c=!1;this.url=e,this.readyState=WebSocket.CONNECTING,this.URL=e,this.onopen=function(e){},this.onclose=function(e){},this.onmessage=function(e){},this.onerror=function(e){},t(e),this.send=function(t){if(n)return(o.debug||a.debugAll)&&console.debug("ReconnectingWebSocket","send",e,t),n.send(t);throw"INVALID_STATE_ERR : Pausing to reconnect websocket"},this.close=function(){n&&(i=!0,n.close())},this.refresh=function(){n&&n.close()}}n.d(t,"a",(function(){return a})),a.debugAll=!1},"97bc":function(e,t,n){},"98c0":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-toolbar-theme",use:"icon-toolbar-theme-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-toolbar-theme"><defs><style type="text/css"></style></defs><path d="M524.48 965.12a456 456 0 0 1-147.52-24.64 32 32 0 0 1-19.52-40.64 32 32 0 0 1 40.96-19.84 362.24 362.24 0 0 0 126.08 21.12 384 384 0 0 0 125.76-21.44 184.64 184.64 0 0 1-7.36-57.92 192 192 0 0 1 243.2-177.92 384 384 0 0 0-19.84-299.52 32 32 0 1 1 56.96-29.12 448 448 0 0 1 18.24 363.2 51.52 51.52 0 0 1-65.92 29.44 121.6 121.6 0 0 0-45.76-7.36 128 128 0 0 0-122.88 122.88 114.56 114.56 0 0 0 7.36 45.76 51.52 51.52 0 0 1-29.44 65.92 437.12 437.12 0 0 1-160.32 30.08z m137.28-89.6zM130.24 684.8a32 32 0 0 1-30.08-21.44 441.28 441.28 0 0 1-24.64-146.24 448 448 0 0 1 448-448 441.92 441.92 0 0 1 201.92 48.32 32 32 0 0 1-29.12 56.96 375.68 375.68 0 0 0-172.8-41.28A384 384 0 0 0 160 642.24a32 32 0 0 1-19.52 40.96 40.32 40.32 0 0 1-10.24 1.6z" p-id="5217" /><path d="M399.04 756.8a56 56 0 0 1-40.64-17.6l-89.28-97.6a55.04 55.04 0 0 1 8-81.28L849.28 136a48.96 48.96 0 0 1 68.48 0 49.6 49.6 0 0 1-5.76 69.12L440.32 738.24a54.72 54.72 0 0 1-40.96 18.56z m6.72-61.12z m-82.56-89.92l75.84 82.56L757.12 283.84z" p-id="5218" /><path d="M236.48 930.88h-7.68a169.92 169.92 0 0 1-117.44-54.72 91.84 91.84 0 0 1-30.08-71.04l2.24-16.32 66.56-34.56-4.48-41.28v-2.24a83.52 83.52 0 0 1 23.68-59.84c92.8-74.88 155.52-43.52 192-3.84a169.28 169.28 0 0 1-124.48 283.84z m-84.16-105.6A61.44 61.44 0 0 0 160 832a104.64 104.64 0 0 0 148.8 6.4 105.28 105.28 0 0 0 6.4-148.8c-19.84-21.44-46.08-35.52-101.76 8.32a56.32 56.32 0 0 0-2.56 12.8l4.8 42.24a55.04 55.04 0 0 1-29.44 54.72z m60.16-128z" p-id="5219" /><path d="M236.16 454.08m-39.68 0a39.68 39.68 0 1 0 79.36 0 39.68 39.68 0 1 0-79.36 0Z" p-id="5220" /><path d="M285.76 325.44m-29.76 0a29.76 29.76 0 1 0 59.52 0 29.76 29.76 0 1 0-59.52 0Z" p-id="5221" /></symbol>'});c.a.add(s);t["default"]=s},"98f4":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-example-emotion-line",use:"icon-example-emotion-line-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-example-emotion-line"><defs><style type="text/css"></style></defs><path d="M512 938.666667C276.352 938.666667 85.333333 747.648 85.333333 512S276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667z m0-85.333334a341.333333 341.333333 0 1 0 0-682.666666 341.333333 341.333333 0 0 0 0 682.666666z m-170.666667-298.666666h341.333334a170.666667 170.666667 0 1 1-341.333334 0z m0-85.333334a64 64 0 1 1 0-128 64 64 0 0 1 0 128z m341.333334 0a64 64 0 1 1 0-128 64 64 0 0 1 0 128z" p-id="1530" /></symbol>'});c.a.add(s);t["default"]=s},"999e":function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-sidebar-breadcrumb",use:"icon-sidebar-breadcrumb-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-sidebar-breadcrumb"><defs><style type="text/css"></style></defs><path d="M165.415385 827.076923c-11.815385 0-19.692308-7.876923-19.692308-19.692308V214.646154c0-11.815385 7.876923-19.692308 19.692308-19.692308h159.507692c7.876923 0 17.723077 3.938462 23.630769 9.846154L576.984615 492.307692c7.876923 9.846154 7.876923 25.6 0 37.415385L346.584615 817.230769c-5.907692 7.876923-15.753846 11.815385-25.6 11.815385L165.415385 827.076923z m706.953846-334.769231L641.969231 206.769231c-9.846154-11.815385-27.569231-15.753846-41.353846-3.938462l-45.292308 37.415385c-13.784615 9.846154-15.753846 29.538462-3.938462 41.353846L738.461538 512 551.384615 744.369231c-9.846154 11.815385-7.876923 31.507692 3.938462 41.353846l45.292308 37.415385c13.784615 9.846154 29.538462 7.876923 41.353846-3.938462L872.369231 531.692308c7.876923-15.753846 7.876923-29.538462 0-39.384616z" p-id="2783" /></symbol>'});c.a.add(s);t["default"]=s},"9a2d":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-image",{style:"width:"+e.realWidth+";height:"+e.realHeight+";",attrs:{src:e.src,fit:"cover","preview-src-list":[e.src]}},[n("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[n("svg-icon",{attrs:{name:"image-load-fail"}})],1)])},o=[],i=(n("a9e3"),{name:"ImagePreview",props:{src:{type:String,required:!0},width:{type:[Number,String],default:""},height:{type:[Number,String],default:""}},data:function(){return{dialogVisible:!1}},computed:{realWidth:function(){return"string"==typeof this.width?this.width:"".concat(this.width,"px")},realHeight:function(){return"string"==typeof this.height?this.height:"".concat(this.height,"px")}},created:function(){},mounted:function(){},methods:{}}),c=i,s=(n("6fb5"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"56b18690",null);t["default"]=r.exports},"9b62":function(e,t,n){},"9c64":function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABoAAAAZCAYAAAAv3j5gAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA9tJREFUeNqsVs9vG1UQnlmbtG5KsgiEGqrKaydOQ13RjcSBG16JIxLuH4DihQNckFIhcalE0zOHqggEKkJ2xIGjI8Td5oQ4sZVIk25tZ13U1qSJ6rRQx7XfG+ZtXcdeh6QmefJod70z882Pb94+hP+xHv4wnercOmPvu/XnscFhAKrvRfVjb4UKIxMhEzUAEuC17gvrlc8q3n624WGARAUyzXFpQhtAG0UQW9JouvISv7IPFYi2QX/iEIhNAXgMQD5g8DtgPI/tUECI6EGDoH1zp/D8Wzxwj2rfJ83GzXYqcjq8dOLDZb8PlYSxwFZznJ/Oj1fjbnXhQED3riUziJD1S0ZQBwJr4qNl57/0736VTGMITGCStDdk8dTFleK+QPe+OTsPSFcCf+cmPl4eaPqfFxNpPB7K4lFNxzACgwGwiLqsN8rCTlxzl5SeNhDZ18ksSAYRimYBCSzvg3iGHsk81YUut9ogGxLkEwJqMdaYph9/84V87btkZiCjO1+eUVnM75Kkw2U8/9onN7rzUnknmsExLavYh0x1jGjMxM6VBUZUdp0MEaxQtwQLPO1t+hY4oj5pEYOgdXJ+pfZMtzQVTUMTfgRBamhBzdXTrIkrDg616QRI7KaBhHq3dLQp5uRfAvrkvnCoLq2Tn64Et5kiNdjhOtvVGGxDsr3krtDsqS9Ks/Q3XaA6l3KLffzjlzPdnSNRk0aAjx5GwDIW13yQyruGjiOY52ivxn/yltypqIUSCviImbbNrGySFf/Z81kpN2URj7KLJldESYR2BpZuy2BfFmO/VX2Q8kxU58gLECKTH1Plc1F78no1504ZFrvJYosuJ36tdqkv18UlHOVejSow4QN2yXBrKqpmJtNLAHZi8VVnpTxfzUAgdqJUzfX+wT50Jk2BK2HiOIO8yJaKKKPo7fQI8BdirR4xec95wLKm7gPvlP7bvSCr0zFdAhYkgUmP2R/3Tz7r34ZcxIDy77tEvtvKzbhrfcO7wrbszETymQd+KKrVI5CbvFG1+waWIzrPFfVYYA8ZAPljOp5iW1N1WXK2UsOnV9ZVIAM7wxm34hHCLCvlfMV+qfNoXEi6lYFtqMdxR3zA3HTJs/fdVK/PTOq9ZTy3Wi7uVUvWz7OztF82otzZQEA47Hlh/fOYeSQWvoJhSIHaBQjs8blbTgfMVA7fWC07B/rwqbW9LPNaRBjhl0NAkszWXamoH+tk7RzK4aSSiBpwBNbCr3OEr6K/z7U9/uLeBoubXjy0U1AHjEjtlxGfpuocoYbwpclSdc9jlzb0oQ7RVg2Hx6qO6hHs/UDU+leAAQA3TNbkABbyGAAAAABJRU5ErkJggg=="},"9e15":function(e,t,n){},"9e88":function(e,t,n){},"9f1c":function(e,t,n){"use strict";n("ab51")},a18c:function(e,t,n){"use strict";var a=n("5530"),o=n("2909"),i=n("1da1"),c=(n("96cf"),n("d3b7"),n("3ca3"),n("ddb0"),n("ac1f"),n("99af"),n("159b"),n("b0c0"),n("2b0e")),s=n("8c4f"),r=n("4360"),d=n("323e"),l=n.n(d),m=(n("a5d8"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"layout"},[n("div",{attrs:{id:"app-main"}},[n("transition",{attrs:{name:"header"}},["pc"==e.$store.state.settings.mode&&e.$store.state.settings.showHeader?n("header",[n("div",{staticClass:"header-container"},[n("div",{staticClass:"main"},[n("Logo"),e.$store.state.menu.routes.length>1?n("div",{staticClass:"nav"},[e._l(e.$store.state.menu.routes,(function(t,a){return[t.children&&0!==t.children.length?n("div",{key:a,class:{item:!0,active:a==e.$store.state.menu.headerActived},on:{click:function(t){return e.switchMenu(a)}}},[t.meta.icon?n("svg-icon",{staticClass:"icon",attrs:{name:t.meta.icon}}):e._e(),t.meta.title?n("span",[e._v(e._s(t.meta.title))]):e._e()],1):e._e()]}))],2):e._e()],1),n("UserMenu")],1)]):e._e()]),n("div",{staticClass:"wrapper"},[n("div",{class:{"sidebar-container":!0,show:"mobile"==e.$store.state.settings.mode&&!e.$store.state.settings.sidebarCollapse}},[n("transition",{attrs:{name:"main-sidebar"}},[e.$store.state.settings.showHeader&&"mobile"!=e.$store.state.settings.mode||!(e.$store.state.menu.routes.length>1||e.$store.state.settings.alwaysShowMainSidebar)?e._e():n("div",{staticClass:"main-sidebar-container"},[n("Logo",{staticClass:"sidebar-logo",attrs:{"show-title":!1}}),n("div",{staticClass:"nav"},[e._l(e.$store.state.menu.routes,(function(t,a){return[t.children&&0!==t.children.length?n("div",{key:a,class:{item:!0,active:a==e.$store.state.menu.headerActived},attrs:{title:t.meta.title},on:{click:function(t){return e.switchMenu(a)}}},[t.meta.icon?n("svg-icon",{staticClass:"icon",attrs:{name:t.meta.icon}}):e._e(),n("span",[e._v(e._s(t.meta.title))])],1):e._e()]}))],2)],1)]),n("div",{class:{"sub-sidebar-container":!0,"is-collapse":"pc"==e.$store.state.settings.mode&&e.$store.state.settings.sidebarCollapse},on:{scroll:e.onSidebarScroll}},[n("Logo",{class:{"sidebar-logo":!0,"sidebar-logo-bg":e.$store.state.menu.routes.length<=1&&!e.$store.state.settings.alwaysShowMainSidebar,shadow:e.sidebarScrollTop},attrs:{"show-logo":e.$store.state.menu.routes.length<=1&&!e.$store.state.settings.alwaysShowMainSidebar}}),n("el-menu",{class:{"is-collapse-without-logo":e.$store.state.menu.routes.length>1&&"pc"==e.$store.state.settings.mode&&e.$store.state.settings.sidebarCollapse},attrs:{"unique-opened":"","default-active":e.$route.meta.activeMenu||e.$route.path,collapse:"pc"==e.$store.state.settings.mode&&e.$store.state.settings.sidebarCollapse,"collapse-transition":!1}},[n("transition-group",{attrs:{name:"sub-sidebar"}},[e._l(e.$store.getters["menu/sidebarRoutes"],(function(t){return[!1!==t.meta.sidebar?n("SidebarItem",{key:t.path,attrs:{item:t,"base-path":t.path}}):e._e()]}))],2)],1)],1)],1),n("div",{class:{"sidebar-mask":!0,show:"mobile"==e.$store.state.settings.mode&&!e.$store.state.settings.sidebarCollapse},on:{click:function(t){return e.$store.commit("settings/toggleSidebarCollapse")}}}),n("div",{staticClass:"main-container",style:{"padding-bottom":e.$route.meta.paddingBottom}},[e.$store.state.settings.enableTabbar&&!e.$store.state.settings.switchTabbarAndTopbar?n("Tabbar"):e._e(),n("Topbar",{class:{shadow:e.scrollTop}}),e.$store.state.settings.enableTabbar&&e.$store.state.settings.switchTabbarAndTopbar?n("Tabbar"):e._e(),n("div",{staticClass:"main"},[n("transition",{attrs:{name:"main",mode:"out-in"}},[e.isRouterAlive?n("keep-alive",{attrs:{include:e.$store.state.keepAlive.list}},[n("RouterView",{key:e.$route.path})],1):e._e()],1)],1),e.showCopyright?n("Copyright"):e._e()],1)]),n("el-backtop",{attrs:{right:20,bottom:20,title:"回到顶部"}})],1),n("Search"),n("ThemeSetting"),n("Watermark"),n("EmergencyContactDialog",{ref:"emergencyContactDialog"}),n("KeyAuthorizationDialog",{ref:"keyAuthorizationDialog"}),n("SendSmsDialog",{ref:"sendSmsDialog"}),n("ResultModifyDialog",{ref:"resultModifyDialog"}),n("AudioDialog",{ref:"audioDialog"})],1)}),u=[],h=(n("d81d"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("router-link",{class:{title:!0,"is-link":e.$store.state.settings.enableDashboard},attrs:{custom:"",to:e.to,title:e.loginedSysName||e.title},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.navigate;return[n("div",{on:{click:a}},[e.showLogo?n("img",{staticClass:"logo",attrs:{src:e.loginedLogo||e.logo}}):e._e(),e.showTitle?n("span",[e._v(e._s(e.loginedSysName||e.title))]):e._e()])]}}])})}),f=[],p={name:"Logo",props:{showLogo:{type:Boolean,default:!0},showTitle:{type:Boolean,default:!0}},data:function(){return{title:"BOE 服务管理系统",logo:n("9c64")}},computed:{to:function(){var e={};return this.$store.state.settings.enableDashboard&&(e.name="dashboard"),e},loginedLogo:function(){if(this.$store.state.user.member)return this.$store.state.user.member.logo},loginedSysName:function(){if(this.$store.state.user.member)return"2"==this.$store.state.user.member.systemVersion?this.$store.state.user.member.systemName?this.$store.state.user.member.systemName+"高级版":"BOE 服务管理系统 - 高级版":this.$store.state.user.member.systemName}}},g=p,b=(n("6ae2"),n("2877")),v=Object(b["a"])(g,h,f,!1,null,"c263d170",null),y=v.exports,w=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"user"},[n("div",{staticClass:"tools"},[e.$store.state.settings.enableNavSearch?n("span",{staticClass:"item",on:{click:function(t){return e.$eventBus.$emit("global-search-toggle")}}},[n("i",{staticClass:"ri-search-line"})]):e._e(),e.$store.state.settings.enableNotification?n("el-popover",{attrs:{trigger:"hover","open-delay":200,placement:"bottom",width:"350"},on:{show:e.handlePopoverShow},model:{value:e.notifyVisible,callback:function(t){e.notifyVisible=t},expression:"notifyVisible"}},[n("Notification",{ref:"tabs"}),n("template",{slot:"reference"},[n("span",{staticClass:"item"},[n("el-badge",{attrs:{value:5}},[n("i",{staticClass:"ri-notification-3-line"})])],1)])],2):e._e(),e.$store.state.settings.enableI18n?n("el-dropdown",{staticClass:"language-container",attrs:{size:"medium"},on:{command:e.languageCommand}},[n("span",{staticClass:"item"},[n("i",{staticClass:"ri-translate"})]),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{attrs:{disabled:"zh-CN"==e.$store.state.settings.defaultLang,command:"zh-CN"}},[e._v("中文(简体)")]),n("el-dropdown-item",{attrs:{disabled:"zh-TW"==e.$store.state.settings.defaultLang,command:"zh-TW"}},[e._v("中文(繁體)")]),n("el-dropdown-item",{attrs:{disabled:"en"==e.$store.state.settings.defaultLang,command:"en"}},[e._v("英文")])],1)],1):e._e(),"pc"==e.$store.state.settings.mode&&e.isFullscreenEnable&&e.$store.state.settings.enableFullscreen?n("span",{staticClass:"item",on:{click:e.fullscreen}},[n("i",{class:e.isFullscreen?"ri-fullscreen-exit-line":"ri-fullscreen-line"})]):e._e(),e.$store.state.settings.enablePageReload?n("span",{staticClass:"item",on:{click:function(t){return e.reload(e.$store.state.settings.enableTabbar?1:2)}}},[n("svg-icon",{attrs:{name:"toolbar-reload"}})],1):e._e(),e.$store.state.settings.enableThemeSetting?n("span",{staticClass:"item",on:{click:function(t){return e.$eventBus.$emit("global-theme-toggle")}}},[n("svg-icon",{attrs:{name:"toolbar-theme"}})],1):e._e()],1),n("el-dropdown",{staticClass:"user-container",attrs:{size:"default"},on:{command:e.userCommand}},[n("div",{staticClass:"user-wrapper"},[n("el-avatar",{attrs:{size:"medium"}},[n("i",{staticClass:"el-icon-user-solid"})]),e._v(" "+e._s(e.$store.state.user.account)+" "),n("i",{staticClass:"el-icon-caret-bottom"})],1),n("el-dropdown-menu",{staticClass:"user-dropdown",attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{directives:[{name:"auth",rawName:"v-auth",value:"self.org",expression:"'self.org'"}],attrs:{command:"updateOrg"}},[e._v("修改信息")]),n("el-dropdown-item",{attrs:{command:"updatePwd"}},[e._v("修改密码")]),n("el-dropdown-item",{attrs:{divided:"",command:"logout"}},[e._v(e._s(e.$t("app.logout")))])],1)],1)],1)},k=[],C=n("93bf"),S=n.n(C),x=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-tabs",{ref:"tabs",staticClass:"notification",model:{value:e.activeName,callback:function(t){e.activeName=t},expression:"activeName"}},[n("el-tab-pane",{staticClass:"container",attrs:{label:"通知 (5)",name:"notice"}},[n("div",{staticClass:"list"},[n("div",{staticClass:"item"},[n("i",{staticClass:"ri-mail-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你收到了 8 份日报")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])]),n("div",{staticClass:"item"},[n("i",{staticClass:"ri-service-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你收到了 3 位同事的好友申请，请及时处理")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])]),n("div",{staticClass:"item"},[n("i",{staticClass:"ri-file-edit-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你有 3 份合同待审批")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])]),n("div",{staticClass:"item"},[n("i",{staticClass:"ri-mail-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你收到了 8 份日报")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])]),n("div",{staticClass:"item"},[n("i",{staticClass:"ri-service-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你收到了 3 位同事的好友申请，请及时处理")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])])]),n("div",{staticClass:"more"},[e._v("进入通知列表")])]),n("el-tab-pane",{staticClass:"container",attrs:{label:"消息",name:"message"}},[n("div",{staticClass:"list"},[n("div",{staticClass:"empty"},[e._v("你已读完所有消息")])])]),n("el-tab-pane",{staticClass:"container",attrs:{label:"待办 (2)",name:"todo"}},[n("div",{staticClass:"list"},[n("div",{staticClass:"item"},[n("i",{staticClass:"ri-bug-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你有 2 个来自项目「Fantastic-admin」的 bug 待处理")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])]),n("div",{staticClass:"item"},[n("i",{staticClass:"ri-git-merge-fill"}),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v("你有 3 个来自项目「Fantastic-admin」的代码合并申请，提交人：Hooray，提交备注：专业版更新")]),n("div",{staticClass:"date"},[e._v("2020-10-10 10:00:00")])])])])])],1)},_=[],I={name:"Notification",props:{},data:function(){return{activeName:"notice"}},mounted:function(){},methods:{}},D=I,T=(n("395f"),Object(b["a"])(D,x,_,!1,null,"081b0652",null)),O=T.exports,E={name:"UserMenu",components:{Notification:O},inject:["reload","generateI18nTitle"],data:function(){return{isFullscreenEnable:S.a.isEnabled,isFullscreen:!1,notifyVisible:!1}},mounted:function(){S.a.isEnabled&&S.a.on("change",this.fullscreenChange)},beforeDestroy:function(){S.a.isEnabled&&S.a.off("change",this.fullscreenChange)},methods:{handlePopoverShow:function(){this.$refs.tabs.$refs.tabs.calcPaneInstances(!0)},fullscreen:function(){S.a.toggle()},fullscreenChange:function(){this.isFullscreen=S.a.isFullscreen},languageCommand:function(e){this.$i18n.locale=e,this.$store.commit("settings/setDefaultLang",e),this.$route.meta.title&&this.$store.commit("settings/setTitle",this.generateI18nTitle(this.$route.meta.i18n,this.$route.meta.title))},userCommand:function(e){var t=this;switch(e){case"dashboard":this.$router.push({name:"dashboard"});break;case"updateOrg":this.$router.push({name:"personalStationOrgEdit",params:{id:this.$store.state.user.member.orgId}});break;case"updatePwd":this.$router.push({name:"personalEditPassword",params:{id:this.$store.state.user.member.orgId}});break;case"logout":this.$store.dispatch("user/logout").then((function(){t.$router.push({name:"login"})}));break}}}},A=E,L=(n("16fc7"),Object(b["a"])(A,w,k,!1,null,"5c2c13ac",null)),N=L.exports,$=function(){var e=this,t=e.$createElement,n=e._self._c||t;return!1!==e.item.meta.sidebar?n("div",{staticClass:"sidebar-item"},[e.hasChildren?n("el-submenu",{attrs:{title:e.generateI18nTitle(e.item.meta.i18n,e.item.meta.title),index:e.resolvePath(e.item.path)}},[n("template",{slot:"title"},[e.item.meta.icon?n("svg-icon",{staticClass:"icon",attrs:{name:e.item.meta.icon}}):e._e(),n("span",[e._v(e._s(e.generateI18nTitle(e.item.meta.i18n,e.item.meta.title)))]),e.badge(e.item.meta.badge).visible&&e.badge(e.item.meta.badge).value>0?n("span",{class:{badge:!0,"badge-text":"text"==e.badge(e.item.meta.badge).type,"badge-dot":"dot"==e.badge(e.item.meta.badge).type}},[e._v(e._s(e.badge(e.item.meta.badge).value))]):e._e()],1),e._l(e.item.children,(function(t){return n("SidebarItem",{key:t.path,attrs:{item:t,"base-path":e.resolvePath(e.item.path)}})}))],2):n("router-link",{attrs:{custom:"",to:e.resolvePath(e.item.path)},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.href,o=t.navigate,i=t.isActive,c=t.isExactActive;return[n("a",{class:[i&&"router-link-active",c&&"router-link-exact-active"],attrs:{href:e.isExternal(e.resolvePath(e.item.path))?e.resolvePath(e.item.path):a,target:e.isExternal(e.resolvePath(e.item.path))?"_blank":"_self"},on:{click:o}},[n("el-menu-item",{attrs:{title:e.generateI18nTitle(e.item.meta.i18n,e.item.meta.title),index:e.resolvePath(e.item.path)}},[e.item.meta.icon?n("svg-icon",{staticClass:"icon",attrs:{name:e.item.meta.icon}}):e._e(),n("span",[e._v(e._s(e.generateI18nTitle(e.item.meta.i18n,e.item.meta.title)))]),e.badge(e.item.meta.badge).visible&&e.badge(e.item.meta.badge).value>0?n("span",{class:{badge:!0,"badge-dot":"dot"==e.badge(e.item.meta.badge).type,"badge-text":"text"==e.badge(e.item.meta.badge).type}},[e._v(e._s(e.badge(e.item.meta.badge).value))]):e._e()],1)],1)]}}],null,!1,2632481e3)})],1):e._e()},M=[],B=(n("5319"),n("df7c")),R=n.n(B),P={name:"SidebarItem",inject:["generateI18nTitle"],props:{item:{type:Object,required:!0},basePath:{type:String,default:""}},watch:{"$store.state.user.member":{handler:function(e){e&&!this.hasChildren&&-1!==this.item.path.indexOf(":orgId")&&(this.item.path=this.item.path.replace(":orgId",e.orgId))},immediate:!0}},data:function(){return{}},computed:{hasChildren:function(){var e=!0;return this.item.children?this.item.children.every((function(e){return!1===e.meta.sidebar}))&&(e=!1):e=!1,e}},created:function(){},mounted:function(){},methods:{isExternal:function(e){return/^(https?:|mailto:|tel:)/.test(e)},resolvePath:function(e){return this.isExternal(e)?e:this.isExternal(this.basePath)?this.basePath:R.a.resolve(this.basePath,e)},badge:function(e){var t={type:"",value:"",visible:!1};return e&&(t.visible=!0,t.value="function"==typeof e?e():e,"boolean"==typeof t.value?(t.type="dot",t.value||(t.visible=!1)):"number"==typeof t.value?(t.type="text",t.value<=0&&(t.visible=!1)):(t.type="text",t.value||(t.visible=!1))),t}}},j=P,z=(n("90d5"),n("fa21"),Object(b["a"])(j,$,M,!1,null,"25a0b108",null)),F=z.exports,W=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{"tabbar-container":!0,fixed:e.$store.state.settings.topbarFixed},attrs:{"data-fixed-calc-width":""}},[n("div",{ref:"tabs",staticClass:"tabs",class:{"tabs-ontop":e.$store.state.settings.switchTabbarAndTopbar},on:{mousewheel:function(e){e.preventDefault()}}},[n("draggable",e._b({on:{start:function(t){e.isDragging=!0},end:function(t){e.isDragging=!1}},model:{value:e.tabbarList,callback:function(t){e.tabbarList=t},expression:"tabbarList"}},"draggable",e.dragOptions,!1),[n("transition-group",{ref:"tab-container",staticClass:"tab-container",attrs:{type:"transition",name:"tabbar",duration:{leave:200},tag:"div"}},e._l(e.$store.state.tabbar.list,(function(t){return n("div",{key:t.path,ref:"tab-"+t.path,refInFor:!0,class:{tab:!0,"tab-ontop":e.$store.state.settings.switchTabbarAndTopbar,"tab-dragging":e.isDragging,actived:t.path==e.activedTabPath},attrs:{title:e.generateI18nTitle(t.i18n,t.title)},on:{click:function(n){return e.$router.push(t.path)},contextmenu:function(n){return n.preventDefault(),e.onTabbarContextmenu(n,t)}}},[n("div",{staticClass:"tab-dividers"}),n("div",{staticClass:"tab-background"},[n("svg",{attrs:{version:"1.1",xmlns:"http://www.w3.org/2000/svg"}},[n("defs",[n("symbol",{attrs:{id:"tab-geometry-left",viewBox:"0 0 214 36"}},[n("path",{attrs:{d:"M17 0h197v36H0v-2c4.5 0 9-3.5 9-8V8c0-4.5 3.5-8 8-8z"}})]),n("symbol",{attrs:{id:"tab-geometry-right",viewBox:"0 0 214 36"}},[n("use",{attrs:{"xlink:href":"#tab-geometry-left"}})]),n("clipPath",{attrs:{id:"crop"}},[n("rect",{staticClass:"mask",attrs:{width:"100%",height:"100%",x:"0"}})])]),n("svg",{attrs:{width:"52%",height:"100%"}},[n("use",{staticClass:"tab-geometry",attrs:{"xlink:href":"#tab-geometry-left",width:"214",height:"36"}})]),n("g",{attrs:{transform:"scale(-1, 1)"}},[n("svg",{attrs:{width:"52%",height:"100%",x:"-100%",y:"0"}},[n("use",{staticClass:"tab-geometry",attrs:{"xlink:href":"#tab-geometry-right",width:"214",height:"36"}})])])])]),n("div",{staticClass:"tab-content"},[n("div",{staticClass:"title"},[e._v(e._s(e.generateI18nTitle(t.i18n,t.title)))]),t.isPin?e._e():n("div",{staticClass:"drag-handle"}),t.isPin?n("i",{staticClass:"ri-pushpin-2-fill action-icon",on:{click:function(n){return n.stopPropagation(),e.$store.dispatch("tabbar/unPin",t.path)}}}):e.$store.state.tabbar.list.length>1?n("i",{staticClass:"ri-close-fill action-icon",on:{click:function(n){return n.stopPropagation(),e.onTabClose(t.path)}}}):e._e()])])})),0)],1)],1),e.isShowMoreAction?n("div",{staticClass:"more-action"},[n("el-dropdown",{on:{command:e.actionCommand}},[n("i",{staticClass:"ri-arrow-down-s-fill"}),n("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[n("el-dropdown-item",{attrs:{command:"other-side",disabled:!e.hasTabbarOtherSideCanClose}},[n("i",{staticClass:"el-icon-close"}),e._v(" 关闭其它标签页 ")]),n("el-dropdown-item",{attrs:{command:"left-side",disabled:!e.hasTabbarLeftSideCanClose}},[n("i",{staticClass:"el-icon-arrow-left"}),e._v(" 关闭左侧标签页 ")]),n("el-dropdown-item",{attrs:{command:"right-side",disabled:!e.hasTabbarRightSideCanClose}},[n("i",{staticClass:"el-icon-arrow-right"}),e._v(" 关闭右侧标签页 ")])],1)],1)],1):e._e()])},U=[],H=(n("7db0"),n("b64b"),n("2b61")),q=n("b76a"),V=n.n(q),G={name:"Tabbar",components:{draggable:V.a},inject:["reload","generateI18nTitle"],data:function(){return{isDragging:!1}},computed:{activedTabPath:function(){return this.$route.meta.activeMenu||this.$route.fullPath},hasTabbarOtherSideCanClose:function(){return this.checkOtherSideHasTabCanClose()},hasTabbarLeftSideCanClose:function(){return this.checkLeftSideHasTabCanClose()},hasTabbarRightSideCanClose:function(){return this.checkRightSideHasTabCanClose()},isShowMoreAction:function(){return this.$store.state.tabbar.list.length>1&&(this.hasTabbarOtherSideCanClose||this.hasTabbarLeftSideCanClose||this.hasTabbarRightSideCanClose)},tabbarList:{get:function(){return this.$store.state.tabbar.list},set:function(e){this.$store.commit("tabbar/sort",e)}},dragOptions:function(){return{animation:200,ghostClass:"tab-ghost",draggable:".tab",handle:".drag-handle",disabled:"mobile"==this.$store.state.settings.mode}}},watch:{$route:{handler:function(e){var t=this;this.$store.state.settings.enableTabbar&&this.$store.dispatch("tabbar/add",e).then((function(){t.$refs["tab-".concat(e.meta.activeMenu||e.fullPath)]&&(t.scrollTo(t.$refs["tab-".concat(e.meta.activeMenu||e.fullPath)][0].offsetLeft),t.tabbarScrollTip())}))},immediate:!0}},mounted:function(){this.$refs["tabs"].addEventListener("DOMMouseScroll",this.handlerMouserScroll,!1),this.$refs["tabs"].addEventListener("mousewheel",this.handlerMouserScroll,!1)},beforeDestroy:function(){this.$refs["tabs"].removeEventListener("DOMMouseScroll",this.handlerMouserScroll),this.$refs["tabs"].removeEventListener("mousewheel",this.handlerMouserScroll)},methods:{tabbarScrollTip:function(){this.$refs["tab-container"].$el.clientWidth>this.$refs["tabs"].clientWidth&&!H["a"].local.has("tabbarScrollTip")&&this.$confirm("顶部标签栏数量超过展示区域范围，你可以将鼠标移到标签栏上，然后通过鼠标滚轮滑动浏览","温馨提示",{confirmButtonText:"知道了",showCancelButton:!1,showClose:!1,closeOnClickModal:!1,closeOnPressEscape:!1,type:"info",center:!0}).then((function(){H["a"].local.set("tabbarScrollTip",!0)}))},handlerMouserScroll:function(e){var t=e.wheelDelta||e.detail,n=-1,a=1,o=0;o=t>0?50*n:50*a,this.$refs["tabs"].scrollBy({left:o})},scrollTo:function(e){this.$refs["tabs"].scrollTo({left:e-50,behavior:"smooth"})},checkOtherSideHasTabCanClose:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.activedTabPath;return this.$store.state.tabbar.list.some((function(t){return!t.isPin&&t.path!=e}))},checkLeftSideHasTabCanClose:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.activedTabPath,n=!0;if(t==this.$store.state.tabbar.list[0].path)n=!1;else{var a=~~Object.keys(this.$store.state.tabbar.list).find((function(n){return e.$store.state.tabbar.list[n].path==t}));n=this.$store.state.tabbar.list.some((function(e,n){return n<a&&!e.isPin&&e.path!=t}))}return n},checkRightSideHasTabCanClose:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.activedTabPath,n=!0;if(t==this.$store.state.tabbar.list[this.$store.state.tabbar.list.length-1].path)n=!1;else{var a=~~Object.keys(this.$store.state.tabbar.list).find((function(n){return e.$store.state.tabbar.list[n].path==t}));n=this.$store.state.tabbar.list.some((function(e,n){return n>=a&&!e.isPin&&e.path!=t}))}return n},onTabClose:function(e){this.$tabbarClose(e)},onOtherSideTabClose:function(e){e!=this.activedTabPath&&this.$router.push(e),this.$store.dispatch("tabbar/removeOtherSide",e)},onLeftSideTabClose:function(e){var t=this;if(e!=this.activedTabPath){var n=~~Object.keys(this.$store.state.tabbar.list).find((function(n){return t.$store.state.tabbar.list[n].path==e})),a=~~Object.keys(this.$store.state.tabbar.list).find((function(e){return t.$store.state.tabbar.list[e].path==t.activedTabPath}));a<n&&this.$router.push(e)}this.$store.dispatch("tabbar/removeLeftSide",e)},onRightSideTabClose:function(e){var t=this;if(e!=this.activedTabPath){var n=~~Object.keys(this.$store.state.tabbar.list).find((function(n){return t.$store.state.tabbar.list[n].path==e})),a=~~Object.keys(this.$store.state.tabbar.list).find((function(e){return t.$store.state.tabbar.list[e].path==t.activedTabPath}));a>n&&this.$router.push(e)}this.$store.dispatch("tabbar/removeRightSide",e)},actionCommand:function(e){switch(e){case"other-side":this.onOtherSideTabClose(this.activedTabPath);break;case"left-side":this.onLeftSideTabClose(this.activedTabPath);break;case"right-side":this.onRightSideTabClose(this.activedTabPath);break}},onTabbarContextmenu:function(e,t){var n=this;this.$contextmenu({items:[{label:"重新加载",icon:"el-icon-refresh",disabled:t.path!=this.activedTabPath,onClick:function(){n.reload(1)}},{label:t.isPin?"取消固定":"固定",divided:!0,disabled:"/dashboard"==t.path,onClick:function(){t.isPin?n.$store.dispatch("tabbar/unPin",t.path):n.$store.dispatch("tabbar/pin",t.path)}},{label:"关闭标签页",icon:"el-icon-close",disabled:this.$store.state.tabbar.list.length<=1,onClick:function(){n.onTabClose(t.path)}},{label:"关闭其它标签页",disabled:!this.checkOtherSideHasTabCanClose(t.path),onClick:function(){n.onOtherSideTabClose(t.path)}},{label:"关闭左侧标签页",disabled:!this.checkLeftSideHasTabCanClose(t.path),onClick:function(){n.onLeftSideTabClose(t.path)}},{label:"关闭右侧标签页",disabled:!this.checkRightSideHasTabCanClose(t.path),onClick:function(){n.onRightSideTabClose(t.path)}}],event:e,zIndex:1e3})}}},J=G,K=(n("c579"),Object(b["a"])(J,W,U,!1,null,"0510ac5a",null)),Y=K.exports,Z=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{"topbar-container":!0,fixed:e.$store.state.settings.topbarFixed},attrs:{"data-fixed-calc-width":""}},[n("div",{staticClass:"left-box"},["mobile"==e.$store.state.settings.mode||e.$store.state.settings.enableSidebarCollapse?n("div",{class:{"sidebar-collapse":!0,"is-collapse":e.$store.state.settings.sidebarCollapse},on:{click:function(t){return e.$store.commit("settings/toggleSidebarCollapse")}}},[n("svg-icon",{attrs:{name:"toolbar-collapse"}})],1):e._e(),e.$store.state.settings.enableBreadcrumb&&"pc"==e.$store.state.settings.mode?n("el-breadcrumb",{attrs:{"separator-class":"el-icon-arrow-right"}},[n("transition-group",{attrs:{name:"breadcrumb"}},[e._l(e.breadcrumbList,(function(t,a){return[a<e.breadcrumbList.length-1?n("el-breadcrumb-item",{key:t.path,attrs:{to:e.pathCompile(t.path)}},[e._v(" "+e._s(e.generateI18nTitle(t.i18n,t.title))+" ")]):n("el-breadcrumb-item",{key:t.path},[e._v(" "+e._s(e.generateI18nTitle(t.i18n,t.title))+" ")])]}))],2)],1):e._e()],1),n("UserMenu")],1)},Q=[],X=n("84d6"),ee=n("4260"),te={name:"Breadcrumb",components:{UserMenu:N},inject:["generateI18nTitle"],computed:{breadcrumbList:function(){var e=this,t=[];return this.$store.state.settings.enableDashboard&&t.push({path:"/dashboard",title:this.$store.state.settings.dashboardTitle,i18n:"route.dashboard"}),this.$store.state.settings.enableFlatRoutes?this.$route.meta.breadcrumbNeste&&this.$route.meta.breadcrumbNeste.map((function(n,a){var o=Object(ee["c"])(n);0!=a&&(o.path="".concat(e.$route.meta.breadcrumbNeste[0].path,"/").concat(n.path)),t.push(o)})):this.$route.matched.map((function(e){e.meta&&e.meta.title&&!1!==e.meta.breadcrumb&&"/dashboard"!=e.path&&t.push({path:e.path,title:e.meta.title,i18n:e.meta.i18n})})),t}},methods:{pathCompile:function(e){var t=Object(X["a"])(e);return t(this.$route.params)}}},ne=te,ae=(n("bc7c"),Object(b["a"])(ne,Z,Q,!1,null,"a502d326",null)),oe=ae.exports,ie=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:{searching:e.isShow},attrs:{id:"search"},on:{click:function(t){e.isShow&&e.$eventBus.$emit("global-search-toggle")}}},[n("div",{staticClass:"container"},[n("div",{staticClass:"search-box",on:{click:function(e){e.stopPropagation()}}},[n("el-input",{ref:"input",attrs:{size:"default","prefix-icon":"el-icon-search",placeholder:"搜索页面",clearable:""},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"esc",27,t.key,["Esc","Escape"])?null:e.$eventBus.$emit("global-search-toggle")}},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}}),e._m(0)],1),n("div",{ref:"search",staticClass:"result"},e._l(e.resultList,(function(t){return n("router-link",{key:t.path,attrs:{custom:"",to:e.isShow?t.path:""},scopedSlots:e._u([{key:"default",fn:function(a){var o=a.href,i=a.navigate;return[n("a",{staticClass:"item",attrs:{href:e.isExternal(t.path)?t.path:o,target:e.isExternal(t.path)?"_blank":"_self"},on:{click:i}},[n("div",{staticClass:"icon"},[t.icon?n("svg-icon",{attrs:{name:t.icon}}):e._e()],1),n("div",{staticClass:"info"},[n("div",{staticClass:"title"},[e._v(" "+e._s(e.generateI18nTitle(t.i18n,t.title))+" "),t.isExternal?n("svg-icon",{attrs:{name:"external-link"}}):e._e()],1),n("div",{staticClass:"breadcrumb"},e._l(t.breadcrumb,(function(t,a){return n("span",{key:a},[e._v(" "+e._s(t)+" "),n("i",{staticClass:"el-icon-arrow-right"})])})),0),n("div",{staticClass:"path"},[e._v(e._s(t.path))])])])]}}],null,!0)})})),1)])])},ce=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"tips"},[e._v("你可以使用快捷键"),n("span",[e._v("alt")]),e._v("+"),n("span",[e._v("s")]),e._v("唤醒搜索面板，按"),n("span",[e._v("esc")]),e._v("退出")])}],se=(n("4de4"),n("841c"),n("a15b"),{name:"Search",inject:["generateI18nTitle"],props:{},data:function(){return{isShow:!1,search:"",sourceList:[]}},computed:{resultList:function(){var e=this,t=[];return t=this.sourceList.filter((function(t){var n=!1;return t.title.indexOf(e.search)>=0&&(n=!0),t.path.indexOf(e.search)>=0&&(n=!0),t.breadcrumb.some((function(t){return t.indexOf(e.search)>=0}))&&(n=!0),n})),t}},watch:{isShow:function(e){var t=this;e?(document.querySelector("body").classList.add("hidden"),this.$refs.search.scrollTop=0,setTimeout((function(){t.$refs.input.$el.children[0].focus()}),100)):(document.querySelector("body").classList.remove("hidden"),setTimeout((function(){t.search=""}),500))}},created:function(){},mounted:function(){var e=this;this.$eventBus.$on("global-search-toggle",(function(){e.isShow=!e.isShow})),this.$store.state.menu.routes.map((function(t){e.getSourceList(t.children)}))},methods:{isExternal:function(e){return/^(https?:|mailto:|tel:)/.test(e)},hasChildren:function(e){var t=!0;return e.children?e.children.every((function(e){return!1===e.meta.sidebar}))&&(t=!1):t=!1,t},getSourceList:function(e){var t=this;e.map((function(e){if(!1!==e.meta.sidebar)if(t.hasChildren(e)){var n=e.meta.baseBreadcrumb?Object(ee["c"])(e.meta.baseBreadcrumb):[];n.push(e.meta.title);var a=Object(ee["c"])(e.children);a.map((function(t){t.meta.baseIcon=e.meta.icon||e.meta.baseIcon,t.meta.baseBreadcrumb=n,t.meta.basePath=e.meta.basePath?[e.meta.basePath,e.path].join("/"):e.path})),t.getSourceList(a)}else{var o=[];e.meta.baseBreadcrumb&&(o=Object(ee["c"])(e.meta.baseBreadcrumb)),o.push(t.generateI18nTitle(e.meta.i18n,e.meta.title));var i="";i=t.isExternal(e.path)?e.path:e.meta.basePath?[e.meta.basePath,e.path].join("/"):e.path,t.sourceList.push({icon:e.meta.icon||e.meta.baseIcon,title:e.meta.title,i18n:e.meta.i18n,breadcrumb:o,path:i,isExternal:t.isExternal(e.path)})}}))}}}),re=se,de=(n("7b68"),Object(b["a"])(re,ie,ce,!1,null,"7d528d02",null)),le=de.exports,me=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-drawer",{attrs:{title:"主题配置",visible:e.isShow,direction:"rtl",size:"pc"==e.$store.state.settings.mode?"500px":"300px"},on:{"update:visible":function(t){e.isShow=t}}},[n("el-alert",{attrs:{title:"主题配置可实时预览效果，更多设置请在 src/settings.js 中进行设置，建议在生产环境隐藏主题配置功能",type:"error",closable:!1}}),n("el-form",{ref:"form",attrs:{"label-position":"pc"==e.$store.state.settings.mode?"right":"top","label-width":"100px",size:"small"}},["pc"==e.$store.state.settings.mode?n("el-form-item",{attrs:{label:"界面布局"}},[n("el-select",{model:{value:e.layout,callback:function(t){e.layout=t},expression:"layout"}},[n("el-option",{attrs:{label:"自适应",value:"adaption"}}),n("el-option",{attrs:{label:"自适应（有最小宽度）",value:"adaption-min-width"}}),n("el-option",{attrs:{label:"定宽居中",value:"center"}}),n("el-option",{attrs:{label:"定宽居中（有最大宽度）",value:"center-max-width"}})],1)],1):e._e(),n("el-form-item",{attrs:{label:"主题风格"}},[n("el-select",{model:{value:e.theme,callback:function(t){e.theme=t},expression:"theme"}},[n("el-option",{attrs:{label:"默认",value:"default"}}),n("el-option",{attrs:{label:"Vue CLI 风格",value:"vue-cli"}}),n("el-option",{attrs:{label:"码云风格",value:"gitee"}}),n("el-option",{attrs:{label:"清新",value:"freshness"}}),n("el-option",{attrs:{label:"素雅",value:"elegant"}}),n("el-option",{attrs:{label:"纯白",value:"pure-white"}})],1)],1),n("el-form-item",{attrs:{label:"组件尺寸"}},[n("el-radio-group",{model:{value:e.elementSize,callback:function(t){e.elementSize=t},expression:"elementSize"}},[n("el-radio-button",{attrs:{label:"large"}},[e._v("默认")]),n("el-radio-button",{attrs:{label:"medium"}},[e._v("中等")]),n("el-radio-button",{attrs:{label:"small"}},[e._v("小")]),n("el-radio-button",{attrs:{label:"mini"}},[e._v("极小")])],1),n("el-alert",{attrs:{title:"可全局设置 Element 组件的尺寸大小",type:"info",closable:!1}})],1),"pc"==e.$store.state.settings.mode?n("el-form-item",{attrs:{label:"头部"}},[n("el-radio-group",{model:{value:e.showHeader,callback:function(t){e.showHeader=t},expression:"showHeader"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("显示")]),n("el-radio-button",{attrs:{label:!1}},[e._v("隐藏")])],1)],1):e._e(),"pc"==e.$store.state.settings.mode?n("el-form-item",{attrs:{label:"侧边栏切换"}},[n("el-radio-group",{model:{value:e.enableSidebarCollapse,callback:function(t){e.enableSidebarCollapse=t},expression:"enableSidebarCollapse"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("启用")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1)],1):e._e(),n("el-form-item",{attrs:{label:"侧边栏导航"}},[n("el-radio-group",{model:{value:e.sidebarCollapse,callback:function(t){e.sidebarCollapse=t},expression:"sidebarCollapse"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("收起")]),n("el-radio-button",{attrs:{label:!1}},[e._v("展开")])],1)],1),n("el-form-item",{attrs:{label:"切换跳转"}},[n("el-radio-group",{model:{value:e.switchSidebarAndPageJump,callback:function(t){e.switchSidebarAndPageJump=t},expression:"switchSidebarAndPageJump"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("启用")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"开启该功能后，切换侧边栏时，页面自动跳转至该侧边栏导航下第一个路由地址",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"标签栏"}},[n("el-radio-group",{model:{value:e.enableTabbar,callback:function(t){e.enableTabbar=t},expression:"enableTabbar"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("启用")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1)],1),n("el-form-item",{attrs:{label:"顶栏"}},[n("el-radio-group",{model:{value:e.topbarFixed,callback:function(t){e.topbarFixed=t},expression:"topbarFixed"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("固定")]),n("el-radio-button",{attrs:{label:!1}},[e._v("不固定")])],1),n("el-alert",{attrs:{title:"包含顶部导航栏和标签栏",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"顶栏显示"}},[n("el-radio-group",{model:{value:e.switchTabbarAndTopbar,callback:function(t){e.switchTabbarAndTopbar=t},expression:"switchTabbarAndTopbar"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("顶部导航栏"),n("br"),n("br"),e._v("标签栏")]),n("el-radio-button",{attrs:{label:!1}},[e._v("标签栏"),n("br"),n("br"),e._v("顶部导航栏")])],1)],1),"pc"==e.$store.state.settings.mode?n("el-form-item",{attrs:{label:"面包屑导航"}},[n("el-radio-group",{model:{value:e.enableBreadcrumb,callback:function(t){e.enableBreadcrumb=t},expression:"enableBreadcrumb"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("启用")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1)],1):e._e(),n("el-form-item",{attrs:{label:"底部版权"}},[n("el-radio-group",{model:{value:e.showCopyright,callback:function(t){e.showCopyright=t},expression:"showCopyright"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("显示")]),n("el-radio-button",{attrs:{label:!1}},[e._v("隐藏")])],1)],1),n("el-form-item",{attrs:{label:"导航搜索"}},[n("el-radio-group",{model:{value:e.enableNavSearch,callback:function(t){e.enableNavSearch=t},expression:"enableNavSearch"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"该功能为页面右上角的搜索按钮，可对侧边栏导航进行快捷搜索",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"通知中心"}},[n("el-radio-group",{model:{value:e.enableNotification,callback:function(t){e.enableNotification=t},expression:"enableNotification"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"该功能为页面右上角的通知中心，具体业务功能需自行开发，框架仅提供展示模版",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"国际化"}},[n("el-radio-group",{model:{value:e.enableI18n,callback:function(t){e.enableI18n=t},expression:"enableI18n"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1)],1),"pc"==e.$store.state.settings.mode?n("el-form-item",{attrs:{label:"全屏"}},[n("el-radio-group",{model:{value:e.enableFullscreen,callback:function(t){e.enableFullscreen=t},expression:"enableFullscreen"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"该功能为页面右上角的全屏按钮",type:"info",closable:!1}}),n("el-alert",{attrs:{title:"不建议开启，该功能使用场景极少，用户习惯于通过窗口“最大化”功能来扩大显示区域，以显示更多内容，并且使用 F11 键也可以进入全屏效果",type:"warning",closable:!1}})],1):e._e(),n("el-form-item",{attrs:{label:"页面刷新"}},[n("el-radio-group",{model:{value:e.enablePageReload,callback:function(t){e.enablePageReload=t},expression:"enablePageReload"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"该功能为页面右上角的刷新按钮，开启时会阻止 F5 键原刷新功能，并采用框架提供的刷新模式进行页面刷新",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"加载进度条"}},[n("el-radio-group",{model:{value:e.enableProgress,callback:function(t){e.enableProgress=t},expression:"enableProgress"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"该功能开启时，跳转路由会看到页面顶部有条蓝色的进度条",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"动态标题"}},[n("el-radio-group",{model:{value:e.enableDynamicTitle,callback:function(t){e.enableDynamicTitle=t},expression:"enableDynamicTitle"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"该功能开启时，页面标题会显示当前路由标题，格式为“页面标题 - 网站名称”；关闭时则显示网站名称，网站名称在项目根目录下 .env.* 文件里配置",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"控制台"}},[n("el-radio-group",{model:{value:e.enableDashboard,callback:function(t){e.enableDashboard=t},expression:"enableDashboard"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1),n("el-alert",{attrs:{title:"控制台即欢迎页，该功能开启时，登录成功默认进入控制台；关闭时则默认进入侧边栏导航第一个导航页面",type:"info",closable:!1}})],1),n("el-form-item",{attrs:{label:"页面水印"}},[n("el-radio-group",{model:{value:e.enableWatermark,callback:function(t){e.enableWatermark=t},expression:"enableWatermark"}},[n("el-radio-button",{attrs:{label:!0}},[e._v("开启")]),n("el-radio-button",{attrs:{label:!1}},[e._v("关闭")])],1)],1)],1)],1)],1)},ue=[],he={name:"ThemeSetting",inject:["reload"],props:{},data:function(){return{isShow:!1}},computed:{layout:{get:function(){return this.$store.state.settings.layout},set:function(e){this.$store.commit("settings/updateThemeSetting",{layout:e})}},theme:{get:function(){return this.$store.state.settings.theme},set:function(e){this.$store.commit("settings/updateThemeSetting",{theme:e})}},elementSize:{get:function(){return this.$store.state.settings.elementSize},set:function(e){this.$ELEMENT.size=e,this.$store.commit("settings/updateThemeSetting",{elementSize:e}),this.reload()}},showHeader:{get:function(){return this.$store.state.settings.showHeader},set:function(e){this.$store.commit("settings/updateThemeSetting",{showHeader:e})}},enableSidebarCollapse:{get:function(){return this.$store.state.settings.enableSidebarCollapse},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableSidebarCollapse:e})}},sidebarCollapse:{get:function(){return this.$store.state.settings.sidebarCollapse},set:function(e){this.$store.commit("settings/updateThemeSetting",{sidebarCollapse:e})}},switchSidebarAndPageJump:{get:function(){return this.$store.state.settings.switchSidebarAndPageJump},set:function(e){this.$store.commit("settings/updateThemeSetting",{switchSidebarAndPageJump:e})}},enableTabbar:{get:function(){return this.$store.state.settings.enableTabbar},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableTabbar:e})}},topbarFixed:{get:function(){return this.$store.state.settings.topbarFixed},set:function(e){this.$store.commit("settings/updateThemeSetting",{topbarFixed:e})}},switchTabbarAndTopbar:{get:function(){return this.$store.state.settings.switchTabbarAndTopbar},set:function(e){this.$store.commit("settings/updateThemeSetting",{switchTabbarAndTopbar:e})}},enableBreadcrumb:{get:function(){return this.$store.state.settings.enableBreadcrumb},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableBreadcrumb:e})}},showCopyright:{get:function(){return this.$store.state.settings.showCopyright},set:function(e){this.$store.commit("settings/updateThemeSetting",{showCopyright:e})}},enableNavSearch:{get:function(){return this.$store.state.settings.enableNavSearch},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableNavSearch:e})}},enableNotification:{get:function(){return this.$store.state.settings.enableNotification},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableNotification:e})}},enableI18n:{get:function(){return this.$store.state.settings.enableI18n},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableI18n:e})}},enableFullscreen:{get:function(){return this.$store.state.settings.enableFullscreen},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableFullscreen:e})}},enablePageReload:{get:function(){return this.$store.state.settings.enablePageReload},set:function(e){this.$store.commit("settings/updateThemeSetting",{enablePageReload:e})}},enableProgress:{get:function(){return this.$store.state.settings.enableProgress},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableProgress:e})}},enableDynamicTitle:{get:function(){return this.$store.state.settings.enableDynamicTitle},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableDynamicTitle:e})}},enableDashboard:{get:function(){return this.$store.state.settings.enableDashboard},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableDashboard:e})}},enableWatermark:{get:function(){return this.$store.state.settings.enableWatermark},set:function(e){this.$store.commit("settings/updateThemeSetting",{enableWatermark:e})}}},mounted:function(){var e=this;this.$eventBus.$on("global-theme-toggle",(function(){e.isShow=!e.isShow}))},methods:{}},fe=he,pe=(n("dc77"),Object(b["a"])(fe,me,ue,!1,null,"7c068ab7",null)),ge=pe.exports,be=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div")},ve=[],ye=n("e33e"),we=n.n(ye),ke={watch:{"$store.state.settings.enableWatermark":{handler:function(e){e?we.a.init({watermark_txt:"Fantastic-admin 水印测试 ".concat(this.$store.state.user.account),watermark_width:150,watermark_x:0,watermark_y:0,watermark_x_space:100,watermark_y_space:100,watermark_alpha:.1}):we.a.init({watermark_txt:" "})},immediate:!0}}},Ce=ke,Se=Object(b["a"])(Ce,be,ve,!1,null,null,null),xe=Se.exports,_e=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[n("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},["chanmb"===e.vo.calledType?n("div",[e._v(" 管家 - "),n("span",{staticClass:"event-name"},[e._v(e._s(e.vo.contactName))])]):n("div",[e._v(" 紧急联系人 - "),n("span",{staticClass:"event-name"},[e._v(e._s(e.vo.contactName)+"("+e._s(e.vo.relationTypeDesc)+")")])])]),n("div",{staticClass:"dialog-content"},[n("div",{staticClass:"left-window"},[n("div",{staticClass:"avatar"},[n("el-avatar",{attrs:{size:60,src:"https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"}})],1),n("div",{staticClass:"call-text"},[e._v(" "+e._s(e.isOver?"通话已结束":"正在为您呼叫…")+" ")]),n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:12}},[n("el-button",{staticStyle:{width:"71px"},attrs:{type:"success",size:"small",disabled:!e.isOver},on:{click:e.handleRedial}},[e._v("重播")])],1),n("el-col",{attrs:{span:12}},[n("el-button",{attrs:{type:"danger",size:"small",icon:"el-icon-phone",disabled:e.hangupBtnDisabled},on:{click:e.handleHangup}},[e._v("结束")])],1)],1)],1),n("div",{staticClass:"right-feedback"},[n("div",{staticClass:"info-item"},[n("div",{staticClass:"field-name"},[e._v("通话时长：")]),n("div",{staticClass:"field-value"},[n("span",{staticClass:"link-record"},[e._v(e._s(e.convertTime))])])]),n("div",{staticClass:"info-item",staticStyle:{display:"block"}},[n("div",{staticClass:"field-name"},[e._v("呼叫结果：")]),n("div",{staticClass:"field-value",staticStyle:{"padding-top":"10px"}},[n("el-radio-group",{staticStyle:{"line-height":"20px"},model:{value:e.form.callResult,callback:function(t){e.$set(e.form,"callResult",t)},expression:"form.callResult"}},e._l(e.callResults,(function(t,a){return n("el-radio",{key:t+a,attrs:{label:t.value}},[e._v(e._s(t.name))])})),1)],1)]),n("div",{staticClass:"info-item",staticStyle:{display:"block"}},[n("div",{staticClass:"field-name"},[e._v("备注：")]),n("div",{staticClass:"field-value",staticStyle:{"padding-top":"10px"}},[n("el-input",{attrs:{type:"textarea",rows:5,maxlength:100,"show-word-limit":"",placeholder:"请输入内容"},model:{value:e.form.feedback,callback:function(t){e.$set(e.form,"feedback",t)},expression:"form.feedback"}})],1)])])]),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{size:"small"},on:{click:e.close}},[e._v("取 消")]),n("el-button",{attrs:{size:"small",type:"primary",disabled:e.feedbackBtnDisabled,loading:e.saveLoading},on:{click:e.handleSaveResult}},[e._v("确 定")])],1)])},Ie=[],De=(n("a9e3"),n("10d6"),n("bc0c"),n("5546"),n("4b51"),n("2a1d"),n("43ee"),n("7146"),n("5a88"),n("2b55"),n("e236"),n("0284"),n("0f1a")),Te=(n("1a5c"),{name:"EmergencyContactDialog",props:{type:{type:Number}},data:function(){return{visible:!1,form:{callResult:void 0,feedback:void 0},vo:{},callBtnDisabled:!1,hangupBtnDisabled:!1,feedbackBtnDisabled:!1,setDateInterval:void 0,totalSecond:0,isOver:!1,saveLoading:!1,callResults:[]}},computed:{convertTime:function(){return this.processSecond(this.totalSecond)}},mounted:function(){var e=this;this.$eventBus.$off("ServicePlatformCallDialog-Handle").$on("ServicePlatformCallDialog-Handle",(function(t){"NOT_RUNING_SIP"===t?(e.callBtnDisabled=!1,e.hangupBtnDisabled=!0,e.feedbackBtnDisabled=!0):"AUTH_LOGIN_FAILURE"===t?(e.callBtnDisabled=!0,e.hangupBtnDisabled=!1,e.feedbackBtnDisabled=!1):"AUTH_LOGOUT_SUCCESS"===t||"AUTH_LOGOUT_FAILURE"===t||("SEAT_HANG_UP"===t||"CUSTOMER_HANG_UP"===t?(e.callBtnDisabled=!1,e.hangupBtnDisabled=!0,e.isOver=!0,e.feedbackBtnDisabled=!1,e.setDateInterval&&clearInterval(e.setDateInterval)):"SEAT_SHOCK_RING"===t?(e.callBtnDisabled=!0,e.hangupBtnDisabled=!1,e.feedbackBtnDisabled=!0):"SEAT_COLLECTED"===t&&(e.callBtnDisabled=!0,e.hangupBtnDisabled=!1,e.isOver=!1,e.feedbackBtnDisabled=!0,e.setDateInterval&&(clearInterval(e.setDateInterval),e.totalSecond=0),e.setDateInterval=setInterval((function(){e.totalSecond+=1}),1e3)))}))},methods:{show:function(e){var t=this;this.visible=!0;var n=JSON.parse(JSON.stringify(e));this.vo=n,this.fetchCallResultDatas(),De["a"].loadSeatInfo(),De["a"].setCallData(this.vo),setTimeout((function(){De["a"].call(t.vo.contactPhone)}),2e3)},close:function(){this.form.callResult=void 0,this.form.feedback=void 0,this.$store.dispatch("custom/setCallDialogShow",{status:!1}),this.visible=!1},handleHangup:function(){this.callBtnDisabled=!1,this.isOver=!0,this.hangupBtnDisabled=!0,De["a"].hangUpAPI()},handleRedial:function(){var e=this;De["a"].redialAPI(this.vo.contactPhone),this.$nextTick((function(){e.isOver=!1,e.callBtnDisabled=!0,e.hangupBtnDisabled=!1,e.feedbackBtnDisabled=!1}))},fetchCallResultDatas:function(e){var t=this;this.callResults=[],this.$api.get("/bms/event/callResultCode",{}).then((function(e){"00000"===e.status&&(t.callResults=e.data)}))},processSecond:function(e){if(e<60)return e+"秒";var t=Math.floor(e/60),n=Math.floor(e%60);if(t<60)return t+"分钟"+n+"秒";var a=Math.floor(t/60),o=Math.floor(t%60);return a+"小时"+o+"分钟"+n+"秒"},handleSaveResult:function(){var e=this;if(this.form.callResult){var t=this.$route.params.id;this.saveLoading=!0;var n={phone:this.vo.contactPhone,orgId:this.vo.orgId,stationId:this.vo.stationId,orderId:t,calledId:this.vo.id,calledType:this.vo.type,callStatus:this.form.callResult,contactLevel:this.vo.contactLevel,descMsg:this.form.feedback,relationTypeDesc:this.vo.relationTypeDesc,calledName:this.vo.contactName,houseId:this.vo.houseId};this.$api.post("/bms/outcall/koala/addCallResult",n).then((function(t){e.saveLoading=!1,"00000"===t.status?(e.$message({type:"success",message:"保存外呼结果成功"}),e.$eventBus.$emit("ServicePlatformEventDetail-Refresh"),e.close()):e.$message({type:"error",message:t.message||"保存外呼结果失败"})})).catch((function(t){e.saveLoading=!1,e.$message({type:"error",message:"保存外呼结果出现错误, 请稍后重试"})}))}else this.$message({type:"error",message:"请选择当前状态"})}},beforeDestroy:function(){this.setDateInterval&&clearInterval(this.setDateInterval)}}),Oe=Te,Ee=(n("8e92"),Object(b["a"])(Oe,_e,Ie,!1,null,"a64575a0",null)),Ae=Ee.exports,Le=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[n("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[e._v(" 钥匙授权 ")]),n("div",{staticClass:"dialog-content"},[n("div",{staticClass:"guard-target"},[e._v(" 守护对象: "),n("span",{staticClass:"event-name"},[e._v(e._s(e.vo.targetName))])]),n("div",{staticClass:"person-cards"},e._l(e.vo.keyAuths,(function(t,a){return n("div",{key:t+a,staticClass:"person-item",class:{authorized:"1"===t.status}},[n("div",{staticClass:"pic"},[n("el-avatar",{staticClass:"avatar-door",attrs:{size:45}},[n("img",{attrs:{src:"/images/event/icon-key-auth-door.png"}})])],1),n("div",{staticClass:"info"},[n("div",{staticClass:"name"},[e._v(e._s(t.applyUserName)+"（"+e._s(t.relationTypeDesc)+"）")]),n("div",{staticClass:"time"},[e._v("申请钥匙权限 "+e._s(t.createTime))])]),"0"!==t.status?n("div",{staticClass:"status-btn"},[e._v(" "+e._s(e.dict.statusKvMapping[t.status])+" ")]):n("div",{staticClass:"status-btn",on:{click:function(n){return e.handleConfirmAuthKey(t.id)}}},[e._v(" "+e._s(e.dict.statusKvMapping[t.status])+" ")])])})),0)])])},Ne=[],$e={name:"KeyAuthorizationDialog",data:function(){return{dict:{statusKvMapping:{0:"未处理",1:"已授权",2:"已归还"}},visible:!1,vo:{keyAuths:[]}}},mounted:function(){},methods:{show:function(e){this.visible=!0,this.vo=e},close:function(){this.$store.dispatch("custom/setKeyAuthorizationDialogShow",{status:!1}),this.visible=!1},handleConfirmAuthKey:function(e){var t=this;e&&this.$confirm("您确定要授权该用户钥匙权限吗？","钥匙授权",{closeOnClickModal:!1,confirmButtonText:"确定",cancelButtonText:"取消",type:"success"}).then((function(){t.handleAuthKey(e)}))},handleAuthKey:function(e){var t=this;this.$api.post("/bms/event/key/auth/".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"钥匙授权成功"}),t.$eventBus.$emit("ServicePlatformEventDetail-Refresh"),t.close()):t.$message({type:"error",message:e.message||"钥匙授权失败"})}))}}},Me=$e,Be=(n("427e"),Object(b["a"])(Me,Le,Ne,!1,null,"80bbdcd0",null)),Re=Be.exports,Pe=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[n("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[e._v(" 短信 ")]),n("div",{staticClass:"dialog-content"},[n("div",{staticClass:"title"},[n("span",{staticClass:"person-name"},[e._v(e._s(e.vo.contactName))]),e._v(" （先生/女士）您好： ")]),n("div",{staticClass:"text"},[e._v(" "+e._s(e.smsTemplate)+" ")]),n("div",{staticClass:"text-source"},[e._v(" 来自-与安服务管理系统 ")])]),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{size:"small"},on:{click:e.close}},[e._v("取 消")]),n("el-button",{attrs:{size:"small",type:"primary",loading:e.loading},on:{click:e.handleSendSms}},[e._v("发 送")])],1)])},je=[],ze={name:"SendSmsDialog",data:function(){return{visible:!1,vo:{},loading:!1,smsTemplate:""}},mounted:function(){},methods:{show:function(e){this.visible=!0,this.vo=e,this.fetchSmsTemplate()},close:function(){this.loading=!1,this.$store.dispatch("custom/setSendSmsDialogShow",{status:!1}),this.visible=!1},fetchSmsTemplate:function(){var e=this;this.$api.get("/bms/event/getSmsTemplate/".concat(this.vo.orderId),{}).then((function(t){"00000"===t.status?e.smsTemplate=t.data:e.$message({type:"error",message:t.message||"获取短信模板失败"})}))},handleSendSms:function(){var e=this;this.loading=!0,this.$api.post("/bms/event/sendSms/".concat(this.vo.id),{}).then((function(t){e.loading=!1,"00000"===t.status?(e.$message({type:"success",message:"短信发送成功"}),e.$eventBus.$emit("ServicePlatformEventDetail-Refresh"),e.close()):e.$message({type:"error",message:t.message||"短信发送失败"})})).catch((function(t){e.loading=!1,e.$message({type:"error",message:"短信发送出现错误, 请稍后重试"})}))}}},Fe=ze,We=(n("09af"),Object(b["a"])(Fe,Pe,je,!1,null,"6205cac2",null)),Ue=We.exports,He=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[n("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[e._v(" 状态修改 ")]),n("div",{staticClass:"dialog-content"},[n("div",{staticClass:"row"},[e._v(" 守护对象: "),n("span",{staticClass:"event-name"},[e._v(e._s(e.vo.contactName))])]),n("div",{staticClass:"row"},[e._v(" 当前状态: "),n("div",{staticStyle:{"margin-top":"10px"}},[n("el-radio-group",{staticStyle:{"line-height":"20px"},model:{value:e.form.callResult,callback:function(t){e.$set(e.form,"callResult",t)},expression:"form.callResult"}},e._l(e.callResults,(function(t,a){return n("el-radio",{key:t+a,attrs:{label:t.value}},[e._v(e._s(t.name))])})),1)],1)])]),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{size:"small"},on:{click:e.close}},[e._v("取 消")]),n("el-button",{attrs:{size:"small",type:"primary",loading:e.loading},on:{click:e.handleResultModify}},[e._v("确 定")])],1)])},qe=[],Ve={name:"KeyAuthorizationDialog",data:function(){return{visible:!1,form:{callResult:void 0},vo:{},loading:!1,callResults:[]}},mounted:function(){},methods:{show:function(e){this.visible=!0,this.vo=e,this.fetchCallResultDatas(),e.status&&(this.form.callResult=parseInt(e.status))},close:function(){this.loading=!1,this.form.callResult=void 0,this.$store.dispatch("custom/setResultModifyDialogShow",{status:!1}),this.visible=!1},fetchCallResultDatas:function(e){var t=this;this.callResults=[],this.$api.get("/bms/event/callResultCode",{}).then((function(e){"00000"===e.status&&(t.callResults=e.data)}))},handleResultModify:function(){var e=this;this.form.callResult?(this.loading=!0,this.$api.post("/bms/event/contact/updateStatus/".concat(this.vo.id,"/").concat(this.form.callResult),{}).then((function(t){e.loading=!1,"00000"===t.status?(e.$message({type:"success",message:"更新联系人状态成功"}),e.$eventBus.$emit("ServicePlatformEventDetail-ResultRefresh",{latitude:e.vo.latitude,longitude:e.vo.longitude}),e.close()):e.$message({type:"error",message:t.message||"更新联系人状态失败"})})).catch((function(t){e.loading=!1,e.$message({type:"error",message:"更新联系人状态出现错误, 请稍后重试"})}))):this.$message({type:"error",message:"请选择当前状态"})}}},Ge=Ve,Je=(n("175f"),Object(b["a"])(Ge,He,qe,!1,null,"5aeb6888",null)),Ke=Je.exports,Ye=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[n("div",{staticClass:"dialog-header",attrs:{slot:"title"},slot:"title"},[e._v(" 录音记录 ")]),n("div",{staticClass:"dialog-content"},[n("div",{staticClass:"row"},[n("div",{staticClass:"field-name"},[e._v("录音对象:")]),n("span",{staticClass:"event-name"},[e._v(e._s(e.obj.calledName||"-"))])]),n("div",{staticClass:"row"},[n("div",{staticClass:"field-name"},[e._v("开始时间:")]),n("span",{staticClass:"event-name"},[e._v(e._s(e.obj.createTime||"-"))])]),n("div",{staticClass:"row"},[n("div",{staticClass:"field-name"},[e._v("音频:")]),e.obj.url?n("audio",{staticClass:"audio",attrs:{src:e.obj.url,controls:"controls"}}):n("span",[e._v("-")])]),n("div",{staticClass:"row"},[n("div",{staticClass:"field-name"},[e._v("备注:")]),e._v(e._s(e.obj.descMsg||"-")+" ")])])])},Ze=[],Qe={name:"AudioDialog",data:function(){return{visible:!1,vo:{},obj:{}}},mounted:function(){},methods:{show:function(e){this.visible=!0,this.vo=e,this.fetchAudio(this.vo.id)},close:function(){this.$store.dispatch("custom/setAudioDialogShow",{status:!1}),this.obj={},this.visible=!1},fetchAudio:function(){var e=this;this.obj={},this.$api.get("/bms/event/getCallRadio/".concat(this.vo.id),{}).then((function(t){"00000"===t.status&&(e.obj=t.data)})).catch((function(t){e.close()}))}}},Xe=Qe,et=(n("d0b4"),Object(b["a"])(Xe,Ye,Ze,!1,null,"4fd7ef50",null)),tt=et.exports,nt={name:"Layout",components:{Logo:y,UserMenu:N,SidebarItem:F,Tabbar:Y,Topbar:oe,Search:le,ThemeSetting:ge,Watermark:xe,EmergencyContactDialog:Ae,KeyAuthorizationDialog:Re,SendSmsDialog:Ue,ResultModifyDialog:Ke,AudioDialog:tt},provide:function(){return{reload:this.reload}},data:function(){return{isRouterAlive:!0,routePath:"",sidebarScrollTop:0,scrollTop:0}},computed:{showCopyright:function(){return"undefined"!==typeof this.$route.meta.copyright?this.$route.meta.copyright:this.$store.state.settings.showCopyright}},watch:{$route:"routeChange","$store.state.settings.sidebarCollapse":function(e){"mobile"==this.$store.state.settings.mode&&(e?document.querySelector("body").classList.remove("hidden"):document.querySelector("body").classList.add("hidden"))},"$store.state.custom.callDialogShow":{handler:function(e){!0===e&&this.$refs.emergencyContactDialog&&this.$store.state.custom.callDialogData&&this.$refs.emergencyContactDialog.show(this.$store.state.custom.callDialogData)},immediate:!0},"$store.state.custom.keyAuthorizationDialogShow":{handler:function(e){!0===e&&this.$refs.keyAuthorizationDialog&&this.$store.state.custom.keyAuthorizationDialogData&&this.$refs.keyAuthorizationDialog.show(this.$store.state.custom.keyAuthorizationDialogData)},immediate:!0},"$store.state.custom.sendSmsDialogShow":{handler:function(e){console.log("newVal",e,this.$refs.sendSmsDialog,this.$store.state.custom.sendSmsDialogData),!0===e&&this.$refs.sendSmsDialog&&this.$store.state.custom.sendSmsDialogData&&this.$refs.sendSmsDialog.show(this.$store.state.custom.sendSmsDialogData)},immediate:!0},"$store.state.custom.resultModifyDialogShow":{handler:function(e){!0===e&&this.$refs.resultModifyDialog&&this.$store.state.custom.resultModifyDialogData&&this.$refs.resultModifyDialog.show(this.$store.state.custom.resultModifyDialogData)},immediate:!0},"$store.state.custom.audioDialogShow":{handler:function(e){!0===e&&this.$refs.audioDialog&&this.$store.state.custom.audioDialogData&&this.$refs.audioDialog.show(this.$store.state.custom.audioDialogData)},immediate:!0}},mounted:function(){this.initHotkey(),window.addEventListener("scroll",this.onScroll)},destroyed:function(){window.removeEventListener("scroll",this.onScroll)},methods:{reload:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;if(this.$store.state.settings.enableTabbar){var n,a=this.$route.meta.activeMenu||this.$route.fullPath;this.$store.state.tabbar.list.map((function(e){e.path==a&&(n=e.name)})),n&&(this.$store.commit("keepAlive/remove",n),this.$router.push({name:"reload"}),this.$nextTick((function(){e.$store.commit("keepAlive/add",n)})))}else 1==t?(this.isRouterAlive=!1,this.$nextTick((function(){return e.isRouterAlive=!0}))):this.$router.push({name:"reload"})},routeChange:function(e,t){e.name==t.name&&this.reload()},initHotkey:function(){var e=this;this.$hotkeys("alt+s",(function(t){e.$store.state.settings.enableNavSearch&&(t.preventDefault(),e.$eventBus.$emit("global-search-toggle"))})),this.$hotkeys("f5",(function(t){e.$store.state.settings.enablePageReload&&(t.preventDefault(),e.reload(e.$store.state.settings.enableTabbar?1:2))}))},onSidebarScroll:function(e){this.sidebarScrollTop=e.target.scrollTop},onScroll:function(){this.scrollTop=document.documentElement.scrollTop||document.body.scrollTop},switchMenu:function(e){this.$store.commit("menu/switchHeaderActived",e),this.$store.state.settings.switchSidebarAndPageJump&&this.$router.push(this.$store.getters["menu/sidebarRoutes"][0].path)}}},at=nt,ot=(n("bf85"),Object(b["a"])(at,m,u,!1,null,"4298b11c",null)),it=ot.exports,ct=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("RouterView")},st=[],rt={name:"EmptyLayout",props:{},data:function(){return{}},created:function(){},mounted:function(){},methods:{}},dt=rt,lt=Object(b["a"])(dt,ct,st,!1,null,null,null),mt=lt.exports,ut={path:"/service/platform",component:it,redirect:"/dashboard",name:"servicePlatform",meta:{title:"机构事件",icon:"el-icon-help",auth:["event.unprocessed","event.my","event.query"]},children:[{path:"dashboard",name:"dashboard",component:function(){return n.e("chunk-5c033668").then(n.bind(null,"4620"))},meta:{title:"未处理事件",badge:function(){return r["a"].state.menuBadge.unprocessedNumber+""},auth:["event.unprocessed"]}},{path:"event/my",name:"servicePlatformMyEvent",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"0c34"))},meta:{title:"我的处理事件",auth:["event.my"]}},{path:"event/query",name:"servicePlatformEventQuery",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"2abb"))},meta:{title:"事件查询",auth:["event.query"]}},{path:"event/chamb/query/:chambId",name:"servicePlatformEventQueryByChamb",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"2abb"))},meta:{sidebar:!1,title:"事件查询",activeMenu:"/person/butler"}},{path:"event/device/query/:deviceCode",name:"servicePlatformEventQueryByDevice",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"2abb"))},meta:{sidebar:!1,title:"事件查询",activeMenu:"/station/index"}},{path:"event/detail/:id",name:"servicePlatformEventDetail",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"4c9e"))},meta:{title:"事件详情",sidebar:!1}},{path:"device-msg",name:"deviceMsgIndex",component:mt,redirect:"/service/platform/device-msg/index",meta:{title:"设备通知"},children:[{path:"",name:"deviceMsgList",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"19d9"))},meta:{title:"设备通知",sidebar:!1,breadcrumb:!1,activeMenu:"/service/platform/device-msg"}},{path:"detail/:id",name:"deviceMsgDetail",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"6848"))},meta:{title:"设备通知详情",sidebar:!1,breadcrumb:!0,activeMenu:"/service/platform/device-msg"}}]}]},ht=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"layout"},[n("div",{attrs:{id:"app-main"}},[n("transition",{attrs:{name:"header"}},[n("Header")],1),n("div",{staticClass:"wrapper"},[n("div",{staticClass:"main-container",style:{"padding-bottom":e.$route.meta.paddingBottom}},[n("div",{staticClass:"main"},[n("transition",{attrs:{name:"main",mode:"out-in"}},[e.isRouterAlive?n("keep-alive",{attrs:{include:e.$store.state.keepAlive.list}},[n("RouterView",{key:e.$route.path})],1):e._e()],1)],1)])])],1)])},ft=[],pt=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"header-container"})},gt=[],bt={name:"Header",data:function(){return{}},computed:{},watch:{},mounted:function(){},beforeDestroy:function(){},methods:{handlerMouserScroll:function(e){var t=e.wheelDelta||e.detail,n=-1,a=1,o=0;o=t>0?50*n:50*a,this.$refs["tabs"].scrollBy({left:o})}}},vt=bt,yt=(n("727e"),Object(b["a"])(vt,pt,gt,!1,null,"1140fed5",null)),wt=yt.exports,kt={name:"VisualiseLayout",components:{Header:wt},provide:function(){return{reload:this.reload}},data:function(){return{isRouterAlive:!0,routePath:"",sidebarScrollTop:0,scrollTop:0}},watch:{$route:"routeChange","$store.state.settings.sidebarCollapse":function(e){"mobile"==this.$store.state.settings.mode&&(e?document.querySelector("body").classList.remove("hidden"):document.querySelector("body").classList.add("hidden"))}},mounted:function(){window.addEventListener("scroll",this.onScroll)},destroyed:function(){window.removeEventListener("scroll",this.onScroll)},methods:{reload:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;if(this.$store.state.settings.enableTabbar){var n,a=this.$route.meta.activeMenu||this.$route.fullPath;this.$store.state.tabbar.list.map((function(e){e.path==a&&(n=e.name)})),n&&(this.$store.commit("keepAlive/remove",n),this.$router.push({name:"reload"}),this.$nextTick((function(){e.$store.commit("keepAlive/add",n)})))}else 1==t?(this.isRouterAlive=!1,this.$nextTick((function(){return e.isRouterAlive=!0}))):this.$router.push({name:"reload"})},routeChange:function(e,t){e.name==t.name&&this.reload()},onSidebarScroll:function(e){this.sidebarScrollTop=e.target.scrollTop},onScroll:function(){this.scrollTop=document.documentElement.scrollTop||document.body.scrollTop}}},Ct=kt,St=(n("e023"),Object(b["a"])(Ct,ht,ft,!1,null,"7426e9dd",null)),xt=St.exports,_t=("".concat(window.location.protocol,"//").concat(window.location.host),{path:"/big/screen",component:xt,redirect:"/big/screen/index",name:"bigScreen",meta:{title:"大屏展示",icon:"el-icon-help",auth:["bigscreen.org"]},children:[{path:"index",name:"bigScreenIndex",meta:{title:"大屏",sidebar:!1}},{path:"platform",name:"platform",component:function(){return n.e("chunk-f44f626a").then(n.bind(null,"1e4b"))},meta:{sidebar:!1}},{path:"realtime/:stationId",name:"realtime",component:function(){return n.e("chunk-5b256ac2").then(n.bind(null,"4e12"))},meta:{title:"实时监控",sidebar:!1}},{path:"orgscreen/:orgId",name:"orgscreen",component:function(){return Promise.all([n.e("chunk-7e208fb6"),n.e("chunk-9e621516")]).then(n.bind(null,"1392"))},meta:{title:"机构大屏",sidebar:!0}},{path:"stationscreen/:orgId/:stationId",name:"stationscreen",component:function(){return Promise.all([n.e("chunk-7e208fb6"),n.e("chunk-53e65238")]).then(n.bind(null,"629a"))},meta:{title:"服务站大屏",sidebar:!1}}]}),It={path:"/station",component:it,redirect:"/station/org",name:"station",meta:{title:"服务站管理",icon:"el-icon-help",auth:["station.org","station.station","device.list"]},children:[{path:"org",name:"orgIndex",component:mt,redirect:"/org/index",meta:{title:"机构管理",auth:["station.org"]},children:[{path:"",component:function(){return n.e("chunk-79963f93").then(n.bind(null,"4960"))},meta:{title:"机构列表",sidebar:!1,breadcrumb:!1,auth:["station.org"]}},{path:"edit/:id",name:"stationOrgEdit",component:function(){return n.e("chunk-3fb9bb48").then(n.bind(null,"6ec2"))},meta:{title:"编辑机构",sidebar:!1,activeMenu:"/station/org"}},{path:"info/:id",name:"stationOrgInfo",component:function(){return n.e("chunk-69cc9a3b").then(n.bind(null,"660a"))},meta:{title:"详情查看",sidebar:!1,activeMenu:"/station/org"}},{path:"device/list/:id",name:"orgDeviceList",component:function(){return n.e("chunk-af6f243c").then(n.bind(null,"0e2c"))},meta:{title:"服务设备详情查看",sidebar:!1,activeMenu:"/station/org",busiType:"org"}}]},{path:"index",name:"stationIndex",component:mt,redirect:"/station/station/index",meta:{title:"服务站管理",auth:["station.station"]},children:[{path:"",component:function(){return n.e("chunk-e0efc5a4").then(n.bind(null,"29fe"))},meta:{title:"服务站列表",sidebar:!1,breadcrumb:!1,auth:["station.station"]}},{path:"by/org/:orgId",name:"stationListByOrg",component:function(){return n.e("chunk-e0efc5a4").then(n.bind(null,"29fe"))},meta:{title:"服务站列表",sidebar:!1,breadcrumb:!1,activeMenu:"/station/index"}},{path:"add",name:"stationAdd",component:function(){return n.e("chunk-b66d24d4").then(n.bind(null,"eb93"))},meta:{title:"新建服务站",sidebar:!1,activeMenu:"/station/index"}},{path:"edit/:id",name:"stationEdit",component:function(){return n.e("chunk-b66d24d4").then(n.bind(null,"eb93"))},meta:{title:"编辑服务站",sidebar:!1,activeMenu:"/station/index"}},{path:"station/info/:id",name:"stationInfo",component:function(){return n.e("chunk-2d0d6592").then(n.bind(null,"71bd"))},meta:{title:"详情查看",sidebar:!1,activeMenu:"/station/index"}},{path:"list/:id",name:"stationDeviceList2",component:function(){return n.e("chunk-af6f243c").then(n.bind(null,"0e2c"))},meta:{title:"服务设备详情查看",sidebar:!1,activeMenu:"/station/index",busiType:"station"}}]},{path:"older",name:"stationOlder",component:mt,redirect:"/station/older",meta:{title:"老人管理",auth:["stationOlder"]},children:[{path:"",component:function(){return n.e("chunk-18286672").then(n.bind(null,"e310"))},meta:{title:"老人列表",sidebar:!1,breadcrumb:!1,auth:["stationOlder"]}},{path:"/station/older/add",name:"stationOlderAdd",component:function(){return n.e("chunk-36b86e8a").then(n.bind(null,"3f67"))},meta:{title:"新建老人",sidebar:!1,activeMenu:"/station/stationOlder"}},{path:"/station/older/edit/:id",name:"stationOlderEdit",component:function(){return n.e("chunk-36b86e8a").then(n.bind(null,"3f67"))},meta:{title:"编辑老人",sidebar:!1,activeMenu:"/station/stationOlder"}}]},{path:"tplhouse",name:"tplhouse",component:mt,redirect:"/station/tplhouse",meta:{title:"户型管理",auth:["tplhouse"]},children:[{path:"",component:function(){return n.e("chunk-e047df54").then(n.bind(null,"6c12"))},meta:{title:"户型列表",sidebar:!1,breadcrumb:!1,auth:["tplhouse"]}},{path:"/station/tplhouse/add",name:"tplhouseAdd",component:function(){return n.e("chunk-5a23524a").then(n.bind(null,"07903"))},meta:{title:"新建户型",sidebar:!1,activeMenu:"/station/tplhouse"}},{path:"/station/tplhouse/edit/:id",name:"tplhouseEdit",component:function(){return n.e("chunk-5a23524a").then(n.bind(null,"07903"))},meta:{title:"编辑户型",sidebar:!1,activeMenu:"/station/tplhouse"}}]},{path:"house",name:"stationHouse",component:mt,redirect:"/station/house",meta:{title:"房号管理",auth:["stationHouse"]},children:[{path:"",component:function(){return n.e("chunk-2ff2a3e5").then(n.bind(null,"a5b2"))},meta:{title:"房号列表",sidebar:!1,breadcrumb:!1,auth:["stationHouse"]}},{path:"/station/house/add",name:"stationHouseAdd",component:function(){return n.e("chunk-97d9faee").then(n.bind(null,"12e9"))},meta:{title:"新建房号",sidebar:!1,activeMenu:"/station/stationHouse"}},{path:"/station/house/edit/:id",name:"stationHouseEdit",component:function(){return n.e("chunk-97d9faee").then(n.bind(null,"12e9"))},meta:{title:"编辑房号",sidebar:!1,activeMenu:"/station/stationHouse"}}]},{path:"device",name:"stationDeviceIndex",component:mt,redirect:"/station/device",meta:{title:"设备管理",auth:["device.list"]},children:[{path:"",component:function(){return n.e("chunk-2a8db916").then(n.bind(null,"f26e"))},meta:{title:"设备列表",sidebar:!1,breadcrumb:!1,auth:["device.list"]}},{path:"device/add",name:"stationDeviceAdd",component:function(){return n.e("chunk-a7b27e72").then(n.bind(null,"2e09"))},meta:{title:"新建设备配置",sidebar:!1,activeMenu:"/station/device"}},{path:"device/edit/:id",name:"stationDeviceEdit",component:function(){return n.e("chunk-a7b27e72").then(n.bind(null,"2e09"))},meta:{title:"编辑设备配置",sidebar:!1,activeMenu:"/station/device"}},{path:"device/info/:id",name:"stationDeviceInfo",component:function(){return n.e("chunk-2d0c1f0b").then(n.bind(null,"47d6"))},meta:{title:"详情查看",sidebar:!1,activeMenu:"/station/device"}},{path:"allocation/:id",name:"stationDeviceAllocationInfo",component:function(){return n.e("chunk-6965e4aa").then(n.bind(null,"c48a"))},meta:{title:"设备分配",sidebar:!1,activeMenu:"/station/index"}}]}]},Dt={path:"/person",component:it,redirect:"/user/list",name:"person",meta:{title:"人员管理",icon:"el-icon-help"},children:[{path:"user",component:mt,name:"userListIndex",meta:{title:"与安宝用户",auth:["person.user"]},children:[{path:"",component:function(){return n.e("person_user").then(n.bind(null,"2420"))},meta:{title:"与安宝联用户",sidebar:!1,breadcrumb:!1,auth:["person.user"]}},{path:"add",name:"userAdd",component:function(){return n.e("person_user").then(n.bind(null,"6c17"))},meta:{title:"新建",sidebar:!1,activeMenu:"/person/user"}},{path:"edit/:id",name:"memberEdit",component:function(){return n.e("person_user").then(n.bind(null,"6c17"))},meta:{title:"编辑",sidebar:!1,activeMenu:"/person/user"}},{path:"device/info/:id",name:"userDeviceInfo",component:function(){return n.e("person_user").then(n.bind(null,"0e2c"))},meta:{title:"服务设备详情查看",sidebar:!1,activeMenu:"/person/user",busiType:"user"}}]},{path:"butler",component:mt,redirect:"/person/butler",meta:{title:"管家管理",auth:["person.butler"]},children:[{path:"",name:"butlerListIndex",component:function(){return n.e("person_butler").then(n.bind(null,"f48f"))},meta:{title:"管家管理",sidebar:!1,breadcrumb:!1,auth:["person.butler"]}},{path:"add",name:"personalButlerAdd",component:function(){return n.e("person_butler").then(n.bind(null,"3170"))},meta:{title:"新建管家",sidebar:!1,activeMenu:"/person/butler"}},{path:"edit/:id",name:"personalButlerEdit",component:function(){return n.e("person_butler").then(n.bind(null,"3170"))},meta:{title:"编辑管家",sidebar:!1,activeMenu:"/person/butler"}},{path:"device/info/chamb/:id",name:"butlerDeviceInfo",component:function(){return n.e("person_butler").then(n.bind(null,"0e2c"))},meta:{title:"服务设备详情查看",sidebar:!1,activeMenu:"/person/butler",busiType:"chamb"}},{path:"info/:id",name:"butlerInfo",component:function(){return n.e("person_butler").then(n.bind(null,"3922"))},meta:{title:"管家详情查看",sidebar:!1,activeMenu:"/person/butler"}}]},{path:"station",component:mt,meta:{title:"服务站账号管理",auth:["person.station"]},children:[{path:"",name:"stationListIndex",component:function(){return n.e("person_user").then(n.bind(null,"19b0"))},meta:{title:"服务站账号管理",sidebar:!1,breadcrumb:!1,auth:["person.station"]}},{path:"add",name:"personalStationAdd",component:function(){return n.e("person_user").then(n.bind(null,"eef6"))},meta:{title:"新建服务站账号",sidebar:!1,activeMenu:"/person/station"}},{path:"edit/:id",name:"personalStationEdit",component:function(){return n.e("person_user").then(n.bind(null,"eef6"))},meta:{title:"编辑服务站账号",sidebar:!1,activeMenu:"/person/station"}},{path:"device/list/:id",name:"stationDeviceList",component:function(){return n.e("person_user").then(n.bind(null,"0e2c"))},meta:{title:"服务设备详情查看",sidebar:!1,activeMenu:"/person/station",busiType:"station"}},{path:"info/:stationId/:id",name:"personalStationInfo",component:function(){return n.e("chunk-2d0b16d0").then(n.bind(null,"2095"))},meta:{title:"服务站账号详情查看",sidebar:!1,activeMenu:"/person/station"}}]}]},Tt={path:"/service",component:it,redirect:"/service/org",name:"service",meta:{title:"服务管理",icon:"el-icon-help",auth:["service","service.org"]},children:[{path:"service",name:"serviceIndex",component:mt,redirect:"/service/index",meta:{title:"服务信息",auth:["service"]},children:[{path:"",component:function(){return n.e("chunk-7f68a383").then(n.bind(null,"64da"))},meta:{title:"服务信息列表",sidebar:!1,breadcrumb:!1,auth:["service"]}},{path:"edit/:id",name:"serviceEdit",component:function(){return n.e("chunk-6a4c9664").then(n.bind(null,"f057"))},meta:{title:"编辑服务信息",sidebar:!1,activeMenu:"/service/service"}},{path:"add",name:"serviceAdd",component:function(){return n.e("chunk-6a4c9664").then(n.bind(null,"f057"))},meta:{title:"新建服务信息",sidebar:!1,activeMenu:"/service/service"}},{path:"info/:id",name:"serviceInfo",component:function(){return n.e("chunk-2cc8b2d8").then(n.bind(null,"4ce0"))},meta:{title:"服务信息详情查看",sidebar:!1,activeMenu:"/service/service",auth:["service"]}},{path:"price/:id",name:"servicePriceIndex",component:function(){return n.e("chunk-e151fbec").then(n.bind(null,"d000"))},meta:{title:"配置价格列表",sidebar:!1,activeMenu:"/service/service"}},{path:"price/edit/:id",name:"servicePriceEdit",component:function(){return n.e("chunk-6ec3bedc").then(n.bind(null,"499d"))},meta:{title:"配置价格",sidebar:!1,activeMenu:"/service/service"}},{path:"price/add/:serviceId",name:"servicePriceAdd",component:function(){return n.e("chunk-6ec3bedc").then(n.bind(null,"499d"))},meta:{title:"配置价格",sidebar:!1,activeMenu:"/service/service"}}]},{path:"spec",name:"serviceSpecIndex",component:mt,redirect:"/service/spec/index",meta:{title:"产品型号管理",auth:["service"]},children:[{path:"",component:function(){return n.e("chunk-0b75a5ee").then(n.bind(null,"8376"))},meta:{title:"产品型号管理",sidebar:!1,breadcrumb:!1,auth:["service"]}},{path:"edit/:id",name:"serviceSpecEdit",component:function(){return n.e("chunk-3d04d692").then(n.bind(null,"b828"))},meta:{title:"编辑产品型号",sidebar:!1,activeMenu:"/service/spec"}},{path:"add",name:"serviceSpecAdd",component:function(){return n.e("chunk-3d04d692").then(n.bind(null,"b828"))},meta:{title:"新建产品型号",sidebar:!1,activeMenu:"/service/spec"}},{path:"info/:id",name:"serviceSpecInfo",component:function(){return n.e("chunk-221bf3a5").then(n.bind(null,"d4d2"))},meta:{title:"产品型号详情查看",sidebar:!1,activeMenu:"/service/spec"}},{path:"org-price-list/:id",name:"specOrgPriceIndex",component:function(){return n.e("chunk-53c4c40a").then(n.bind(null,"2818"))},meta:{title:"配置价格列表",sidebar:!1,activeMenu:"/service/spec"}},{path:"org-price/edit/:id",name:"specOrgPriceEdit",component:function(){return n.e("chunk-34edc248").then(n.bind(null,"f384"))},meta:{title:"配置价格",sidebar:!1,activeMenu:"/service/spec"}},{path:"org-price/add/:modelId",name:"specOrgPriceAdd",component:function(){return n.e("chunk-34edc248").then(n.bind(null,"f384"))},meta:{title:"配置价格",sidebar:!1,activeMenu:"/service/spec"}}]},{path:"order",name:"serviceOrderIndex",component:mt,redirect:"/service/order/index",meta:{title:"订单管理",auth:["service","service.org"]},children:[{path:"",component:function(){return n.e("chunk-76b299c6").then(n.bind(null,"1c82"))},meta:{title:"订单管理",sidebar:!1,breadcrumb:!1,auth:["service","service.org"]}},{path:"edit/:id",name:"serviceOrderEdit",component:function(){return n.e("chunk-2bbcde3c").then(n.bind(null,"41a8"))},meta:{title:"编辑订单",sidebar:!1,activeMenu:"/service/order"}},{path:"add",name:"serviceOrderAdd",component:function(){return n.e("chunk-2bbcde3c").then(n.bind(null,"41a8"))},meta:{title:"新建订单",sidebar:!1,activeMenu:"/service/order"}},{path:"info/:id",name:"serviceOrderInfo",component:function(){return n.e("chunk-228cfa60").then(n.bind(null,"a47c"))},meta:{title:"订单详情查看",sidebar:!1,activeMenu:"/service/order",auth:["service","service.org"]}}]},{path:"record",name:"serviceRecordIndex",component:mt,redirect:"/service/record/index",meta:{title:"服务记录",auth:["service","service.org"]},children:[{path:"event/detail/:id",name:"servicePlatformEventDetail",component:function(){return n.e("multilevel_menu_example").then(n.bind(null,"4c9e"))},meta:{title:"事件详情",sidebar:!1}},{path:"",name:"serviceRecordList",component:function(){return n.e("chunk-79e35d2b").then(n.bind(null,"f216"))},meta:{title:"服务记录",sidebar:!1,breadcrumb:!1,auth:["service","service.org"]}},{path:"detail/:id",name:"serviceRecordDetail",component:function(){return n.e("chunk-695a0040").then(n.bind(null,"4f9a"))},meta:{title:"服务记录查看",sidebar:!1,activeMenu:"/service/record",auth:["service","service.org"]}}]},{path:"feedback",name:"serviceFeedbackIndex",component:mt,redirect:"/service/feedback/index",meta:{title:"反馈信息",activeMenu:"/service/feedback",auth:["service.feedback"]},children:[{path:"index",component:function(){return n.e("chunk-5458a1a2").then(n.bind(null,"08a5"))},meta:{title:"反馈信息",sidebar:!1,breadcrumb:!1,activeMenu:"/service/feedback",auth:["service.feedback"]}},{path:"info/:id",name:"serviceFeedbackInfo",component:function(){return n.e("chunk-58b14f72").then(n.bind(null,"1280"))},meta:{title:"反馈信息详情查看",sidebar:!1,auth:["service.feedback"]}}]}]},Ot={path:"/family",component:it,redirect:"/family/family",name:"family",meta:{title:"家庭管理",icon:"el-icon-help"},children:[{path:"family",component:mt,name:"familyListIndex",meta:{title:"家庭管理",auth:["family.family"]},children:[{path:"",component:function(){return n.e("family").then(n.bind(null,"7701"))},meta:{title:"列表",sidebar:!1,breadcrumb:!1,auth:["family.family"]}},{path:"add",name:"familyAdd",component:function(){return n.e("family").then(n.bind(null,"c624"))},meta:{title:"新建",sidebar:!1,activeMenu:"/family/family"}},{path:"edit/:id",name:"familyEdit",component:function(){return n.e("family").then(n.bind(null,"c624"))},meta:{title:"修改",sidebar:!1,activeMenu:"/family/family"}},{path:"info/:id",name:"familyInfo",component:function(){return n.e("family").then(n.bind(null,"7f5d"))},meta:{title:"查看",sidebar:!1,activeMenu:"/family/family"}}]},{path:"older",component:mt,redirect:"/family/older",meta:{title:"被监护人管理",auth:["family.older"]},children:[{path:"",name:"olderListIndex",component:function(){return n.e("older").then(n.bind(null,"2565"))},meta:{title:"列表",sidebar:!1,breadcrumb:!1,auth:["family.older"]}},{path:"add",name:"olderAdd",component:function(){return n.e("older").then(n.bind(null,"6278"))},meta:{title:"新建",sidebar:!1,activeMenu:"/family/older"}},{path:"edit/:id",name:"olderEdit",component:function(){return n.e("older").then(n.bind(null,"6278"))},meta:{title:"修改",sidebar:!1,activeMenu:"/family/older"}},{path:"info/:id",name:"olderInfo",component:function(){return n.e("older").then(n.bind(null,"75ab"))},meta:{title:"查看",sidebar:!1,activeMenu:"/family/older"}}]},{path:"contact",component:mt,meta:{title:"紧急联系人管理",auth:["family.contact"]},children:[{path:"",name:"contactListIndex",component:function(){return n.e("contact").then(n.bind(null,"1833"))},meta:{title:"列表",sidebar:!1,breadcrumb:!1,auth:["family.contact"]}},{path:"add",name:"contactAdd",component:function(){return n.e("contact").then(n.bind(null,"b165"))},meta:{title:"新建",sidebar:!1,activeMenu:"/family/contact"}},{path:"edit/:id",name:"contactEdit",component:function(){return n.e("contact").then(n.bind(null,"b165"))},meta:{title:"修改",sidebar:!1,activeMenu:"/family/contact"}},{path:"info/:id",name:"contactInfo",component:function(){return n.e("contact").then(n.bind(null,"5337"))},meta:{title:"查看",sidebar:!1,activeMenu:"/family/contact"}}]}]},Et={path:"/report",component:it,redirect:"/report/daily",name:"report",meta:{sidebar:!1,title:"报告",icon:"el-icon-help",auth:["report"]},children:[{path:"sleep",name:"sleepDaily",component:function(){return n.e("daily").then(n.bind(null,"df7f"))},meta:{title:"睡眠报告",breadcrumb:!1}}]},At={path:"/memberCard",component:it,redirect:"/memberCard/delivery",name:"memberCard",meta:{title:"会员服务",icon:"el-icon-help",auth:["memberCard.delivery"]},children:[{path:"delivery",name:"deliveryIndex",component:mt,redirect:"/delivery/index",meta:{title:"提货管理",auth:["memberCard.delivery"]},children:[{path:"",component:function(){return n.e("chunk-c456c89c").then(n.bind(null,"b169"))},meta:{title:"提货信息列表",sidebar:!1,breadcrumb:!1,auth:["memberCard.delivery"]}},{path:"edit/:id",name:"deliveryEdit",component:function(){return n.e("chunk-4357e93a").then(n.bind(null,"7088"))},meta:{title:"编辑提货信息/发货",sidebar:!1,activeMenu:"/delivery/delivery"}},{path:"info/:id",name:"deliveryInfo",component:function(){return n.e("chunk-4b706208").then(n.bind(null,"f0b4"))},meta:{title:"提货信息详情查看",sidebar:!1,activeMenu:"/delivery/delivery",auth:["service"]}}]}]};c["default"].use(s["a"]);var Lt=[{path:"/login",name:"login",component:function(){return n.e("chunk-1057cd98").then(n.bind(null,"dd7b"))},meta:{title:"登录",i18n:"route.login"}},{path:"/",component:it,redirect:"/dashboard",children:[{path:"personal",component:mt,redirect:"/personal/setting",meta:{title:"个人中心",breadcrumb:!1},children:[{path:"setting",name:"personalSetting",component:function(){return n.e("chunk-1512520a").then(n.bind(null,"09b3"))},meta:{title:"个人设置",i18n:"route.personal.setting"}},{path:"edit/password",name:"personalEditPassword",component:function(){return n.e("chunk-2d0c7e72").then(n.bind(null,"5331"))},meta:{title:"修改密码",i18n:"route.personal.editpassword"}},{path:"edit/:id",name:"personalStationOrgEdit",component:function(){return n.e("chunk-3fb9bb48").then(n.bind(null,"6ec2"))},meta:{title:"修改信息",sidebar:!1}}]},{path:"reload",name:"reload",component:function(){return n.e("chunk-2d0b59f4").then(n.bind(null,"1a4b"))}}]},{path:"/mp",name:"mp",component:function(){return n.e("chunk-cbed2192").then(n.bind(null,"0905"))},meta:{title:"跳转小程序",i18n:"route.mp"}},{path:"/call",name:"call",component:function(){return n.e("chunk-687551bc").then(n.bind(null,"1e00"))},meta:{title:"SIP",i18n:"route.mp"}},{path:"/call2",name:"call2",component:function(){return n.e("chunk-75a17c6b").then(n.bind(null,"b3d9"))},meta:{title:"SIP2",i18n:"route.mp"}},{path:"/test-simple",name:"test-simple",component:function(){return n.e("chunk-2c13f016").then(n.bind(null,"eaf1"))},meta:{title:"简单条件编译测试"}}],Nt=[{meta:{title:"演示",icon:"sidebar-default"},children:[ut,_t,It,At,Dt,Tt,Et,Ot]}],$t=[{path:"*",component:function(){return n.e("chunk-1f24b7aa").then(n.bind(null,"8cdb"))},meta:{title:"404",sidebar:!1}}],Mt=new s["a"]({routes:Lt,mode:"history"}),Bt=s["a"].prototype.push;s["a"].prototype.push=function(e){return Bt.call(this,e).catch((function(e){return e}))};var Rt=s["a"].prototype.replace;s["a"].prototype.replace=function(e){return Rt.call(this,e).catch((function(e){return e}))},Mt.beforeEach(function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,n,i){var c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("x",n.path,t.path),r["a"].state.settings.enableProgress&&l.a.start(),!r["a"].getters["user/isLogin"]||r["a"].state.menu.isGenerate){e.next=12;break}return r["a"].state.settings.enableTabbar&&r["a"].commit("tabbar/recoveryStorage",r["a"].state.user.account),"system"===r["a"].state.user.member.role?Lt[1].children=[{path:"dashboard",redirect:"/station/org",meta:{title:"机构管理",sidebar:!1}}].concat(Object(o["a"])(Lt[1].children)):Lt[1].children=[{path:"dashboard",redirect:"/service/platform/dashboard",meta:{title:"未处理事件",sidebar:!1}}].concat(Object(o["a"])(Lt[1].children)),Mt.matcher=new s["a"]({routes:Lt}).matcher,e.next=8,r["a"].dispatch("menu/generateRoutes",{asyncRoutes:Nt,currentPath:t.path});case 8:c=e.sent,c.push.apply(c,$t),c.forEach((function(e){Mt.addRoute(e)})),i(Object(a["a"])(Object(a["a"])({},t),{},{replace:!0}));case 12:r["a"].state.menu.isGenerate&&r["a"].commit("menu/setHeaderActived",t.path),r["a"].getters["user/isLogin"]?t.name&&(0!==t.matched.length?"login"==t.name?i({name:"dashboard",replace:!0}):r["a"].state.settings.enableDashboard||"dashboard"!=t.name||(r["a"].state.settings.enableTabbar&&r["a"].state.tabbar.list.length>0?i({path:r["a"].state.tabbar.list[0].path,replace:!0}):r["a"].getters["menu/sidebarRoutes"].length>0&&i({path:r["a"].getters["menu/sidebarRoutes"][0].path,replace:!0})):i({path:"/404"})):"login"!=t.name&&"mp"!=t.name&&i({name:"login",query:{redirect:t.fullPath}}),i();case 15:case"end":return e.stop()}}),e)})));return function(t,n,a){return e.apply(this,arguments)}}()),Mt.afterEach((function(){r["a"].state.settings.enableProgress&&l.a.done()}));t["a"]=Mt},a244:function(e,t,n){var a={"./404.svg":"0143","./example-crown.svg":"770f","./example-emotion-laugh-line.svg":"1ec3","./example-emotion-line.svg":"98f4","./example-emotion-unhappy-line.svg":"ade1","./example-star.svg":"253b","./example-vip.svg":"af15","./eye-open.svg":"c80b","./eye.svg":"7364","./icon-refresh.svg":"fff5","./image-load-fail.svg":"3a6c","./index-component.svg":"1470","./index-document.svg":"dbed","./index-page.svg":"a8de","./loading-audio.svg":"fc86","./loading-ball-triangle.svg":"3f1d","./loading-bars.svg":"f05f","./loading-circles.svg":"dd21","./loading-grid.svg":"2933","./loading-hearts.svg":"ba95","./loading-oval.svg":"d8b1","./loading-puff.svg":"6e89","./loading-rings.svg":"79df","./loading-spinning-circles.svg":"0069","./loading-tail-spin.svg":"576c","./loading-three-dots.svg":"0c6e","./password.svg":"b009","./sidebar-breadcrumb.svg":"999e","./sidebar-default.svg":"d46e","./sidebar-menu.svg":"e17a","./toolbar-collapse.svg":"7f39","./toolbar-reload.svg":"1676","./toolbar-theme.svg":"98c0","./user.svg":"6aed"};function o(e){var t=i(e);return n(t)}function i(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}o.keys=function(){return Object.keys(a)},o.resolve=i,e.exports=o,o.id="a244"},a497:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("bm-overlay",{ref:"customOverlay",class:{sample:!0},style:e.pointColor,attrs:{pane:"labelPane"},on:{draw:e.draw}},[n("div",{staticClass:"title-wrap"},[n("span",[e._v(e._s(e.content.stationName))]),"2"===e.$store.state.user.member.systemVersion?n("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}],staticClass:"join-btn",on:{click:function(t){return e.onClick(e.content.stationId)}}},[e._v("进入")]):e._e()]),n("div",{staticClass:"field"},[n("span",{staticClass:"field-name"},[e._v("负责人: ")]),n("span",[e._v(e._s(e.content.director||"-"))])]),n("div",{staticClass:"field"},[n("span",{staticClass:"field-name"},[e._v("电话: ")]),n("span",[e._v(e._s(e.content.phone||"-"))])]),n("div",{staticClass:"field"},[n("span",{staticClass:"field-name"},[e._v("地址: ")]),n("span",[e._v(e._s(e.content.addr||"-"))])])])},o=[],i=(n("d81d"),n("bd0c")),c={name:"StationBMapDot",components:{BmOverlay:i["BmOverlay"]},props:["content","position","color","onClick"],data:function(){return{pointColor:""}},watch:{position:{handler:function(){this.$refs.customOverlay.reload()},deep:!0}},mounted:function(){this.pointColor=this.color},methods:{draw:function(e){var t=e.el,n=e.BMap,a=e.map,o=this.position,i=o.lng,c=o.lat,s=a.pointToOverlayPixel(new n.Point(i,c));t.style.left=s.x-250+"px",t.style.top=s.y-140+"px"}}},s=c,r=(n("5966"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"63c7a4c4",null);t["default"]=d.exports},a4b8:function(e,t,n){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},a8de:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-index-page",use:"icon-index-page-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-index-page"><defs><style type="text/css"></style></defs><path d="M0 60.235294l0 903.529412 1024 0 0-903.529412-1024 0zM963.764706 120.470588l0 180.705882-903.529412 0 0-180.705882 903.529412 0zM60.235294 903.529412l0-542.117647 903.529412 0 0 542.117647-903.529412 0zM421.647059 240.941176l-60.235294 0 0-60.235294 60.235294 0 0 60.235294zM301.176471 240.941176l-60.235294 0 0-60.235294 60.235294 0 0 60.235294zM180.705882 240.941176l-60.235294 0 0-60.235294 60.235294 0 0 60.235294z" p-id="9982" /></symbol>'});c.a.add(s);t["default"]=s},aae7:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"map"},[n("el-autocomplete",{staticClass:"search",attrs:{"fetch-suggestions":e.onSearch,"trigger-on-focus":!1,clearable:"","prefix-icon":"el-icon-location-information",placeholder:"请输入地址关键字"},on:{select:e.onSelect},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.item;return[n("div",{staticClass:"search-name"},[e._v(e._s(a.name))]),n("span",{staticClass:"search-address",attrs:{title:a.district+a.address}},[e._v(e._s(a.district+a.address))])]}}]),model:{value:e.search,callback:function(t){e.search=t},expression:"search"}}),n("div",{style:"height:"+e.realHeight+";",attrs:{id:"amap"}})],1)},o=[],i=(n("a9e3"),n("99af"),n("d81d"),n("ac1f"),n("841c"),n("b0c0"),{name:"AmapMarker",props:{v:{type:String,default:"1.4.15"},appkey:{type:String,default:""},height:{type:[Number,String],default:500},lnglat:{type:Array,default:function(){return[]}}},data:function(){return{search:"",searchOption:{citylimit:!0},map:"",marker:"",autoComplete:"",placeSearch:""}},computed:{realHeight:function(){return"string"==typeof this.height?this.height:"".concat(this.height,"px")}},created:function(){},mounted:function(){var e=this;if("undefined"===typeof AMap){var t=document.createElement("script");t.charset="utf-8",t.src="https://webapi.amap.com/maps?v=".concat(this.v,"&key=").concat(this.appkey,"&plugin=AMap.Autocomplete"),document.head.appendChild(t),t.onload=function(){e.init()}}else this.$nextTick((function(){e.init()}))},methods:{init:function(){var e=this;this.map=new AMap.Map("amap",{zoom:12}),AMap.plugin("AMap.ToolBar",(function(){var t=new AMap.ToolBar;e.map.addControl(t)})),this.marker=new AMap.Marker({draggable:!0,cursor:"move"}),this.marker.on("dragend",(function(t){e.$emit("update:lnglat",[t.lnglat.lng,t.lnglat.lat])})),this.autoComplete=new AMap.Autocomplete,this.map.on("click",(function(t){e.addMarket(t.lnglat.getLng(),t.lnglat.getLat())})),2==this.lnglat.length&&this.addMarket(this.lnglat[0],this.lnglat[1])},addMarket:function(e,t){this.map.remove(this.marker),this.marker.setPosition([e,t]),this.map.add(this.marker),this.map.setFitView(),this.$emit("update:lnglat",[e,t])},onSearch:function(e,t){this.autoComplete.search(e,(function(e,n){t(n.tips)}))},onSelect:function(e){this.search=e.name,e.location?this.addMarket(e.location.lng,e.location.lat):this.map.setCity(e.adcode)}}}),c=i,s=(n("efd5"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"0db7337e",null);t["default"]=r.exports},ab51:function(e,t,n){},ab7c:function(e,t,n){"use strict";n("f269")},ade1:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-example-emotion-unhappy-line",use:"icon-example-emotion-unhappy-line-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-example-emotion-unhappy-line"><defs><style type="text/css"></style></defs><path d="M512 938.666667C276.352 938.666667 85.333333 747.648 85.333333 512S276.352 85.333333 512 85.333333s426.666667 191.018667 426.666667 426.666667-191.018667 426.666667-426.666667 426.666667z m0-85.333334a341.333333 341.333333 0 1 0 0-682.666666 341.333333 341.333333 0 0 0 0 682.666666z m-213.333333-128a213.333333 213.333333 0 0 1 426.666666 0h-85.333333a128 128 0 0 0-256 0H298.666667z m42.666666-256a64 64 0 1 1 0-128 64 64 0 0 1 0 128z m341.333334 0a64 64 0 1 1 0-128 64 64 0 0 1 0 128z" p-id="1804" /></symbol>'});c.a.add(s);t["default"]=s},af15:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-example-vip",use:"icon-example-vip-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-example-vip"><defs><style type="text/css"></style></defs><path d="M270.218971 121.212343h483.474286a29.257143 29.257143 0 0 1 23.3472 11.644343l188.416 249.885257a29.257143 29.257143 0 0 1-1.8432 37.419886L533.942857 887.749486a29.257143 29.257143 0 0 1-43.037257 0.058514L60.416 421.595429a29.257143 29.257143 0 0 1-1.930971-37.390629l188.328228-251.260343a29.257143 29.257143 0 0 1 23.405714-11.702857z" fill="#FFA100" p-id="1151" /><path d="M768.292571 121.212343l197.163886 261.558857a29.257143 29.257143 0 0 1-1.8432 37.390629L532.714057 889.066057a11.702857 11.702857 0 0 1-20.304457-7.899428L512 257.024l256.292571-135.840914z" fill="#FFC663" p-id="1152" /><path d="M721.598171 386.340571a29.257143 29.257143 0 0 1 0.994743 1.024l22.7328 23.873829a29.257143 29.257143 0 0 1 0 40.3456l-189.410743 198.890057-22.7328 23.873829a29.257143 29.257143 0 0 1-1.726171 1.667657l1.755429-1.667657a29.4912 29.4912 0 0 1-19.456 9.0112 28.935314 28.935314 0 0 1-18.080915-4.9152 30.193371 30.193371 0 0 1-4.856685-4.096l1.960228 1.872457-0.965486-0.877714-0.994742-0.994743-22.7328-23.873829-189.410743-198.890057a29.257143 29.257143 0 0 1 0-40.374857l22.7328-23.844572a29.257143 29.257143 0 0 1 42.364343 0L512 563.960686l168.228571-176.596115a29.257143 29.257143 0 0 1 41.3696-1.024z" fill="#FFFFFF" p-id="1153" /></symbol>'});c.a.add(s);t["default"]=s},af20:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"header"},[n("div",[n("div",{staticClass:"title"},[e._v(e._s(e.title))]),n("div",{staticClass:"content"},[e._t("content",[e._v(e._s(e.content))])],2)]),n("div",[e._t("default")],2)])},o=[],i={name:"PageHeader",props:{title:{type:String,required:!0},content:{type:String,default:""}}},c=i,s=(n("3eac"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"10ccd334",null);t["default"]=r.exports},af53:function(e,t,n){},b009:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-password",use:"icon-password-usage",viewBox:"0 0 128 128",content:'<symbol xmlns="http://www.w3.org/2000/svg" viewBox="0 0 128 128" id="icon-password"><path d="M108.8 44.322H89.6v-5.36c0-9.04-3.308-24.163-25.6-24.163-23.145 0-25.6 16.881-25.6 24.162v5.361H19.2v-5.36C19.2 15.281 36.798 0 64 0c27.202 0 44.8 15.281 44.8 38.961v5.361zm-32 39.356c0-5.44-5.763-9.832-12.8-9.832-7.037 0-12.8 4.392-12.8 9.832 0 3.682 2.567 6.808 6.407 8.477v11.205c0 2.718 2.875 4.962 6.4 4.962 3.524 0 6.4-2.244 6.4-4.962V92.155c3.833-1.669 6.393-4.795 6.393-8.477zM128 64v49.201c0 8.158-8.645 14.799-19.2 14.799H19.2C8.651 128 0 121.359 0 113.201V64c0-8.153 8.645-14.799 19.2-14.799h89.6c10.555 0 19.2 6.646 19.2 14.799z" /></symbol>'});c.a.add(s);t["default"]=s},b3cc:function(e,t,n){"use strict";n.r(t);var a={callDialogShow:!1,callDialogData:{},keyAuthorizationDialogShow:!1,keyAuthorizationDialogData:{},sendSmsDialogShow:!1,sendSmsDialogData:{},resultModifyDialogShow:!1,resultModifyDialogData:{},audioDialogShow:!1,audioDialogData:{}},o={},i={setCallDialogShow:function(e,t){var n=e.commit;n("SET_CALL_DIALOG_SHOW",t)},setKeyAuthorizationDialogShow:function(e,t){var n=e.commit;n("SET_KEY_AUTHORIZATION_DIALOG_SHOW",t)},setSendSmsDialogShow:function(e,t){var n=e.commit;n("SET_SEND_SMS_DIALOG_SHOW",t)},setResultModifyDialogShow:function(e,t){var n=e.commit;n("SET_RESULT_MODIFY_DIALOG_SHOW",t)},setAudioDialogShow:function(e,t){var n=e.commit;n("SET_AUDIO_DIALOG_SHOW",t)}},c={SET_CALL_DIALOG_SHOW:function(e,t){if(!0===t.status){e.callDialogShow=!0;var n=JSON.parse(JSON.stringify(t.vo));t.type&&(n["type"]=t.type),e.callDialogData=n}else e.callDialogShow=!1,e.callDialogData={}},SET_KEY_AUTHORIZATION_DIALOG_SHOW:function(e,t){!0===t.status?(e.keyAuthorizationDialogShow=!0,e.keyAuthorizationDialogData=t.vo):(e.keyAuthorizationDialogShow=!1,e.keyAuthorizationDialogData={})},SET_SEND_SMS_DIALOG_SHOW:function(e,t){!0===t.status?(e.sendSmsDialogShow=!0,e.sendSmsDialogData=t.vo):(e.sendSmsDialogShow=!1,e.sendSmsDialogData={})},SET_RESULT_MODIFY_DIALOG_SHOW:function(e,t){!0===t.status?(e.resultModifyDialogShow=!0,e.resultModifyDialogData=t.vo):(e.resultModifyDialogShow=!1,e.resultModifyDialogData={})},SET_AUDIO_DIALOG_SHOW:function(e,t){!0===t.status?(e.audioDialogShow=!0,e.audioDialogData=t.vo):(e.audioDialogShow=!1,e.audioDialogData={})}};t["default"]={namespaced:!0,state:a,actions:i,getters:o,mutations:c}},b963:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d3b7"),n("ddb0"),n("313e")),c={name:"ChartLine",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=this.chart||i.init(this.$refs.chart);var e={tooltip:{trigger:"axis"},xAxis:[{type:"category",data:this.data.names,axisLine:{lineStyle:{color:"#0c3b71"}},axisLabel:{show:!0,color:"#49DBFF",fontSize:14,padding:[0,0,0,-10]}}],grid:{left:50,right:50,top:40,bottom:40},yAxis:[{type:"value",splitNumber:3,splitLine:{lineStyle:{color:"#0c3b71"}},axisLine:{lineStyle:{color:"#0c3b71"}},axisLabel:{color:"#49DBFF",formatter:"{value}"},nameTextStyle:{color:"#0c3b71"},splitArea:{show:!1}}],series:[{name:"数量",type:"line",data:this.data.values,lineStyle:{normal:{width:4,color:{type:"linear",colorStops:[{offset:0,color:"#9f6afb"},{offset:1,color:"#48D8BF"}],globalCoord:!1},shadowColor:"rgba(72,216,191, 0.3)",shadowBlur:10,shadowOffsetY:20}},itemStyle:{normal:{color:"#fff",borderWidth:10,borderColor:"#A9F387"}},smooth:!0}]};this.chart.setOption(e,!0)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},ba15:function(e,t,n){},ba95:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-hearts",use:"icon-loading-hearts-usage",viewBox:"0 0 140 64",content:'<symbol viewBox="0 0 140 64" xmlns="http://www.w3.org/2000/svg" fill="#fff" id="icon-loading-hearts">\r\n    <path d="M30.262 57.02L7.195 40.723c-5.84-3.976-7.56-12.06-3.842-18.063 3.715-6 11.467-7.65 17.306-3.68l4.52 3.76 2.6-5.274c3.717-6.002 11.47-7.65 17.305-3.68 5.84 3.97 7.56 12.054 3.842 18.062L34.49 56.118c-.897 1.512-2.793 1.915-4.228.9z" fill-opacity=".5">\r\n        <animate attributeName="fill-opacity" begin="0s" dur="1.4s" values="0.5;1;0.5" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </path>\r\n    <path d="M105.512 56.12l-14.44-24.272c-3.716-6.008-1.996-14.093 3.843-18.062 5.835-3.97 13.588-2.322 17.306 3.68l2.6 5.274 4.52-3.76c5.84-3.97 13.592-2.32 17.307 3.68 3.718 6.003 1.998 14.088-3.842 18.064L109.74 57.02c-1.434 1.014-3.33.61-4.228-.9z" fill-opacity=".5">\r\n        <animate attributeName="fill-opacity" begin="0.7s" dur="1.4s" values="0.5;1;0.5" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </path>\r\n    <path d="M67.408 57.834l-23.01-24.98c-5.864-6.15-5.864-16.108 0-22.248 5.86-6.14 15.37-6.14 21.234 0L70 16.168l4.368-5.562c5.863-6.14 15.375-6.14 21.235 0 5.863 6.14 5.863 16.098 0 22.247l-23.007 24.98c-1.43 1.556-3.757 1.556-5.188 0z"></path>\r\n</symbol>'});c.a.add(s);t["default"]=s},bb60:function(e,t,n){},bc0c:function(e,t,n){(function(e,t){t.fn.inputNumber=function(){e(this).css({imeMode:"disabled","-moz-user-select":"none"}),e(this).bind("keypress",(function(e){return 1!=e.ctrlKey&&1!=e.shiftKey&&(e.which>=48&&e.which<=57&&0==e.ctrlKey&&0==e.shiftKey||0==e.which||8==e.which||(1!=e.ctrlKey||99!=e.which&&e.which,!1))})).bind("contextmenu",(function(){return!1})).bind("selectstart",(function(){return!1})).bind("paste",(function(){return!1}))}}).call(this,n("1157"),n("1157"))},bc7c:function(e,t,n){"use strict";n("97bc")},bdf9:function(e,t,n){"use strict";n("2fbd")},be35:function(e,t,n){},bf85:function(e,t,n){"use strict";n("42f0")},c00a:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return 0===e.name.indexOf("el-icon-")||0===e.name.indexOf("ri-")?n("i",{class:e.name}):n("svg",e._g({staticClass:"svg-icon",attrs:{"aria-hidden":"true"}},e.$listeners),[n("use",{attrs:{"xlink:href":"#icon-"+e.name}})])},o=[],i={name:"SvgIcon",props:{name:{type:String,required:!0}}},c=i,s=(n("2d40"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"61414faf",null);t["default"]=r.exports},c119:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[e.check()?e._t("default"):e._t("no-auth")],2)},o=[],i=n("4260"),c={name:"Auth",props:{value:{type:[String,Array],default:""}},methods:{check:function(){return Object(i["a"])(this.value)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},c3d0:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d81d"),n("b0c0"),n("d3b7"),n("ddb0"),n("99af"),n("313e")),c={name:"ChartMutiBarLine",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=i.init(this.$refs.chart),console.log("this.data",this.data.names);var e=[],t=[],n=[];this.data.series.map((function(a){var o=1e3*Math.random(),i=1e3*Math.random(),c=1e3*Math.random(),s=1e3*Math.random(),r=1e3*Math.random(),d=1e3*Math.random(),l=1e3*Math.random();n.push(a.name),e.push({name:a.name,type:"line",yAxisIndex:1,smooth:!0,showAllSymbol:!0,symbol:"circle",symbolSize:8,data:[12+o,36+i,114+c,722+s,1123+r,54+d,0+l,0+o,0+o,0+o,0+o,0+o]}),t.push({name:a.name,type:"bar",barWidth:15,itemStyle:{normal:{}},data:a.values})}));var a=this.data.names.map((function(e){return e+"月"})),o=o={grid:{top:50,left:70,bottom:40},tooltip:{trigger:"axis",axisPointer:{type:"shadow",label:{show:!0}}},legend:{data:n,top:"2%",right:"10",textStyle:{color:"rgba(250,250,250,0.6)",fontSize:14}},xAxis:{data:a,axisLine:{show:!0,lineStyle:{color:"#26D9FF",width:2}},axisTick:{show:!0},axisLabel:{show:!0,textStyle:{color:"#49DBFF",fontSize:14}},splitArea:{show:!0,areaStyle:{color:["rgba(250,250,250,0.1)","rgba(250,250,250,0)"]}}},yAxis:[{type:"value",nameTextStyle:{color:"#ebf8ac",fontSize:14},splitLine:{show:!1},axisTick:{show:!0},axisLine:{show:!0,lineStyle:{color:"#26D9FF",width:2}},axisLabel:{show:!0,textStyle:{color:"#49DBFF",fontSize:14}}},{type:"value",nameTextStyle:{color:"#ebf8ac",fontSize:16},position:"right",splitLine:{show:!1},axisTick:{show:!1},axisLine:{show:!1},axisLabel:{show:!0,formatter:"{value} %",textStyle:{color:"#49DBFF",fontSize:14}}}],series:[].concat(e,t)};this.chart.setOption(o)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},c40b:function(e,t,n){"use strict";n.r(t);var a={dot:!0,number:10,unprocessedNumber:0,text:"热门"},o={},i={},c={switchDot:function(e){e.dot=!e.dot},setNumber:function(e,t){e.number=t},setUnprocessedNumber:function(e,t){e.unprocessedNumber=t},setText:function(e,t){e.text=t}};t["default"]={namespaced:!0,state:a,actions:i,getters:o,mutations:c}},c435:function(e,t,n){},c50b:function(e,t,n){},c579:function(e,t,n){"use strict";n("686b")},c6db:function(e,t,n){"use strict";n("6b0d")},c729:function(e,t,n){"use strict";n("86a3")},c78e:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d3b7"),n("ddb0"),n("b680"),n("99af"),n("b0c0"),n("313e")),c={name:"ChartPie",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){this.chart=this.chart||i.init(this.$refs.chart);for(var e=this.data.names,t=this.data.values,n=[],a=0,o=0;o<e.length;o++)n.push({name:e[o],value:t[o]}),a+=t[o]||0;for(var c=[],s=0;s<e.length;s++)c.push({name:e[s],value:100});console.log(n,c);var r=["#9f6afb","#324dfe","#7f1df4","#5377fd","#536EBF","#6E93FF","#5FEBFF","#8AF1FF","#8AF1FF"],d={legend:{type:"scroll",bottom:20,data:e,formatter:function(e){return"{title|".concat(e,"}")},textStyle:{rich:{title:{color:"#BCF0FE",width:70,fontSize:14},percent:{color:"#BCF0FE",fontSize:14}}}},tooltip:{trigger:"item"},series:[{itemStyle:{normal:{color:function(e){return r[e.dataIndex]},borderColor:"#172659",borderWidth:3,label:{show:!0,position:"outside",color:"#ddd",formatter:function(e){var t=0;return t=a>0?(e.value/a*100).toFixed(0):0,"{title|".concat(e.name,"}\n\n数量: ").concat(e.value,"({percent|").concat(t,"}{percent|%})")},textStyle:{rich:{title:{align:"left",color:"#BCF0FE",fontSize:14},percent:{fontSize:12}}}},labelLine:{length:10,length2:20,show:!0,color:"#00ffff"}}},type:"pie",radius:["25%","46%"],center:["50%","46%"],labelLine:{normal:{lineStyle:{color:"#34569D"}}},label:{show:!1},data:n},{itemStyle:{normal:{color:"rgba(62,109,255,0.1)"}},type:"pie",hoverAnimation:!1,radius:["20%","51%"],center:["50%","46%"],label:{normal:{show:!1}},tooltip:{show:!1},data:[{value:1}],z:-1}]};this.chart.setOption(d,!0)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},c80b:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-eye-open",use:"icon-eye-open-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" id="icon-eye-open"><defs><style></style></defs><path d="M512 128q69.675 0 135.51 21.163t115.498 54.997 93.483 74.837 73.685 82.006 51.67 74.837 32.17 54.827L1024 512q-2.347 4.992-6.315 13.483T998.87 560.17t-31.658 51.669-44.331 59.99-56.832 64.34-69.504 60.16-82.347 51.5-94.848 34.687T512 896q-69.675 0-135.51-21.163t-115.498-54.826-93.483-74.326-73.685-81.493-51.67-74.496-32.17-54.997L0 513.707q2.347-4.992 6.315-13.483t18.816-34.816 31.658-51.84 44.331-60.33 56.832-64.683 69.504-60.331 82.347-51.84 94.848-34.816T512 128.085zm0 85.333q-46.677 0-91.648 12.331t-81.152 31.83-70.656 47.146-59.648 54.485-48.853 57.686-37.675 52.821-26.325 43.99q12.33 21.674 26.325 43.52t37.675 52.351 48.853 57.003 59.648 53.845T339.2 767.02t81.152 31.488T512 810.667t91.648-12.331 81.152-31.659 70.656-46.848 59.648-54.186 48.853-57.344 37.675-52.651T927.957 512q-12.33-21.675-26.325-43.648t-37.675-52.65-48.853-57.345-59.648-54.186-70.656-46.848-81.152-31.659T512 213.334zm0 128q70.656 0 120.661 50.006T682.667 512 632.66 632.661 512 682.667 391.339 632.66 341.333 512t50.006-120.661T512 341.333zm0 85.334q-35.328 0-60.33 25.002T426.666 512t25.002 60.33T512 597.334t60.33-25.002T597.334 512t-25.002-60.33T512 426.666z" /></symbol>'});c.a.add(s);t["default"]=s},c91a:function(e,t,n){"use strict";n("3781")},ca00:function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"g",(function(){return i})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return s})),n.d(t,"c",(function(){return r})),n.d(t,"b",(function(){return d})),n.d(t,"h",(function(){return l})),n.d(t,"a",(function(){return m}));var a=n("2909"),o=(n("53ca"),n("ac1f"),n("5319"),n("a15b"),n("159b"),n("b64b"),n("d3b7"),n("25f0"),n("4d63"),n("4d90"),n("4de4"),n("25eb"),n("a9e3"),n("466d"),n("1276"),n("d81d"),n("b680"),n("13d5"),function(e,t){var n,a={"Y+":t.getFullYear().toString(),"m+":(t.getMonth()+1).toString(),"d+":t.getDate().toString(),"H+":t.getHours().toString(),"M+":t.getMinutes().toString(),"S+":t.getSeconds().toString()};for(var o in a)n=new RegExp("("+o+")").exec(e),n&&(e=e.replace(n[1],1==n[1].length?a[o]:a[o].padStart(n[1].length,"0")));return e});var i=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"H:i:s",n={};n.H=Number.parseInt(e/3600),n.i=Number.parseInt((e-3600*n.H)/60),n.s=Number.parseInt(e-3600*n.H-60*n.i),n.H<10&&(n.H="0"+n.H),n.i<10&&(n.i="0"+n.i),n.s<10&&(n.s="0"+n.s);var a=t.replace("H",n.H).replace("i",n.i).replace("s",n.s);return a};function c(e){var t=Math.floor(e/3600),n=Math.floor(e%3600/60),a=e%60,o="";return t&&(o+=t+"小时"),n&&(o+=n+"分钟"),a&&(o+=a+"秒"),o}function s(e){if(!e)return"";var t=new Date(e);return t.setDate(t.getDate()-1),t.toISOString().split("T")[0]}function r(e){if(!e)return"";var t=new Date(e),n=t.getMonth()+1,a=t.getDate();return n+"/"+a}function d(e,t,n){if(e)return e.map((function(e){var a=Math.floor((new Date(e.startX)-t)/(n-t)*1e3),o=Math.floor((new Date(e.endX)-t)/(n-t)*1e3);return[a,o,e.height]}))}function l(e,t){return e?e.toFixed(t):0}function m(e,t){var n=e.map((function(e){return e[t]}));if(!n||0==n.length)return{max:0,min:0,average:0,median:0};var o=Math.max.apply(Math,Object(a["a"])(n)),i=Math.min.apply(Math,Object(a["a"])(n)),c=n.reduce((function(e,t){return e+t}),0)/n.length,s=n.sort((function(e,t){return e-t})),r=s.length%2===0?(s[s.length/2]+s[s.length/2-1])/2:s[Math.floor(s.length/2)];return{max:o,min:i,average:c.toFixed(0),median:r}}},cad4:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-table-column",{attrs:{prop:e.prop,label:e.label,width:e.width,align:e.align},scopedSlots:e._u([{key:"default",fn:function(t){return[e.$store.state.dict[e.dictField]?n("span",[e._v(e._s(e.$store.state.dict[e.dictField][t.row[t.column.property]]))]):n("span",[e._v(e._s(t.row[t.column.property]))])]}}])})},o=[],i={name:"DictTableColumn",props:{label:String,prop:String,width:{},align:{type:String,default:"center"},dictField:String},data:function(){return{}},created:function(){},mounted:function(){},methods:{}},c=i,s=(n("4c2b"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"26d06df4",null);t["default"]=r.exports},cc5f:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{class:"trend "+(e.isUp?"up":"down")},[e.prefix?n("span",{staticClass:"prefix"},[e._v(e._s(e.prefix))]):e._e(),n("span",{staticClass:"text"},[e._v(e._s(e.value))]),e.suffix?n("span",{staticClass:"suffix"},[e._v(e._s(e.suffix))]):e._e(),n("i",{class:e.isUp?"el-icon-caret-top":"el-icon-caret-bottom"})])},o=[],i=(n("caad"),{name:"Trend",props:{value:{type:String,required:!0},type:{type:String,validator:function(e){return["up","down"].includes(e)},default:"up"},prefix:{type:String,default:""},suffix:{type:String,default:""},reverse:{type:Boolean,default:!1}},computed:{isUp:function(){var e="up"===this.type;return this.reverse&&(e=!e),e}}}),c=i,s=(n("4afc"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"73c57911",null);t["default"]=r.exports},d0b4:function(e,t,n){"use strict";n("4a62")},d0d7:function(e,t,n){},d193:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"scrolling-table-"+this.key}},[n("div",{staticClass:"scrolling-table-header flex-row justify-between"},e._l(e.headers,(function(t,a){return n("div",{key:a,staticClass:"scrolling-table-header-cell",style:{flex:t.width}},[e._v(" "+e._s(t.text)+" ")])})),0),n("div",{staticClass:"scrolling-wrap",style:e.wrapStyle},[n("div",{ref:"scrollList",staticClass:"scrolling-table-body"},e._l(e.data,(function(t,a){return n("div",{key:a,staticClass:"scrolling-table-row flex-row justify-between"},e._l(e.headers,(function(a,o){return n("div",{key:o,staticClass:"scrolling-table-cell",style:{flex:a.width}},[e._v(" "+e._s(t[a.name])+" ")])})),0)})),0)])])},o=[],i=n("5530"),c=(n("a9e3"),n("159b"),n("d81d"),n("b680"),{name:"ScrollingTable",props:{data:{type:Array,default:!0},headers:{type:Array,default:!0},rowNum:{type:Number,default:5}},data:function(){return{scrollInterval:null,currentIndex:0,itemHeight:1.4,key:Math.round(1e4*Math.random())}},mounted:function(){var e=this;this.$nextTick((function(){e.normalizeColumnWidths(),e.startScrolling()}))},watch:{data:function(e,t){this.data=e,this.stopScrolling(),this.startScrolling()}},beforeDestroy:function(){this.stopScrolling()},computed:{wrapStyle:function(){return{height:"".concat(this.rowNum*this.itemHeight,"rem")}}},methods:{normalizeColumnWidths:function(){var e=0;this.headers.forEach((function(t){t.width&&(e+=parseFloat(t.width))})),e>100&&(this.headers=this.headers.map((function(t){return Object(i["a"])(Object(i["a"])({},t),{},{width:t.width?(parseFloat(t.width)/e*100).toFixed(2):void 0})})))},startScrolling:function(){var e=this,t=this.$refs.scrollList;0!=t.length?this.data.length<=this.rowNum?console.log("数据未超过显示区域，不进行滚动"):this.scrollInterval=setInterval((function(){e.currentIndex++,e.data.length-e.rowNum+1==e.currentIndex?(e.currentIndex=0,t.style.transition="none",t.style.transform="translateY(0)",requestAnimationFrame((function(){t.style.transition="",t.style.transform="translateY(-".concat(e.itemHeight*e.currentIndex,"rem)")}))):t.style.transform="translateY(-".concat(e.itemHeight*e.currentIndex,"rem)")}),2e3):console.error("list 为空")},stopScrolling:function(){this.scrollInterval&&clearInterval(this.scrollInterval)}}}),s=c,r=(n("e33b"),n("2877")),d=Object(r["a"])(s,a,o,!1,null,"0103a3d1",null);t["default"]=d.exports},d208:function(e,t,n){},d2d5:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"batch-action-bar"},[n("el-checkbox",{attrs:{indeterminate:e.isIndeterminate,disabled:!e.data.length},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("当页全选")]),e.selectionData.length?n("div",{staticClass:"tips"},[e._v("已选 "+e._s(e.selectionData.length)+" 项")]):e._e(),n("el-form",{attrs:{disabled:!e.selectionData.length}},[e._t("default")],2)],1)},o=[],i={name:"BatchActionBar",props:{data:{type:Array,default:function(){return[]}},selectionData:{type:Array,default:function(){return[]}}},data:function(){return{}},computed:{checkAll:{get:function(){var e=!1;return 0!=this.data.length&&this.data.length==this.selectionData.length&&(e=!0),e},set:function(e){e?this.$emit("check-all"):this.$emit("check-null")}},isIndeterminate:function(){var e=!1;return this.selectionData.length>0&&this.selectionData.length<this.data.length&&(e=!0),e}},created:function(){},mounted:function(){},methods:{}},c=i,s=(n("c729"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"2c7bda43",null);t["default"]=r.exports},d2f0:function(e,t,n){"use strict";n("af53")},d3a3:function(e,t,n){},d46e:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-sidebar-default",use:"icon-sidebar-default-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-sidebar-default"><defs><style type="text/css"></style></defs><path d="M487 71.425a75 75 0 0 1 72.575 0l335.7 185.5a75 75 0 0 1 38.75 65.65V690.85a75 75 0 0 1-38.75 65.625l-335.7 185.55a75 75 0 0 1-72.55 0l-335.7-185.5a75 75 0 0 1-38.75-65.675V322.6a75 75 0 0 1 38.75-65.6L487 71.4v0.025zM859 322.6L523.275 137.1l-335.7 185.5v368.25l335.7 185.5 335.75-185.5V322.6z m-601.75 37.1A37.5 37.5 0 0 1 308.2 345l215.1 118.875L738.45 345a37.5 37.5 0 0 1 36.25 65.625l-213.9 118.25V764.55a37.5 37.5 0 0 1-75 0v-235.7l-213.9-118.2a37.5 37.5 0 0 1-14.65-51v0.05z" p-id="9747" /></symbol>'});c.a.add(s);t["default"]=s},d82b:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d015"),n("313e")),c={name:"ChartLiquidFill",props:{data:{type:Object,default:function(){return{}}},color:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){var e=this;this.chart=i.init(this.$refs.chart);this.data.value;var t={series:[{type:"liquidFill",radius:"94%",data:[.3,.2],backgroundStyle:{color:{type:"radial",x:.5,y:.5,r:.5,colorStops:[{offset:0,color:"rgba(0,24,55, 0)"},{offset:.75,color:"rgba(0,24,55, 0)"},{offset:1,color:this.color.outline||"#8d48f9"}],globalCoord:!1}},outline:{borderDistance:5,itemStyle:{borderWidth:3,borderColor:this.color.outline||"#8d48f9"}},color:this.color.colors||["rgba(197,163,253, 0.6)","rgba(141,72,241, 0.5)"],label:{formatter:function(t){return(e.data.value||0)+""},textStyle:{fontSize:30,color:"white"}}}]};this.chart.setOption(t)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},d8b1:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-oval",use:"icon-loading-oval-usage",viewBox:"0 0 38 38",content:'<symbol viewBox="0 0 38 38" xmlns="http://www.w3.org/2000/svg" stroke="#fff" id="icon-loading-oval">\r\n    <g fill="none" fill-rule="evenodd">\r\n        <g transform="translate(1 1)" stroke-width="2">\r\n            <circle stroke-opacity=".5" cx="18" cy="18" r="18" />\r\n            <path d="M36 18c0-9.94-8.06-18-18-18">\r\n                <animateTransform attributeName="transform" type="rotate" from="0 18 18" to="360 18 18" dur="1s" repeatCount="indefinite"></animateTransform>\r\n            </path>\r\n        </g>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},dbed:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-index-document",use:"icon-index-document-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-index-document"><defs><style type="text/css"></style></defs><path d="M346.96 320.528l329.936 0 0 48-329.936 0 0-48Z" p-id="2439" /><path d="M346.96 435.568l329.936 0 0 48-329.936 0 0-48Z" p-id="2440" /><path d="M816.24 127.984l-608 0c-8.848 0-16 7.152-16 16l0 533.168c-0.176 1.536-0.464 3.056-0.464 4.64 0 78.592 58.896 143.552 134.848 153.424l-0.464 0.352 474.096 60.448c17.664 0 32-14.32 32-32L832.256 143.984C832.24 135.136 825.072 127.984 816.24 127.984zM752.24 803.296l-317.44-186.16c-7.456-4.736-13.504-1.424-13.504 7.424l0 54.752 0-0.224c-0.064 0.912 0 1.776 0 2.704 0 41.28-33.392 74.864-74.672 74.864s-74.384-30.848-74.384-72.128c0-1.456 0.144-3.52 0-4.928l0-2.048L272.24 207.984l480 0L752.24 803.296z" p-id="2441" /></symbol>'});c.a.add(s);t["default"]=s},dc77:function(e,t,n){"use strict";n("ba15")},dd21:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-circles",use:"icon-loading-circles-usage",viewBox:"0 0 135 135",content:'<symbol viewBox="0 0 135 135" xmlns="http://www.w3.org/2000/svg" fill="#fff" id="icon-loading-circles">\r\n    <path d="M67.447 58c5.523 0 10-4.477 10-10s-4.477-10-10-10-10 4.477-10 10 4.477 10 10 10zm9.448 9.447c0 5.523 4.477 10 10 10 5.522 0 10-4.477 10-10s-4.478-10-10-10c-5.523 0-10 4.477-10 10zm-9.448 9.448c-5.523 0-10 4.477-10 10 0 5.522 4.477 10 10 10s10-4.478 10-10c0-5.523-4.477-10-10-10zM58 67.447c0-5.523-4.477-10-10-10s-10 4.477-10 10 4.477 10 10 10 10-4.477 10-10z">\r\n        <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="-360 67 67" dur="2.5s" repeatCount="indefinite"></animateTransform>\r\n    </path>\r\n    <path d="M28.19 40.31c6.627 0 12-5.374 12-12 0-6.628-5.373-12-12-12-6.628 0-12 5.372-12 12 0 6.626 5.372 12 12 12zm30.72-19.825c4.686 4.687 12.284 4.687 16.97 0 4.686-4.686 4.686-12.284 0-16.97-4.686-4.687-12.284-4.687-16.97 0-4.687 4.686-4.687 12.284 0 16.97zm35.74 7.705c0 6.627 5.37 12 12 12 6.626 0 12-5.373 12-12 0-6.628-5.374-12-12-12-6.63 0-12 5.372-12 12zm19.822 30.72c-4.686 4.686-4.686 12.284 0 16.97 4.687 4.686 12.285 4.686 16.97 0 4.687-4.686 4.687-12.284 0-16.97-4.685-4.687-12.283-4.687-16.97 0zm-7.704 35.74c-6.627 0-12 5.37-12 12 0 6.626 5.373 12 12 12s12-5.374 12-12c0-6.63-5.373-12-12-12zm-30.72 19.822c-4.686-4.686-12.284-4.686-16.97 0-4.686 4.687-4.686 12.285 0 16.97 4.686 4.687 12.284 4.687 16.97 0 4.687-4.685 4.687-12.283 0-16.97zm-35.74-7.704c0-6.627-5.372-12-12-12-6.626 0-12 5.373-12 12s5.374 12 12 12c6.628 0 12-5.373 12-12zm-19.823-30.72c4.687-4.686 4.687-12.284 0-16.97-4.686-4.686-12.284-4.686-16.97 0-4.687 4.686-4.687 12.284 0 16.97 4.686 4.687 12.284 4.687 16.97 0z">\r\n        <animateTransform attributeName="transform" type="rotate" from="0 67 67" to="360 67 67" dur="8s" repeatCount="indefinite"></animateTransform>\r\n    </path>\r\n</symbol>'});c.a.add(s);t["default"]=s},dd3d:function(e,t,n){"use strict";n("16fe")},de71:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"search-container"},[e._t("default"),e.showMore?n("div",{staticClass:"more"},[n("el-button",{attrs:{type:"text",size:"small",icon:e.isUnfold?"el-icon-caret-top":"el-icon-caret-bottom"},on:{click:e.toggle}},[e._v(e._s(e.isUnfold?"收起":"展开"))])],1):e._e()],2)},o=[],i={name:"SearchBar",props:{showMore:{type:Boolean,default:!1},unfold:{type:Boolean,default:!1}},data:function(){return{isUnfold:!this.unfold}},watch:{unfold:{handler:function(){this.toggle()},immediate:!0}},methods:{toggle:function(){this.isUnfold=!this.isUnfold,this.$emit("toggle",this.isUnfold)}}},c=i,s=(n("d2f0"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"3601e503",null);t["default"]=r.exports},e023:function(e,t,n){"use strict";n("8b84")},e17a:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-sidebar-menu",use:"icon-sidebar-menu-usage",viewBox:"0 0 1024 1024",content:'<symbol class="icon" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" id="icon-sidebar-menu"><defs><style type="text/css"></style></defs><path d="M913.6 135.2H380.2c-26.7 0-44.4 17.8-44.4 44.4V224c0 26.7 17.8 44.4 44.4 44.4h533.3c26.7 0 44.4-17.8 44.4-44.4v-44.4c0.1-26.6-17.7-44.4-44.3-44.4zM158 135.2h-44.4c-22.2 0-44.4 22.2-44.4 44.4V224c0 22.2 22.2 44.4 44.4 44.4H158c22.2 0 44.4-22.2 44.4-44.4v-44.4c0.1-22.2-22.1-44.4-44.4-44.4zM158 446.3h-44.4c-22.2 0-44.4 22.2-44.4 44.4v44.4c0 22.2 22.2 44.4 44.4 44.4H158c22.2 0 44.4-22.2 44.4-44.4v-44.4c0.1-22.2-22.1-44.4-44.4-44.4zM913.6 446.3H380.2c-26.7 0-44.4 17.8-44.4 44.4v44.4c0 26.7 17.8 44.4 44.4 44.4h533.3c26.7 0 44.4-17.8 44.4-44.4v-44.4c0.1-26.6-17.7-44.4-44.3-44.4zM158 757.4h-44.4c-22.2 0-44.4 22.2-44.4 44.4v44.4c0 22.2 22.2 44.4 44.4 44.4H158c22.2 0 44.4-22.2 44.4-44.4v-44.4c0.1-22.2-22.1-44.4-44.4-44.4zM913.6 757.4H380.2c-26.7 0-44.4 17.8-44.4 44.4v44.4c0 26.7 17.8 44.4 44.4 44.4h533.3c26.7 0 44.4-17.8 44.4-44.4v-44.4c0.1-26.6-17.7-44.4-44.3-44.4z" p-id="3547" /></symbol>'});c.a.add(s);t["default"]=s},e236:function(e,t,n){(function(e){n("ac1f"),n("841c"),n("5319"),n("4d63"),n("25f0"),n("159b"),n("b64b"),n("1276"),n("498a"),n("fb6a");(function(t){t.version="1.0.0",t.author="xiaoyx",t.email="<EMAIL>",t.GetLocation=function(){return window.location.pathname+window.location.search+window.location.hash},t.QueryString=function(e,t){e=e.replace(/[\[]/,"\\[").replace(/[\]]/,"\\]");var n=new RegExp("[\\?&]"+e+"=([^&#]*)"),a=n.exec(location.search),o=null;return null===a?"":(o=a[1].replace(/\+/g," "),t?o:decodeURIComponent(decodeURIComponent(o)))},t.Stringify=function(e){if(!e||e.constructor!==Object)throw new Error("Query object should be an object.");var t="";return Object.keys(e).forEach((function(n){t+=n+"="+encodeURIComponent(e[n])+"&"})),t=t.replace(/\&$/g,""),t},t.ParseQuery=function(t){var n={};if("string"!==typeof t&&(t=window.location.search),t=t.replace(/^\?/g,""),!t)return{};for(var a,o=t.split("&"),i=0;i<o.length;++i)a=o[i].indexOf("="),a<0&&(a=o[i].length),n[e.trim(decodeURIComponent(o[i].slice(0,a)))]=e.trim(decodeURIComponent(o[i].slice(a+1)));return n},t.UpdateSearchParam=function(e,t){var n=this.ParseQuery();if(void 0===t)delete n[e];else{if(t=encodeURIComponent(t),n[e]===t)return;n[e]=t}var a="?"+this.Stringify(n);window.history.replaceState(null,"",a+location.hash)},t.GetAgreement=function(){return console.log(window.location),window.location.protocol},t.GetHost=function(){return window.location.host},t.GetPath=function(){return window.location.pathname},t.GetPort=function(){return window.location.port},t.GetHash=function(){return window.location.hash}})(window.UrlFmt||{})}).call(this,n("1157"))},e264:function(e,t,n){"use strict";n.r(t);n("caad"),n("2532"),n("d81d"),n("4de4");var a={list:[]},o={},i={},c={add:function(e,t){"string"===typeof t?!e.list.includes(t)&&e.list.push(t):t.map((function(t){t&&!e.list.includes(t)&&e.list.push(t)}))},remove:function(e,t){e.list="string"===typeof t?e.list.filter((function(e){return e!=t})):e.list.filter((function(e){return!t.includes(e)}))},clean:function(e){e.list=[]}};t["default"]={namespaced:!0,state:a,actions:i,getters:o,mutations:c}},e2f4:function(e,t,n){var a={"./custom.js":"b3cc","./dict.js":"29e7","./keepAlive.js":"e264","./menu.js":"1307","./menuBadge.js":"c40b","./settings.js":"0781","./tabbar.js":"7dca","./user.js":"0f9a"};function o(e){var t=i(e);return n(t)}function i(e){if(!n.o(a,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return a[e]}o.keys=function(){return Object.keys(a)},o.resolve=i,e.exports=o,o.id="e2f4"},e33b:function(e,t,n){"use strict";n("f11b")},e4c7:function(e){e.exports=JSON.parse('["platform-eleme","eleme","delete-solid","delete","s-tools","setting","user-solid","user","phone","phone-outline","more","more-outline","star-on","star-off","s-goods","goods","warning","warning-outline","question","info","remove","circle-plus","success","error","zoom-in","zoom-out","remove-outline","circle-plus-outline","circle-check","circle-close","s-help","help","minus","plus","check","close","picture","picture-outline","picture-outline-round","upload","upload2","download","camera-solid","camera","video-camera-solid","video-camera","message-solid","bell","s-cooperation","s-order","s-platform","s-fold","s-unfold","s-operation","s-promotion","s-home","s-release","s-ticket","s-management","s-open","s-shop","s-marketing","s-flag","s-comment","s-finance","s-claim","s-custom","s-opportunity","s-data","s-check","s-grid","menu","share","d-caret","caret-left","caret-right","caret-bottom","caret-top","bottom-left","bottom-right","back","right","bottom","top","top-left","top-right","arrow-left","arrow-right","arrow-down","arrow-up","d-arrow-left","d-arrow-right","video-pause","video-play","refresh","refresh-right","refresh-left","finished","sort","sort-up","sort-down","rank","loading","view","c-scale-to-original","date","edit","edit-outline","folder","folder-opened","folder-add","folder-remove","folder-delete","folder-checked","tickets","document-remove","document-delete","document-copy","document-checked","document","document-add","printer","paperclip","takeaway-box","search","monitor","attract","mobile","scissors","umbrella","headset","brush","mouse","coordinate","magic-stick","reading","data-line","data-board","pie-chart","data-analysis","collection-tag","film","suitcase","suitcase-1","receiving","collection","files","notebook-1","notebook-2","toilet-paper","office-building","school","table-lamp","house","no-smoking","smoking","shopping-cart-full","shopping-cart-1","shopping-cart-2","shopping-bag-1","shopping-bag-2","sold-out","sell","present","box","bank-card","money","coin","wallet","discount","price-tag","news","guide","male","female","thumb","cpu","link","connection","open","turn-off","set-up","chat-round","chat-line-round","chat-square","chat-dot-round","chat-dot-square","chat-line-square","message","postcard","position","turn-off-microphone","microphone","close-notification","bangzhu","time","odometer","crop","aim","switch-button","full-screen","copy-document","mic","stopwatch","medal-1","medal","trophy","trophy-1","first-aid-kit","discover","place","location","location-outline","location-information","add-location","delete-location","map-location","alarm-clock","timer","watch-1","watch","lock","unlock","key","service","mobile-phone","bicycle","truck","ship","basketball","football","soccer","baseball","wind-power","light-rain","lightning","heavy-rain","sunrise","sunrise-1","sunset","sunny","cloudy","partly-cloudy","cloudy-and-sunny","moon","moon-night","dish","dish-1","food","chicken","fork-spoon","knife-fork","burger","tableware","sugar","dessert","ice-cream","hot-water","water-cup","coffee-cup","cold-drink","goblet","goblet-full","goblet-square","goblet-square-full","refrigerator","grape","watermelon","cherry","apple","pear","orange","coffee","ice-tea","ice-drink","milk-tea","potato-strips","lollipop","ice-cream-square","ice-cream-round"]')},e635:function(e,t,n){},e702:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"300px"}})},o=[],i=(n("a9e3"),n("99af"),n("b0c0"),n("d81d"),n("313e")),c={name:"LineChart",props:{lineColor:{type:String,default:"#01B09A"},datas:{type:Array,default:function(){return[]}},markLines:{type:Array,default:function(){return[0,0,0]}},max:{type:Number||String,default:0},showAxisX:{type:Boolean,default:!0},title:{type:String}},data:function(){return{chart:null,option:{tooltip:{show:!0,trigger:"axis",formatter:function(e){for(var t="",n=0;n<e.length;n++)t+="".concat(e[n].name,"\r\n").concat(e[n].seriesName,": ").concat(e[n].value);return t},textStyle:{fontSize:12}},grid:{left:"8%",right:"10%",bottom:"0",top:"20",containLabel:!0},xAxis:{data:[],show:this.hiddenAxisX,axisLabel:{color:"#01B09A",textStyle:{fontSize:"13"}},axisTick:{show:!0,lineStyle:{color:"#01B09A",width:"2"}},axisLine:{lineStyle:{color:"#01B09A",width:"2"}},splitLine:{show:!1,lineStyle:{color:"#01B09A",width:"2"}}},yAxis:{min:0,axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{show:!1}},series:[{name:this.title,type:"line",smooth:!0,showSymbol:!1,data:[],lineStyle:{width:2,color:this.lineColor},markLine:{symbol:!1,tooltip:{show:!1},data:[{yAxis:this.markLines[0],name:"过速",lineStyle:{color:"#ED2E1C"},label:{normal:{color:"#666",formatter:"过速"}}},{yAxis:this.markLines[1],name:"正常",lineStyle:{color:"#81D88A"},label:{normal:{color:"#666",formatter:"正常"}}},{yAxis:this.markLines[2],name:"过缓",lineStyle:{color:"#FAD347"},label:{normal:{color:"#666",formatter:"过缓"}}}]}}]}}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){var e=[],t=[];this.datas.map((function(n){e.push(n[0]),t.push(n[1])}));var n=Math.max.apply(Math,t)||0;n<this.max&&(n=this.max),this.option.xAxis.data=e||[],this.option.yAxis.max=n>0?n+parseInt(.1*n):n,this.option.series[0].data=t||[],this.chart=i.init(this.$refs.chart),this.chart.setOption(this.option)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},e961:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})},o=[],i=(n("d81d"),n("d3b7"),n("ddb0"),n("313e")),c={name:"ChartBarLine",props:{data:{type:Object,default:function(){return{}}}},data:function(){return{chart:null}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{initChart:function(){var e=this;this.chart=i.init(this.$refs.chart);var t=[];this.data.names.map((function(n,a){t.push({name:n,value:e.data.values[a]})}));var n=[];this.data.names.map((function(t,a){n.push({name:t,value:e.data.comparisons[a]})}));var a={grid:{left:"10%",right:"13%",bottom:40,top:"8%"},tooltip:{trigger:"axis",axisPointer:{type:"shadow",crossStyle:{color:"#999"}}},xAxis:[{type:"category",data:this.data.names,axisLabel:{textStyle:{color:"#49DBFF",fontSize:14,fontFamily:"Microsoft YaHei"}},axisLine:{show:!1},axisTick:{show:!1}}],yAxis:[{type:"value",axisLabel:{formatter:"{value}",textStyle:{color:"#49DBFF",fontSize:12,fontFamily:"Microsoft YaHei"}},splitLine:{show:!0,lineStyle:{color:"rgba(255,255,255,0.1)"}},axisLine:{show:!1},axisTick:{show:!1}},{type:"value",axisLabel:{formatter:"{value}",textStyle:{color:"#49DBFF",fontSize:12,fontFamily:"Microsoft YaHei"}},splitLine:{show:!1},axisLine:{show:!1},axisTick:{show:!1}}],series:[{name:"数量",type:"bar",data:t,barGap:"-100%",barCategoryGap:"60%",itemStyle:{normal:{color:new i.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"#499ef4"},{offset:1,color:"#3eb5dd"}])},emphasis:{color:new i.graphic.LinearGradient(0,0,0,1,[{offset:0,color:"#3eb5dd"},{offset:1,color:"#499ef4"}])}}},{name:"环比",type:"line",yAxisIndex:1,data:n,itemStyle:{normal:{color:"#ff7f50"}}}]};this.chart.setOption(a)}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},e9c1:function(e,t,n){},efc0:function(e,t,n){"use strict";n("7ed4")},efd5:function(e,t,n){"use strict";n("d0d7")},f034:function(e,t,n){},f05f:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-bars",use:"icon-loading-bars-usage",viewBox:"0 0 135 140",content:'<symbol viewBox="0 0 135 140" xmlns="http://www.w3.org/2000/svg" fill="#fff" id="icon-loading-bars">\r\n    <rect y="10" width="15" height="120" rx="6">\r\n        <animate attributeName="height" begin="0.5s" dur="1s" values="120;110;100;90;80;70;60;50;40;140;120" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="y" begin="0.5s" dur="1s" values="10;15;20;25;30;35;40;45;50;0;10" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </rect>\r\n    <rect x="30" y="10" width="15" height="120" rx="6">\r\n        <animate attributeName="height" begin="0.25s" dur="1s" values="120;110;100;90;80;70;60;50;40;140;120" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="y" begin="0.25s" dur="1s" values="10;15;20;25;30;35;40;45;50;0;10" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </rect>\r\n    <rect x="60" width="15" height="140" rx="6">\r\n        <animate attributeName="height" begin="0s" dur="1s" values="120;110;100;90;80;70;60;50;40;140;120" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="y" begin="0s" dur="1s" values="10;15;20;25;30;35;40;45;50;0;10" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </rect>\r\n    <rect x="90" y="10" width="15" height="120" rx="6">\r\n        <animate attributeName="height" begin="0.25s" dur="1s" values="120;110;100;90;80;70;60;50;40;140;120" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="y" begin="0.25s" dur="1s" values="10;15;20;25;30;35;40;45;50;0;10" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </rect>\r\n    <rect x="120" y="10" width="15" height="120" rx="6">\r\n        <animate attributeName="height" begin="0.5s" dur="1s" values="120;110;100;90;80;70;60;50;40;140;120" calcMode="linear" repeatCount="indefinite"></animate>\r\n        <animate attributeName="y" begin="0.5s" dur="1s" values="10;15;20;25;30;35;40;45;50;0;10" calcMode="linear" repeatCount="indefinite"></animate>\r\n    </rect>\r\n</symbol>'});c.a.add(s);t["default"]=s},f079:function(e,t,n){},f10c:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:e.showDialog}},[e._v(e._s(e.title)+" "),e._t("default")],2),n("el-dialog",{attrs:{title:"日期选择",visible:e.dialogVisible,width:"30%",modal:!1,"destroy-on-close":""},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("el-date-picker",{attrs:{type:"date",placeholder:"选择日期"},model:{value:e.selectedDate,callback:function(t){e.selectedDate=t},expression:"selectedDate"}}),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:function(t){e.dialogVisible=!1}}},[e._v("取消")]),n("el-button",{attrs:{type:"primary"},on:{click:e.handleConfirm}},[e._v("确定")])],1)],1)],1)},o=[],i=(n("a9e3"),n("99af"),n("ca00")),c={name:"ReportDatePicker",props:{itemId:{type:String|Number,required:!0},url:{type:String,required:!0}},data:function(){return{dialogVisible:!1,selectedDate:null,title:""}},methods:{formatDate:i["d"],showDialog:function(){this.dialogVisible=!0},handleConfirm:function(){if(this.selectedDate){var e=this.formatDate("YYYY-mm-dd",this.selectedDate),t="".concat(this.url,"?id=").concat(this.itemId,"&date=").concat(e);window.open(t,"_blank"),this.dialogVisible=!1}}}},s=c,r=n("2877"),d=Object(r["a"])(s,a,o,!1,null,null,null);t["default"]=d.exports},f11b:function(e,t,n){},f269:function(e,t,n){},f9e6:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{ref:"chart",staticStyle:{width:"100%",height:"300px"}})},o=[],i=(n("d81d"),n("ca00")),c=n("313e"),s=["#45B3FF","#FF7D17","#20CD8A","#01B09A"],r={name:"RectChart",props:{datas:{type:Array,default:function(){return[]}}},data:function(){return{chart:null,option:{title:{show:!1,textStyle:{color:"gray",fontSize:13},text:"暂无数据",left:"center",top:"center"},tooltip:{show:!0,formatter:"{b}"},xAxis:{show:!0,axisTick:{show:!1},splitLine:{show:!1},axisLabel:{show:!1}},yAxis:{show:!0,axisLine:{show:!1},axisTick:{show:!1},axisLabel:{show:!1}},grid:{top:10,bottom:2},series:[{type:"custom",renderItem:function(e,t){var n=t.value(2),a=t.coord([t.value(0),n]),o=t.size([t.value(1)-t.value(0),n]),i=t.style();return{type:"rect",shape:{x:a[0],y:a[1],width:o[0],height:o[1]},style:i}},dimensions:["from","to","profit"],encode:{x:[0,1],y:2,tooltip:[0,1,2],itemName:3},data:[]}]}}},mounted:function(){var e=this;this.initChart(),window.addEventListener("resize",(function(){e.chart.resize()}))},methods:{convertTimeToValues:i["b"],initChart:function(){!this.datas||this.datas.length<1?this.option.title.show=!0:(this.option.series[0].data=this.datas.map((function(e,t){return{value:[e.startX,e.endX,e.height+1,e.createDate],itemStyle:{color:s[e.height]}}})),this.chart=c.init(this.$refs.chart),this.chart.setOption(this.option))}}},d=r,l=n("2877"),m=Object(l["a"])(d,a,o,!1,null,null,null);t["default"]=m.exports},fa21:function(e,t,n){"use strict";n("a4b8")},fbc6:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"absolute-container"},[e._t("default")],2)},o=[],i={name:"AbsoluteContainer",props:{},data:function(){return{}},mounted:function(){},methods:{}},c=i,s=(n("34de"),n("2877")),r=Object(s["a"])(c,a,o,!1,null,"332726d4",null);t["default"]=r.exports},fc86:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-loading-audio",use:"icon-loading-audio-usage",viewBox:"0 0 55 80",content:'<symbol viewBox="0 0 55 80" xmlns="http://www.w3.org/2000/svg" fill="#FFF" id="icon-loading-audio">\r\n    <g transform="matrix(1 0 0 -1 0 80)">\r\n        <rect width="10" height="20" rx="3">\r\n            <animate attributeName="height" begin="0s" dur="4.3s" values="20;45;57;80;64;32;66;45;64;23;66;13;64;56;34;34;2;23;76;79;20" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </rect>\r\n        <rect x="15" width="10" height="80" rx="3">\r\n            <animate attributeName="height" begin="0s" dur="2s" values="80;55;33;5;75;23;73;33;12;14;60;80" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </rect>\r\n        <rect x="30" width="10" height="50" rx="3">\r\n            <animate attributeName="height" begin="0s" dur="1.4s" values="50;34;78;23;56;23;34;76;80;54;21;50" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </rect>\r\n        <rect x="45" width="10" height="30" rx="3">\r\n            <animate attributeName="height" begin="0s" dur="2s" values="30;45;13;80;56;72;45;76;34;23;67;30" calcMode="linear" repeatCount="indefinite"></animate>\r\n        </rect>\r\n    </g>\r\n</symbol>'});c.a.add(s);t["default"]=s},fff5:function(e,t,n){"use strict";n.r(t);var a=n("e017"),o=n.n(a),i=n("21a1"),c=n.n(i),s=new o.a({id:"icon-icon-refresh",use:"icon-icon-refresh-usage",viewBox:"0 0 48 48",content:'<symbol viewBox="0 0 48 48" xmlns="http://www.w3.org/2000/svg" id="icon-icon-refresh"><g><g transform="translate(-0.500000, 0.000000)"><rect fill-opacity="0.01" fill="#FFFFFF" x="0.5" y="0" width="48" height="48" stroke-width="4" stroke="none" fill-rule="evenodd" /><path d="M42,24 C42,14.0588745 33.9411255,6 24,6 C21.559467,6 19.2323766,6.48570507 17.1101186,7.36572524 C16.0143739,7.82008913 14.9732329,8.37956827 13.999274,9.03158409 C13.0176762,9.68871375 12.1043185,10.4398373 11.2720779,11.2720779 C10.4398373,12.1043185 9.68871375,13.0176762 9.03158409,13.999274 M6,24 C6,33.9411255 14.0588745,42 24,42 L24,42 C26.440533,42 28.7676234,41.5142949 30.8898814,40.6342748 C31.9856261,40.1799109 33.0267671,39.6204317 34.000726,38.9684159 C34.9823238,38.3112862 35.8956815,37.5601627 36.7279221,36.7279221 C37.5601627,35.8956815 38.3112862,34.9823238 38.9684159,34.000726" stroke="#409eff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" fill="none" fill-rule="evenodd" /><path d="M34,16 L50,16" stroke="#409eff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" transform="translate(42.000000, 16.000000) rotate(90.000000) translate(-42.000000, -16.000000) " fill="none" fill-rule="evenodd" /><path d="M-2,32 L14,32" stroke="#409eff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round" transform="translate(6.000000, 32.000000) rotate(90.000000) translate(-6.000000, -32.000000) " fill="none" fill-rule="evenodd" /></g></g></symbol>'});c.a.add(s);t["default"]=s}});