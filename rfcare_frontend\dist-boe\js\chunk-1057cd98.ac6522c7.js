(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1057cd98"],{b8c0:function(e,t,o){"use strict";o("d08e")},d08e:function(e,t,o){},dd7b:function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("div",{staticClass:"bg-banner"}),o("div",{attrs:{id:"login-box"}},[o("div",{staticClass:"login-banner"}),o("el-form",{directives:[{name:"show",rawName:"v-show",value:"login"==e.formType,expression:"formType == 'login'"}],ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,size:"default",autocomplete:"on","label-position":"left"}},[o("div",{staticClass:"title-container"},[o("h3",{staticClass:"title"},[e._v(e._s(e.title))])]),o("div",[o("el-form-item",{attrs:{prop:"account"}},[o("el-input",{ref:"name",attrs:{placeholder:e.$t("app.account"),type:"text",tabindex:"1",autocomplete:"on"},model:{value:e.loginForm.account,callback:function(t){e.$set(e.loginForm,"account",t)},expression:"loginForm.account"}},[o("svg-icon",{attrs:{slot:"prefix",name:"user"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"password"}},[o("el-input",{ref:"password",attrs:{type:e.passwordType,placeholder:e.$t("app.password"),tabindex:"2",autocomplete:"on"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}},[o("svg-icon",{attrs:{slot:"prefix",name:"password"},slot:"prefix"}),o("svg-icon",{attrs:{slot:"suffix",name:"password"===e.passwordType?"eye":"eye-open"},on:{click:e.showPassword},slot:"suffix"})],1)],1)],1),o("div",{staticClass:"flex-bar"},[o("el-checkbox",{model:{value:e.loginForm.remember,callback:function(t){e.$set(e.loginForm,"remember",t)},expression:"loginForm.remember"}},[e._v("记住我")]),o("el-button",{attrs:{type:"text"},on:{click:function(t){e.formType="reset"}}},[e._v("忘记密码")])],1),o("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,type:"primary",size:"default"},nativeOn:{click:function(t){return t.preventDefault(),e.handleLogin(t)}}},[e._v(e._s(e.$t("app.login")))])],1),o("el-form",{directives:[{name:"show",rawName:"v-show",value:"reset"==e.formType,expression:"formType == 'reset'"}],ref:"resetForm",staticClass:"login-form",attrs:{model:e.resetForm,rules:e.resetRules,size:"default","auto-complete":"on","label-position":"left"}},[o("div",{staticClass:"title-container"},[o("h3",{staticClass:"title"},[e._v("重置密码")])]),o("div",[o("el-form-item",{attrs:{prop:"account"}},[o("el-input",{ref:"name",attrs:{placeholder:e.$t("app.account"),type:"text",tabindex:"1",autocomplete:"on"},model:{value:e.resetForm.account,callback:function(t){e.$set(e.resetForm,"account",t)},expression:"resetForm.account"}},[o("svg-icon",{attrs:{slot:"prefix",name:"user"},slot:"prefix"})],1)],1),o("el-form-item",{attrs:{prop:"captcha"}},[o("el-input",{ref:"captcha",attrs:{placeholder:e.$t("app.captcha"),type:"text",tabindex:"2",autocomplete:"on"},model:{value:e.resetForm.captcha,callback:function(t){e.$set(e.resetForm,"captcha",t)},expression:"resetForm.captcha"}},[o("svg-icon",{attrs:{slot:"prefix",name:"user"},slot:"prefix"}),o("el-button",{attrs:{slot:"append",disabled:e.captchaCountdown>0},on:{click:e.getCaptcha},slot:"append"},[e._v(" "+e._s(e.captchaCountdown>0?e.captchaCountdown+"秒后重新获取":"发送验证码")+" ")])],1)],1),o("el-form-item",{attrs:{prop:"newPassword"}},[o("el-input",{ref:"newPassword",attrs:{type:e.passwordType,placeholder:e.$t("app.newPassword"),tabindex:"3",autocomplete:"on"},model:{value:e.resetForm.newPassword,callback:function(t){e.$set(e.resetForm,"newPassword",t)},expression:"resetForm.newPassword"}},[o("svg-icon",{attrs:{slot:"prefix",name:"password"},slot:"prefix"}),o("svg-icon",{attrs:{slot:"suffix",name:"password"===e.passwordType?"eye":"eye-open"},on:{click:e.showPassword},slot:"suffix"})],1)],1)],1),o("el-row",{staticStyle:{"padding-top":"20px"},attrs:{gutter:15}},[o("el-col",{attrs:{md:6}},[o("el-button",{staticStyle:{width:"100%"},attrs:{size:"default"},on:{click:function(t){e.formType="login"}}},[e._v(e._s(e.$t("app.goLogin")))])],1),o("el-col",{attrs:{md:18}},[o("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,type:"primary",size:"default"},nativeOn:{click:function(t){return t.preventDefault(),e.handleFind(t)}}},[e._v(e._s(e.$t("app.check")))])],1)],1)],1)],1),e.$store.state.settings.showCopyright?o("Copyright"):e._e(),e._m(0)],1)},s=[function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticStyle:{"text-align":"center",position:"fixed",bottom:"10px",left:"0",right:"0",margin:"0 auto"}},[o("a",{staticStyle:{color:"#353239"},attrs:{href:"//beian.miit.gov.cn/",target:"_blank"}},[e._v("京ICP备********号")])])}],r=o("2b61"),n={name:"Login",data:function(){return{title:"BOE 服务管理系统",formType:"login",loginForm:{account:r["a"].local.get("login_account"),password:"",remember:r["a"].local.has("login_account")},loginRules:{account:[{required:!0,trigger:"blur",message:"请输入用户名"}],password:[{required:!0,trigger:"blur",message:"请输入密码"},{min:6,max:18,trigger:"blur",message:"密码长度为6到18位"}]},resetForm:{account:r["a"].local.get("login_account"),captcha:"",newPassword:""},resetRules:{account:[{required:!0,trigger:"blur",message:"请输入用户名"}],captcha:[{required:!0,trigger:"blur",message:"请输入验证码"}],newPassword:[{required:!0,trigger:"blur",message:"请输入新密码"},{min:6,max:18,trigger:"blur",message:"密码长度为6到18位"}]},loading:!1,passwordType:"password",redirect:void 0,captchaTimer:null,captchaCountdown:0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0}},methods:{showPassword:function(){var e=this;this.passwordType="password"===this.passwordType?"":"password",this.$nextTick((function(){e.$refs.password.focus()}))},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&(e.loading=!0,e.$store.dispatch("user/login",e.loginForm).then((function(){e.loading=!1,e.loginForm.remember?r["a"].local.set("login_account",e.loginForm.account):r["a"].local.remove("login_account"),e.redirect||("system"===e.$store.state.user.member.role?e.redirect="/station/org":e.redirect="/service/platform/dashboard"),e.$router.push({path:e.redirect||"/",query:{needTip:!0}})})).catch((function(){e.loading=!1})))}))},getCaptcha:function(){var e=this;this.captchaCountdown>0||(this.resetForm.account?(this.loading=!0,this.$store.dispatch("user/getCaptcha",this.resetForm.account).then((function(){e.loading=!1,e.$message({message:"验证码已发送，请注意查收",type:"success"}),e.startCaptchaCountdown()})).catch((function(){e.loading=!1,e.$message({message:"验证码发送失败",type:"error"})}))):this.$message({message:"请输入用户名",type:"warning"}))},startCaptchaCountdown:function(){var e=this;this.captchaCountdown=60,this.captchaTimer=setInterval((function(){e.captchaCountdown--,e.captchaCountdown<=0&&(clearInterval(e.captchaTimer),e.captchaTimer=null)}),1e3)},handleFind:function(){var e=this;this.$refs.resetForm.validate((function(t){if(t){e.loading=!0;var o={account:e.resetForm.account,captcha:e.resetForm.captcha,newPassword:e.resetForm.newPassword};e.$store.dispatch("user/resetPassword",o).then((function(){e.loading=!1,e.$message({message:"密码重置成功，请使用新密码登录",type:"success"}),e.resetForm={account:e.resetForm.account,captcha:"",newPassword:""},e.formType="login"})).catch((function(){e.loading=!1}))}}))},testAccount:function(e){this.loginForm.account=e,this.loginForm.password="123456",this.handleLogin()}},beforeDestroy:function(){this.captchaTimer&&clearInterval(this.captchaTimer)}},i=n,c=(o("b8c0"),o("2877")),l=Object(c["a"])(i,a,s,!1,null,"89ebcd44",null);t["default"]=l.exports}}]);