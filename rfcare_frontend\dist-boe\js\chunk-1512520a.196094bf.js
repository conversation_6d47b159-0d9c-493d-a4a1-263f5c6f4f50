(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1512520a"],{"09b3":function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("page-main",{staticStyle:{"margin-top":"70px"}},[a("el-tabs",{staticStyle:{height:"600px"},attrs:{"tab-position":"left"}},[a("el-tab-pane",{staticClass:"basic",attrs:{label:"基本设置"}},[a("h2",[t._v("基本设置")]),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:16}},[a("el-form",{ref:"form",attrs:{model:t.form,"label-width":"120px","label-suffix":"："}},[a("el-form-item",{attrs:{label:"名 称"}},[a("el-input",{attrs:{placeholder:"请输入你的名称"},model:{value:t.form.name,callback:function(e){t.$set(t.form,"name",e)},expression:"form.name"}})],1),a("el-form-item",{attrs:{label:"手机号"}},[a("el-input",{attrs:{placeholder:"请输入你的手机号"},model:{value:t.form.mobile,callback:function(e){t.$set(t.form,"mobile",e)},expression:"form.mobile"}})],1),a("el-form-item",{attrs:{label:"QQ 号"}},[a("el-input",{attrs:{placeholder:"请输入你的 QQ 号"},model:{value:t.form.qq,callback:function(e){t.$set(t.form,"qq",e)},expression:"form.qq"}})],1),a("el-form-item",{attrs:{label:"微信号"}},[a("el-input",{attrs:{placeholder:"请输入你的微信号"},model:{value:t.form.wechat,callback:function(e){t.$set(t.form,"wechat",e)},expression:"form.wechat"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"}},[t._v("保存")])],1)],1)],1),a("el-col",{attrs:{span:8}},[a("image-upload",{staticClass:"headimg-upload",attrs:{url:t.form.headimg,action:"http://scrm.1daas.com/api/upload/upload",name:"image",data:{token:"TKD628431923530324"},notip:""},on:{"update:url":function(e){return t.$set(t.form,"headimg",e)},onSuccess:t.handleSuccess}})],1)],1)],1),a("el-tab-pane",{staticClass:"security",attrs:{label:"安全设置"}},[a("h2",[t._v("安全设置")]),a("div",{staticClass:"setting-list"},[a("div",{staticClass:"item"},[a("div",{staticClass:"content"},[a("div",{staticClass:"title"},[t._v("账户密码")]),a("div",{staticClass:"desc"},[t._v("当前密码强度：强")])]),a("div",{staticClass:"action"},[a("el-button",{attrs:{type:"text"},on:{click:t.editPassword}},[t._v("修改")])],1)]),a("div",{staticClass:"item"},[a("div",{staticClass:"content"},[a("div",{staticClass:"title"},[t._v("密保手机")]),a("div",{staticClass:"desc"},[t._v("已绑定手机：187****3441")])]),a("div",{staticClass:"action"},[a("el-button",{attrs:{type:"text"}},[t._v("修改")])],1)]),a("div",{staticClass:"item"},[a("div",{staticClass:"content"},[a("div",{staticClass:"title"},[t._v("备用邮箱")]),a("div",{staticClass:"desc"},[t._v("当前未绑定备用邮箱")])]),a("div",{staticClass:"action"},[a("el-button",{attrs:{type:"text"}},[t._v("绑定")])],1)])])])],1)],1)],1)},i=[],l=(a("caad"),a("b0c0"),{name:"PersonalSetting",beforeRouteLeave:function(t,e,a){["personalEditPassword"].includes(t.name)&&this.$store.commit("keepAlive/add","PersonalSetting"),a()},props:{},data:function(){return{form:{headimg:"",mobile:"",name:"",qq:"",wechat:""}}},created:function(){},mounted:function(){},methods:{handleSuccess:function(t){""==t.error?this.form.headimg=t.data.path:this.$message.warning(t.error)},editPassword:function(){this.$router.push({name:"personalEditPassword"})}}}),o=l,n=(a("e6ca"),a("2877")),r=Object(n["a"])(o,s,i,!1,null,"0e00670a",null);e["default"]=r.exports},d38d:function(t,e,a){},e6ca:function(t,e,a){"use strict";a("d38d")}}]);