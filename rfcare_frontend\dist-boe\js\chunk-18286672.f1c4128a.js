(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-18286672"],{"4ded":function(e,t,a){"use strict";a("bb27")},bb27:function(e,t,a){},e310:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"老人管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:""}},[a("el-input",{attrs:{placeholder:"根据姓名、手机号搜索"},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.preventDefault(),e.handleSearch(t))}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),a("el-form-item",{attrs:{label:"服务站:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchStations},model:{value:e.search.stationId,callback:function(t){e.$set(e.search,"stationId",t)},expression:"search.stationId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.stations,(function(e){return a("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})}))],2)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"stationOlderAdd"})}}},[e._v("新增")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"genderDesc",label:"性别",width:"60",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"idcardTypeDesc",label:"证件类型",align:"center"}}),a("el-table-column",{attrs:{prop:"idcardCode",label:"证件号码",align:"center"}}),a("el-table-column",{attrs:{prop:"checkInStatus",label:"是否入住",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("1"==a.checkInStatus?"是":"否")+" ")]}}])}),a("el-table-column",{attrs:{prop:"bedNum",label:"入住床位",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"checkInTime",label:"入住时间",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"stationOlderEdit",params:{id:t.row.id}})}}},[e._v("修改")]),t.row.bedNum||t.row.checkInTime?e._e():a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return e.handleDelOperate(t.row.id)}}},[e._v("删除")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},l=[],i=a("5530"),r=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),{name:"stationOlderListIndex",data:function(){return{dict:{stations:[]},search:{keyword:void 0,stationId:void 0,name:void 0,phone:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=Object(i["a"])(Object(i["a"])({},this.search),{},{pageReqVo:this.table.pageInfo});console.log(t),this.$api.post("/bms/station/older/selectByPage",t).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleDelOperate:function(e){var t=this;e&&this.$confirm("您确定要删除该记录?","删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((function(){t.$api.get("/bms/station/older/delete?id=".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"已经成功删除!"}),t.fetchDatas()):t.$message({type:"error",message:"删除失败"})}))})).catch((function(){t.$message({type:"error",message:"删除失败"})}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()},handleFetchStations:function(e){var t=this;!0===e?this.$api.get("/bms/station/list4Select",{}).then((function(e){"00000"===e.status&&e.data?t.dict.stations=e.data:t.dict.stations=[]})):this.search.stationId||(this.dict.chambs=[],this.search.chambId=void 0)}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,butlerListIndex")}))},beforeRouteLeave:function(e,t,a){["butlerInfo"].includes(e.name)||(console.log("移除 EmptyLayout,butlerListIndex"),this.$store.commit("keepAlive/remove","EmptyLayout,butlerListIndex")),a()}}),s=r,o=(a("4ded"),a("2877")),c=Object(o["a"])(s,n,l,!1,null,"591de49a",null);t["default"]=c.exports}}]);