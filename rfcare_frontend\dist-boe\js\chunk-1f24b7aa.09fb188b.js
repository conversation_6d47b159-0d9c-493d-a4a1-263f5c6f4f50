(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1f24b7aa"],{"193a":function(t,n,e){"use strict";e("df98")},"8cdb":function(t,n,e){"use strict";e.r(n);var a=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("div",{staticClass:"notfound"},[e("svg-icon",{attrs:{name:"404"}}),e("div",{staticClass:"content"},[e("h1",[t._v("404")]),e("div",{staticClass:"desc"},[t._v("抱歉，你访问的页面不存在")]),e("el-button",{attrs:{type:"primary"},on:{click:t.goBack}},[t._v(t._s(t.countdown)+"秒后，返回首页")])],1)],1)},o=[],s={beforeRouteLeave:function(t,n,e){clearInterval(this.inter),e()},data:function(){return{inter:null,countdown:5}},mounted:function(){var t=this;this.$store.state.settings.enableTabbar&&this.$store.dispatch("tabbar/remove",this.$route.meta.activeMenu||this.$route.fullPath),this.inter=setInterval((function(){t.countdown--,0==t.countdown&&(clearInterval(t.inter),t.goBack())}),1e3)},methods:{goBack:function(){this.$router.push("/")}}},i=s,c=(e("193a"),e("2877")),r=Object(c["a"])(i,a,o,!1,null,"215d75a8",null);n["default"]=r.exports},df98:function(t,n,e){}}]);