(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-221bf3a5"],{"2a8a":function(t,e,a){"use strict";a("3d67")},"3d67":function(t,e,a){},d4d2:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"服务信息详情查看"}}),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{staticStyle:{width:"100px"},attrs:{md:6}},[t.reqForm.serverIcon?a("el-image",{style:"width: 80px; height: 80px; vertical-align: middle;",attrs:{src:t.reqForm.serverIcon,fit:"fill"}}):t._e()],1),a("el-col",{attrs:{md:18}},[a("div",[a("span",{staticClass:"label-field"},[t._v("产品型号:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.productModelName))])]),a("div",[a("span",{staticClass:"label-field"},[t._v("说明:")]),t._v(" "),a("span",{staticClass:"value-field",domProps:{innerHTML:t._s(t.reqForm.productModelDesc)}})])])],1)],1),a("page-main",{attrs:{title:"服务包"}},[a("page-main",{staticStyle:{margin:"0","margin-top":"20px"},attrs:{title:"基础服务信息"}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:t.reqForm.serverBaseVOList,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"serverTypeCName",label:"服务类型",width:"140",align:"center"}})],1)],1),a("page-main",{staticStyle:{margin:"0"},attrs:{title:"服务包价格"}},t._l(t.reqForm.productModelPriceVOList,(function(e,r){return a("el-row",{key:r,staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px",background:"rgb(49 172 209 / 8%)"},attrs:{gutter:20}},[a("el-col",{attrs:{md:4}},[a("span",{staticClass:"label-field"},[t._v("计费方式:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(e.billingMethodName||"-"))])]),a("el-col",{attrs:{md:4}},[a("span",{staticClass:"label-field"},[t._v("单价:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(e.unitPrice||"-"))])]),a("el-col",{attrs:{md:4}},[a("span",{staticClass:"label-field"},[t._v("优惠价:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(e.discountPrice||"-"))])])],1)})),1)],1),t.reqForm.serverAppreciationVOList&&t.reqForm.serverAppreciationVOList.length?a("page-main",{attrs:{title:"增值服务"}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:t.reqForm.serverAppreciationVOList,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"serverTypeCName",label:"服务类型",width:"140",align:"center"}})],1)],1):t._e(),a("fixed-action-bar",[a("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)},i=[],l={data:function(){return{reqForm:{serverBaseVOList:[],serverAppreciationVOList:[],productModelPriceVOList:[]}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(t){var e=this;t&&this.$api.get("/bms/productModel/getProductModelInfo/".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(e.reqForm=t.data)}))}}},s=l,n=(a("2a8a"),a("2877")),o=Object(n["a"])(s,r,i,!1,null,"6870496a",null);e["default"]=o.exports}}]);