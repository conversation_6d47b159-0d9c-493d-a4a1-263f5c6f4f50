(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-228cfa60"],{8859:function(e,t,a){},a47c:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"订单详情查看"}}),a("page-main",[a("span",{staticClass:"label-field"},[e._v("支付状态:")]),a("span",{staticClass:"value-field",style:{color:e.dict.statusColor[e.reqForm.payStatus]||"#999"}},[e._v(e._s(e.reqForm.payStatusCName))]),"1"==e.reqForm.payStatus?a("span",{staticClass:"label-field",staticStyle:{"margin-left":"100px"}},[a("el-button",{on:{click:e.cancelOrder}},[e._v("取消")])],1):e._e()]),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("订单类型:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.orderTypeCName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("订单编号:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.orderNo))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("客户:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.customerName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("所属机构:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.orgCName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("订单名称:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.orderName))])])]),"org"!==e.reqForm.orderType?a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("设备名称:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.devName))])])]):e._e(),"org"!==e.reqForm.orderType?a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("设备编号:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.devCode))])])]):e._e()],1)],1),a("page-main",{attrs:{title:"服务信息"}},[a("el-row",{staticStyle:{padding:"0","box-sizing":"border-box"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("购买类型:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.buyTypeCName))])])]),e.reqForm.productModelVO?a("el-col",{staticStyle:{"margin-top":"20px"},attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("产品型号:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.productModelVO.productModelName||""))])])]):e._e()],1),a("div",{staticStyle:{"margin-top":"20px"}},[e._v("基础服务")]),a("el-table",{ref:"table",staticClass:"list-table",staticStyle:{"margin-top":"12px"},attrs:{data:e.reqForm.serverBaseVOList,border:"",stripe:"","span-method":e.objectSpanMethod}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"serverTypeCName",label:"服务类型",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.serverTypeCName||"基础服务")+" ")]}}])}),a("el-table-column",{attrs:{prop:"billingMethodCName",label:"计价方式",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"单价",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"billingUnit",label:"单位",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.billingUnit||"元")+" ")]}}])}),a("el-table-column",{attrs:{prop:"buyNum",label:"数量",width:"140",align:"center"}})],1),a("div",{staticStyle:{"margin-top":"20px"}},[e._v("增值服务")]),a("el-table",{ref:"table",staticClass:"list-table",staticStyle:{"margin-top":"12px"},attrs:{data:e.reqForm.serverAppreciationVOList,border:"",stripe:""}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"serverTypeCName",label:"服务类型",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.serverTypeCName||"增值服务")+" ")]}}])}),a("el-table-column",{attrs:{prop:"billingMethodCName",label:"计价方式",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"unitPrice",label:"单价",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"billingUnit",label:"单位",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.billingUnit||"元")+" ")]}}])}),a("el-table-column",{attrs:{prop:"buyNum",label:"数量",width:"140",align:"center"}})],1)],1),a("page-main",{attrs:{title:"订单价格"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("总价:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.totalAmount||0))]),e._v(" 元")])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("实际支付金额:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.payAmount))]),e._v(" 元")])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("账户名:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.accountName))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("账号:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.accountNo))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("备注:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.remark))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("支付方式:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.dict.payMethod[e.reqForm.payMethod]))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("创建人:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.createUserName))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("创建时间:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.createTime))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("支付时间:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.payTime))])])])],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1)},s=[],r=a("1da1"),i=a("5530"),o=(a("96cf"),a("d81d"),{data:function(){return{reqForm:{productModelName:"10000",serverBaseVOList:[],serverAppreciationVOList:[],productModelPriceVOList:[]},dict:{payMethod:{YL:"银联",ZFB:"支付宝",WX:"微信",YSF:"云闪付",SZRMB:"数字人民币",XJ:"现金",QT:"其他"},orgs:[],statusColor:{1:"#ff3609",2:"#25c525",3:"#999"}},orderId:void 0}},mounted:function(){this.orderId=this.$route.params.id,this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/server-order/getOrderInfo/".concat(e),{}).then((function(e){if("00000"===e.status&&e.data){if(e.data.productModelVO){var a=(e.data.productModelVO.modelItemList||[]).map((function(t){return Object(i["a"])(Object(i["a"])({},t),{},{billingMethod:e.data.productModelVO.billingMethod,billingMethodCName:e.data.productModelVO.billingMethodCName,billingUnit:e.data.productModelVO.billingUnit,buyNum:e.data.productModelVO.buyNum,id:e.data.productModelVO.id,modelId:e.data.productModelVO.modelId,orderId:e.data.productModelVO.orderId,productModelDesc:e.data.productModelVO.productModelDesc,productModelName:e.data.productModelVO.productModelName,unitDiscountPrice:e.data.productModelVO.unitDiscountPrice,unitPrice:e.data.productModelVO.unitPrice})}));e.data.serverBaseVOList=a}e.data.serverAppreciationVOList=e.data.serverItemList||[],t.reqForm=e.data}}))},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(a>=4)return 0===t?{rowspan:1e3,colspan:1}:{rowspan:0,colspan:0}},cancelOrder:function(){var e=this;return Object(r["a"])(regeneratorRuntime.mark((function t(){var a;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("1"===e.reqForm.payStatus){t.next=2;break}return t.abrupt("return");case 2:return t.prev=2,t.next=5,e.$api.get("/bms/server-order/cancelPay?orderId=".concat(e.orderId));case 5:a=t.sent,"00000"===a.status&&(e.$message({type:"success",message:"取消成功"}),setTimeout((function(){e.submitLoading=!1,e.$router.go(-1)}),500)),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](2),e.$message({type:"error",message:t.t0.message||"取消失败"});case 12:case"end":return t.stop()}}),t,null,[[2,9]])})))()}}}),d=o,n=(a("c89b"),a("2877")),c=Object(n["a"])(d,l,s,!1,null,"75505a97",null);t["default"]=c.exports},c89b:function(e,t,a){"use strict";a("8859")}}]);