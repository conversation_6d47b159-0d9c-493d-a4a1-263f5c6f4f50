(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2a8db916"],{"067f":function(e,t,a){"use strict";a("aea4")},"0db0":function(e,t,a){"use strict";a("f1e9")},"470a":function(e,t,a){},5905:function(e,t,a){},6062:function(e,t,a){"use strict";var s=a("6d61"),l=a("6566");e.exports=s("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),l)},6566:function(e,t,a){"use strict";var s=a("9bf2").f,l=a("7c73"),i=a("e2cc"),n=a("0366"),o=a("19aa"),r=a("2266"),c=a("7dd0"),d=a("2626"),u=a("83ab"),h=a("f183").fastKey,p=a("69f3"),b=p.set,f=p.getterFor;e.exports={getConstructor:function(e,t,a,c){var d=e((function(e,s){o(e,d,t),b(e,{type:t,index:l(null),first:void 0,last:void 0,size:0}),u||(e.size=0),void 0!=s&&r(s,e[c],{that:e,AS_ENTRIES:a})})),p=f(t),v=function(e,t,a){var s,l,i=p(e),n=m(e,t);return n?n.value=a:(i.last=n={index:l=h(t,!0),key:t,value:a,previous:s=i.last,next:void 0,removed:!1},i.first||(i.first=n),s&&(s.next=n),u?i.size++:e.size++,"F"!==l&&(i.index[l]=n)),e},m=function(e,t){var a,s=p(e),l=h(t);if("F"!==l)return s.index[l];for(a=s.first;a;a=a.next)if(a.key==t)return a};return i(d.prototype,{clear:function(){var e=this,t=p(e),a=t.index,s=t.first;while(s)s.removed=!0,s.previous&&(s.previous=s.previous.next=void 0),delete a[s.index],s=s.next;t.first=t.last=void 0,u?t.size=0:e.size=0},delete:function(e){var t=this,a=p(t),s=m(t,e);if(s){var l=s.next,i=s.previous;delete a.index[s.index],s.removed=!0,i&&(i.next=l),l&&(l.previous=i),a.first==s&&(a.first=l),a.last==s&&(a.last=i),u?a.size--:t.size--}return!!s},forEach:function(e){var t,a=p(this),s=n(e,arguments.length>1?arguments[1]:void 0,3);while(t=t?t.next:a.first){s(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!m(this,e)}}),i(d.prototype,a?{get:function(e){var t=m(this,e);return t&&t.value},set:function(e,t){return v(this,0===e?0:e,t)}}:{add:function(e){return v(this,e=0===e?0:e,e)}}),u&&s(d.prototype,"size",{get:function(){return p(this).size}}),d},setStrong:function(e,t,a){var s=t+" Iterator",l=f(t),i=f(s);c(e,t,(function(e,t){b(this,{type:s,target:e,state:l(e),kind:t,last:void 0})}),(function(){var e=i(this),t=e.kind,a=e.last;while(a&&a.removed)a=a.previous;return e.target&&(e.last=a=a?a.next:e.state.first)?"keys"==t?{value:a.key,done:!1}:"values"==t?{value:a.value,done:!1}:{value:[a.key,a.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),a?"entries":"values",!a,!0),d(t)}}},6901:function(e,t,a){"use strict";a("b38e")},"6d61":function(e,t,a){"use strict";var s=a("23e7"),l=a("da84"),i=a("94ca"),n=a("6eeb"),o=a("f183"),r=a("2266"),c=a("19aa"),d=a("861d"),u=a("d039"),h=a("1c7e"),p=a("d44e"),b=a("7156");e.exports=function(e,t,a){var f=-1!==e.indexOf("Map"),v=-1!==e.indexOf("Weak"),m=f?"set":"add",g=l[e],w=g&&g.prototype,S=g,y={},I=function(e){var t=w[e];n(w,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(v&&!d(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return v&&!d(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(v&&!d(e))&&t.call(this,0===e?0:e)}:function(e,a){return t.call(this,0===e?0:e,a),this})},D=i(e,"function"!=typeof g||!(v||w.forEach&&!u((function(){(new g).entries().next()}))));if(D)S=a.getConstructor(t,e,f,m),o.REQUIRED=!0;else if(i(e,!0)){var x=new S,$=x[m](v?{}:-0,1)!=x,k=u((function(){x.has(1)})),R=h((function(e){new g(e)})),N=!v&&u((function(){var e=new g,t=5;while(t--)e[m](t,t);return!e.has(-0)}));R||(S=t((function(t,a){c(t,S,e);var s=b(new g,t,S);return void 0!=a&&r(a,s[m],{that:s,AS_ENTRIES:f}),s})),S.prototype=w,w.constructor=S),(k||N)&&(I("delete"),I("has"),f&&I("get")),(N||$)&&I(m),v&&w.clear&&delete w.clear}return y[e]=S,s({global:!0,forced:S!=g},y),p(S,e),v||a.setStrong(S,e,f),S}},"75c0":function(e,t,a){"use strict";a("e01b")},"82dc":function(e,t,a){"use strict";a("5905")},aea4:function(e,t,a){},b38e:function(e,t,a){},ddea:function(e,t,a){"use strict";a("470a")},e01b:function(e,t,a){},e09d:function(e,t,a){"use strict";a("e623")},e1a4:function(e,t,a){"use strict";function s(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function l(e){var t=/^\d{11}$/;return t.test(e)}function i(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function n(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function o(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function r(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function c(e){return r(e)||o(e)}a.d(t,"b",(function(){return s})),a.d(t,"c",(function(){return l})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return c}))},e623:function(e,t,a){},f1e9:function(e,t,a){},f26e:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"设备管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{directives:[{name:"auth",rawName:"v-auth",value:["self.system"],expression:"[ 'self.system' ]"}],attrs:{label:"所属机构:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.search.orgId,callback:function(t){e.$set(e.search,"orgId",t)},expression:"search.orgId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.orgs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{attrs:{label:"服务站:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchStations},model:{value:e.search.stationId,callback:function(t){e.$set(e.search,"stationId",t)},expression:"search.stationId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.stations,(function(e){return a("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})}))],2)],1),a("el-form-item",{attrs:{label:"设备类型:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.deviceType,callback:function(t){e.$set(e.search,"deviceType",t)},expression:"search.deviceType"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"与安宝Mini",value:"1"}}),a("el-option",{attrs:{label:"与安宝Pro",value:"2"}}),a("el-option",{attrs:{label:"随身宝",value:"3"}}),a("el-option",{attrs:{label:"急救呼",value:"4"}}),a("el-option",{attrs:{label:"PRI",value:"5"}})],1)],1),a("el-form-item",{attrs:{label:"激活状态:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.activeStatus,callback:function(t){e.$set(e.search,"activeStatus",t)},expression:"search.activeStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"已激活",value:"1"}}),a("el-option",{attrs:{label:"未激活",value:"0"}})],1)],1),a("el-form-item",{attrs:{label:"运行状态:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.status,callback:function(t){e.$set(e.search,"status",t)},expression:"search.status"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"在线",value:"1"}}),a("el-option",{attrs:{label:"离线",value:"2"}})],1)],1),a("el-form-item",{attrs:{label:"运行模式:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.devModel,callback:function(t){e.$set(e.search,"devModel",t)},expression:"search.devModel"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"测试模式",value:"1"}}),a("el-option",{attrs:{label:"日常模式",value:"0"}})],1)],1),a("el-form-item",{attrs:{label:"关联管家:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchChambs},model:{value:e.search.chambId,callback:function(t){e.$set(e.search,"chambId",t)},expression:"search.chambId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.chambs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{attrs:{label:"产品型号:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchSpecs},model:{value:e.search.modelId,callback:function(t){e.$set(e.search,"modelId",t)},expression:"search.modelId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),"system"===e.$store.state.user.member.role?a("el-option",{attrs:{label:"空",value:"0"}}):e._e(),e._l(e.dict.specs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{attrs:{label:"设备编码:"}},[a("el-input",{attrs:{placeholder:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.devCode,callback:function(t){e.$set(e.search,"devCode",t)},expression:"search.devCode"}})],1),a("el-form-item",[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:e.handleSyncDevice}},[e._v("同步设备")]),a("el-button",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"primary",icon:"el-icon-more",size:"small"},on:{click:e.handleOpenAllocation}},[e._v("分配产品型号")]),a("el-button",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"primary",icon:"el-icon-s-operation",size:"small"},on:{click:e.handleOpenRenew}},[e._v("续期")]),a("el-button",{directives:[{name:"auth",rawName:"v-auth",value:["self.org","self.station"],expression:"[ 'self.org', 'self.station' ]"}],attrs:{type:"primary",icon:"el-icon-s-operation",size:"small"},on:{click:e.handleOpenOrgAllocation}},[e._v("分配服务")]),a("el-button",{attrs:{type:"primary",icon:"el-icon-s-operation",size:"small"},on:{click:e.showSOSNum}},[e._v("设置SOS号码")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[0!==t.row.wifiRssiLevel?a("i",{staticClass:"iconfont",class:e.iconClass(t.row.wifiRssiLevel),staticStyle:{"font-size":"20px"}}):e._e(),0===t.row.wifiRssiLevel?a("i",{staticClass:"iconfont",class:e.iconClass(t.row.wifiRssiLevel),staticStyle:{"font-size":"20px",color:"red"}}):e._e(),a("span",{staticStyle:{"margin-left":"10px"}},[e._v(e._s(t.row.devCode))])]}}])}),a("el-table-column",{attrs:{prop:"deviceTypeName",label:"设备类型",align:"center"}}),a("el-table-column",{attrs:{prop:"productModelName",label:"产品型号",align:"center"}}),a("el-table-column",{attrs:{prop:"serverCount",label:"服务数量",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("el-link",{attrs:{type:"primary"},on:{click:function(t){return e.$refs.stationDeviceServiceListDialog.show(s.id)}}},[e._v(e._s(s.serverCount||0))])]}}])}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"激活状态",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"运行状态",align:"center"}}),a("el-table-column",{attrs:{prop:"devModelName",label:"运行模式",align:"center"}}),a("el-table-column",{attrs:{prop:"chambName",label:"关联管家",width:"160",align:"center"}}),"system"===e.$store.state.user.member.role?a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",width:"160",align:"center"}}):e._e(),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStatusName",label:"基础服务状态",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStartDate",label:"基础服务生效时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"serverEndDate",label:"基础服务失效时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"入库时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"activeTime",label:"首次激活时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",fixed:"right",label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:["self.org","self.station"],expression:"[ 'self.org', 'self.station' ]"}],attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"stationDeviceEdit",params:{id:t.row.id}})}}},[e._v("信息维护")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"stationDeviceInfo",params:{id:t.row.id}})}}},[e._v("详情查看")]),"1"===t.row.devModel?a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return e.handle3D(t.row.id)}}},[e._v("3D")]):e._e(),"2"===e.$store.state.user.member.systemVersion&&"3"==t.row.devScene?a("ReportDatePicker",{attrs:{"item-id":t.row.id,url:"/report/sleep"}},[e._v("睡眠报告")]):e._e()]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1),a("StationDeviceSyncDialog",{ref:"stationDeviceSyncDialog",on:{"on-refresh":e.handleRefresh}}),a("StationDeviceSpecAllocationDialog",{ref:"stationDeviceSpecAllocationDialog",on:{"on-refresh":e.handleRefresh}}),a("StationDeviceSpecRenewDialog",{ref:"stationDeviceSpecRenewDialog",on:{"on-refresh":e.handleRefresh}}),a("StationDeviceServiceListDialog",{ref:"stationDeviceServiceListDialog"}),a("StationDeviceSpecAllocationOrgDialog",{ref:"stationDeviceSpecAllocationOrgDialog",on:{"on-refresh":e.handleRefresh}}),a("el-dialog",{attrs:{title:"SOS号码",visible:e.setSOSDialogVisible,width:"30%"},on:{"update:visible":function(t){e.setSOSDialogVisible=t}}},[a("el-input",{attrs:{placeholder:"请输入SOS号码"},on:{blur:e.validPhone},model:{value:e.sosNum,callback:function(t){e.sosNum=t},expression:"sosNum"}}),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.setSOSDialogVisible=!1}}},[e._v("取 消")]),a("el-button",{attrs:{type:"primary"},on:{click:e.setSOSNum}},[e._v("确 定")])],1)],1)],1)},l=[],i=a("5530"),n=(a("ac1f"),a("841c"),a("d81d"),a("4de4"),a("d3b7"),a("6062"),a("3ca3"),a("ddb0"),a("caad"),a("b0c0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{width:"500px",visible:e.visible,title:"设备同步","modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[e._v(" 所属机构: "),a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.selected,callback:function(t){e.selected=t},expression:"selected"}},e._l(e.dict.orgs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small"},on:{click:e.close}},[e._v("取 消")]),a("el-button",{attrs:{size:"small",type:"primary",loading:e.loading},on:{click:e.handleSyncDevice}},[e._v("确 定")])],1)],1)}),o=[],r={data:function(){return{visible:!1,selected:void 0,dict:{orgs:[]},loading:!1}},mounted:function(){},methods:{show:function(e){this.info=e,this.visible=!0},close:function(){this.selected=void 0,this.visible=!1,this.loading=!1,this.$emit("on-close")},handleSyncDevice:function(){var e=this,t=this.dict.orgs.filter((function(t){return t.id===e.selected}));t&&t.length?(this.loading=!0,this.$api.get("/bms/Device/synDev",{params:{companyId:t[0].companyId}}).then((function(t){e.$message({type:"success",message:"同步成功!"}),e.close(),e.$emit("on-refresh"),e.loading=!1})).catch((function(){e.loading=!1}))):this.$message.warning("请先选择所属机构")},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))}}},c=r,d=(a("e09d"),a("2877")),u=Object(d["a"])(c,n,o,!1,null,"249b6abb",null),h=u.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"分配产品型号",width:"1000px",visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("page-main",{staticStyle:{margin:"0",padding:"0"},attrs:{title:"正在为 "+(e.orgName||"")+"机构 下 "+(e.deviceIds.length||0)+" 台设备 分配产品型号"}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.convertTableDatas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("el-radio",{staticStyle:{color:"#fff","margin-left":"8px"},attrs:{label:s.id},model:{value:e.currentRow,callback:function(t){e.currentRow=t},expression:"currentRow"}})]}}])}),a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"productModelName",label:"产品型号",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"productModelDesc",label:"说明",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"buyNum",label:"购买数量",align:"center"}}),a("el-table-column",{attrs:{prop:"usableNum",label:"可用数量",align:"center"}}),a("el-table-column",{attrs:{prop:"usedNum",label:"已用数量",align:"center"}}),a("el-table-column",{attrs:{prop:"billingMethodName",label:"计费方式",align:"center"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary",disabled:!e.currentRow,loading:e.loading},on:{click:e.handleSubmit}},[e._v("确定")])],1)],1)},b=[],f=(a("2532"),{components:{},data:function(){return{visible:!1,loading:!1,currentRow:void 0,table:{datas:[]},selectedRows:[],orgId:void 0,orgName:void 0,deviceIds:[]}},props:{title:String,selectedIds:{type:Array,default:function(){return[]}}},computed:{convertTableDatas:function(){var e=this,t=[];return(this.table.datas||[]).map((function(a){e.selectedIds.includes(a.id)||t.push(a)})),t}},mounted:function(){},methods:{show:function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[];this.orgId=e,this.orgName=t,this.deviceIds=a,this.fetchDatas(),this.visible=!0},close:function(){this.orgId=void 0,this.orgName=void 0,this.currentRow=void 0,this.deviceIds=[],this.table.datas=[],this.selectedRows=[],this.visible=!1},fetchDatas:function(){var e=this;this.$api.get("/bms/Device/listDistributeModel4Dev",{params:{orgId:this.orgId}}).then((function(t){"00000"===t.status&&t.data?e.table.datas=t.data:e.table.datas=[]}))},handleSubmit:function(){var e=this;this.submitLoading=!0;var t=this.table.datas.filter((function(t){return t.id===e.currentRow})),a=t&&t.length?t[0]:{};this.$api.post("/bms/Device/distributeModel4Dev",{orgId:this.orgId,modelId:a.modelId,devIdlsit:this.deviceIds,billingMethod:a.billingMethod,usableNum:a.usableNum,productModelName:a.productModelName,productModelDesc:a.productModelDesc}).then((function(t){"00000"===t.status?(e.$message({type:"success",message:"分配产品型号成功"}),e.submitLoading=!1,e.$emit("on-refresh"),e.close()):(e.submitLoading=!1,e.$message({type:"error",message:"分配产品型号失败"}))})).catch((function(t){e.submitLoading=!1}))},handleSelectionChange:function(e){this.selectedRows=this.$refs.table.selection||[]},checkItemFn:function(e){return!(this.selectedIds||[]).includes(e.id)}}}),v=f,m=(a("75c0"),Object(d["a"])(v,p,b,!1,null,"4f970c85",null)),g=m.exports,w=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"产品型号续费",width:"1000px",visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("page-main",{staticStyle:{margin:"0",padding:"0"},attrs:{title:"正在为 "+(e.orgName||"")+"机构 下 "+(e.deviceIds.length||0)+" 台设备 进行续费"}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.convertTableDatas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{width:"50",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("el-radio",{staticStyle:{color:"#fff","margin-left":"8px"},attrs:{label:s.id},model:{value:e.currentRow,callback:function(t){e.currentRow=t},expression:"currentRow"}})]}}])}),a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"productModelName",label:"产品型号",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"productModelDesc",label:"说明",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"buyNum",label:"购买数量",align:"center"}}),a("el-table-column",{attrs:{prop:"usableNum",label:"可用数量",align:"center"}}),a("el-table-column",{attrs:{prop:"usedNum",label:"已用数量",align:"center"}}),a("el-table-column",{attrs:{prop:"billingMethodName",label:"计费方式",align:"center"}})],1)],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary",disabled:!e.currentRow,loading:e.loading},on:{click:e.handleSubmit}},[e._v("确定")])],1)],1)},S=[],y={components:{},data:function(){return{visible:!1,loading:!1,currentRow:void 0,table:{datas:[]},selectedRows:[],orgId:void 0,orgName:void 0,modelId:void 0,billingMethod:void 0,deviceIds:[]}},props:{title:String,selectedIds:{type:Array,default:function(){return[]}}},computed:{convertTableDatas:function(){var e=this,t=[];return(this.table.datas||[]).map((function(a){e.selectedIds.includes(a.id)||t.push(a)})),t}},mounted:function(){},methods:{show:function(e,t,a,s){var l=arguments.length>4&&void 0!==arguments[4]?arguments[4]:[];this.orgId=e,this.orgName=t,this.modelId=a,this.billingMethod=s,this.deviceIds=l,this.fetchDatas(),this.visible=!0},close:function(){this.orgId=void 0,this.orgName=void 0,this.modelId=void 0,this.billingMethod=void 0,this.currentRow=void 0,this.deviceIds=[],this.table.datas=[],this.selectedRows=[],this.visible=!1},fetchDatas:function(){var e=this;this.$api.get("/bms/Device/listRenewalModel4Dev",{params:{orgId:this.orgId,modelId:this.modelId,billingMethod:this.billingMethod}}).then((function(t){"00000"===t.status&&t.data?e.table.datas=t.data:e.table.datas=[]}))},handleSubmit:function(){var e=this;this.loading=!0;var t=this.table.datas.filter((function(t){return t.id===e.currentRow})),a=t&&t.length?t[0]:{};this.$api.post("/bms/Device/renewalModel4Dev",{orgId:this.orgId,modelId:a.modelId,devIdlsit:this.deviceIds,billingMethod:a.billingMethod,usableNum:a.usableNum,productModelName:a.productModelName,productModelDesc:a.productModelDesc}).then((function(t){"00000"===t.status?(e.$message({type:"success",message:"产品型号续费成功"}),e.loading=!1,e.$emit("on-refresh"),e.fetchDatas()):(e.loading=!1,e.$message({type:"error",message:"产品型号续费失败"}))})).catch((function(t){e.loading=!1}))},handleSelectionChange:function(e){this.selectedRows=this.$refs.table.selection||[]},checkItemFn:function(e){return!(this.selectedIds||[]).includes(e.id)}}},I=y,D=(a("ddea"),Object(d["a"])(I,w,S,!1,null,"40bdf314",null)),x=D.exports,$=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"服务使用情况",width:"1100px",visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"80px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务类型:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择服务类型"},model:{value:e.search.serverCate,callback:function(t){e.$set(e.search,"serverCate",t)},expression:"search.serverCate"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"基础服务",value:"1"}}),a("el-option",{attrs:{label:"增值服务",value:"2"}})],1)],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务状态:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择服务类型"},model:{value:e.search.serverStatus,callback:function(t){e.$set(e.search,"serverStatus",t)},expression:"search.serverStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"有效",value:"1"}}),a("el-option",{attrs:{label:"失效",value:"2"}}),a("el-option",{attrs:{label:"未生效",value:"3"}})],1)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:""}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编号",width:"90",align:"center"}}),a("el-table-column",{attrs:{prop:"devName",label:"设备名称",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"130",align:"center"}}),a("el-table-column",{attrs:{prop:"serverDesc",label:"服务说明",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"90",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStatusName",label:"服务状态",width:"90",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStartDate",label:"生效时间",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"serverEndDate",label:"失效时间",width:"110",align:"center"}})],1),a("el-pagination",{staticClass:"pagination",staticStyle:{"margin-top":"10px"},attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)},k=[],R={components:{},data:function(){return{visible:!1,loading:!1,dict:{stations:[]},search:{stationId:void 0,serverCate:void 0,serverStatus:void 0,devName:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}},devId:void 0}},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},show:function(e){this.fetchStations(),this.devId=e,this.fetchDatas(),this.visible=!0},close:function(){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=10,this.search.stationId=void 0,this.search.serverCate=void 0,this.search.serverStatus=void 0,this.search.devName=void 0,this.devId=void 0,this.table.datas=[],this.visible=!1},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/Device/listServer4Dev",{params:Object(i["a"])(Object(i["a"])({devId:this.devId},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},fetchStations:function(){var e=this;this.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}}},N=R,C=(a("0db0"),Object(d["a"])(N,$,k,!1,null,"75973b09",null)),_=C.exports,O=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"分配服务",width:"1000px",visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("page-main",{staticStyle:{margin:"0",padding:"0"},attrs:{title:"正在为 "+(e.orgName||"")+"机构 下 "+(e.deviceIds.length||0)+" 台设备 分配服务信息"}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.convertTableDatas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",align:"center"}}),a("el-table-column",{attrs:{prop:"buyNum",label:"购买数量",align:"center"}}),a("el-table-column",{attrs:{prop:"usableNum",label:"可用数量",align:"center"}}),a("el-table-column",{attrs:{prop:"usedNum",label:"已用数量",align:"center"}}),a("el-table-column",{attrs:{prop:"billingMethodName",label:"计费方式",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",fixed:"right",label:"使用情况",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var s=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.$refs.stationDeviceSpecAllocationOrgServiceDialog.show(s.orgId,s.serverId)}}},[e._v("查看")])]}}])})],1),a("el-pagination",{staticClass:"pagination",staticStyle:{"margin-top":"20px"},attrs:{"current-page":e.table.pageInfo.current,size:e.table.pageInfo.pageSize,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"small",type:"primary",disabled:!e.selectedRows.length,loading:e.loading},on:{click:e.handleSubmit}},[e._v("确定(已选择"+e._s(e.selectedRows.length||0)+"条)")])],1),a("StationDeviceSpecAllocationOrgServiceDialog",{ref:"stationDeviceSpecAllocationOrgServiceDialog"})],1)},z=[],T=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{title:"服务使用情况",width:"1100px",visible:e.visible,"modal-append-to-body":!1,"append-to-body":!0,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"80px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务站:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择服务站"},model:{value:e.search.stationId,callback:function(t){e.$set(e.search,"stationId",t)},expression:"search.stationId"}},[a("el-option",{attrs:{label:"全部",value:""}}),e._l(e.dict.stations,(function(e){return a("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})}))],2)],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务类型:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择服务类型"},model:{value:e.search.serverCate,callback:function(t){e.$set(e.search,"serverCate",t)},expression:"search.serverCate"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"基础服务",value:"1"}}),a("el-option",{attrs:{label:"增值服务",value:"2"}})],1)],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务状态:"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择服务类型"},model:{value:e.search.serverStatus,callback:function(t){e.$set(e.search,"serverStatus",t)},expression:"search.serverStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"有效",value:"1"}}),a("el-option",{attrs:{label:"失效",value:"2"}}),a("el-option",{attrs:{label:"未生效",value:"3"}})],1)],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"设备名称:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.devName,callback:function(t){e.$set(e.search,"devName",t)},expression:"search.devName"}})],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:""}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"130",align:"center"}}),a("el-table-column",{attrs:{prop:"serverDesc",label:"服务说明",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编号",width:"90",align:"center"}}),a("el-table-column",{attrs:{prop:"devName",label:"设备名称",align:"center"}}),a("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"90",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStatusName",label:"服务状态",width:"90",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStartDate",label:"生效时间",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"serverEndDate",label:"失效时间",width:"110",align:"center"}})],1)],1)},M=[],F={components:{},data:function(){return{visible:!1,loading:!1,dict:{stations:[]},search:{stationId:void 0,serverCate:void 0,serverStatus:void 0,devName:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}},orgId:void 0,serverId:void 0}},methods:{show:function(e,t){this.fetchStations(),this.orgId=e,this.serverId=t,this.fetchDatas(),this.visible=!0},close:function(){this.orgId=void 0,this.serverId=void 0,this.table.datas=[],this.visible=!1},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/Device/listDev4Server",{params:Object(i["a"])(Object(i["a"])({},this.search),{},{orgId:this.orgId,serverId:this.serverId})}).then((function(t){"00000"===t.status&&t.data?e.table.datas=t.data:e.table.datas=[]}))},fetchStations:function(){var e=this;this.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}}},A=F,j=(a("82dc"),Object(d["a"])(A,T,M,!1,null,"116d954d",null)),E=j.exports,P={components:{StationDeviceSpecAllocationOrgServiceDialog:E},data:function(){return{visible:!1,loading:!1,currentRow:void 0,table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}},selectedRows:[],orgId:void 0,orgName:void 0,modelId:void 0,deviceIds:[]}},props:{title:String,selectedIds:{type:Array,default:function(){return[]}}},computed:{convertTableDatas:function(){var e=this,t=[];return(this.table.datas||[]).map((function(a){e.selectedIds.includes(a.id)||t.push(a)})),t}},mounted:function(){},methods:{show:function(e,t,a,s){this.orgId=e,this.orgName=t,this.modelId=a,this.deviceIds=s,this.pageInfo={current:1,pageSize:10,total:0},this.fetchDatas(),this.visible=!0},close:function(){this.orgId=void 0,this.orgName=void 0,this.currentRow=void 0,this.modelId=void 0,this.deviceIds=[],this.table.datas=[],this.selectedRows=[],this.visible=!1},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/Device/listDistributeServer4Dev",{params:{orgId:this.orgId,modelId:this.modelId,current:this.table.pageInfo.current,pageSize:this.table.pageInfo.pageSize}}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleSubmit:function(){var e=this;this.$confirm("确认后，服务立即生效，是否确认?","提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(t,a,s){if("confirm"===t){a.confirmButtonLoading=!0;var l=e.selectedRows.map((function(t){return Object(i["a"])(Object(i["a"])({},t),{},{modelId:e.modelId,devIdlsit:e.deviceIds})}));e.$api.post("/bms/Device/distributeServer4Dev",l).then((function(t){"00000"===t.status?(e.$message({type:"success",message:"分配服务成功"}),e.$emit("on-refresh"),e.close()):e.$message({type:"error",message:"分配服务失败"}),s(!0)})).catch((function(e){s(!1)})).finally((function(){a.confirmButtonLoading=!1}))}else s()}}).catch((function(){}))},handleSelectionChange:function(e){this.selectedRows=this.$refs.table.selection||[]}}},L=P,V=(a("067f"),Object(d["a"])(L,O,z,!1,null,"26a5a1df",null)),B=V.exports,W=a("f10c"),Z=a("e1a4"),J={name:"stationDeviceIndex",components:{StationDeviceSyncDialog:h,StationDeviceSpecAllocationDialog:g,StationDeviceSpecRenewDialog:x,StationDeviceServiceListDialog:_,StationDeviceSpecAllocationOrgDialog:B,ReportDatePicker:W["default"]},data:function(){return{dict:{orgs:[],stations:[],chambs:[],specs:[]},search:{orgId:void 0,stationId:void 0,chambId:void 0,activeStatus:void 0,devCode:void 0,modelId:void 0,status:"",devModel:""},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}},selectedTableRows:[],setSOSDialogVisible:!1,sosNum:""}},mounted:function(){this.fetchDatas()},methods:{iconClass:function(e){var t="";switch(e){case 0:t="icon-WIFI-0";break;case 1:t="icon-WIFI-1";break;case 2:t="icon-WIFI-2";break;case 3:t="icon-WIFI-3";break;case 4:t="icon-WIFI-4";break;default:break}return t},handleRadioChange:function(e){this.search.status=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=void 0;"org"!==this.$store.state.user.member.role&&"station"!==this.$store.state.user.member.role||(t="1"),this.$api.post("/bms/Device/list",Object(i["a"])(Object(i["a"])({},this.search),{},{pageReqVo:this.table.pageInfo,forServer:t})).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleDelOperate:function(e){var t=this;e&&this.$confirm("您确定要删除这条消息?","删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((function(){t.$api.post("/bms/Device/del/".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"已经成功删除!"}),t.fetchDatas()):t.$message({type:"error",message:"删除失败"})}))})).catch((function(){}))},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))},handleFetchSpecs:function(){var e=this;this.$api.get("/bms/productModel/listModel4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.specs=t.data:e.dict.specs=[]}))},handleFetchStations:function(e){var t=this;!0===e?this.$api.get("/bms/station/list4Select",{}).then((function(e){"00000"===e.status&&e.data?t.dict.stations=e.data:t.dict.stations=[]})):this.search.stationId||(this.dict.chambs=[],this.search.chambId=void 0)},handleFetchChambs:function(e){var t=this;!0===e&&this.$api.get("/bms/sysuser/listKeeper4Select",{params:{stationId:this.search.stationId}}).then((function(e){"00000"===e.status&&e.data?t.dict.chambs=e.data:t.dict.chambs=[]}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()},handleRefresh:function(){this.handlePageCurrentChange(1)},handleSyncDevice:function(){this.$refs.stationDeviceSyncDialog.show()},handleSelectionChange:function(e){this.selectedTableRows=this.$refs.table.selection||[]},handleOpenAllocation:function(){if(this.selectedTableRows&&this.selectedTableRows.length){var e=this.selectedTableRows.map((function(e){return e.orgId})),t=this.selectedTableRows.filter((function(e){return void 0!=e.productModelName&&null!=e.productModelName}));1!=new Set(e).size||t.length?this.$message({type:"warning",message:"请选择相同机构，型号为空进行分配操作！"}):this.$refs.stationDeviceSpecAllocationDialog.show(this.selectedTableRows[0].orgId,this.selectedTableRows[0].orgName,this.selectedTableRows.map((function(e){return e.devId})))}else this.$message({type:"warning",message:"请先选择待操作数据!"})},handleOpenOrgAllocation:function(){if(this.selectedTableRows&&this.selectedTableRows.length){var e=this.selectedTableRows.map((function(e){return e.orgId+"-"+e.modelId}));if(1==new Set(e).size){var t=this.selectedTableRows.some((function(e){return"0"===e.activeStatus}));if(t)this.$message({type:"warning",message:"不能为未激活设备分配服务！"});else{var a=this.selectedTableRows.some((function(e){return!e.serverCount||0===+e.serverCount}));a?this.$message({type:"warning",message:"设备还未分配产品服务"}):this.$refs.stationDeviceSpecAllocationOrgDialog.show(this.selectedTableRows[0].orgId,this.selectedTableRows[0].orgName,this.selectedTableRows[0].modelId,this.selectedTableRows.map((function(e){return e.devId})))}}else this.$message({type:"warning",message:"请选择相同机构，同一产品型号进行分配操作！"})}else this.$message({type:"warning",message:"请先选择待操作数据!"})},validPhone:function(e){e.target.value?(console.log(e.target.value),Object(Z["c"])(e.target.value)||this.$message({type:"warning",message:"请输入正确的11位号码"})):this.$message({type:"warning",message:"请输入号码"})},showSOSNum:function(){if(this.selectedTableRows&&this.selectedTableRows.length){var e=this.selectedTableRows.some((function(e){return"1"!=e.status}));if(e)this.$message({type:"warning",message:"不能为离线设备设置SOS号码！"});else{var t=this.selectedTableRows.some((function(e){return"3"!=e.deviceType&&"4"!=e.deviceType}));t?this.$message({type:"warning",message:"只能给随身宝设备设置SOS号码！"}):this.setSOSDialogVisible=!0}}else this.$message({type:"warning",message:"请先选择待操作数据!"})},setSOSNum:function(){var e=this,t=this.selectedTableRows.map((function(e){return e.id}));console.log(t),this.loading=!0,this.$api.post("/bms/Device/setSOSNum",{idList:t,sosNum:this.sosNum}).then((function(t){"00000"===t.status?(e.$message({type:"success",message:"设置完成，请及时验证"}),e.loading=!1,e.setSOSDialogVisible=!1):(e.loading=!1,e.$message({type:"error",message:"设置失败"}))})).catch((function(t){e.loading=!1}))},handleOpenRenew:function(){if(this.selectedTableRows&&this.selectedTableRows.length){var e=this.selectedTableRows.map((function(e){return e.orgId+"-"+e.modelId}));if(1==new Set(e).size){var t=this.selectedTableRows.some((function(e){return!e.productModelName}));if(t)this.$message({type:"warning",message:"请先为设备分配产品型号"});else{var a=this.selectedTableRows.some((function(e){return"0"===e.activeStatus}));a?this.$message({type:"warning",message:"产品还未激活, 不能续费"}):this.$refs.stationDeviceSpecRenewDialog.show(this.selectedTableRows[0].orgId,this.selectedTableRows[0].orgName,this.selectedTableRows[0].modelId,this.selectedTableRows[0].billingMethod,this.selectedTableRows.map((function(e){return e.devId})))}}else this.$message({type:"warning",message:"请选择相同机构，同一产品型号进行续费操作！"})}else this.$message({type:"warning",message:"请先选择待操作数据!"})},handle3D:function(e){var t=this;this.$api.get("/bms/Device/getRealTimeMonitorUrl/".concat(e)).then((function(e){"00000"===e.status&&e.data?window.open(e.data,"_blank"):t.$message({type:"warning",message:"无法获取3D页面地址"})}))},gotoReport:function(e){}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","stationDeviceIndex")}))},beforeRouteLeave:function(e,t,a){["stationDeviceInfo"].includes(e.name)||this.$store.commit("keepAlive/remove","stationDeviceIndex"),a()}},K=J,U=(a("6901"),Object(d["a"])(K,s,l,!1,null,"4d91d240",null));t["default"]=U.exports}}]);