(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2bbcde3c"],{"0def":function(e,t,r){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},"1b5c":function(e,t,r){},2973:function(e,t,r){"use strict";r("0def")},"41a8":function(e,t,r){"use strict";r.r(t);var l=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{"padding-bottom":"80px"}},[r("page-header",{attrs:{title:(e.$route.params.id?"编辑":"新增")+"订单"}}),r("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.rules,"label-width":"110px",size:"small"}},[r("page-main",{attrs:{title:"基础信息"}},[r("el-row",{staticStyle:{padding:"20px","box-sizing":"border-box"},attrs:{gutter:20}},[r("el-col",{attrs:{md:8}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"订单类型",prop:"orderType"}},[r("el-select",{attrs:{placeholder:"请选择订单类型"},model:{value:e.reqForm.orderType,callback:function(t){e.$set(e.reqForm,"orderType",t)},expression:"reqForm.orderType"}},[r("el-option",{attrs:{label:"机构",value:"org"}})],1)],1)],1),e.$route.params.id?r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"订单编号",prop:"productModelName"}},[e._v(" "+e._s(e.reqForm.orderNo)+" ")])],1):e._e(),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"客户",prop:"customerName"}},[r("el-input",{staticStyle:{"max-width":"214px"},attrs:{placeholder:"请输入客户"},model:{value:e.reqForm.customerName,callback:function(t){e.$set(e.reqForm,"customerName",t)},expression:"reqForm.customerName"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"所属机构",prop:"orgId"}},[r("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.reqForm.orgId,callback:function(t){e.$set(e.reqForm,"orgId",t)},expression:"reqForm.orgId"}},e._l(e.dict.orgs,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"订单名称",prop:"orderName"}},[r("el-input",{staticStyle:{"max-width":"214px"},attrs:{placeholder:"请输入订单名称"},model:{value:e.reqForm.orderName,callback:function(t){e.$set(e.reqForm,"orderName",t)},expression:"reqForm.orderName"}})],1)],1)],1)],1),r("page-main",{attrs:{title:"服务信息"}},[e.reqForm.id?e._e():r("el-radio-group",{attrs:{size:"small"},model:{value:e.reqForm.buyType,callback:function(t){e.$set(e.reqForm,"buyType",t)},expression:"reqForm.buyType"}},[r("el-radio-button",{attrs:{label:"model"}},[e._v("产品型号")]),r("el-radio-button",{attrs:{label:"server"}},[e._v("服务")])],1),"model"===e.reqForm.buyType?r("div",{staticStyle:{"margin-top":"20px"}},[r("div",[e._v(" 产品型号: "),r("el-select",{staticStyle:{"margin-left":"10px"},attrs:{disabled:e.reqForm.id,size:"small",filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchModels,change:e.handleModelChange},model:{value:e.modelId,callback:function(t){e.modelId=t},expression:"modelId"}},e._l(e.dict.models,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1),r("div",{staticStyle:{position:"relative","margin-top":"10px"}},[e._v(" 基础服务: "),r("el-table",{ref:"table",staticClass:"list-table",staticStyle:{"margin-top":"15px"},attrs:{data:e.serverTables,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"},"span-method":e.objectSpanMethod}},[r("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),r("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),r("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"120",align:"center"}},[[e._v(" 基础服务 ")]],2),r("el-table-column",{attrs:{prop:"billingMethodName",label:"计价方式",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.$index,a=t.row;return[r("el-select",{staticStyle:{width:"100%"},attrs:{size:"small",filterable:"",placeholder:"请选择"},on:{change:function(t){return e.handleBillingMethodChange(l,t)}},model:{value:a.billingMethod,callback:function(t){e.$set(a,"billingMethod",t)},expression:"row.billingMethod"}},[r("el-option",{attrs:{label:"按年",value:"Y"}}),r("el-option",{attrs:{label:"按季",value:"Q"}}),r("el-option",{attrs:{label:"按月",value:"M"}})],1)]}}],null,!1,1389229671)}),r("el-table-column",{attrs:{prop:"unitPrice",label:"单价",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[r("el-input",{attrs:{type:"number",size:"small",placeholder:"请输入单价"},model:{value:l.unitPrice,callback:function(t){e.$set(l,"unitPrice",t)},expression:"row.unitPrice"}})]}}],null,!1,3994408811)}),r("el-table-column",{attrs:{prop:"billingUnit",label:"单位",width:"90",align:"center"}},[[e._v(" 元 ")]],2),r("el-table-column",{attrs:{prop:"buyNum",label:"数量",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[r("el-input",{attrs:{type:"number",size:"small",placeholder:"请输入数量"},model:{value:l.buyNum,callback:function(t){e.$set(l,"buyNum",t)},expression:"row.buyNum"}})]}}],null,!1,2729569637)})],1)],1)]):e._e(),r("div",{staticStyle:{position:"relative","margin-top":"20px"}},[e._v(" 增值服务: "),r("el-button",{staticStyle:{position:"absolute",top:"0",right:"0"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.$refs.selectorDialog.show(e.modelId)}}},[e._v("选择服务")]),r("el-table",{ref:"table",staticClass:"list-table",staticStyle:{"margin-top":"15px"},attrs:{data:e.zhengZhiTables,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[r("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),r("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),r("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"120",align:"center"}},[[e._v(" 增值服务 ")]],2),r("el-table-column",{attrs:{prop:"billingMethodName",label:"计价方式",width:"140",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.$index,a=t.row;return[r("el-select",{staticStyle:{width:"100%"},attrs:{size:"small",filterable:"",placeholder:"请选择"},on:{change:function(t){return e.handleIncrementBillingMethodChange(l,a.id,t)}},model:{value:a.billingMethod,callback:function(t){e.$set(a,"billingMethod",t)},expression:"row.billingMethod"}},[r("el-option",{attrs:{label:"按年",value:"Y"}}),r("el-option",{attrs:{label:"按季",value:"Q"}}),r("el-option",{attrs:{label:"按月",value:"M"}})],1)]}}])}),r("el-table-column",{attrs:{prop:"unitPrice",label:"单价",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[r("el-input",{attrs:{type:"number",size:"small",placeholder:"请输入单价"},model:{value:l.unitPrice,callback:function(t){e.$set(l,"unitPrice",t)},expression:"row.unitPrice"}})]}}])}),r("el-table-column",{attrs:{prop:"billingUnit",label:"单位",width:"90",align:"center"}},[[e._v(" 元 ")]],2),r("el-table-column",{attrs:{prop:"buyNum",label:"数量",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[r("el-input",{attrs:{type:"number",min:1,size:"small",placeholder:"请输入数量"},model:{value:l.buyNum,callback:function(t){e.$set(l,"buyNum",t)},expression:"row.buyNum"}})]}}])}),r("el-table-column",{attrs:{prop:"x9",label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.$index;return[r("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.handleRemoveServiceAction(l)}}},[e._v("删除")])]}}])})],1)],1)],1),r("page-main",{attrs:{title:"订单价格"}},[r("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px",width:"500px"},attrs:{gutter:20}},[r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"总价",prop:"totalAmount"}},[r("div",[r("span",{staticStyle:{"font-size":"22px",color:"red"}},[e._v(e._s(e.calcTotalPrice))]),e._v(" 元")])])],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"实际支付金额",prop:"payAmount"}},[r("el-input",{attrs:{type:"number",placeholder:"请输入实际支付金额"},model:{value:e.reqForm.payAmount,callback:function(t){e.$set(e.reqForm,"payAmount",t)},expression:"reqForm.payAmount"}},[r("template",{slot:"append"},[e._v("元")])],2)],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"支付方式",prop:"payMethod"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择支付方式"},model:{value:e.reqForm.payMethod,callback:function(t){e.$set(e.reqForm,"payMethod",t)},expression:"reqForm.payMethod"}},[r("el-option",{attrs:{label:"银联",value:"YL"}}),r("el-option",{attrs:{label:"支付宝",value:"ZFB"}}),r("el-option",{attrs:{label:"微信",value:"WX"}}),r("el-option",{attrs:{label:"云闪付",value:"YSF"}}),r("el-option",{attrs:{label:"数字人民币",value:"SZRMB"}}),r("el-option",{attrs:{label:"现金",value:"XJ"}}),r("el-option",{attrs:{label:"其他",value:"QT"}})],1)],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"账户名",prop:"accountName"}},[r("el-input",{attrs:{placeholder:"请输入账户名"},model:{value:e.reqForm.accountName,callback:function(t){e.$set(e.reqForm,"accountName",t)},expression:"reqForm.accountName"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"账号",prop:"accountNo"}},[r("el-input",{attrs:{placeholder:"请输入账号"},model:{value:e.reqForm.accountNo,callback:function(t){e.$set(e.reqForm,"accountNo",t)},expression:"reqForm.accountNo"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",rows:5,placeholder:"请输入备注"},model:{value:e.reqForm.remark,callback:function(t){e.$set(e.reqForm,"remark",t)},expression:"reqForm.remark"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"订单状态",prop:"payStatus"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择支付方式"},model:{value:e.reqForm.payStatus,callback:function(t){e.$set(e.reqForm,"payStatus",t)},expression:"reqForm.payStatus"}},[r("el-option",{attrs:{label:"待支付",value:"1"}}),r("el-option",{attrs:{label:"已支付",value:"2"}}),r("el-option",{attrs:{label:"已取消",value:"3"}})],1)],1)],1)],1)],1)],1),r("fixed-action-bar",[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1),r("SelectorDialog",{ref:"selectorDialog",attrs:{title:e.currModelName,"selected-ids":e.zhengZhiTables.map((function(e){return e.id}))||[]},on:{"on-selected":e.handleFeedbackSelected}})],1)},a=[],o=r("2909"),i=r("5530"),n=(r("d81d"),r("99af"),r("b680"),r("4de4"),r("b0c0"),r("a434"),r("365c")),s=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{attrs:{title:"选择服务",width:"800px",visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[r("page-main",{staticStyle:{margin:"0",padding:"0"},attrs:{title:"正在为产品型号 "+(e.title||"")+" 设置增值服务"}},[r("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.convertTableDatas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"50",align:"center",selectable:e.checkItemFn}}),r("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),r("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),r("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"140",align:"center"}})],1)],1),r("div",{attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"small",type:"primary",disabled:!e.selectedRows.length,loading:e.loading},on:{click:e.handleSelectedAction}},[e._v("确定(已选择"+e._s(e.selectedRows.length||0)+"条)")])],1)],1)},d=[],c=(r("caad"),r("2532"),{components:{},data:function(){return{visible:!1,loading:!1,productModelId:void 0,table:{datas:[]},selectedRows:[]}},props:{title:String,selectedIds:{type:Array,default:function(){return[]}}},computed:{convertTableDatas:function(){var e=this,t=[];return(this.table.datas||[]).map((function(r){e.selectedIds.includes(r.id)||t.push(r)})),t}},mounted:function(){},methods:{show:function(e){this.productModelId=e,this.fetchDatas(),this.visible=!0},close:function(){this.table.datas=[],this.selectedRows=[],this.productModelId=void 0,this.visible=!1},fetchDatas:function(){var e=this;this.$api.get("/bms/server/listZengzhiServer",{params:{productModelId:this.productModelId}}).then((function(t){"00000"===t.status&&t.data?e.table.datas=t.data:e.table.datas=[]}))},handleSelectedAction:function(){this.$emit("on-selected",this.selectedRows||[]),this.close()},handleSelectionChange:function(e){this.selectedRows=this.$refs.table.selection||[]},checkItemFn:function(e){return!(this.selectedIds||[]).includes(e.id)}}}),u=c,m=(r("c560"),r("2877")),p=Object(m["a"])(u,s,d,!1,null,"3ce539ee",null),b=p.exports,h={components:{SelectorDialog:b},data:function(){return{baseURL:n["a"],keyword:"",currModelName:void 0,dict:{serverTypes:[],models:[],orgs:[]},reqForm:{id:void 0,buyType:"model",discountAmount:0,orderType:"org",customerName:void 0,orgId:void 0,orderName:void 0,totalAmount:0,payAmount:void 0,payMethod:void 0,accountName:void 0,accountNo:void 0,remark:void 0,payStatus:"1",productModelName:void 0,productModelVO:{},serverItemList:[]},rules:{orderType:[{required:!0,trigger:"blur",message:"请填写订单类型"}],customerName:[{required:!0,trigger:"blur",message:"请填写客户"}],orgId:[{required:!0,trigger:"blur",message:"请填写所属机构"}],orderName:[{required:!0,trigger:"blur",message:"请填写订单名称"}],payAmount:[{required:!0,trigger:"blur",message:"请填写实际支付金额"}],payMethod:[{required:!0,trigger:"blur",message:"请填写支付方式"}],payStatus:[{required:!0,trigger:"blur",message:"请填写订单状态"}]},modelId:void 0,serverTables:[],zhengZhiTables:[],submitLoading:!1}},computed:{calcTotalPrice:function(){var e=this.serverTables.map((function(e){return(e.unitPrice||0)*(e.buyNum||0)})),t=this.zhengZhiTables.map((function(e){return(e.unitPrice||0)*(e.buyNum||0)})),r=0;return e.concat(t).map((function(e){r+=e})),r?r.toFixed(2):0}},mounted:function(){this.handleFetchOrgs(),this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/server-order/getOrderInfo/".concat(e),{}).then((function(e){if("00000"===e.status&&e.data){t.reqForm=e.data;var r=t.reqForm.id&&e.data.productModelVO&&e.data.productModelVO.modelItemList?(e.data.productModelVO.modelItemList||[]).map((function(t){return Object(i["a"])(Object(i["a"])({},t),{},{billingMethod:e.data.productModelVO.billingMethod,billingMethodCName:e.data.productModelVO.billingMethodCName,billingUnit:e.data.productModelVO.billingUnit,buyNum:e.data.productModelVO.buyNum,id:e.data.productModelVO.id,modelId:e.data.productModelVO.modelId,orderId:e.data.productModelVO.orderId,productModelDesc:e.data.productModelVO.productModelDesc,productModelName:e.data.productModelVO.productModelName,unitDiscountPrice:e.data.productModelVO.unitDiscountPrice,unitPrice:e.data.productModelVO.unitPrice})})):[];e.data.productModelVO&&(t.modelId=e.data.productModelVO.modelId),t.serverTables=r,t.zhengZhiTables=e.data.serverItemList||[],t.handleFetchModels()}}))},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))},handleFetchModels:function(){var e=this;"model"===this.reqForm.buyType&&this.$api.get("/bms/productModel/listModel4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.models=t.data:e.dict.models=[]}))},handleFetchZengzhiServer:function(){var e=this;this.$api.get("/bms/server/listZengzhiServer",{}).then((function(t){"00000"===t.status&&t.data?e.zengzhiServers=t.data:e.zengzhiServers=[]}))},handleBillingMethodChange:function(e,t){var r=this;t&&this.$api.get("/bms/productModel/getPriceByBillingMethod",{params:{productModelId:this.modelId,billingMethod:t}}).then((function(t){"00000"===t.status&&t.data?r.$set(r.serverTables[e],"unitPrice",t.data.unitPrice):r.$set(r.serverTables[e],"unitPrice",void 0)}))},handleIncrementBillingMethodChange:function(e,t,r){var l=this;r&&this.$api.get("/bms/server/getPriceByBillingMethod",{params:{serverId:t,billingMethod:r}}).then((function(t){"00000"===t.status&&t.data?l.$set(l.zhengZhiTables[e],"unitPrice",t.data.unitPrice):l.$set(l.zhengZhiTables[e],"unitPrice",void 0)}))},handleModelChange:function(e){var t=this;if(this.modelId=e,e){var r=this.dict.models.filter((function(t){return t.id===e}));this.currModelName=r&&r.length?r[0].name:"",this.$api.get("/bms/productModel/listServerByModelId",{params:{productModelId:e}}).then((function(e){if("00000"===e.status&&e.data){var r=[],l=[];e.data.map((function(e){"2"===e.serverCate||"增值服务"===e.serverCateName?l.push(e):"1"!==e.serverCate&&"基础服务"!==e.serverCateName||r.push(e)})),t.serverTables=r}else t.serverTables=[]}))}else this.serverTables=[]},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var r=JSON.parse(JSON.stringify(t.reqForm)),l={},a=[];if("model"===t.reqForm.buyType){if(t.serverTables&&t.serverTables.length){var i=t.serverTables[0].billingMethod,n=t.serverTables[0].unitPrice,s=t.serverTables[0].buyNum;if(!i)return t.$message({type:"error",message:"验证基础服务失败, 请选择 计价方式"}),void(t.submitLoading=!1);if(!n&&0!==n)return t.$message({type:"error",message:"验证基础服务失败, 请输入 单价"}),void(t.submitLoading=!1);if(!s)return t.$message({type:"error",message:"验证基础服务失败, 请输入 数量"}),void(t.submitLoading=!1);l["billingMethod"]=i,l["unitPrice"]=n,l["buyNum"]=s,l["modelItemList"]=t.serverTables}l["modelId"]=t.modelId}if(t.zhengZhiTables&&t.zhengZhiTables.length){var d=void 0,c=t.zhengZhiTables.some((function(e){return d||(e.billingMethod||(d="验证增值服务失败, 请选择 计价方式"),e.unitPrice||0===e.unitPrice||(d="验证增值服务失败, 请输入 单价"),e.buyNum||(d="验证增值服务失败, 请输入 数量")),!e.billingMethod||!e.unitPrice&&0!==e.unitPrice||!e.buyNum}));if(c)return t.$message({type:"error",message:d}),void(t.submitLoading=!1);a=Object(o["a"])(t.zhengZhiTables),a.map((function(e){e["serverId"]||(e["serverId"]=e.id)}))}r["productModelVO"]=l,r["serverItemList"]=a,r["discountedAmount"]=t.reqForm.payAmount,r["totalAmount"]=t.calcTotalPrice;var u=t.$route.params.id?t.$api.post("/bms/server-order/updateOrder",r):t.$api.post("/bms/server-order/addOrder",r);u.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),t.submitLoading=!1,setTimeout((function(){t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},handleFeedbackSelected:function(e){this.zhengZhiTables=this.zhengZhiTables.concat(e)},handleRemoveServiceAction:function(e){this.zhengZhiTables.splice(e,1)},handleOpenServiceChk:function(){this.reqForm.productModelName?this.$refs.selectorDialog.show():this.$message({type:"warning",message:"请先填写产品型号"})},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,r=e.columnIndex;if(r>=3)return 0===t?{rowspan:1e3,colspan:1}:{rowspan:0,colspan:0}}}},g=h,v=(r("2973"),r("97af"),Object(m["a"])(g,l,a,!1,null,"302c7910",null));t["default"]=v.exports},"97af":function(e,t,r){"use strict";r("1b5c")},a06d:function(e,t,r){},c560:function(e,t,r){"use strict";r("a06d")}}]);