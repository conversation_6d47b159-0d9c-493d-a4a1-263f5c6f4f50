(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2c13f016"],{8763:function(n,e,t){},eaf1:function(n,e,t){"use strict";t.r(e);var a=function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"test-simple"},[t("h1",[n._v("简单条件编译测试")]),n._m(0),n._m(1),t("div",{staticClass:"content"},[t("p",[n._v("当前公司: "+n._s(n.currentCompany))]),t("p",[n._v("环境变量: "+n._s(n.envCompany))]),t("p",[n._v("编译时公司: "+n._s(n.compileTimeCompany))])])])},o=[function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"boe-banner"},[t("h2",[n._v("BOE 专用横幅")]),t("p",[n._v("这个内容只在 BOE 版本中显示")])])},function(){var n=this,e=n.$createElement,t=n._self._c||e;return t("div",{staticClass:"default-banner"},[t("h2",[n._v("默认版本横幅")]),t("p",[n._v("这个内容只在默认版本中显示")])])}],s={name:"TestSimple",data:function(){return{currentCompany:"unknown",envCompany:"boe",compileTimeCompany:"unknown"}},mounted:function(){this.currentCompany="BOE",console.log("BOE 版本代码执行"),this.compileTimeCompany="DEFAULT (编译时)",console.log("默认编译时代码执行")}},c=s,i=(t("ff8a"),t("2877")),r=Object(i["a"])(c,a,o,!1,null,"38074f6f",null);e["default"]=r.exports},ff8a:function(n,e,t){"use strict";t("8763")}}]);