(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2cc8b2d8"],{"4ce0":function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticStyle:{"padding-bottom":"80px"}},[s("page-header",{attrs:{title:"服务信息详情查看"}}),s("page-main",{attrs:{title:"基础信息"}},[s("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[s("el-col",{staticStyle:{width:"100px"},attrs:{md:6}},[e.reqForm.serverIcon?s("el-image",{style:"width: 80px; height: 80px; vertical-align: middle;",attrs:{src:e.reqForm.serverIcon,fit:"fill"}}):e._e()],1),s("el-col",{attrs:{md:18}},[s("div",[s("span",{staticClass:"label-field"},[e._v("服务名称:")]),e._v(" "),s("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.serverName))])]),s("div",[s("span",{staticClass:"label-field"},[e._v("服务介绍:")]),e._v(" "),s("span",{staticClass:"value-field",domProps:{innerHTML:e._s(e.reqForm.serverDesc)}})]),s("div",[s("span",{staticClass:"label-field"},[e._v("精简协议:")]),e._v(" "),s("span",{staticClass:"value-field",domProps:{innerHTML:e._s(e.reqForm.simpleAgreement)}})]),s("div",[s("span",{staticClass:"label-field"},[e._v("服务协议:")]),e._v(" "),s("span",{staticClass:"value-field",domProps:{innerHTML:e._s(e.reqForm.agreement)}})])])],1)],1),s("page-main",{attrs:{title:"服务价格"}},e._l(e.reqForm.serverPriceVOList,(function(t,i){return s("el-row",{key:i,staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px",background:"rgb(49 172 209 / 8%)"},attrs:{gutter:20}},[s("el-col",{attrs:{md:4}},[s("span",{staticClass:"label-field"},[e._v("计费方式:")]),e._v(" "),s("span",{staticClass:"value-field"},[e._v(e._s(t.billingMethodName||"-"))])]),s("el-col",{attrs:{md:4}},[s("span",{staticClass:"label-field"},[e._v("单价:")]),e._v(" "),s("span",{staticClass:"value-field"},[e._v(e._s(t.unitPrice||"-"))])]),s("el-col",{attrs:{md:4}},[s("span",{staticClass:"label-field"},[e._v("优惠价:")]),e._v(" "),s("span",{staticClass:"value-field"},[e._v(e._s(t.discountPrice||"-"))])])],1)})),1),s("fixed-action-bar",[s("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1)},a=[],r=(s("d81d"),s("4de4"),{data:function(){return{reqForm:{serverPriceVOList:[]}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/server/getServerInfo/".concat(e),{}).then((function(e){if("00000"===e.status&&e.data){if(e.data.serverPriceVOList&&e.data.serverPriceVOList.length){var s=[null,null,null];e.data.serverPriceVOList.map((function(e){"Y"===e.billingMethod?s[0]=e:"Q"===e.billingMethod?s[1]=e:"M"===e.billingMethod&&(s[2]=e)})),e.data.serverPriceVOList=s.filter((function(e){return e}))}t.reqForm=e.data}}))}}}),l=r,n=(s("9653"),s("2877")),c=Object(n["a"])(l,i,a,!1,null,"369cc428",null);t["default"]=c.exports},9653:function(e,t,s){"use strict";s("f62b")},f62b:function(e,t,s){}}]);