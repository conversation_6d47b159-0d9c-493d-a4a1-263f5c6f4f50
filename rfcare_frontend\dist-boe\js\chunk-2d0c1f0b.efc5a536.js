(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c1f0b"],{"47d6":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"详情查看"}}),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 设备编码: "+e._s(e.vo.devCode||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 入库时间: "+e._s(e.vo.createTime||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备名称: "+e._s(e.vo.devName||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备类型: "+e._s(e.vo.deviceTypeName||"-")+" ")]),e.isSsb(e.vo)?e._e():a("el-col",{attrs:{md:8}},[e._v(" 设备场景: "+e._s(e.vo.devSceneName||"-")+" ")]),e.isSsb(e.vo)?e._e():a("el-col",{attrs:{md:8}},[e._v(" 设备模式: "+e._s(e.vo.devModelName||"-")+" ")]),a("el-col",{attrs:{md:16}},[e._v(" 地址: "+e._s(e.vo.houseAddr||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 门牌号: "+e._s(e.vo.houseNumber||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 所属服务站: "+e._s(e.vo.stationName||"-")+" ")]),e.isSsb(e.vo)?e._e():a("el-col",{attrs:{md:8}},[e._v(" 摔倒响应时间: "+e._s(e.vo.fallResponseTime||"-")+" ")]),e.isSsb(e.vo)?e._e():a("el-col",{attrs:{md:8}},[e._v(" 久滞响应时间: "+e._s(e.vo.longlagResponseTime||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活状态: "+e._s(e.vo.activeStatusName||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活时间: "+e._s(e.vo.activeTime||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 运行状态: "+e._s(e.vo.statusName||"-")+" ")]),e.isSsb(e.vo)?e._e():a("el-col",{attrs:{md:8}},[e._v(" 安装方式: "+e._s(e.vo.spotTypeName||"-")+" ")])],1)],1),e.isSsb(e.vo)?e._e():a("page-main",{attrs:{title:"房间尺寸"}},[a("el-popover",{attrs:{placement:"left",width:"500",trigger:"hover"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/room-"+e.vo.spotType+".jpg"}}),a("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),a("el-table",{staticClass:"list-table",attrs:{data:e.vo.roomVO?[e.vo.roomVO]:[],border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"x",label:"长（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"y",label:"宽（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"z",label:"高（米）",align:"center"}})],1)],1),e.isSsb(e.vo)?e._e():a("page-main",{attrs:{title:"传感器方位"}},[a("el-popover",{attrs:{placement:"left",width:"500",trigger:"hover"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/device-"+e.vo.spotType+".jpg"}}),a("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),a("el-table",{staticClass:"list-table",attrs:{data:e.vo.roomSensorVO?[e.vo.roomSensorVO]:[],border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"x",label:"长（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"y",label:"宽（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"z",label:"高（米）",align:"center"}})],1)],1),e.isSsb(e.vo)?e._e():a("page-main",{attrs:{title:"门方位"}},[a("el-popover",{attrs:{placement:"left",width:"500",trigger:"hover"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/door-"+e.vo.spotType+"-0.jpg"}}),a("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),a("el-table",{staticClass:"list-table",attrs:{data:e.vo.roomGateVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"gid",label:"门名称",align:"center"}}),a("el-table-column",{attrs:{prop:"length",label:"长（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"width",label:"宽（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"height",label:"高（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"direction",label:"门方位",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e.directionType(a.direction))+" ")]}}],null,!1,3077903142)})],1)],1),e.isSsb(e.vo)?e._e():a("page-main",{attrs:{title:"区域"}},[a("el-popover",{attrs:{placement:"left",width:"500",trigger:"hover"}},[a("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/area-"+e.vo.spotType+".jpg"}}),a("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),a("el-table",{staticClass:"list-table",attrs:{data:e.vo.roomRegionVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"rid",label:"区域名称",align:"center"}}),a("el-table-column",{attrs:{prop:"cls",label:"区域范围",align:"center"}}),a("el-table-column",{attrs:{prop:"positionY",label:"区域中心至区域头部",align:"center"}}),a("el-table-column",{attrs:{prop:"positionX",label:"区域中心至墙侧边",align:"center"}}),a("el-table-column",{attrs:{prop:"scaleX",label:"区域长（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"scaleY",label:"区域宽（米）",align:"center"}}),a("el-table-column",{attrs:{prop:"scaleZ",label:"区域高（米）",align:"center"}})],1)],1),a("page-main",{attrs:{title:"用户信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 关联用户: "+e._s(e.vo.memberPhone||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 关联管家: "+e._s(e.vo.chambName||"-")+" ")])],1)],1),a("page-main",{attrs:{title:"被监护人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.vo.orderOlderVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"电话",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"birthday",label:"出生年月",align:"center"}}),a("el-table-column",{attrs:{prop:"idcardTypeDesc",label:"证件类型",align:"center"}}),a("el-table-column",{attrs:{prop:"idcardCode",label:"证件号码",align:"center"}}),a("el-table-column",{attrs:{prop:"liveTypeDesc",label:"居住类型",align:"center"}})],1)],1),e.isSsb(e.vo)?e._e():a("page-main",{attrs:{title:"紧急联系人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.vo.orderContactorVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"contactName",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"contactPhone",label:"电话",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}],null,!1,3709586088)}),a("el-table-column",{attrs:{prop:"relationTypeDesc",label:"联系人标签",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址","min-width":200,align:"center"}})],1)],1),"3"==e.vo.deviceType?a("page-main",{attrs:{title:"定位记录(最近5条记录）"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.vo.locationList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"eventTime",label:"时间",align:"center"}}),a("el-table-column",{attrs:{prop:"address",label:"地址",align:"center"}})],1)],1):e._e(),e.$store.state.user.member&&"system"!==e.$store.state.user.member.role?a("page-main",{attrs:{title:"管家处理事件"}},[a("div",{staticStyle:{position:"absolute",left:"125px",top:"15px","font-size":"14px",color:"#666"}},[e._v(" (共"),a("span",{staticStyle:{color:"#40b0ff",margin:"0 3px"}},[e._v(e._s(e.table.total||0))]),e._v("条) ")]),a("div",{staticStyle:{position:"absolute",right:"20px",top:"10px"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:e.handleRadioChange},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),a("el-radio-button",{attrs:{label:"0"}},[e._v("未处理")]),a("el-radio-button",{attrs:{label:"1"}},[e._v("进行中")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("已完成")]),a("el-radio-button",{attrs:{label:"3"}},[e._v("主动取消")])],1),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",size:"small",plain:""},on:{click:e.gotoChambEventPage}},[e._v("查看全部事件")])],1),a("el-row",{staticStyle:{"box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"title",label:"事件位置",minWidth:"300",align:"center"}}),a("el-table-column",{attrs:{prop:"orderTypeName",label:"告警事件",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",width:"130",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"事件处理状态",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"seatName",label:"事件处理人",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"事件发生时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"modifyTime",label:"事件更新时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"servicePlatformEventDetail",params:{id:t.row.id}})}}},[e._v("查看详情")])]}}],null,!1,2822615732)})],1)],1)],1)],1):e._e(),e.isSsb(e.vo)?e._e():a("page-main",{attrs:{title:"参数信息"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.vo.heartRate>0||e.vo.breathRate>0?[{heartRate:e.vo.heartRate,breathRate:e.vo.breathRate,remark:e.vo.remark}]:[],border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",label:"目标",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"heartRate",label:"心率频次(次/分)",align:"center"}}),a("el-table-column",{attrs:{prop:"breathRate",label:"呼吸频次(次/分)",align:"center"}}),a("el-table-column",{attrs:{prop:"remark",label:"备注",align:"center"}})],1)],1),a("page-main",{attrs:{title:"服务信息"}},[a("el-row",{staticStyle:{padding:"0 0","box-sizing":"border-box"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("产品型号:")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.vo.productModelName))])])])],1),e.isSsb(e.vo)?e._e():a("div",[a("div",{staticStyle:{"margin-top":"20px"}},[e._v("基础服务")]),a("el-table",{ref:"table",staticClass:"list-table",staticStyle:{"margin-top":"12px"},attrs:{data:e.vo.baseServerDevVOList,border:"",stripe:"","span-method":e.objectSpanMethod}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"billingMethodName",label:"计费方式",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStartDate",label:"生效时间",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverEndDate",label:"失效时间",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStatusName",label:"服务状态",width:"140",align:"center"}})],1),a("div",{staticStyle:{"margin-top":"20px"}},[e._v("增值服务")]),a("el-table",{ref:"table",staticClass:"list-table",staticStyle:{"margin-top":"12px"},attrs:{data:e.vo.zengzhiServerDevVOList,border:"",stripe:""}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"serverCateName",label:"服务类型",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"billingMethodName",label:"计费方式",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStartDate",label:"生效时间",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverEndDate",label:"失效时间",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"serverStatusName",label:"服务状态",width:"140",align:"center"}})],1)],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1)},r=[],o=(a("caad"),a("b0c0"),{name:"stationDeviceInfo",data:function(){return{vo:{},status:"",table:{datas:[],total:0}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{isSsb:function(e){return"3"==e.deviceType||"4"==e.deviceType},directionType:function(e){switch(e){case 0:return"左";case 1:return"前";case 2:return"右";case 3:return"后";default:return"未知"}},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/Device/getDeviceInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.vo=e.data,t.fetchEventList())}))},handleRadioChange:function(e){this.fetchEventList(e)},fetchEventList:function(e){var t=this;this.vo.devCode&&this.$api.get("/bms/event/listByDevice",{params:{deviceCode:this.vo.devCode,status:e,pageSize:5}}).then((function(e){"00000"===e.status&&e.data&&(t.table.datas=e.data,t.table.total=e.page.total)}))},gotoChambEventPage:function(){this.$router.push({name:"servicePlatformEventQueryByDevice",params:{deviceCode:this.vo.devCode}})},objectSpanMethod:function(e){e.row,e.column;var t=e.rowIndex,a=e.columnIndex;if(a>=4)return 0===t?{rowspan:1e3,colspan:1}:{rowspan:0,colspan:0}}},beforeRouteLeave:function(e,t,a){var l=["stationDeviceIndex"].includes(e.name);!1===l&&this.$store.commit("keepAlive/remove","stationDeviceIndex"),a()}}),s=o,n=a("2877"),i=Object(n["a"])(s,l,r,!1,null,null,null);t["default"]=i.exports}}]);