(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d6592"],{"71bd":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"详情查看"}}),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:12}},[t._v(" 服务站编号: "+t._s(t.vo.code)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 所属机构: "+t._s(t.vo.orgName)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 服务站名称: "+t._s(t.vo.name)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 服务站简称: "+t._s(t.vo.shortName)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 服务站地址: "+t._s(t.vo.addr)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 经度: "+t._s(t.vo.longitude)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 纬度: "+t._s(t.vo.latitude)+" ")]),a("el-col",{attrs:{md:12}},[t._v(" 联系电话: "+t._s(t.vo.phone)+" ")])],1)],1),a("page-main",{attrs:{title:"服务信息"}},[a("el-row",{staticStyle:{"box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[t._v(" 服务站关联的设备("+t._s(t.vo.devCount||0)+"): "),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:t.vo.deviceVOList,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"memberPhone",label:"用户号码",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"houseAddr",label:"地址",align:"center"}}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"是否激活设备",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"modifyTime",label:"激活时间",width:"200",align:"center"}})],1)],1)],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)},o=[],r={data:function(){return{vo:{}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(t){var e=this;t&&this.$api.get("/bms/station/getStationInfo/".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(e.vo=t.data)}))}}},n=r,i=a("2877"),s=Object(i["a"])(n,l,o,!1,null,null,null);e["default"]=s.exports}}]);