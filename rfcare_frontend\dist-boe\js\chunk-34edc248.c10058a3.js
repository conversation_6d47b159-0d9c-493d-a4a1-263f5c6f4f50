(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34edc248"],{"26ef":function(t,e,i){},5002:function(t,e,i){"use strict";i("26ef")},f384:function(t,e,i){"use strict";i.r(e);var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{"padding-bottom":"80px"}},[i("page-header",{attrs:{title:"配置价格"}}),i("el-form",{ref:"reqForm",attrs:{model:t.reqForm,rules:t.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},["org"!==t.$store.state.user.member.role?i("page-main",{attrs:{title:"基础信息"}},[i("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px",width:"820px",margin:"0 auto"},attrs:{gutter:20}},[i("el-col",{attrs:{md:24}},[i("span",{staticClass:"label-field"},[t._v("产品名称:")]),t._v(" "),i("span",{staticClass:"value-field"},[t._v(t._s(t.productModel.productModelName))])]),i("el-col",{attrs:{md:24}},[i("span",{staticClass:"label-field"},[t._v("产品介绍:")]),t._v(" "),i("span",{staticClass:"value-field"},[t._v(t._s(t.productModel.productModelDesc))])]),i("el-col",{attrs:{md:24}},[i("span",{staticClass:"label-field",staticStyle:{position:"relative"}},[i("span",{staticStyle:{position:"absolute",left:"-14px",top:"0",color:"#f56c6c"}},[t._v("*")]),t._v("所属机构:")]),i("el-form-item",{staticStyle:{display:"inline-block"},attrs:{prop:"orgId"}},[i("el-select",{attrs:{size:"small",disabled:void 0!=t.reqForm.id,filterable:"",placeholder:"请选择"},on:{"visible-change":t.handleFetchOrgs},model:{value:t.reqForm.orgId,callback:function(e){t.$set(t.reqForm,"orgId",e)},expression:"reqForm.orgId"}},t._l(t.dict.orgs,(function(t){return i("el-option",{key:t.id,attrs:{label:t.name,value:t.id}})})),1)],1)],1)],1)],1):t._e(),i("page-main",{attrs:{title:"产品价格"}},[i("div",{staticClass:"service-list"},[t._l(t.reqForm.priceList,(function(e,r){return i("el-row",{key:r,staticClass:"service-item",attrs:{gutter:20}},[r>0?i("i",{staticClass:"el-icon-delete",staticStyle:{position:"absolute",top:"20px",right:"20px",color:"red",cursor:"pointer"},on:{click:function(e){return t.handleRemovePriceAction(r)}}}):t._e(),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"计费方式",prop:"priceList."+r+".billingMethod",rules:[{required:!0,message:"请选择计费方式",trigger:"change"}]}},[i("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.billingMethod,callback:function(i){t.$set(e,"billingMethod",i)},expression:"item.billingMethod"}},["Y"!==e.billingMethod&&t.billingMethodMapping["Y"]?t._e():i("el-option",{attrs:{label:"按年",value:"Y"}}),"Q"!==e.billingMethod&&t.billingMethodMapping["Q"]?t._e():i("el-option",{attrs:{label:"按季",value:"Q"}}),"M"!==e.billingMethod&&t.billingMethodMapping["M"]?t._e():i("el-option",{attrs:{label:"按月",value:"M"}})],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"单价",prop:"priceList."+r+".unitPrice",rules:[{required:!0,message:"请输入单价",trigger:"blur"}]}},[i("el-input",{attrs:{type:"number",placeholder:"请输入单价"},model:{value:e.unitPrice,callback:function(i){t.$set(e,"unitPrice",i)},expression:"item.unitPrice"}},[i("template",{slot:"append"},[t._v("元")])],2)],1)],1)],1)})),t.reqForm.priceList.length<3?i("div",{staticStyle:{"margin-top":"20px","margin-left":"-10px"}},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.handleAddPriceAction}},[t._v("添加价格信息")])],1):t._e()],2)])],1),i("fixed-action-bar",[i("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("取消")]),i("el-button",{attrs:{type:"primary",loading:t.submitLoading},on:{click:function(e){return t.handleSubmit("reqForm")}}},[t._v("保存")])],1)],1)},o=[],a=(i("d81d"),i("a434"),i("365c")),l={data:function(){return{baseURL:a["a"],dict:{orgs:[]},reqForm:{id:void 0,modelId:void 0,orgId:void 0,priceList:[{billingMethod:void 0,unitPrice:void 0}]},productModel:{},reqFormRules:{orgId:[{required:!0,message:"请选择所属机构",trigger:"change"}]},submitLoading:!1}},computed:{billingMethodMapping:function(){var t={};return(this.reqForm.priceList||[]).map((function(e){t[e.billingMethod]=!0})),t}},mounted:function(){this.handleFetchOrgs(),this.$route.params.modelId?(this.reqForm.modelId=this.$route.params.modelId,this.fetchProductDetail(this.$route.params.modelId)):this.fetchDetail(this.$route.params.id)},methods:{fetchProductDetail:function(t){var e=this;t&&this.$api.get("/bms/productModel/getProductModelInfo/".concat(this.reqForm.modelId),{}).then((function(t){"00000"===t.status&&t.data&&(e.productModel=t.data)}))},fetchDetail:function(t){var e=this;t&&this.$api.get("/bms/productModel/getProductPriceInfo/".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(e.reqForm=t.data,e.fetchProductDetail(t.data.modelId))}))},handleFetchOrgs:function(){var t=this;this.$api.get("/bms/org/list4Select",{}).then((function(e){"00000"===e.status&&e.data?t.dict.orgs=e.data:t.dict.orgs=[]}))},handleAddPriceAction:function(){this.reqForm.priceList.push({billingMethod:void 0,unitPrice:void 0})},handleRemovePriceAction:function(t){this.reqForm.priceList.splice(t,1)},handleSubmit:function(t){var e=this;this.submitLoading=!0,this.$refs[t].validate((function(t){if(!t)return e.submitLoading=!1,e.$message({type:"error",message:"验证失败"}),!1;var i=JSON.parse(JSON.stringify(e.reqForm)),r=e.$route.params.id?e.$api.post("/bms/productModel/updateProductOrgCfg",i):e.$api.post("/bms/productModel/addProductOrgCfg",i);r.then((function(t){"00000"===t.status?(e.$message({type:"success",message:(e.reqForm.id?"修改":"新增")+"完成"}),e.submitLoading=!1,setTimeout((function(){e.$router.go(-1)}),500)):(e.submitLoading=!1,e.$message({type:"error",message:(e.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(t){e.submitLoading=!1}))}))}}},s=l,n=(i("5002"),i("2877")),d=Object(n["a"])(s,r,o,!1,null,"6569c31e",null);e["default"]=d.exports}}]);