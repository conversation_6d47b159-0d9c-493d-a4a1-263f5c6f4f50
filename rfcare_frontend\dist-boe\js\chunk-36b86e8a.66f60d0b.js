(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-36b86e8a"],{1067:function(e,t,r){"use strict";r("497b")},"3f67":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"被监护人"}}),r("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[r("page-main",{attrs:{title:"基本信息"}},[r("el-row",{staticClass:"block-row",attrs:{gutter:20}},[r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"所属服务站",prop:"stationId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择所属服务站"},model:{value:e.reqForm.stationId,callback:function(t){e.$set(e.reqForm,"stationId",t)},expression:"reqForm.stationId"}},e._l(e.dict.stations,(function(e){return r("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"姓名",prop:"name"}},[r("el-input",{attrs:{maxlength:20,disabled:e.reqForm.hasOrder,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.name,callback:function(t){e.$set(e.reqForm,"name",t)},expression:"reqForm.name"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"性别",prop:"gender"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.gender,callback:function(t){e.$set(e.reqForm,"gender",t)},expression:"reqForm.gender"}},e._l(e.dict.genders,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[r("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入",disabled:e.reqForm.hasOrder},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"证件类型",prop:"idcardType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",disabled:e.reqForm.hasOrder},model:{value:e.reqForm.idcardType,callback:function(t){e.$set(e.reqForm,"idcardType",t)},expression:"reqForm.idcardType"}},e._l(e.dict.idcardType,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"证件号码",prop:"idcardCode"}},[r("el-input",{attrs:{maxlength:30,disabled:e.reqForm.hasOrder,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.idcardCode,callback:function(t){e.$set(e.reqForm,"idcardCode",t)},expression:"reqForm.idcardCode"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"出生日期",prop:"birthday"}},[r("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择开始日期"},model:{value:e.reqForm.birthday,callback:function(t){e.$set(e.reqForm,"birthday",t)},expression:"reqForm.birthday"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"身高(cm)",prop:"height"}},[r("el-input",{attrs:{type:"number",max:300,min:0,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.height,callback:function(t){e.$set(e.reqForm,"height",t)},expression:"reqForm.height"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"体重(kg)",prop:"weight"}},[r("el-input",{attrs:{type:"number",max:999,min:0,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.weight,callback:function(t){e.$set(e.reqForm,"weight",t)},expression:"reqForm.weight"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"血型",prop:"bloodtype"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.bloodtype,callback:function(t){e.$set(e.reqForm,"bloodtype",t)},expression:"reqForm.bloodtype"}},e._l(e.dict.bloodtype,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"既往病史",prop:"medicalHistory"}},[r("el-input",{attrs:{type:"textarea",rows:5,maxlength:200,"show-word-limit":"",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.medicalHistory,callback:function(t){e.$set(e.reqForm,"medicalHistory",t)},expression:"reqForm.medicalHistory"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"过敏史",prop:"allergies"}},[r("el-input",{attrs:{type:"textarea",rows:5,maxlength:200,"show-word-limit":"","show-word-limitv-model":"reqForm.allergies",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.allergies,callback:function(t){e.$set(e.reqForm,"allergies",t)},expression:"reqForm.allergies"}})],1)],1)],1)],1),r("page-main",{attrs:{title:"联系人"}},e._l(e.reqForm.contacts,(function(t,a){return r("el-row",{key:"contacts"+a,staticClass:"block-row",attrs:{gutter:20}},[r("el-col",{attrs:{md:6}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"姓名",prop:"contacts."+a+".contactName",rules:e.reqFormRules.contactName}},[r("el-input",{attrs:{placeholder:"请输入",maxlength:20},model:{value:t.contactName,callback:function(r){e.$set(t,"contactName",r)},expression:"contactor.contactName"}})],1)],1),r("el-col",{attrs:{md:6}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"电话",prop:"contacts."+a+".contactPhone",rules:e.reqFormRules.contactPhone}},[r("el-input",{attrs:{placeholder:"请输入"},model:{value:t.contactPhone,callback:function(r){e.$set(t,"contactPhone",r)},expression:"contactor.contactPhone"}})],1)],1),r("el-col",{attrs:{md:6}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"性别"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.gender,callback:function(r){e.$set(t,"gender",r)},expression:"contactor.gender"}},e._l(e.dict.genders,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:6}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"关系类型"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:t.relationType,callback:function(r){e.$set(t,"relationType",r)},expression:"contactor.relationType"}},e._l(e.dict.relationTypes,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1)],1)})),1),r("div",{staticClass:"item-card"},[r("el-row",{staticClass:"block-row",attrs:{gutter:20}},[r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{"margin-top":"20px"}},[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},o=[],l=r("1da1"),n=(r("96cf"),r("e1a4")),i={data:function(){var e=function(){var e=Object(l["a"])(regeneratorRuntime.mark((function e(t,r,a){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:r?Object(n["b"])(r)||a(new Error("请输入正确的11位手机号码")):a(new Error("请输入手机号")),a();case 2:case"end":return e.stop()}}),e)})));return function(t,r,a){return e.apply(this,arguments)}}(),t=function(){var e=Object(l["a"])(regeneratorRuntime.mark((function e(t,r,a){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:r?Object(n["e"])(r)||a(new Error("姓名只支持中/英文，中文2-10个字，英文50字符")):a(new Error("请输入姓名")),a();case 2:case"end":return e.stop()}}),e)})));return function(t,r,a){return e.apply(this,arguments)}}();return{dict:{stations:[],genders:[{code:"M",name:"男"},{code:"W",name:"女"}],bloodtype:[{code:"01",name:"A"},{code:"02",name:"B"},{code:"03",name:"O"},{code:"04",name:"AB"},{code:"05",name:"其他"}],liveType:[{code:"1",name:"独居"},{code:"2",name:"非独居"},{code:"3",name:"集中居住"},{code:"4",name:"其他"}],idcardType:[{code:"0",name:"身份证"},{code:"1",name:"护照"},{code:"2",name:"港澳通行证"},{code:"3",name:"军官证"},{code:"4",name:"其他"}],safeLevel:[{code:"1",name:"一级"},{code:"2",name:"二级"},{code:"3",name:"三级"}],relationTypes:[{code:"kinsman",name:"亲属"},{code:"neighbor",name:"邻居"},{code:"chanmb",name:"管家"},{code:"other",name:"其他"}]},reqForm:{id:void 0,name:void 0,addr:void 0,houseNumber:void 0,contacts:[{id:void 0,contactName:void 0,gender:void 0,relationType:void 0,contactPhone:void 0},{id:void 0,contactName:void 0,gender:void 0,relationType:void 0,contactPhone:void 0}]},submitLoading:!1,fullLoading:void 0,reqFormRules:{stationId:[{required:!0,trigger:"change",message:"请选择"}],olderValiPhone:[{required:!0,trigger:"blur",validator:e}],valiName:[{required:!0,trigger:"blur",message:"请填写姓名"}],name:[{required:!0,trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],phone:[{required:!0,trigger:"blur",validator:e,getValuesMethod:this.getValuesMethod}],idcardType:[{required:!0,message:"请选择证件类型",trigger:"change"}],idcardCode:[{required:!0,message:"请输入证件号码",trigger:"blur"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}],contactName:[{required:!0,message:"请输入联系人姓名",trigger:"blur"}],contactPhone:[{required:!0,trigger:"blur",validator:e}]}}},mounted:function(){var e=this;return Object(l["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.fetchStations();case 2:e.fetchDetail(e.$route.params.id);case 3:case"end":return t.stop()}}),t)})))()},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("bms/station/older/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data),t.fullLoading.close()})).catch((function(e){console.log(e),t.fullLoading.close()})))},fetchStations:function(){var e=this;return Object(l["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}));case 1:case"end":return t.stop()}}),t)})))()},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var r=JSON.parse(JSON.stringify(t.reqForm));t.$api.post("/bms/station/older/save",r).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},c=i,s=(r("1067"),r("2877")),d=Object(s["a"])(c,a,o,!1,null,"112343b4",null);t["default"]=d.exports},"497b":function(e,t,r){},e1a4:function(e,t,r){"use strict";function a(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function o(e){var t=/^\d{11}$/;return t.test(e)}function l(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function n(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function i(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function c(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function s(e){return c(e)||i(e)}r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return o})),r.d(t,"d",(function(){return l})),r.d(t,"a",(function(){return n})),r.d(t,"e",(function(){return s}))}}]);