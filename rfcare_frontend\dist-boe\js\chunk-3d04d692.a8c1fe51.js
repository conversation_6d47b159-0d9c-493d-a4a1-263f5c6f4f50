(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3d04d692"],{6814:function(e,t,i){},"952f":function(e,t,i){},a3bc:function(e,t,i){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},b671:function(e,t,i){"use strict";i("a3bc")},b828:function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{"padding-bottom":"80px"}},[i("page-header",{attrs:{title:(e.$route.params.id?"编辑":"新增")+"服务信息"}}),i("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.rules,"label-position":"top","label-width":"80px",size:"small"}},[i("page-main",{attrs:{title:"基础信息"}},[i("el-row",{staticStyle:{padding:"20px","box-sizing":"border-box",width:"840px",margin:"0 auto"},attrs:{gutter:20}},[i("el-col",{attrs:{md:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"产品型号",prop:"productModelName"}},[i("el-input",{attrs:{placeholder:"请输入产品型号"},model:{value:e.reqForm.productModelName,callback:function(t){e.$set(e.reqForm,"productModelName",t)},expression:"reqForm.productModelName"}})],1)],1),i("el-col",{attrs:{md:24}},[i("el-form-item",{attrs:{label:"是否包含基础服务:",prop:"needBaseServer"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:e.reqForm.needBaseServer,callback:function(t){e.$set(e.reqForm,"needBaseServer",t)},expression:"reqForm.needBaseServer"}},[i("el-option",{attrs:{label:"是",value:"1"}}),i("el-option",{attrs:{label:"否",value:"0"}})],1)],1)],1),i("el-col",{attrs:{md:24}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"说明",prop:"productModelDesc"}},[i("editor",{attrs:{id:"productModelDescEditor",init:e.productModelDescEditor},model:{value:e.reqForm.productModelDesc,callback:function(t){e.$set(e.reqForm,"productModelDesc",t)},expression:"reqForm.productModelDesc"}})],1)],1)],1)],1),1==e.reqForm.needBaseServer?i("div",[i("page-main",{attrs:{title:"服务包信息(基础服务信息)"}},[i("el-button",{staticStyle:{position:"absolute",top:"12px",right:"16px"},attrs:{type:"primary",size:"mini"},on:{click:e.handleOpenServiceChk}},[e._v("选择服务")]),i("div",{staticStyle:{width:"780px",margin:"0 auto"}},[i("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[i("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),i("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"200",align:"center"}}),i("el-table-column",{attrs:{prop:"serverTypeCName",label:"服务类型",width:"140",align:"center"}}),i("el-table-column",{attrs:{prop:"x9",label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.$index;return[i("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.handleRemoveServiceAction(r)}}},[e._v("删除")])]}}],null,!1,3327849943)})],1)],1)],1),i("page-main",{attrs:{title:"服务价格"}},[i("div",{staticClass:"service-list"},[e._l(e.reqForm.productModelPriceVOList,(function(t,r){return e.reqForm.productModelPriceVOList&&e.reqForm.productModelPriceVOList.length>0?i("el-row",{key:r,staticClass:"service-item",attrs:{gutter:20}},[r>0?i("i",{staticClass:"el-icon-delete",staticStyle:{position:"absolute",top:"20px",right:"20px",color:"red",cursor:"pointer"},on:{click:function(t){return e.handleRemovePriceAction(r)}}}):e._e(),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"计费方式",prop:"productModelPriceVOList."+r+".billingMethod",rules:[{required:!0,message:"请选择计费方式",trigger:"change"}]}},[i("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.billingMethod,callback:function(i){e.$set(t,"billingMethod",i)},expression:"item.billingMethod"}},["Y"!==t.billingMethod&&e.billingMethodMapping["Y"]?e._e():i("el-option",{attrs:{label:"按年",value:"Y"}}),"Q"!==t.billingMethod&&e.billingMethodMapping["Q"]?e._e():i("el-option",{attrs:{label:"按季",value:"Q"}}),"M"!==t.billingMethod&&e.billingMethodMapping["M"]?e._e():i("el-option",{attrs:{label:"按月",value:"M"}})],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"单价",prop:"productModelPriceVOList."+r+".unitPrice",rules:[{required:!0,message:"请输入单价",trigger:"blur"}]}},[i("el-input",{attrs:{type:"number",placeholder:"请输入单价"},model:{value:t.unitPrice,callback:function(i){e.$set(t,"unitPrice",i)},expression:"item.unitPrice"}},[i("template",{slot:"append"},[e._v("元")])],2)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"优惠价",prop:"discountPrice"}},[i("el-input",{attrs:{type:"number",placeholder:"请输入优惠价"},model:{value:t.discountPrice,callback:function(i){e.$set(t,"discountPrice",i)},expression:"item.discountPrice"}},[i("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1):e._e()})),!e.reqForm.productModelPriceVOList||e.reqForm.productModelPriceVOList.length<3?i("div",{staticStyle:{"margin-top":"20px","margin-left":"0"}},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleAddPriceAction}},[e._v("添加服务包价格")])],1):e._e()],2)])],1):e._e()],1),i("fixed-action-bar",[i("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1),i("SelectorDialog",{ref:"selectorDialog",attrs:{title:e.reqForm.serverName,"selected-ids":e.table.datas.map((function(e){return e.id}))||[]},on:{"on-selected":e.handleFeedbackSelected}})],1)},l=[],a=(i("d81d"),i("a434"),i("99af"),i("365c")),o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("el-dialog",{attrs:{title:"选择服务",width:"900px",visible:e.visible,"modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[i("page-main",{staticStyle:{margin:"0",padding:"0"},attrs:{title:"正在为产品型号 "+(e.title||"")+" 设置基础服务"}},[i("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.convertTableDatas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center",selectable:e.checkItemFn}}),i("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),i("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"170",align:"center"}}),i("el-table-column",{attrs:{prop:"createUserName",label:"创建人",width:"140",align:"center"}}),i("el-table-column",{attrs:{prop:"createTime",label:"创建日期",width:"170",align:"center"}})],1)],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{size:"small",type:"primary",disabled:!e.selectedRows.length,loading:e.loading},on:{click:e.handleSelectedAction}},[e._v("确定(已选择"+e._s(e.selectedRows.length||0)+"条)")])],1)],1)},s=[],n=(i("caad"),i("2532"),{components:{},data:function(){return{visible:!1,loading:!1,table:{datas:[]},selectedRows:[]}},props:{title:String,selectedIds:{type:Array,default:function(){return[]}}},computed:{convertTableDatas:function(){var e=this,t=[];return(this.table.datas||[]).map((function(i){e.selectedIds.includes(i.id)||t.push(i)})),t}},mounted:function(){},methods:{show:function(){this.fetchDatas(),this.visible=!0},close:function(){this.table.datas=[],this.selectedRows=[],this.visible=!1},fetchDatas:function(){var e=this;this.$api.get("/bms/productModel/listServer4Model",{params:{}}).then((function(t){"00000"===t.status&&t.data?e.table.datas=t.data:e.table.datas=[]}))},handleSelectedAction:function(){this.$emit("on-selected",this.selectedRows||[]),this.close()},handleSelectionChange:function(e){console.log(e,this.$refs.table.selection),this.selectedRows=this.$refs.table.selection||[]},checkItemFn:function(e){return!(this.selectedIds||[]).includes(e.id)}}}),d=n,c=(i("d948"),i("2877")),u=Object(c["a"])(d,o,s,!1,null,"683e8ce0",null),p=u.exports,m=(i("e562"),i("ca72")),b=(i("030f"),{language_url:"/tinymce/langs/zh_CN.js",language:"zh_CN",skin_url:"/tinymce/skins/ui/oxide",branding:!1,plugins:"link code table lists"}),h={components:{SelectorDialog:p,Editor:m["a"]},data:function(){var e=function(e,t,i){i()};return{baseURL:a["a"],keyword:"",dict:{serverTypes:[]},reqForm:{id:void 0,needBaseServer:"1",productModelName:void 0,productModelDesc:void 0,productModelPriceVOList:[{billingMethod:void 0,unitPrice:void 0,discountPrice:void 0}]},rules:{productModelName:[{required:!0,trigger:"blur",message:"请填写产品型号"}],needBaseServer:[{required:!0,trigger:"change",message:"请选择"}],productModelDesc:[{validator:e}]},table:{datas:[]},submitLoading:!1,productModelDescEditor:Object.assign({selector:"#productModelDescEditor"},b)}},computed:{billingMethodMapping:function(){var e={};return(this.reqForm.productModelPriceVOList||[]).map((function(t){e[t.billingMethod]=!0})),e}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/productModel/getProductModelInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data,t.table.datas=e.data.serverBaseVOList||[])}))},handleAddPriceAction:function(){this.reqForm.productModelPriceVOList||(this.reqForm.productModelPriceVOList=[]),this.reqForm.productModelPriceVOList.push({billingMethod:void 0,unitPrice:void 0})},handleRemovePriceAction:function(e){this.reqForm.productModelPriceVOList.splice(e,1)},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var i=JSON.parse(JSON.stringify(t.reqForm));i["serverBaseVOList"]=t.table.datas;var r=t.$route.params.id?t.$api.post("/bms/productModel/updateProductModel",i):t.$api.post("/bms/productModel/addProductModel",i);r.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),t.submitLoading=!1,setTimeout((function(){t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},handleFeedbackSelected:function(e){e.map((function(e){e.selectorType="select"})),this.table.datas=this.table.datas.concat(e)},handleRemoveServiceAction:function(e){var t=this,i=this.table.datas[e];this.reqForm.id&&i&&"select"!=i.selectorType?this.$api.post("/bms/productModel/deleteServerFromProductModel",{id:this.reqForm.id,serverId:i.id}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data,t.table.datas=e.data.serverBaseVOList||[])})):this.table.datas.splice(e,1)},handleOpenServiceChk:function(){this.reqForm.productModelName?this.$refs.selectorDialog.show():this.$message({type:"warning",message:"请先填写产品型号"})}}},g=h,f=(i("b671"),i("d255"),Object(c["a"])(g,r,l,!1,null,"59703bae",null));t["default"]=f.exports},d255:function(e,t,i){"use strict";i("6814")},d948:function(e,t,i){"use strict";i("952f")}}]);