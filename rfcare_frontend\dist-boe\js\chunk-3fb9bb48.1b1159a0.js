(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3fb9bb48"],{"203f":function(e,t,r){},"6ec2":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("page-header",{attrs:{title:"修改机构"}}),"org"!==e.$store.state.user.member.role?r("page-main",{attrs:{title:"基础信息"}},[r("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[r("el-col",{attrs:{md:8}},[r("span",{staticClass:"label-field"},[e._v("机构编号:")]),e._v(" "),r("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.orgCode))])]),r("el-col",{attrs:{md:8}},[r("span",{staticClass:"label-field"},[e._v("机构名称:")]),e._v(" "),r("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.name))])]),r("el-col",{attrs:{md:8}},[r("span",{staticClass:"label-field"},[e._v("机构简称:")]),e._v(" "),r("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.shortName))])]),r("el-col",{attrs:{md:8}},[r("span",{staticClass:"label-field"},[e._v("企业ID:")]),e._v(" "),r("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.companyId))])]),r("el-col",{attrs:{md:12}},[r("span",{staticClass:"label-field"},[e._v("企业KEY:")]),e._v(" "),r("span",{staticClass:"value-field"},[e._v(e._s(e.reqForm.companyKey))])]),r("el-col",{attrs:{md:24}},[r("span",{staticClass:"label-field",staticStyle:{width:"140px",position:"relative"}},[e._v(" 外部url: "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top-end"}},[r("div",{staticStyle:{"line-height":"26px","font-size":"13px"},attrs:{slot:"content"},slot:"content"},[e._v(" 用于根据设备编号跳转外部系统，带devCode参数，如填入：https://www.yourdomain.com/yourpage.html"),r("br"),e._v(" 则跳转时是：https://www.yourdomain.com/yourpage.html?devCode=ABCD ")]),r("i",{staticClass:"el-icon-question",staticStyle:{position:"absolute",top:"0",right:"-6px"}})])],1),r("el-input",{staticStyle:{"margin-left":"20px",width:"560px"},attrs:{placeholder:"请输入系统url",size:"small"},model:{value:e.reqForm.externalUrl,callback:function(t){e.$set(e.reqForm,"externalUrl",t)},expression:"reqForm.externalUrl"}})],1),r("el-col",{attrs:{md:8}},[r("span",{staticClass:"label-field",staticStyle:{width:"140px",position:"relative"}},[e._v(" 是否推广增值服务: "),r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",placement:"top-end"}},[r("div",{staticStyle:{"line-height":"26px","font-size":"13px"},attrs:{slot:"content"},slot:"content"},[e._v(" 选择是，与安宝用户绑定该机构下的设备，根据产品型号推送该设备可购买的增值服务；"),r("br"),e._v(" 选择否，与安宝用户只能查看设备产品型号、使用中服务； ")]),r("i",{staticClass:"el-icon-question",staticStyle:{position:"absolute",top:"0",right:"-6px"}})])],1),r("el-select",{staticStyle:{"margin-left":"20px",width:"160px"},attrs:{size:"small"},model:{value:e.reqForm.spreadServer,callback:function(t){e.$set(e.reqForm,"spreadServer",t)},expression:"reqForm.spreadServer"}},[r("el-option",{attrs:{label:"否",value:"0"}}),r("el-option",{attrs:{label:"是",value:"1"}})],1)],1)],1)],1):e._e(),r("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[r("page-main",{attrs:{title:"系统资料"}},[r("el-row",{staticStyle:{padding:"20px 160px","box-sizing":"border-box"},attrs:{gutter:20}},[r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{width:"300px"},attrs:{label:"系统名称",prop:"systemName"}},[r("el-input",{attrs:{placeholder:"请输入系统名称"},model:{value:e.reqForm.systemName,callback:function(t){e.$set(e.reqForm,"systemName",t)},expression:"reqForm.systemName"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"企业LOGO",prop:"logo"}},[r("ImageUpload",{attrs:{url:e.reqForm.logo||void 0,action:e.baseURL+"/bms/org/img/upload",headers:{token:e.$store.state.user.token}},on:{"on-success":e.handleLogoChange,"on-remove":function(t){e.reqForm.logo=void 0}}})],1)],1),"org"===e.$store.state.user.member.role?r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{"margin-top":"20px","text-align":"center"}},[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1):e._e()],1)],1),"org"!==e.$store.state.user.member.role?r("page-main",{attrs:{title:"机构账号"}},[r("el-row",{staticStyle:{padding:"20px 160px","box-sizing":"border-box"},attrs:{gutter:20}},[r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"用户名",prop:"userName"}},[r("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入用户名",disabled:!!e.reqForm.sysUserVO.id},model:{value:e.reqForm.userName,callback:function(t){e.$set(e.reqForm,"userName",t)},expression:"reqForm.userName"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[r("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入手机号",disabled:!!e.reqForm.sysUserVO.id},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[r("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入邮箱"},model:{value:e.reqForm.email,callback:function(t){e.$set(e.reqForm,"email",t)},expression:"reqForm.email"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"服务有效期",prop:"servStartDate"}},[r("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择开始日期"},model:{value:e.reqForm.servStartDate,callback:function(t){e.$set(e.reqForm,"servStartDate",t)},expression:"reqForm.servStartDate"}}),r("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[e._v("至")]),r("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择结束日期"},model:{value:e.reqForm.servEndDate,callback:function(t){e.$set(e.reqForm,"servEndDate",t)},expression:"reqForm.servEndDate"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"默认密码"}},[e._v(" 123456 ")])],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{"margin-top":"20px","text-align":"center"}},[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1):e._e()],1)],1)},s=[],o=r("1da1"),n=(r("96cf"),r("365c")),l=r("e1a4"),i={data:function(){var e=this,t=function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(r,a,s){var o,n,i,m,u,c,d,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("org"===e.$store.state.user.member.role&&s(),o=r.getValuesMethod,n=o(),i=n.email,m=n.userId,a){t.next=9;break}i||s(new Error("手机号码或邮箱必填一个")),t.next=19;break;case 9:return Object(l["b"])(a)||s(new Error("请输入正确的11位手机号码")),u="phone=".concat(a),m&&(u+="&userId=".concat(m)),t.next=14,e.$api.get("/bms/sysuser/validPhoneUnique?".concat(u));case 14:c=t.sent,d=c.status,c.message,p=c.data,"00000"===d&&!0===p&&s(new Error("手机号码已存在"));case 19:s();case 20:case"end":return t.stop()}}),t)})));return function(e,r,a){return t.apply(this,arguments)}}(),r=function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(r,a,s){var o,n,i,m,u,c,d;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("org"===e.$store.state.user.member.role&&s(),a){t.next=5;break}s(new Error("请输入用户名")),t.next=18;break;case 5:return Object(l["d"])(a)||s(new Error("请输入4到16位（字母，数字，下划线，减号）")),o=r.getValuesMethod,n=o(),i=n.userId,m="userName=".concat(a),i&&(m+="&userId=".concat(i)),t.next=13,e.$api.get("/bms/sysuser/validUserNameUnique?".concat(m));case 13:u=t.sent,c=u.status,u.message,d=u.data,"00000"===c&&!0===d&&s(new Error("用户名已存在"));case 18:s();case 19:case"end":return t.stop()}}),t)})));return function(e,r,a){return t.apply(this,arguments)}}(),a=function(){var t=Object(o["a"])(regeneratorRuntime.mark((function t(r,a,s){var o,n,i,m,u,c,d,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if("org"===e.$store.state.user.member.role&&s(),o=r.getValuesMethod,n=o(),i=n.phone,m=n.userId,a){t.next=9;break}i||s(new Error("手机号码或邮箱必填一个")),t.next=19;break;case 9:return Object(l["a"])(a)||s(new Error("请输入正确的邮箱")),u="email=".concat(a),m&&(u+="&userId=".concat(m)),t.next=14,e.$api.get("/bms/sysuser/validEmailUnique?".concat(u));case 14:c=t.sent,d=c.status,c.message,p=c.data,"00000"===d&&!0===p&&s(new Error("邮箱已存在"));case 19:s();case 20:case"end":return t.stop()}}),t)})));return function(e,r,a){return t.apply(this,arguments)}}();return{baseURL:n["a"],reqForm:{id:void 0,userId:void 0,systemName:void 0,logo:void 0,servStartDate:void 0,servEndDate:void 0,spreadServer:"0",sysUserVO:{id:void 0}},reqFormRules:{userName:[{required:!0,trigger:"blur",validator:r,getValuesMethod:this.getValuesMethod}],phone:[{trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],email:[{trigger:"blur",validator:a,getValuesMethod:this.getValuesMethod}]},submitLoading:!1}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/org/getOrganizationInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(e.data&&!e.data.sysUserVO&&(e.data.sysUserVO={}),e.data.spreadServer||(e.data.spreadServer="0"),e.data.userId=e.data.sysUserVO.id,e.data.userName=e.data.sysUserVO.userName,e.data.phone=e.data.sysUserVO.phone,e.data.email=e.data.sysUserVO.email,t.reqForm=e.data)}))},handleLogoChange:function(e){e&&e.data?this.reqForm.logo=e.data:this.reqForm.logo=""},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;JSON.parse(JSON.stringify(t.reqForm));t.$api.post("/bms/org/updateOrganization",{id:t.reqForm.id,systemName:t.reqForm.systemName,logo:t.reqForm.logo||"",servStartDate:t.reqForm.servStartDate,servEndDate:t.reqForm.servEndDate,spreadServer:t.reqForm.spreadServer,externalUrl:t.reqForm.externalUrl,sysUserVO:{id:t.reqForm.userId,userName:t.reqForm.userName,phone:t.reqForm.phone,email:t.reqForm.email,servStartDate:t.reqForm.servStartDate,servEndDate:t.reqForm.servEndDate}}).then((function(e){"00000"===e.status?("org"===t.$store.state.user.member.role&&t.$store.dispatch("user/setSystemInfo",{logo:t.reqForm.logo||"",systemName:t.reqForm.systemName}),t.$message({type:"success",message:"修改成功"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:"修改失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},m=i,u=(r("cb8c"),r("2877")),c=Object(u["a"])(m,a,s,!1,null,"b87ee18a",null);t["default"]=c.exports},cb8c:function(e,t,r){"use strict";r("203f")},e1a4:function(e,t,r){"use strict";function a(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function s(e){var t=/^\d{11}$/;return t.test(e)}function o(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function n(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function l(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function i(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function m(e){return i(e)||l(e)}r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return o})),r.d(t,"a",(function(){return n})),r.d(t,"e",(function(){return m}))}}]);