(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4357e93a"],{"32b3":function(e,t,a){},7088:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"发货信息"}}),a("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.rules,"label-position":"top","label-width":"80px",size:"small"}},[a("page-main",{attrs:{title:"发货信息"}},[e.info.list&&0==e.info.list.length?a("el-row",{staticStyle:{padding:"20px","box-sizing":"border-box",width:"1500px",margin:"0 auto"},attrs:{gutter:20}},[a("el-col",{attrs:{md:12}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"快递单号：",prop:"expressNumber"}},[a("el-input",{staticStyle:{width:"50%"},attrs:{placeholder:"请输入快递单号"},model:{value:e.reqForm.expressNumber,callback:function(t){e.$set(e.reqForm,"expressNumber",t)},expression:"reqForm.expressNumber"}})],1)],1),a("el-col",{attrs:{md:12}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"快递公司：",prop:"expressCompany"}},[a("el-select",{attrs:{placeholder:"请选择快递公司"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.reqForm.expressCompany,callback:function(t){e.$set(e.reqForm,"expressCompany",t)},expression:"reqForm.expressCompany"}},[a("el-option",{attrs:{label:"顺丰速递",value:"顺丰速递"}}),a("el-option",{attrs:{label:"邮政EMS",value:"邮政EMS"}}),a("el-option",{attrs:{label:"丰巢",value:"丰巢"}}),a("el-option",{attrs:{label:"货拉拉",value:"货拉拉"}}),a("el-option",{attrs:{label:"中通快递",value:"中通快递"}}),a("el-option",{attrs:{label:"韵达快递",value:"韵达快递"}}),a("el-option",{attrs:{label:"百世快运",value:"百世快运"}}),a("el-option",{attrs:{label:"圆通速递",value:"圆通速递"}}),a("el-option",{attrs:{label:"德邦快递",value:"德邦快递"}}),a("el-option",{attrs:{label:"申通快递",value:"申通快递"}}),a("el-option",{attrs:{label:"跑腿",value:"跑腿"}}),a("el-option",{attrs:{label:"丰巢",value:"丰巢"}}),a("el-option",{attrs:{label:"其它",value:"其它"}})],1)],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{staticStyle:{width:"500px",height:"100px"},attrs:{label:"备注：",prop:"serverDesc"}},[a("el-input",{attrs:{type:"textarea",rows:4,placeholder:"请输入备注信息"},model:{value:e.reqForm.remarks,callback:function(t){e.$set(e.reqForm,"remarks",t)},expression:"reqForm.remarks"}})],1)],1)],1):e._e()],1),a("page-main",{attrs:{title:"提货信息"}},[a("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(t){return e.copyInfo()}}},[e._v("复制收货信息")]),a("el-row",{staticStyle:{padding:"10px","box-sizing":"border-box",width:"1500px",margin:"0 auto"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("会员卡号：")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.info.cardNumber))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("会员卡名称：")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.info.cardName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("提货用户：")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.info.name))])])])],1),a("el-row",{staticStyle:{padding:"10px","box-sizing":"border-box",width:"1500px",margin:"0 auto"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("收货人姓名：")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.info.consignee))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[e._v("收货人电话：")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.info.phone))])])])],1),a("el-row",{staticStyle:{padding:"10px","box-sizing":"border-box",width:"1500px",margin:"0 auto"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[e._v("收货人详细地址：")]),e._v(" "),a("span",{staticClass:"value-field"},[e._v(e._s(e.info.address))])])])],1)],1),a("page-main",{attrs:{title:"提货明细"}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.info.goods,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{prop:"goodName",label:"设备名称",align:"center"}}),a("el-table-column",{attrs:{prop:"goodModel",label:"型号",align:"center"}}),a("el-table-column",{attrs:{prop:"goodNum",label:"数量",align:"center"}}),a("el-table-column",{attrs:{prop:"serverTime",label:"服务年限(年)",align:"center"}})],1)],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)},i=[],r=a("ca72"),l=(a("030f"),{components:{Editor:r["a"]},data:function(){return{reqForm:{deliveryId:void 0,expressNumber:void 0,expressCompany:void 0,remarks:void 0},info:{cardNumber:void 0,cardName:void 0,name:void 0,consignee:void 0,phone:void 0,address:void 0,goods:[],list:[]},rules:{expressNumber:[{required:!0,trigger:"blur",message:"请填写快递单号"},{min:4,max:30,message:"长度在 4 到 30 个字符",trigger:"blur"}],expressCompany:[{required:!0,trigger:"change",message:"请填写快递公司"}]},submitLoading:!1}},mounted:function(){this.reqForm.deliveryId=this.$route.params.id,this.fetchDetail(this.$route.params.id)},computed:{},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/delivery/getInfo",{params:{id:e}}).then((function(e){"00000"===e.status&&e.data&&(t.info=e.data)}))},copyInfo:function(){if(void 0!=this.info.consignee&&void 0!=this.info.phone&&void 0!=this.info.address){var e="收货人姓名："+this.info.consignee;e+=" 收货人电话："+this.info.phone,e+=" 收货人详细地址："+this.info.address;var t=this;this.$copyText(e).then((function(a){t.$message({message:"复制成功",type:"success"}),console.log(e)}),(function(e){t.$message.error("复制失败"),console.log(e)}))}else this.$message.error("没有信息可以复制到粘贴板")},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var a=JSON.parse(JSON.stringify(t.reqForm));console.log(a),t.$api.post("/bms/delivery/addCardDeviceDeliveryItem",a).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),t.submitLoading=!1,setTimeout((function(){t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}}),o=l,n=(a("b855"),a("94e4"),a("2877")),d=Object(n["a"])(o,s,i,!1,null,"8baaa9bc",null);t["default"]=d.exports},"94e4":function(e,t,a){"use strict";a("32b3")},b855:function(e,t,a){"use strict";a("fb96")},fb96:function(e,t,a){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}}}]);