(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5458a1a2"],{"08a5":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"问题反馈"}}),a("page-main",[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"memberName",label:"用户名",width:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"feedbackTypeName",label:"反馈类型",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"problemTypeName",label:"问题类型",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"problemDesc",label:"问题描述",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"feedbackPic",label:"反馈图片",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[n.feedbackPic&&n.feedbackPic.length?a("img",{staticStyle:{width:"40px",height:"40px"},attrs:{src:n.feedbackPic.split(",")[0]}}):e._e()]}}])}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"170",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"serviceFeedbackInfo",params:{id:n.id}})}}},[e._v("详情查看")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},l=[],s=a("5530"),r=(a("ac1f"),a("841c"),{data:function(){return{dict:{stations:[]},search:{serverName:void 0},table:{datas:[],pageInfo:{current:1,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handleRadioChange:function(e){this.search.status=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/problemFeedback/listProblemFeedback",{params:Object(s["a"])(Object(s["a"])({},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleChangeStatus:function(e,t){var a=this;e&&void 0!=t&&null!=t&&this.$api.post("/bms/server/invalidServer",{id:e,validStatus:t}).then((function(e){"00000"===e.status?(a.$message({type:"success",message:"更新成功!"}),a.fetchDatas()):a.$message({type:"error",message:e.message||"操作失败!"})}))},resetForm:function(){Object.assign(this.$data.search,this.$options.data().search),this.fetchDatas()}}}),i=r,c=(a("f2c1"),a("2877")),o=Object(c["a"])(i,n,l,!1,null,"691da55f",null);t["default"]=o.exports},e631:function(e,t,a){},f2c1:function(e,t,a){"use strict";a("e631")}}]);