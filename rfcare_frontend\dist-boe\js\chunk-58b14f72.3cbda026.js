(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58b14f72"],{1280:function(t,e,a){"use strict";a.r(e);var s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"反馈问题详情查看"}}),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[t._v("反馈类型:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.feedbackTypeName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[t._v("问题类型:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.problemTypeName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[t._v("提交人:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.memberName))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[t._v("手机号:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.phone))])])]),a("el-col",{attrs:{md:8}},[a("div",[a("span",{staticClass:"label-field"},[t._v("反馈时间:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.createTime))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field"},[t._v("问题描述:")]),t._v(" "),a("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.problemDesc))])])]),a("el-col",{attrs:{md:24}},[a("div",[a("span",{staticClass:"label-field",staticStyle:{"vertical-align":"top"}},[t._v("反馈图片:")]),t.reqForm.feedbackPic?a("span",{staticClass:"value-field",staticStyle:{"vertical-align":"text-top","margin-top":"10px"}},t._l(t.reqForm.feedbackPic.split(","),(function(t,e){return a("image-preview",{key:e,staticStyle:{"margin-right":"12px"},attrs:{src:t,width:120,height:120}})})),1):t._e()])])],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)},l=[],i={data:function(){return{reqForm:{}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(t){var e=this;t&&this.$api.get("/bms/problemFeedback/getProblemFeedback/".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(e.reqForm=t.data)}))}}},r=i,c=(a("5689"),a("2877")),n=Object(c["a"])(r,s,l,!1,null,"bbe5335c",null);e["default"]=n.exports},"1e2b":function(t,e,a){},5689:function(t,e,a){"use strict";a("1e2b")}}]);