(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5a23524a"],{"07903":function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"户型"}}),o("el-tabs",{attrs:{type:"border-card"},on:{"tab-click":e.handleTabClick}},[o("el-tab-pane",{attrs:{label:"基本信息"}},[o("div",{staticStyle:{"font-size":"13px"}},[o("span",{staticStyle:{color:"#f6a929"}},[e._v("提示：")]),o("span",{staticStyle:{color:"#999"}},[e._v("先保存基本信息，再设置房间信息")])]),o("el-form",{ref:"reqForm",attrs:{model:e.reqForm,"label-position":"top","label-width":"80px",size:"small"}},[o("el-row",{staticClass:"block-row",attrs:{gutter:20}},[o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"名称",prop:"houseName",rules:e.rules.houseName}},[o("el-input",{attrs:{maxlength:50,disabled:!!e.$route.params.id,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"1栋50平A户型"},model:{value:e.reqForm.houseName,callback:function(t){e.$set(e.reqForm,"houseName",t)},expression:"reqForm.houseName"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"所属服务站",prop:"stationId",rules:e.rules.stationId}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择所属服务站",disabled:!!e.$route.params.id},model:{value:e.reqForm.stationId,callback:function(t){e.$set(e.reqForm,"stationId",t)},expression:"reqForm.stationId"}},e._l(e.dict.stations,(function(e){return o("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})})),1)],1)],1),o("el-col",{attrs:{md:24}},[o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{type:"textarea",rows:5,maxlength:200,"show-word-limit":"","show-word-limitv-model":"reqForm.remark",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.remark,callback:function(t){e.$set(e.reqForm,"remark",t)},expression:"reqForm.remark"}})],1)],1),o("el-col",{attrs:{md:24}},[o("el-form-item",{staticStyle:{"margin-top":"20px"}},[o("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),o("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1),o("el-tab-pane",{attrs:{label:"卧室",disabled:!e.hasHouse}},[e.bedRoomList&&e.bedRoomList.length>0?o("RoomList",{attrs:{data:e.bedRoomList,onEdit:e.editRoom,refresh:e.getRoomList}}):e._e(),e.tplHouseUsed?o("div",{staticStyle:{"font-size":"13px"}},[o("span",{staticStyle:{color:"#f6a929"}},[e._v("提示：")]),o("span",{staticStyle:{color:"#999"}},[e._v("已被引用的户型不支持修改")])]):o("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.editRoom(void 0)}}},[e._v("增加")])],1),o("el-tab-pane",{attrs:{label:"客厅",disabled:!e.hasHouse}},[e.livingRoomList&&e.livingRoomList.length>0?o("RoomList",{attrs:{data:e.livingRoomList,onEdit:e.editRoom,refresh:e.getRoomList}}):e._e(),e.tplHouseUsed?o("div",{staticStyle:{"font-size":"13px"}},[o("span",{staticStyle:{color:"#f6a929"}},[e._v("提示：")]),o("span",{staticStyle:{color:"#999"}},[e._v("已被引用的户型不支持修改")])]):o("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.editRoom(void 0)}}},[e._v("增加")])],1),o("el-tab-pane",{attrs:{label:"厨房",disabled:!e.hasHouse}},[e.kitchenRoomList&&e.kitchenRoomList.length>0?o("RoomList",{attrs:{data:e.kitchenRoomList,onEdit:e.editRoom,refresh:e.getRoomList}}):e._e(),e.tplHouseUsed?o("div",{staticStyle:{"font-size":"13px"}},[o("span",{staticStyle:{color:"#f6a929"}},[e._v("提示：")]),o("span",{staticStyle:{color:"#999"}},[e._v("已被引用的户型不支持修改")])]):o("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.editRoom(void 0)}}},[e._v("增加")])],1),o("el-tab-pane",{attrs:{label:"卫生间",disabled:!e.hasHouse}},[e.bathRoomList&&e.bathRoomList.length>0?o("RoomList",{attrs:{data:e.bathRoomList,onEdit:e.editRoom,refresh:e.getRoomList}}):e._e(),e.tplHouseUsed?o("div",{staticStyle:{"font-size":"13px"}},[o("span",{staticStyle:{color:"#f6a929"}},[e._v("提示：")]),o("span",{staticStyle:{color:"#999"}},[e._v("已被引用的户型不支持修改")])]):o("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.editRoom(void 0)}}},[e._v("增加")])],1),o("el-tab-pane",{attrs:{label:"其他",disabled:!e.hasHouse}},[e.otherRoomList&&e.otherRoomList.length>0?o("RoomList",{attrs:{data:e.otherRoomList,onEdit:e.editRoom,refresh:e.getRoomList}}):e._e(),e.tplHouseUsed?o("div",{staticStyle:{"font-size":"13px"}},[o("span",{staticStyle:{color:"#f6a929"}},[e._v("提示：")]),o("span",{staticStyle:{color:"#999"}},[e._v("已被引用的户型不支持修改")])]):o("el-button",{attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.editRoom(void 0)}}},[e._v("增加")])],1)],1),o("el-drawer",{attrs:{title:""+e.currRoomTypeName,size:"40%",visible:e.drawer,"before-close":e.drawerClose,"destroy-on-close":"","show-close":!1,wrapperClosable:!1},on:{"update:visible":function(t){e.drawer=t}}},[o("RoomEdit",{attrs:{roomId:e.tplRoomId,tplHouseId:e.tplHouseId,roomType:e.currRoomType,close:e.drawerClose,refresh:e.getRoomList}})],1)],1)},a=[],s=o("1da1"),i=(o("99af"),o("96cf"),o("2a06")),l=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"room-list"},[o("el-row",{attrs:{gutter:20}},e._l(e.data,(function(t,r){return o("el-col",{key:r,attrs:{span:8}},[o("div",{staticClass:"custom-card"},[o("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"},attrs:{slot:"header"},slot:"header"},[o("div",[e._v(e._s(t.roomName))]),o("div",[t.used?o("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(o){return e.editCard(t.tplHouseRoomId)}}},[e._v("查看")]):o("el-button",{attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(o){return e.editCard(t.tplHouseRoomId)}}},[e._v("编辑")]),t.used?e._e():o("el-popconfirm",{staticStyle:{"margin-left":"10px"},attrs:{title:"确定删除吗？"},on:{confirm:function(o){return e.deleteCard(t.tplHouseRoomId)}}},[o("el-button",{attrs:{slot:"reference",type:"danger",plain:"",size:"mini"},slot:"reference"},[e._v("删除")])],1)],1)])])])})),1)],1)},n=[],m={name:"RoomList",props:{data:{type:Array,required:!0},onEdit:{type:Function,required:!0},refresh:{type:Function,required:!0}},data:function(){return{}},created:function(){},methods:{deleteCard:function(e){var t=this;e&&this.$api.get("/bms/tplhouse/room/deleteRoom?id=".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"删除成功"}),t.refresh()):t.$message({type:"error",message:"删除失败"})}))},editCard:function(e){console.log("id:",e),this.onEdit(e||void 0)}}},c=m,u=(o("192d"),o("2877")),d=Object(u["a"])(c,l,n,!1,null,"623fcd3c",null),p=d.exports,g=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"main"},[o("el-form",{ref:"formName",attrs:{model:e.formData,"label-position":"top","label-width":"80px",size:"small",disabled:e.formData.used}},[o("RoomItemCard",{attrs:{title:"基本信息",imgUrl:"/images/device/room/1.png"}},[o("el-row",{staticClass:"block-row",attrs:{gutter:20}},[o("el-col",{attrs:{md:24}},[o("el-form-item",{attrs:{label:"房间名称",prop:"roomName",rules:e.rules.roomName}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"主卧"},model:{value:e.formData.roomName,callback:function(t){e.$set(e.formData,"roomName",t)},expression:"formData.roomName"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"前",prop:"x",rules:e.rules.roomX}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"4"},model:{value:e.formData.x,callback:function(t){e.$set(e.formData,"x",t)},expression:"formData.x"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"左",prop:"y",rules:e.rules.roomY}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"4"},model:{value:e.formData.y,callback:function(t){e.$set(e.formData,"y",t)},expression:"formData.y"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"高",prop:"z",rules:e.rules.roomZ}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"2.8"},model:{value:e.formData.z,callback:function(t){e.$set(e.formData,"z",t)},expression:"formData.z"}})],1)],1)],1)],1),o("RoomItemCard",{attrs:{title:"门方位",imgUrl:"/images/device/room/4.png"}},[o("el-row",{staticClass:"block-row",attrs:{gutter:20}},[o("DynamicForm",{attrs:{formItems:e.formData.tplHouseRoomEntranceList,onRemove:e.removeRoomItem,btnName:"添加门方位"},scopedSlots:e._u([{key:"formFields",fn:function(t){var r=t.item;return[o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"门名称",rules:e.rules.gateName}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"门名称"},model:{value:r.gateName,callback:function(t){e.$set(r,"gateName",t)},expression:"item.gateName"}})],1)],1),o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"门位置",rules:e.rules.gateDirection}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:r.direction,callback:function(t){e.$set(r,"direction",t)},expression:"item.direction"}},e._l(e.dict.doorSide,(function(e,t){return o("el-option",{key:t,attrs:{label:e.name,value:e.code}})})),1)],1)],1),o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"左长(米，门左侧离左边墙距离)",rules:e.rules.roomGateLeftLen}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.leftLen,callback:function(t){e.$set(r,"leftLen",t)},expression:"item.leftLen"}})],1)],1),o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"宽(米)",rules:e.rules.roomGateWidth}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.width,callback:function(t){e.$set(r,"width",t)},expression:"item.width"}})],1)],1)]}}])})],1)],1),o("RoomItemCard",{attrs:{title:"设备位置",imgUrl:"/images/device/room/2-1.png",name:"顶装示例图",imgUrl2:"/images/device/room/2-2.png",name2:"侧装示例图"}},[o("el-row",{staticClass:"block-row",attrs:{gutter:20}},[o("DynamicForm",{attrs:{formItems:e.formData.tplHouseRoomDeviceList,onRemove:e.removeRoomItem,btnName:"添加设备"},scopedSlots:e._u([{key:"formFields",fn:function(t){var r=t.item;return[o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"设备名称",rules:e.rules.deviceName}},[o("el-input",{attrs:{maxlength:50,disabled:!!r.tplHouseRoomDeviceId,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"1号位"},model:{value:r.deviceName,callback:function(t){e.$set(r,"deviceName",t)},expression:"item.deviceName"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"安装方式",rules:e.rules.deviceSpotType}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.spotTypeChange},model:{value:r.spotType,callback:function(t){e.$set(r,"spotType",t)},expression:"item.spotType"}},e._l(e.dict.spotTypes,(function(e){return o("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),o("el-col",{attrs:{md:8}},[1==r.spotType?o("el-form-item",{attrs:{label:"电源插孔朝向",rules:e.rules.deviceDirection}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:r.direction,callback:function(t){e.$set(r,"direction",t)},expression:"item.direction"}},e._l(e.dict.doorSide,(function(e,t){return o("el-option",{key:t,attrs:{label:e.name,value:e.code}})})),1)],1):o("el-form-item",{attrs:{label:"设备所在墙",rules:e.rules.deviceDirection}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:r.direction,callback:function(t){e.$set(r,"direction",t)},expression:"item.direction"}},e._l(e.dict.doorSide,(function(e,t){return o("el-option",{key:t,attrs:{label:e.name,value:e.code}})})),1)],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"左",rules:e.rules.deviceX}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"4"},model:{value:r.x,callback:function(t){e.$set(r,"x",t)},expression:"item.x"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"后",rules:e.rules.deviceY}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"4"},model:{value:r.y,callback:function(t){e.$set(r,"y",t)},expression:"item.y"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{attrs:{label:"设备高",rules:e.rules.deviceZ}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"2.8"},model:{value:r.z,callback:function(t){e.$set(r,"z",t)},expression:"item.z"}})],1)],1),o("el-col",{attrs:{md:24}},[o("el-form-item",{attrs:{label:"设置检测范围(否为检测整个房间)",rules:e.rules.setRange}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:r.setRange,callback:function(t){e.$set(r,"setRange",t)},expression:"item.setRange"}},e._l(e.dict.yn,(function(e){return o("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),1==r.setRange?o("div",[o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"检测范围前",rules:e.rules.rangeFront}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"2.8"},model:{value:r.rangeFront,callback:function(t){e.$set(r,"rangeFront",t)},expression:"item.rangeFront"}})],1)],1),o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"检测范围后",rules:e.rules.rangeBack}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"2.8"},model:{value:r.rangeBack,callback:function(t){e.$set(r,"rangeBack",t)},expression:"item.rangeBack"}})],1)],1),o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"检测范围左",rules:e.rules.rangeLeft}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"4"},model:{value:r.rangeLeft,callback:function(t){e.$set(r,"rangeLeft",t)},expression:"item.rangeLeft"}})],1)],1),o("el-col",{attrs:{md:6}},[o("el-form-item",{attrs:{label:"检测范围右",rules:e.rules.rangeRight}},[o("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"4"},model:{value:r.rangeRight,callback:function(t){e.$set(r,"rangeRight",t)},expression:"item.rangeRight"}})],1)],1)],1):e._e()]}}])})],1)],1),o("RoomItemCard",{attrs:{title:"区域",imgUrl:"/images/device/room/5.png"}},[o("el-row",{staticClass:"block-row",attrs:{gutter:20}},[o("DynamicForm",{attrs:{formItems:e.formData.tplHouseRoomRegionList,onRemove:e.removeRoomItem,btnName:"添加区域"},scopedSlots:e._u([{key:"formFields",fn:function(t){var r=t.item;return[o("el-col",{attrs:{md:12}},[o("el-form-item",{attrs:{label:"区域名称",rules:e.rules.regionName}},[o("el-input",{attrs:{maxlength:50,disabled:!!r.tplHouseRoomRegionId||!!r.cls,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"1号床"},model:{value:r.regionName,callback:function(t){e.$set(r,"regionName",t)},expression:"item.regionName"}})],1)],1),o("el-col",{attrs:{md:12}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"区域类型",rules:e.rules.regionName}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:r.cls,callback:function(t){e.$set(r,"cls",t)},expression:"item.cls"}},[o("el-option",{attrs:{label:"床",value:1}})],1)],1)],1),o("el-col",{attrs:{md:12}},[o("el-form-item",{attrs:{label:"左(米，床离左墙距离)",rules:e.rules.roomRegionPositionX}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.positionX,callback:function(t){e.$set(r,"positionX",t)},expression:"item.positionX"}})],1)],1),o("el-col",{attrs:{md:12}},[o("el-form-item",{attrs:{label:"后(米，床离门所在墙距离)",rules:e.rules.roomRegionPositionY}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.positionY,callback:function(t){e.$set(r,"positionY",t)},expression:"item.positionY"}})],1)],1),o("el-col",{attrs:{md:12}},[o("el-form-item",{attrs:{label:"床左(米，靠左墙床边长)",rules:e.rules.roomRegionScaleX}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.scaleX,callback:function(t){e.$set(r,"scaleX",t)},expression:"item.scaleX"}})],1)],1),o("el-col",{attrs:{md:12}},[o("el-form-item",{attrs:{label:"床前(米，靠门所在墙床边长)",rules:e.rules.roomRegionScaleY}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.scaleY,callback:function(t){e.$set(r,"scaleY",t)},expression:"item.scaleY"}})],1)],1),o("el-col",{attrs:{md:12}},[o("el-form-item",{attrs:{label:"床高(米)",rules:e.rules.roomRegionScaleZ}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:r.scaleZ,callback:function(t){e.$set(r,"scaleZ",t)},expression:"item.scaleZ"}})],1)],1)]}}])})],1)],1),o("RoomItemCard",{attrs:{title:"床位与设备位置对应"}},e._l(e.formData.regionDeviceList,(function(t,r){return o("el-row",{key:"regionDeviceList"+r,staticClass:"block-row",attrs:{gutter:20}},[o("el-col",{attrs:{md:6}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"床位",prop:"regionDeviceList."+r+".bedName"}},[o("el-input",{attrs:{maxlength:20,disabled:""},model:{value:t.bedName,callback:function(o){e.$set(t,"bedName",o)},expression:"bed.bedName"}})],1)],1),o("el-col",{attrs:{md:18}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"设备位置",prop:"regionDeviceList."+r+".deviceNameList"}},[o("el-checkbox-group",{model:{value:t.deviceNameList,callback:function(o){e.$set(t,"deviceNameList",o)},expression:"bed.deviceNameList"}},e._l(e.formData.tplHouseRoomDeviceList,(function(t,r){return o("el-checkbox",{key:t.deviceName,attrs:{label:t.deviceName}},[e._v(e._s(t.deviceName))])})),1)],1)],1)],1)})),1),o("div",{staticClass:"item-card"},[o("el-row",{staticClass:"block-row",attrs:{gutter:20}},[o("el-col",{attrs:{md:24}},[e.formData.used?o("div",{staticStyle:{margin:"20px"}},[o("el-button",{attrs:{disabled:!1},on:{click:e.clickClose}},[e._v("关闭")])],1):o("el-form-item",{staticStyle:{margin:"20px"}},[o("el-button",{on:{click:e.clickClose}},[e._v("取消")]),o("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("formName")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},f=[],h=o("5530"),b=(o("4de4"),function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"room-item-card"},[o("div",{staticClass:"header"},[o("div",{staticClass:"title"},[e._v(e._s(e.title))]),o("div",[e.imgUrl?o("el-popover",{attrs:{placement:"left",trigger:"hover",width:"500"}},[o("img",{staticStyle:{width:"100%"},attrs:{src:e.imgUrl}}),o("el-link",{attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v(e._s(e.name))])],1):e._e(),e.imgUrl2?o("el-popover",{staticStyle:{"margin-left":"20px"},attrs:{placement:"left",offset:100,trigger:"hover",width:"500"}},[o("img",{staticStyle:{width:"100%"},attrs:{src:e.imgUrl2}}),o("el-link",{attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v(e._s(e.name2))])],1):e._e()],1)]),e._t("default")],2)}),v=[],y={name:"RoomItemCard",props:{title:{type:String,required:!0},imgUrl:{type:String,required:!1},name:{type:String,required:!1,default:"示例图"},imgUrl2:{type:String,required:!1},name2:{type:String,required:!1}},data:function(){return{}},methods:{}},R=y,k=(o("18a7"),Object(u["a"])(R,b,v,!1,null,"b2bcefd6",null)),L=k.exports,x={components:{RoomItemCard:L,DynamicForm:i["default"]},name:"RoomEdit",props:{tplHouseId:{type:String,default:!0},roomId:{type:String,required:!1},roomType:{type:String,required:!0},close:{type:Function,required:!0},refresh:{type:Function,required:!0}},data:function(){return{dict:{spotTypes:[{code:"1",name:"顶装"},{code:"2",name:"侧装"}],yn:[{code:"0",name:"否"},{code:"1",name:"是"}],doorSide:[{code:1,name:"前"},{code:3,name:"后"},{code:0,name:"左"},{code:2,name:"右"}]},formData:{tplHouseRoomDeviceList:[],tplHouseRoomEntranceList:[],tplHouseRoomRegionList:[],regionDeviceList:[]},submitLoading:!1,rules:{roomName:[{required:!0,trigger:"blur",message:"请填房间名称"}],roomX:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomY:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomZ:[{required:!0,trigger:"blur",message:"请填写(米)"}],deviceName:[{required:!0,trigger:"blur",message:"请填设备位名称"}],deviceDirection:[{required:!0,trigger:"change",message:"请选择"}],deviceSpotType:[{required:!0,trigger:"change",message:"请选择设备安装方式"}],deviceX:[{required:!0,trigger:"blur",message:"请填写(米)"}],deviceY:[{required:!0,trigger:"blur",message:"请填写(米)"}],deviceZ:[{required:!0,trigger:"blur",message:"请填写(米)"}],setRange:[{required:!0,trigger:"change",message:"请选择(米)"}],rangeFront:[{required:!0,trigger:"change",message:"请选择"}],rangeBack:[{required:!0,trigger:"blur",message:"请填写(米)"}],rangeLeft:[{required:!0,trigger:"blur",message:"请填写(米)"}],rangeRight:[{required:!0,trigger:"blur",message:"请填写(米)"}],gateName:[{required:!0,trigger:"blur",message:"请填门名称"}],gateDirection:[{required:!0,trigger:"change",message:"请选择"}],roomGateLeftLen:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomGateWidth:[{required:!0,trigger:"blur",message:"请填写(米)"}],regionName:[{required:!0,trigger:"blur",message:"请填写区域名称"}],roomRegionPositionX:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomRegionPositionY:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomRegionScaleX:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomRegionScaleY:[{required:!0,trigger:"blur",message:"请填写(米)"}],roomRegionScaleZ:[{required:!0,trigger:"blur",message:"请填写(米)"}]}}},created:function(){this.fetchDetail(),this.$watch("formData.tplHouseRoomRegionList",(function(e,t){var o=this;if(this.formData.tplHouseRoomRegionList&&this.formData.tplHouseRoomRegionList.length>0)for(var r=0;r<this.formData.tplHouseRoomRegionList.length;r++){var a=this.formData.tplHouseRoomRegionList[r].regionName;"1"==this.formData.tplHouseRoomRegionList[r].cls&&function(){var e={bedName:a,deviceNameList:[]};o.formData.regionDeviceList.some((function(t){return t.bedName===e.bedName}))||o.formData.regionDeviceList.push(e)}()}return this.formData.regionDeviceList=this.formData.regionDeviceList.filter((function(e){return e.bedName})),this.formData.regionDeviceList=this.formData.regionDeviceList.filter((function(e){return o.formData.tplHouseRoomRegionList.some((function(t){return t.regionName==e.bedName}))})),console.log("regionDeviceList:",this.formData.regionDeviceList),this.formData.regionDeviceList})),this.$watch("formData",(function(e,t){console.log("formData:",this.formData)}))},methods:{fetchDetail:function(){var e=this;this.roomId&&this.$api.get("/bms/tplhouse/room/getById?id="+this.roomId).then((function(t){"00000"===t.status&&(e.formData=t.data)}))},clickClose:function(){this.close()},removeRoomItem:function(e){return!0},spotTypeChange:function(){},handleSubmit:function(e){var t=this;console.log(this.formData),this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var o=JSON.parse(JSON.stringify(t.formData));console.log("save:",o),t.$api.post("/bms/tplhouse/room/save",Object(h["a"])(Object(h["a"])({},o),{},{tplHouseId:t.tplHouseId,roomType:t.roomType})).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"操作完成"}),t.submitLoading=!1,t.refresh(),t.close(!0)):(t.submitLoading=!1,t.$message({type:"error",message:"操作失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},D=x,$=(o("1c77"),Object(u["a"])(D,g,f,!1,null,"12c730dc",null)),S=$.exports,w={components:{DynamicForm:i["default"],RoomList:p,RoomEdit:S},data:function(){return{dict:{stations:[]},drawer:!1,tplHouseUsed:!1,tplHouseId:void 0,tplRoomId:void 0,hasHouse:!1,reqForm:{},submitLoading:!1,fullLoading:void 0,currRoomType:void 0,currRoomTypeName:"",bedRoomList:[],livingRoomList:[],kitchenRoomList:[],bathRoomList:[],otherRoomList:[],rules:{houseName:[{required:!0,trigger:"blur",message:"请填写名称"}],stationId:[{required:!0,trigger:"change",message:"请选择"}]}}},created:function(){var e=this;return Object(s["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.$route.params.id&&(e.tplHouseId=e.$route.params.id,e.hasHouse=!0),t.next=3,e.fetchStations();case 3:e.fetchDetail();case 4:case"end":return t.stop()}}),t)})))()},methods:{handleTabClick:function(e,t){console.log(e.label),this.currRoomTypeName=e.label,this.currRoomType=e.index,this.tplHouseId||0==e.index?this.getRoomList():this.$message({type:"error",message:"请先设置户型基本信息"})},fetchStations:function(){var e=this;return Object(s["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}));case 1:case"end":return t.stop()}}),t)})))()},getRoomList:function(){var e=this;this.$api.get("/bms/tplhouse/room/getByHouseIdAndType?tplHouseId=".concat(this.tplHouseId,"&roomType=").concat(this.currRoomType),{}).then((function(t){if("00000"===t.status&&t.data)switch(console.log("bedRoomList",t.data),e.currRoomType){case"1":e.bedRoomList=t.data;break;case"2":e.livingRoomList=t.data;break;case"3":e.kitchenRoomList=t.data;break;case"4":e.bathRoomList=t.data;break;case"5":e.otherRoomList=t.data;break}e.fullLoading.close()})).catch((function(t){console.log(t),e.fullLoading.close()}))},fetchDetail:function(){var e=this,t=this.$route.params.id;t&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("bms/tplhouse/getById?id=".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(console.log("reqForm",t.data),e.reqForm=t.data,e.tplHouseUsed=t.data.used),e.fullLoading.close()})).catch((function(t){console.log(t),e.fullLoading.close()})))},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var o=JSON.parse(JSON.stringify(t.reqForm));t.$api.post("/bms/tplhouse/save",o).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),t.tplHouseId=e.data.tplHouseId,t.hasHouse=!0,setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},editRoom:function(e){this.tplRoomId=e,this.drawer=!0},drawerClose:function(e){var t=this;e?this.drawer=!1:this.$confirm("确认关闭？").then((function(e){t.drawer=!1})).catch((function(e){}))},saveRoom:function(e,t){var o=this;this.submitLoading=!0,this.$message({type:"error",message:"点击保存"+e}),this.$refs[e].validate((function(e){if(!e)return o.submitLoading=!1,o.$message({type:"error",message:"验证失败"}),!1;var r=JSON.parse(JSON.stringify(t));o.$api.post("/bms/tplhouse/room/save",r).then((function(e){"00000"===e.status?(o.$message({type:"success",message:"操作完成"}),setTimeout((function(){o.submitLoading=!1,o.$router.go(-1)}),500)):(o.submitLoading=!1,o.$message({type:"error",message:"操作失败"}))})).catch((function(e){o.submitLoading=!1}))})),this.submitLoading=!1}}},_=w,N=(o("6fc6"),Object(u["a"])(_,r,a,!1,null,"3091d6f7",null));t["default"]=N.exports},"18a7":function(e,t,o){"use strict";o("b84d")},"192d":function(e,t,o){"use strict";o("7771")},"1c77":function(e,t,o){"use strict";o("3eee")},"3eee":function(e,t,o){},"57fa":function(e,t,o){},"6fc6":function(e,t,o){"use strict";o("57fa")},7771:function(e,t,o){},b84d:function(e,t,o){}}]);