(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b256ac2"],{"01b9":function(e,t,a){},"0449":function(e,t,a){"use strict";a("3d00")},"156f":function(e,t,a){"use strict";a("8354")},"1d2e":function(e,t,a){},"2d2c":function(e,t,a){},"3d00":function(e,t,a){},4190:function(e,t,a){},4277:function(e,t,a){"use strict";var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bg-container flex-col"},[a("div",{staticClass:"bg flex-col"}),a("div",{staticClass:"main flex-col"},[a("div",{staticClass:"top flex-col"},[a("div",{staticClass:"page-title flex-col"},[a("span",{staticClass:"text"},[e._v(e._s(e.title))])])]),a("div",{staticClass:"curr-time"},[a("span",[e._v(e._s(e.currentTime))])]),e.path?a("div",{staticClass:"notice",on:{click:e.openNotice}},[e.noticeStatus?a("span",{staticClass:"icon iconfont icon-shengyin notice-icon"}):a("span",{staticClass:"icon iconfont icon-shengyinguanbi notice-icon"})]):e._e(),e._t("default")],2),a("el-dialog",{staticClass:"event-dialog",attrs:{title:"报警消息",visible:e.dialogTableVisible},on:{"update:visible":function(t){e.dialogTableVisible=t}}},[a("el-row",{attrs:{gutter:20,justify:"space-around"}},[a("el-col",{attrs:{span:20}},[a("div",[e._v("总计："+e._s(e.eventList.length))])]),a("el-col",{attrs:{span:4}},[a("div",[a("el-button",{attrs:{type:"primary",size:"mini"},on:{click:e.removeAll}},[e._v("全部知道了")])],1)])],1),a("el-table",{staticStyle:{"margin-top":"12px"},attrs:{data:e.eventList}},[a("el-table-column",{attrs:{property:"devName",label:"设备名称",width:"180"}}),a("el-table-column",{attrs:{property:"orderType",label:"类型",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(e.getOrderTypeName(a.orderType))+" ")]}}])}),a("el-table-column",{attrs:{property:"createTime",label:"发生时间"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return e.remove(t.row.id)}}},[e._v("知道了")])]}}])})],1)],1)],1)},i=[],n=a("2909"),o=a("1da1"),r=(a("96cf"),a("4de4"),a("99af"),a("5a0c")),c=a.n(r),l=a("5530");function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function d(e,t){for(var a=0;a<t.length;a++){var s=t[a];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}function h(e,t,a){return t&&d(e.prototype,t),a&&d(e,a),e}a("a434");var f=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};u(this,e),this.config=Object(l["a"])({lang:"zh-cn",voiceURI:"Microsoft Huihui - Chinese (Simplified, PRC)",volume:.7,rate:.7,pitch:1,repeatCount:3,onMessage:null},t),this.isEnabled=!1,this.speech=null,this.eventQueue=[],this.currentSpeakText=null,this.currentSpeakCount=0,this.batchSpeak=!1,this.init()}return h(e,[{key:"init",value:function(){var e=this;if("undefined"===typeof SpeechSynthesisUtterance)return this.showMessage("您的浏览器不支持TTS语音功能"),!1;try{return this.speech=new SpeechSynthesisUtterance,this.speech.lang=this.config.lang,this.speech.voiceURI=this.config.voiceURI,this.speech.volume=this.config.volume,this.speech.rate=this.config.rate,this.speech.pitch=this.config.pitch,this.speech.onend=function(){e.handleSpeechEnd()},console.log("TTS初始化成功"),!0}catch(t){return console.error("TTS初始化失败:",t),this.showMessage("TTS初始化失败"),!1}}},{key:"handleSpeechEnd",value:function(){if(this.currentSpeakText){if(this.currentSpeakCount+=1,this.currentSpeakCount<this.config.repeatCount)return console.log("第".concat(this.currentSpeakCount+1,"次重复播报")),void this.playNotice(this.currentSpeakText);this.currentSpeakText=null,this.batchSpeak||this.eventQueue.splice(0,1),this.playNextNotice()}}},{key:"toggleNotice",value:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(){var t,a=arguments;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a.length>0&&void 0!==a[0]?a[0]:null,this.isEnabled=null===t?!this.isEnabled:t,!this.isEnabled){e.next=8;break}return e.next=5,this.playNotice("已开启语音通知");case 5:this.showMessage("已开启语音通知!"),e.next=9;break;case 8:this.showMessage("语音通知已关闭!");case 9:return e.abrupt("return",this.isEnabled);case 10:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}()},{key:"speakText",value:function(e){this.isEnabled&&e&&this.speech&&(this.speech.text=e,console.log("开始播放:",e),window.speechSynthesis.speak(this.speech),console.log("播放命令已发送"))}},{key:"cancel",value:function(){window.speechSynthesis&&window.speechSynthesis.cancel(),this.currentSpeakText=null,this.currentSpeakCount=0}},{key:"playNotice",value:function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(this.isEnabled){e.next=3;break}return console.log("已关闭语音通知!"),e.abrupt("return");case 3:this.speakText(t);case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},{key:"playNextNotice",value:function(){if(this.eventQueue.length>=3)return this.batchSpeak=!0,void this.speakText("最近发生".concat(this.eventQueue.length,"次告警"));if(this.batchSpeak=!1,window.speechSynthesis&&window.speechSynthesis.speaking)console.log("当前已有正在播放的语音!");else{var e=this.eventQueue[0];if(e){console.log("播放事件:",e);var t=this.buildEventText(e);this.currentSpeakCount=0,this.currentSpeakText=t,this.playNotice(t)}}}},{key:"buildEventText",value:function(e){return"".concat(e.devName,"发生").concat(this.getOrderTypeName(e.orderType),"告警")}},{key:"getOrderTypeName",value:function(e){var t={21:"跌倒",22:"久滞",23:"坠床",31:"主动呼叫"};return t[e]||"未知"}},{key:"addEvent",value:function(e){this.eventQueue.push(e)}},{key:"removeAllEvents",value:function(){this.eventQueue=[],this.cancel()}},{key:"getQueueLength",value:function(){return this.eventQueue.length}},{key:"getStatus",value:function(){return{isEnabled:this.isEnabled,queueLength:this.eventQueue.length,isPlaying:!!window.speechSynthesis&&window.speechSynthesis.speaking,currentText:this.currentSpeakText,currentCount:this.currentSpeakCount}}},{key:"showMessage",value:function(e){this.config.onMessage&&"function"===typeof this.config.onMessage?this.config.onMessage(e):console.log("TTS消息:",e)}},{key:"destroy",value:function(){this.cancel(),this.eventQueue=[],this.currentSpeakText=null,this.currentSpeakCount=0,this.speech=null,console.log("TTS实例已销毁")}}]),e}(),p=f,v={props:{title:String,path:String},computed:{noticeStatus:function(){return!!this.ttsManager&&this.ttsManager.isEnabled}},data:function(){return{currentTime:"",socket:void 0,eventList:[],dialogTableVisible:!1,ttsManager:null,wsConnected:!1,wsReconnectAttempts:0,wsMaxReconnectAttempts:5,wsReconnectInterval:3e3,wsHeartbeatTimer:null,wsHeartbeatInterval:3e4,wsReconnectTimer:null}},watch:{path:function(e){this.path=e,this.closeWS(),this.initWS()}},mounted:function(){var e=this;this.setInterval=setInterval((function(){e.currentTime=c()().format("YYYY-MM-DD HH:mm:ss")}),1e3),this.ttsManager=new p({onMessage:function(t){e.$message(t)}}),console.log("path:"+this.path),this.path&&this.initWS()},beforeDestroy:function(){this.closeWS(),this.ttsManager&&this.ttsManager.destroy()},methods:{initWS:function(){if(void 0!==WebSocket)if(this.path)try{console.log("正在建立WebSocket连接:",this.path),this.socket=new WebSocket(this.path),this.socket.onopen=this.open,this.socket.onerror=this.error,this.socket.onmessage=this.getMessage,this.socket.onclose=this.close}catch(e){console.error("WebSocket连接创建失败:",e),this.scheduleReconnect()}else console.warn("WebSocket路径为空，无法建立连接");else this.$message("您的浏览器不支持socket")},open:function(){console.log("WebSocket连接成功"),this.wsConnected=!0,this.wsReconnectAttempts=0,this.startHeartbeat()},error:function(e){console.error("WebSocket连接错误:",e),this.wsConnected=!1,this.stopHeartbeat(),this.scheduleReconnect()},getMessage:function(e){console.log("getMessage:",e);try{var t=JSON.parse(e.data);if("heartbeat"===t.type||"pong"===t.type)return void console.log("收到心跳响应");if(t.data){var a=t.data,s=a.wsdata,i=a.type;if(console.log("wsdata:",s),"EVENT_ORDER"==i&&this.noticeStatus){this.dialogTableVisible||(this.dialogTableVisible=!0);var n=this.eventList.length;this.eventList.push(s),this.ttsManager&&(this.ttsManager.addEvent(s),0==n&&this.ttsManager.playNextNotice())}console.log(s)}}catch(o){console.error("解析WebSocket消息失败:",o,e.data)}},send:function(){var e="";this.socket.send(e)},close:function(e){console.log("WebSocket连接已关闭:",e),this.wsConnected=!1,this.stopHeartbeat(),1e3!==e.code&&(console.log("WebSocket异常关闭，准备重连..."),this.scheduleReconnect())},remove:function(e){this.eventList=this.eventList.filter((function(t){return t.id!=e})),0==this.eventList.length&&(this.dialogTableVisible=!1)},removeAll:function(){this.eventList=[],this.dialogTableVisible=!1,this.ttsManager&&this.ttsManager.removeAllEvents()},openNotice:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!e.ttsManager){t.next=3;break}return t.next=3,e.ttsManager.toggleNotice();case 3:case"end":return t.stop()}}),t)})))()},cancel:function(){this.ttsManager&&this.ttsManager.cancel()},playNextNotice:function(){this.ttsManager&&(this.ttsManager.eventQueue=Object(n["a"])(this.eventList),this.ttsManager.playNextNotice()),0==this.eventList.length&&(this.dialogTableVisible=!1)},getOrderTypeName:function(e){switch(e){case"21":return"跌倒";case"22":return"久滞";case"23":return"坠床";case"24":return"离床";case"31":return"主动呼叫";case"32":return"户外呼叫"}},closeWS:function(){this.stopHeartbeat(),this.clearReconnectTimer(),this.socket&&(this.socket.onopen=null,this.socket.onmessage=null,this.socket.onerror=null,this.socket.onclose=null,this.socket.readyState===WebSocket.OPEN&&this.socket.close(1e3,"正常关闭"),this.socket=null),this.wsConnected=!1},startHeartbeat:function(){var e=this;this.stopHeartbeat(),this.wsHeartbeatTimer=setInterval((function(){if(e.socket&&e.socket.readyState===WebSocket.OPEN)try{e.socket.send(JSON.stringify({type:"heartbeat",timestamp:Date.now()})),console.log("发送心跳消息")}catch(t){console.error("发送心跳消息失败:",t),e.scheduleReconnect()}else console.warn("WebSocket连接不可用，停止心跳"),e.stopHeartbeat(),e.scheduleReconnect()}),this.wsHeartbeatInterval)},stopHeartbeat:function(){this.wsHeartbeatTimer&&(clearInterval(this.wsHeartbeatTimer),this.wsHeartbeatTimer=null)},scheduleReconnect:function(){var e=this;if(this.wsReconnectAttempts>=this.wsMaxReconnectAttempts)return console.error("WebSocket重连次数已达上限，停止重连"),void this.$message.error("WebSocket连接失败，请刷新页面重试");this.clearReconnectTimer();var t=Math.min(this.wsReconnectInterval*Math.pow(2,this.wsReconnectAttempts),3e4);console.log("WebSocket将在".concat(t,"ms后进行第").concat(this.wsReconnectAttempts+1,"次重连")),this.wsReconnectTimer=setTimeout((function(){e.wsReconnectAttempts++,e.initWS()}),t)},clearReconnectTimer:function(){this.wsReconnectTimer&&(clearTimeout(this.wsReconnectTimer),this.wsReconnectTimer=null)}}},m=v,g=(a("b5c5"),a("2877")),b=Object(g["a"])(m,s,i,!1,null,"47ca0bc3",null);t["a"]=b.exports},"451a":function(e,t,a){"use strict";a("8b44")},"4e12":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"flex-col"},[a("Bg",{attrs:{title:e.station.shortName+"实时监控"}},[a("div",{staticClass:"search flex-row"},[a("span",{staticClass:"house-label"},[e._v("房间")]),a("div",{staticClass:"house-select"},[a("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.handleFamilyChange,clear:e.handleFamilyClear},model:{value:e.search.familyId,callback:function(t){e.$set(e.search,"familyId",t)},expression:"search.familyId"}},e._l(e.dict.families,(function(e){return a("el-option",{key:e.value,attrs:{label:e.name,value:e.id}})})),1)],1),a("div",{staticClass:"search-input"},[a("el-input",{attrs:{type:"text",placeholder:"根据床位号、被监护人姓名搜索"},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),a("div",{staticClass:"search-btn btn flex-col",on:{click:e.handleSearch}},[a("span",{staticClass:"text"},[e._v("查询")])]),a("div",{staticClass:"search-btn btn flex-col",on:{click:e.handleReset}},[a("span",{staticClass:"text"},[e._v("重置")])])]),a("div",{staticClass:"device-list flex-row justify-between"},[e.warningHasData?a("div",{staticClass:"warning flex-col"},[a("div",{staticClass:"title flex-row align-center"},[a("div",{staticClass:"warning-block"}),a("span",{staticClass:"text"},[e._v("设备报警")])]),e.warningDatas.datas&&e.warningDatas.datas.length>0?a("div",{staticClass:"list flex-row justify-between"},e._l(e.warningDatas.datas,(function(t,s){return a("div",{key:t.devId+"_"+t.orderType+s,staticClass:"card flex-col align-center justify-between"},[a("div",{staticClass:"bed flex-col justify-center"},[t.bedNum?a("div",{staticClass:"bed-label"},[e._v(e._s(t.familyName+"#"+t.bedNum+"-"+t.devSceneName))]):a("div",{staticClass:"bed-label"},[e._v(e._s(t.familyName+"-"+t.devSceneName))])]),a("div",{staticClass:"older justify-center"},[a("span",{staticClass:"name"},[t.olderName?a("span",[e._v(" "+e._s(t.olderName))]):e._e(),t.genderDesc?a("span",[e._v(e._s("-"+t.genderDesc))]):e._e(),t.age?a("span",[e._v(e._s("-"+t.age))]):e._e()])]),a("div",{staticClass:"status flex-row justify-between"},["3"==t.devScene||"1"==t.devScene?a("div",{staticClass:"status-item flex-col justify-between align-center"},[a("span",{staticClass:"icon iconfont icon-youren status-icon",style:{color:"0"!=t.moveStatus?"#35fff4":"#999"}}),a("span",{staticClass:"descr",style:{color:"0"!=t.moveStatus?"#fff":"#999"}},[e._v(e._s(t.moveStatusName))])]):e._e(),"2"==t.devScene?a("div",{staticClass:"status-item flex-col justify-between align-center"},[a("span",{staticClass:"icon iconfont icon-youren status-icon",style:{color:"0"!=t.personStatus?"#35fff4":"#999"}}),a("span",{staticClass:"descr",style:{color:"0"!=t.personStatus?"#fff":"#999"}},[e._v(e._s(t.personStatusName))])]):e._e(),"3"==t.devScene?a("div",{staticClass:"status-item flex-col justify-between"},[a("span",{staticClass:"icon iconfont icon-zaichuang status-icon",style:{color:"0"!=t.inbedStatus?"#35fff4":"#999"}}),a("span",{staticClass:"descr",style:{color:"0"!=t.inbedStatus?"#fff":"#999"}},[e._v(e._s(t.inbedStatusName))])]):e._e()]),a("div",{staticClass:"btn-block flex-row justify-around"},[a("div",{staticClass:"card-btn btn btn1 flex-row justify-evenly align-center",on:{click:function(a){return e.handleRealtime(t)}}},[a("span",{staticClass:"icon iconfont icon-shishitongji btn-icon"}),a("span",{staticClass:"text"},[e._v("实时统计")])]),3==t.devScene?a("div",{staticClass:"card-btn btn btn2 flex-row justify-evenly align-center",on:{click:function(a){return e.handleReport(t)}}},[a("span",{staticClass:"icon iconfont icon-chakanbaogao btn-icon"}),a("span",{staticClass:"text"},[e._v("查看报告")])]):e._e()]),a("div",{staticClass:"card-bottom flex-col justify-center"},[a("div",{staticClass:"event"},[e._v(e._s(t.orderTypeName))])])])})),0):e._e(),a("div",{staticClass:"pagination-block flex-col align-center"},[a("el-pagination",{attrs:{background:"",small:"",layout:"prev, pager, next, jumper","current-page":e.warningDatas.pageInfo.current,"page-size":e.warningDatas.pageInfo.pageSize,total:e.warningDatas.pageInfo.total},on:{"current-change":e.handleWarningCurrentChange,"update:currentPage":function(t){return e.$set(e.warningDatas.pageInfo,"current",t)},"update:current-page":function(t){return e.$set(e.warningDatas.pageInfo,"current",t)}}})],1)]):e._e(),a("div",{staticClass:"info flex-col"},[a("div",{staticClass:"title flex-row align-center"},[a("div",{staticClass:"info-block"}),a("span",{staticClass:"text"},[e._v("设备检测")])]),e.infoDatas.datas&&e.infoDatas.datas.length>0?a("div",{staticClass:"list flex-row justify-between"},e._l(e.infoDatas.datas,(function(t){return a("div",{key:t.devId,staticClass:"card flex-col align-center justify-between"},[a("div",{staticClass:"bed flex-col justify-center"},[t.bedNum?a("div",{staticClass:"bed-label"},[e._v(e._s(t.familyName+"#"+t.bedNum+"-"+t.devSceneName))]):a("div",{staticClass:"bed-label"},[e._v(e._s(t.familyName+"-"+t.devSceneName))])]),a("div",{staticClass:"older justify-center"},[a("span",{staticClass:"name"},[t.olderName?a("span",[e._v(" "+e._s(t.olderName))]):e._e(),t.genderDesc?a("span",[e._v(e._s("-"+t.genderDesc))]):e._e(),t.age?a("span",[e._v(e._s("-"+t.age))]):e._e()])]),a("div",{staticClass:"status flex-row justify-between"},["3"==t.devScene||"1"==t.devScene?a("div",{staticClass:"status-item flex-col justify-between align-center"},[a("span",{staticClass:"icon iconfont icon-youren status-icon",style:{color:"0"!=t.moveStatus?"#35fff4":"#999"}}),a("span",{staticClass:"descr",style:{color:"0"!=t.moveStatus?"#fff":"#999"}},[e._v(e._s(t.moveStatusName))])]):e._e(),"2"==t.devScene?a("div",{staticClass:"status-item flex-col justify-between align-center"},[a("span",{staticClass:"icon iconfont icon-youren status-icon",style:{color:"0"!=t.personStatus?"#35fff4":"#999"}}),a("span",{staticClass:"descr",style:{color:"0"!=t.personStatus?"#fff":"#999"}},[e._v(e._s(t.personStatusName))])]):e._e(),"3"==t.devScene?a("div",{staticClass:"status-item flex-col justify-between"},[a("span",{staticClass:"icon iconfont icon-zaichuang status-icon",style:{color:"0"!=t.inbedStatus?"#35fff4":"#999"}}),a("span",{staticClass:"descr",style:{color:"0"!=t.inbedStatus?"#fff":"#999"}},[e._v(e._s(t.inbedStatusName))])]):e._e()]),a("div",{staticClass:"btn-block flex-row justify-around"},[a("div",{staticClass:"card-btn btn btn1 flex-row justify-evenly align-center",on:{click:function(a){return e.handleRealtime(t)}}},[a("span",{staticClass:"icon iconfont icon-shishitongji btn-icon"}),a("span",{staticClass:"text"},[e._v("实时统计")])]),3==t.devScene?a("div",{staticClass:"card-btn btn btn2 flex-row justify-evenly align-center",on:{click:function(a){return e.handleReport(t)}}},[a("span",{staticClass:"icon iconfont icon-chakanbaogao btn-icon"}),a("span",{staticClass:"text"},[e._v("查看报告")])]):e._e()]),a("div",{staticStyle:{height:"40px"}})])})),0):e._e(),a("div",{staticClass:"pagination-block flex-col align-center"},[a("el-pagination",{attrs:{background:"",small:"",layout:"prev, pager, next, jumper","current-page":e.infoDatas.pageInfo.current,"page-size":e.infoDatas.pageInfo.pageSize,total:e.infoDatas.pageInfo.total},on:{"current-change":e.handleInfoCurrentChange,"update:currentPage":function(t){return e.$set(e.infoDatas.pageInfo,"current",t)},"update:current-page":function(t){return e.$set(e.infoDatas.pageInfo,"current",t)}}})],1)])])]),a("el-dialog",{staticClass:"realtime-dialog",attrs:{title:e.dialogTitle,visible:e.showRealtimeDialog,close:e.closeRealtimeDialog,width:"72%","destroy-on-close":"",center:""},on:{"update:visible":function(t){e.showRealtimeDialog=t}}},[e.currentDevice&&"1"==e.currentDevice.devScene?a("Parlour",{attrs:{devInfo:e.devInfo,sleepValue:e.sleepValue}}):e._e(),e.currentDevice&&"2"==e.currentDevice.devScene?a("Toilet",{attrs:{devInfo:e.devInfo,sleepValue:e.sleepValue,lineDatas:e.toiletLineDatas}}):e._e(),e.currentDevice&&"3"==e.currentDevice.devScene?a("Bedroom",{attrs:{devInfo:e.devInfo,sleepValue:e.sleepValue,lineDatas:e.bedroomLineDatas,lineDatas2:e.bedroomLineDatas2}}):e._e()],1)],1)},i=[],n=a("5530"),o=a("1da1"),r=(a("96cf"),a("ac1f"),a("841c"),a("99af"),a("d81d"),a("4de4"),a("5319"),a("caad"),a("a630"),a("3ca3"),a("d3b7"),a("6062"),a("ddb0"),a("c9b7"),a("ca00")),c=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"bedroom"},[a("DialogTop",{attrs:{device:e.devInfo}}),a("div",{staticClass:"chart-area flex-row flex-wrap justify-between"},[a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("DeviceStatus",{attrs:{device:e.devInfo}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("PersonStatus",{attrs:{sleepValue:e.sleepValue,setStyle:e.setStyle,devScene:"3"}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col justify-around"},[a("div",{staticClass:"flex-row justify-between"},[a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-xinshuaijiance data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("心率")]),a("div",{staticClass:"desc"},[e._v(e._s(e.devInfo.heartRate||"-")+"次")])])]),e.lineDatas&&e.lineDatas.length?a("LineChart",{attrs:{"line-color":"#4D7BFF",datas:e.lineDatas,title:"心率",markLines:[100,60,20],max:130}}):e._e()],1),a("div",{staticClass:"flex-row justify-between align-center"},[a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-huxijiance data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("呼吸")]),a("div",{staticClass:"desc"},[e._v(e._s(e.devInfo.breathRate||"-")+"次")])])]),e.lineDatas2&&e.lineDatas2.length?a("LineChart",{attrs:{"line-color":"#01B09A",datas:e.lineDatas2,title:"呼吸",markLines:[20,12,1],max:28}}):e._e()],1)])])])],1)},l=[],u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"dialog-top flex-row justify-between"},[a("div",{staticClass:"flex-row"},[a("span",{staticClass:"curr-status"},[e._v("当前状态")]),"3"==e.device.devScene||"1"==e.device.devScene?a("span",{staticClass:"move-status",style:{color:"0"!=e.device.moveStatus?"#35fff4":"#999"}},[e._v(e._s(e.device.moveStatusName))]):e._e(),"3"==e.device.devScene?a("span",{staticClass:"move-status",style:{color:"0"!=e.device.inbedStatus?"#35fff4":"#999"}},[e._v(e._s(e.device.inbedStatusName))]):e._e(),"2"==e.device.devScene?a("span",{staticClass:"move-status",style:{color:"0"!=e.device.personStatus?"#35fff4":"#999"}},[e._v(e._s(e.device.personStatusName))]):e._e()])])},d=[],h={name:"DialogTop",props:{device:{type:Object,required:!0}},methods:{}},f=h,p=(a("60c5"),a("2877")),v=Object(p["a"])(f,u,d,!1,null,"45e4cf3c",null),m=v.exports,g=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("canvas",{staticStyle:{height:"50px"},style:e.style,attrs:{"canvas-id":"sleepCanvas",id:"sleepCanvas"},on:{click:e.handleSleepcharts}}),a("div",{staticStyle:{display:"flex","justify-content":"space-between","font-size":"20px",color:"#999",margin:"18px 0 0 0"}},[a("div",[e._v(e._s(e.startSleepTime))]),e.touchValue.sleepState?a("div",{staticStyle:{"text-align":"center",width:"300px",height:"48px","border-radius":"28px","font-size":"24px","line-height":"48px",background:"#eee"},style:{color:4===Object.keys(e.colors).length&&"人员离开"===e.touchValue.sleepState?"#949494":e.colorMapping[e.touchValue.sleepState]||"#fff"}},[e._v(" "+e._s(e.touchValue.startTime)+"-"+e._s(e.touchValue.endTime)+" "+e._s(e.touchValue.sleepState)+" ")]):e._e(),a("div",[e._v(e._s(e.endSleepTime))])])])},b=[],y=(a("159b"),{name:"SleepChart",props:{sleepValue:{type:Array},setStyle:{type:Object},colors:{type:Object,default:function(){return{"监测到人":"#F66C3E","人员离开":"#00A3F0","人员在床":"#01B09A","人员离床":"#EDEDED"}}}},watch:{sleepValue:function(e,t){var a=this;this.sleepValue=e,this.calculateSNTime(),this.$nextTick((function(){a.width?a.drawCharts():a.setStyleFn(!0)}))}},computed:{convertColor:function(){return 4===this.colors.length&&"人员离开"===this.touchValue.sleepState?"#949494":this.colorMapping[this.touchValue.sleepState]||"#fff"}},data:function(){return{touchValue:{},startSleepTime:"",endSleepTime:"",style:"",colorMapping:this.colors}},mounted:function(){this.calculateSNTime(),this.setStyleFn(!0)},methods:{setStyleFn:function(e){var t=this;this.style="margin-left:".concat(this.setStyle.marginLeft,"; width:").concat(this.setStyle.width,"; display: block;"),this.$nextTick((function(){setTimeout((function(){t.getDescBox(e)}),300)}))},getDescBox:function(e){var t=this,a=document.querySelector("#sleepCanvas");if(a){var s=a.getBoundingClientRect();s?(this.width=s.width,this.height=s.height,e&&this.drawCharts()):setTimeout((function(){return t.getDescBox(e)}),100)}},getAllTime:function(){var e=this.sleepValue[this.sleepValue.length-1].endTime-this.sleepValue[0].startTime;return e},timestampToTime:function(e){var t=new Date(1e3*e),a=t.getFullYear()+"-",s=(t.getMonth()+1<10?"0"+(t.getMonth()+1):t.getMonth()+1)+"-",i=(t.getDate()<10?"0"+t.getDate():t.getDate())+" ",n=(t.getHours()<10?"0"+t.getHours():t.getHours())+":",o=(t.getMinutes()<10?"0"+t.getMinutes():t.getMinutes())+":",r=t.getSeconds()<10?"0"+t.getSeconds():t.getSeconds();return a+s+i+n+o+r},calculateSNTime:function(){this.startSleepTime=this.timestampToTime(this.sleepValue[0].startTime).substr(10,6),this.endSleepTime=this.timestampToTime(this.sleepValue[this.sleepValue.length-1].endTime).substr(10,6)},drawCharts:function(){var e=this,t=this.getAllTime(),a=this.width,s=(this.height,document.getElementById("sleepCanvas")),i=s.getContext("2d");i.save(),i.beginPath(),i.lineWidth=1;var n=0;this.sleepValue.map((function(s,o){i.fillStyle=e.colorMapping[s.sleepState]||"#fff";var r=s.endTime-s.startTime;r=r/t*a,i.fillRect(n,10,r,100),n+=r,i.stroke()})),i.restore(),i.save()},handleSleepcharts:function(e){var t=this,a=this.getAllTime(),s=e.detail.x-e.currentTarget.offsetLeft,i=this.width,n=s/i*a,o=this.sleepValue[0].startTime+n,r=!1;this.sleepValue.forEach((function(e){!r&&o>e.startTime&&o<e.endTime&&(t.touchValue.startTime=t.timestampToTime(e.startTime).substr(11,5),t.touchValue.endTime=t.timestampToTime(e.endTime).substr(11,5),t.touchValue.sleepState=e.sleepState,t.touchValue.sleepStateText=e.sleepState,console.log("cc",e.startTime,e.endTime,t.timestampToTime(e.startTime).substr(11,5),t.timestampToTime(e.endTime).substr(11,5),e.sleepState),t.$forceUpdate())}))}}}),w=y,S=(a("e215"),Object(p["a"])(w,g,b,!1,null,"7a7ffb74",null)),x=S.exports,C=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"line-chart",attrs:{id:e.optionId}})])},T=[],D=(a("a9e3"),a("b0c0"),a("313e")),_={name:"LineChart",props:{lineColor:{type:String,default:"#01B09A"},datas:{type:Array,default:function(){return[]}},markLines:{type:Array,default:function(){return[0,0,0]}},max:{type:Number||String,default:0},showAxisX:{type:Boolean,default:!0},title:{type:String}},data:function(){return{optionId:void 0,ec:{lazyLoad:!0},option:{tooltip:{show:!0,trigger:"axis",formatter:function(e){for(var t="",a=0;a<e.length;a++)t+="".concat(e[a].name,"\r\n").concat(e[a].seriesName,": ").concat(e[a].value);return t},textStyle:{fontSize:12}},grid:{left:"8%",right:"10%",bottom:"0",top:"20",containLabel:!0},xAxis:{data:[],show:this.hiddenAxisX,axisLabel:{color:"#01B09A",textStyle:{fontSize:"13"}},axisTick:{show:!0,lineStyle:{color:"#01B09A",width:"2"}},axisLine:{lineStyle:{color:"#01B09A",width:"2"}},splitLine:{show:!1,lineStyle:{color:"#01B09A",width:"2"}}},yAxis:{min:0,axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{show:!1}},series:[{name:this.title,type:"line",smooth:!0,showSymbol:!1,data:[],lineStyle:{width:2,color:this.lineColor},markLine:{symbol:!1,tooltip:{show:!1},data:[{yAxis:this.markLines[0],name:"过速",lineStyle:{color:"#ED2E1C"},label:{normal:{color:"#666",formatter:"过速"}}},{yAxis:this.markLines[1],name:"正常",lineStyle:{color:"#81D88A"},label:{normal:{color:"#666",formatter:"正常"}}},{yAxis:this.markLines[2],name:"过缓",lineStyle:{color:"#FAD347"},label:{normal:{color:"#666",formatter:"过缓"}}}]}}]}}},mounted:function(){for(var e=this,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",a=t.length,s="",i=0;i<32;i++)s+=t.charAt(Math.floor(Math.random()*a));this.optionId=s;var n=[],o=[];this.datas.map((function(e){n.push(e[0]),o.push(e[1])}));var r=Math.max.apply(Math,o)||0;r<this.max&&(r=this.max),this.option.xAxis.data=n||[],this.option.yAxis.max=r>0?r+parseInt(.1*r):r,this.option.series[0].data=o||[],this.$nextTick((function(){console.log("init line chart"),console.log("h5");var t=document.getElementById(e.optionId),a=D["init"](t);a.setOption(e.option)}))},methods:{}},k=_,I=(a("b502"),Object(p["a"])(k,C,T,!1,null,"2fc23f39",null)),j=I.exports,R=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"flex-row flex-wrap justify-between"},[a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-shijian1 data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("总停留时长")]),a("div",{staticClass:"desc"},[e._v(e._s(e.device.parlorOtherStayDuration||"-")+"分")])])]),a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-huodongshuju data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("今日活动")]),a("div",{staticClass:"desc"},[e._v(e._s(e.device.walkingDistance||"-")+"米")])])]),a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-a-icon_menchumen data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("今日进出")]),a("div",{staticClass:"desc"},[e._v(e._s(e.device.toiletInoutFrequency||"-")+"次")])])]),a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-dangtianshichang data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("最大停留时长")]),a("div",{staticClass:"desc"},[e._v(e._s(e.device.toiletMaxStayDuration||"-")+"分")])])]),a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-xinshuaijiance data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("心率")]),a("div",{staticClass:"desc"},[e._v(e._s(e.device.heartRate||"-")+"次")])])]),a("div",{staticClass:"device-data flex-row"},[a("span",{staticClass:"icon iconfont icon-huxijiance data-icon"}),a("div",{staticClass:"flex-col"},[a("div",{staticClass:"title"},[e._v("呼吸")]),a("div",{staticClass:"desc"},[e._v(e._s(e.device.breathRate||"-")+"次")])])])])},A=[],N={name:"DeviceStatus",props:{device:{type:Object,required:!0}},methods:{}},L=N,M=(a("6b69"),Object(p["a"])(L,R,A,!1,null,"17e0b74f",null)),E=M.exports,V=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"person-status flex-col"},[a("span",{staticClass:"card-title"},[e._v("人员实时状态")]),a("div",[e.sleepValue&&e.sleepValue.length?a("SleepCharts",{attrs:{sleepValue:e.sleepValue,setStyle:e.setStyle,colors:{"人员离床":"#ED2E1C","人员在床":"#81D88A","监测到人":"#01B09A","人员离开":"#EDEDED"}}}):e._e(),a("div",{staticClass:"dynamic-list flex-row justify-between"},["3"==e.devScene?a("div",{staticClass:"legend flex-row align-center"},[a("div",{staticClass:"legend-box",staticStyle:{"background-color":"#ed2e1c"}}),a("div",{staticClass:"legend-label"},[e._v("人员离床")])]):e._e(),"3"==e.devScene?a("div",{staticClass:"legend flex-row align-center"},[a("div",{staticClass:"legend-box",staticStyle:{"background-color":"#81d88a"}}),a("div",{staticClass:"legend-label"},[e._v("人员在床")])]):e._e(),e._m(0),e._m(1)])],1)])},O=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"legend flex-row align-center"},[a("div",{staticClass:"legend-box",staticStyle:{"background-color":"#01b09a"}}),a("div",{staticClass:"legend-label"},[e._v("监测到人")])])},function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"legend flex-row align-center"},[a("div",{staticClass:"legend-box",staticStyle:{"background-color":"#ededed"}}),a("div",{staticClass:"legend-label"},[e._v("人员离开")])])}],$={name:"PersonStatus",components:{SleepCharts:x},props:{sleepValue:{type:Array,required:!0},setStyle:{type:Object},devScene:{type:String,required:!0}},methods:{}},z=$,B=(a("156f"),Object(p["a"])(z,V,O,!1,null,"6db52f0f",null)),W=B.exports,H={name:"Bedroom",components:{DialogTop:m,SleepCharts:x,LineChart:j,DeviceStatus:E,PersonStatus:W},data:function(){return{setStyle:{width:"100%"}}},props:{devInfo:{type:Object,required:!0},sleepValue:{type:Array,required:!0},lineDatas:{type:Array,required:!0},lineDatas2:{type:Array,required:!0}},methods:{}},P=H,F=(a("451a"),Object(p["a"])(P,c,l,!1,null,"64e4a606",null)),q=F.exports,Q=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"parlour"},[a("DialogTop",{attrs:{device:e.devInfo}}),a("div",{staticClass:"chart-area flex-row flex-wrap justify-between"},[a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("DeviceStatus",{attrs:{device:e.devInfo}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("PersonStatus",{attrs:{sleepValue:e.sleepValue,setStyle:e.setStyle,devScene:"1"}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("AnnularChart",{attrs:{num:e.devInfo.walkingDistance||"0",unit:"米",desc:"今日活动"}})],1)])])],1)},Y=[],U=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"annular-chart",attrs:{id:e.optionId}})},J=[],G={name:"AnnularChart",props:{num:{type:Number||String,default:0},unit:{type:String,default:"米"},desc:{type:String,default:""}},data:function(){return{optionId:void 0,ec:{lazyLoad:!0},option:{title:{text:"{a|"+this.num+"}{b|"+this.unit+"}\n{c|"+this.desc+"}",x:"center",y:"center",textStyle:{rich:{a:{fontSize:38,color:"#01B09A",fontWeight:"600"},b:{fontSize:14,color:"#01B09A",padding:[0,0,10,10]},c:{fontSize:14,color:"#01B09A",padding:[5,0]}}}},angleAxis:{max:100,clockwise:!0,show:!1,boundaryGap:["40%","40%"],startAngle:90},radiusAxis:{type:"category",show:!0,axisLabel:{show:!1},axisLine:{show:!1},axisTick:{show:!1}},polar:[{center:["50%","50%"],radius:"165%"}],series:[{name:"小环",type:"gauge",radius:"110%",center:["50%","50%"],startAngle:0,endAngle:360,axisLine:{show:!1},axisTick:{show:!0,lineStyle:{color:"#ededed",width:6,shadowBlur:1,shadowColor:"transparent"},length:16,splitNumber:4},splitLine:{show:!1},axisLabel:{show:!1},detail:{show:!1}},{type:"bar",z:10,data:[this.num],showBackground:!1,backgroundStyle:{color:"blue",borderWidth:10,width:10},coordinateSystem:"polar",roundCap:!0,barWidth:16,itemStyle:{normal:{opacity:1,color:new D["graphic"].LinearGradient(0,0,1,1,[{offset:0,color:"#01B09A"},{offset:1,color:"#01B09A"}])}}}]}}},mounted:function(){for(var e=this,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",a=t.length,s="",i=0;i<32;i++)s+=t.charAt(Math.floor(Math.random()*a));this.optionId=s,this.$nextTick((function(){var t=document.getElementById(e.optionId),a=D["init"](t);a.setOption(e.option)}))},methods:{}},X=G,K=(a("819f"),Object(p["a"])(X,U,J,!1,null,"6afd30d9",null)),Z=K.exports,ee={name:"Parlour",components:{DialogTop:m,DeviceStatus:E,PersonStatus:W,AnnularChart:Z},data:function(){return{setStyle:{width:"100%"}}},props:{devInfo:{type:Object,required:!0},sleepValue:{type:Array,required:!0}},methods:{}},te=ee,ae=(a("5c37"),Object(p["a"])(te,Q,Y,!1,null,"25ce3759",null)),se=ae.exports,ie=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"toilet"},[a("DialogTop",{attrs:{device:e.devInfo}}),a("div",{staticClass:"chart-area flex-row flex-wrap justify-between"},[a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("DeviceStatus",{attrs:{device:e.devInfo}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("PersonStatus",{attrs:{sleepValue:e.sleepValue,setStyle:e.setStyle,devScene:"2"}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("AnnularChart",{attrs:{num:e.devInfo.toiletInoutFrequency||0,desc:"今日进出",unit:"次"}})],1)]),a("div",{staticClass:"card flex-row flex-wrap justify-evenly"},[a("div",{staticClass:"card-boby flex-col"},[a("span",{staticClass:"card-title"},[e._v("停留时间统计")]),a("ResidenceTimeChart",{attrs:{dataObj:e.lineDatas}})],1)])])],1)},ne=[],oe=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"chart-canvas",attrs:{id:e.optionId}})])},re=[],ce={props:{dataObj:{type:Object,default:function(){return{}}}},data:function(){return{optionId:void 0,ec:{lazyLoad:!0},option:{tooltip:{trigger:"axis",axisPointer:{type:"shadow"},formatter:function(e){return console.log("params",e[0].marker),"".concat(e[0].seriesName,": ").concat(e[0].data,"分")},textStyle:{fontSize:12}},grid:{left:"2%",right:"4%",bottom:"4%",top:"10%",containLabel:!0},xAxis:{type:"category",data:this.dataObj.xArrs||[],axisLine:{show:!1},axisTick:{show:!1},axisLabel:{textStyle:{color:"#555",fontSize:12}}},yAxis:{type:"value",axisLine:{show:!1},axisTick:{show:!1},splitLine:{show:!1},axisLabel:{textStyle:{color:"#555",fontSize:12}}},series:[{name:"停留时长",type:"bar",barWidth:16,itemStyle:{normal:{color:new D["graphic"].LinearGradient(0,0,0,1,[{offset:0,color:"#01B09A"},{offset:1,color:"#01B09A"}]),borderRadius:[12,12,0,0]}},data:this.dataObj.values||[]}]}}},mounted:function(){for(var e=this,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",a=t.length,s="",i=0;i<32;i++)s+=t.charAt(Math.floor(Math.random()*a));this.optionId=s,this.$nextTick((function(){var t=document.getElementById(e.optionId),a=D["init"](t);a.setOption(e.option)}))},methods:{}},le=ce,ue=(a("d7db"),Object(p["a"])(le,oe,re,!1,null,null,null)),de=ue.exports,he={name:"Toilet",components:{DialogTop:m,DeviceStatus:E,PersonStatus:W,AnnularChart:Z,ResidenceTimeChart:de},data:function(){return{setStyle:{width:"100%"}}},props:{devInfo:{type:Object,required:!0},sleepValue:{type:Array,required:!0},lineDatas:{type:Object,required:!0}},methods:{}},fe=he,pe=(a("0449"),Object(p["a"])(fe,ie,ne,!1,null,"ed40fb9e",null)),ve=pe.exports,me=a("4277"),ge={components:{Bg:me["a"],Bedroom:q,Parlour:se,Toilet:ve},data:function(){return{dict:{families:[]},station:{},search:{stationId:void 0,familyId:void 0,keyword:void 0},warningDatas:{datas:[],pageInfo:{current:1,pageSize:6,total:0}},warningHasData:!1,infoDatas:{datas:[],pageInfo:{current:1,pageSize:12,total:0}},showRealtimeDialog:!1,dialogTitle:"实时统计",currentDevice:void 0,devInfo:{},sleepValue:[],toiletLineDatas:{},bedroomLineDatas:[],bedroomlineDatas2:[]}},mounted:function(){var e=this;"2"===this.$store.state.user.member.systemVersion?(this.search.stationId=this.$route.params.stationId,this.getFamilyList(),this.fetchStationDetail(this.$route.params.stationId),this.initData(),setInterval((function(){e.clearPageDate(),e.initData()}),3e4)):this.$message({type:"warning",message:"当前版本不支持该功能!"})},methods:{formatDate:r["d"],initData:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){var a,s,i,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=Object(n["a"])(Object(n["a"])({},e.search),{},{pageReqVo:e.warningDatas.pageInfo}),console.log(a),t.next=4,e.$api.post("/bms/screen/realtime/getAlarmDeviceList",a);case 4:return s=t.sent,"00000"===s.status&&s.data?(e.warningDatas.datas=s.data,e.warningDatas.pageInfo.total=s.page.total):(e.warningDatas.datas=[],e.warningDatas.pageInfo.total=0),e.warningHasData=e.warningDatas.pageInfo.total>0,e.warningHasData||(e.infoDatas.pageInfo.pageSize=18),i=Object(n["a"])(Object(n["a"])({},e.search),{},{pageReqVo:e.infoDatas.pageInfo}),console.log(i),t.next=12,e.$api.post("/bms/screen/realtime/getDeviceList",i);case 12:o=t.sent,"00000"===o.status&&o.data?(e.infoDatas.datas=o.data,e.infoDatas.pageInfo.total=o.page.total):(e.infoDatas.datas=[],e.infoDatas.pageInfo.total=0);case 14:case"end":return t.stop()}}),t)})))()},clearPageDate:function(){this.warningDatas={datas:[],pageInfo:{current:1,pageSize:6,total:0}},this.warningHasData=!1,this.infoDatas={datas:[],pageInfo:{current:1,pageSize:12,total:0}}},fetchStationDetail:function(e){var t=this;e&&this.$api.get("/bms/station/getStationInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.station=e.data)}))},handleInfoCurrentChange:function(e){this.infoDatas.pageInfo.current=e,this.initData()},handleWarningCurrentChange:function(e){this.warningDatas.pageInfo.current=e,this.initData()},handleSearch:function(){this.warningDatas.pageInfo.current=1,this.infoDatas.pageInfo.current=1,this.initData()},handleReset:function(){this.search.familyId=void 0,this.search.keyword=void 0,this.warningDatas.pageInfo.current=1,this.infoDatas.pageInfo.current=1},handleFamilyClear:function(){this.dict.families=[],this.search.familyId=void 0},handleFamilyChange:function(e){this.search.familyId=e,this.initData()},getFamilyList:function(){var e=this;this.search.stationId?this.$api.get("/bms/station/house/getListByStationId",{params:{stationId:this.search.stationId}}).then((function(t){"00000"===t.status&&t.data?e.dict.families=t.data:e.dict.families=[]})):this.dict.families=[]},handleRealtime:function(e){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return console.log("点击设备"),t.clearDialogData(),t.showRealtimeDialog=!0,t.currentDevice=e,t.dialogTitle=t.currentDevice.bedNum?t.currentDevice.familyName+"#"+t.currentDevice.bedNum+" - "+t.currentDevice.devSceneName:t.currentDevice.familyName+" - "+t.currentDevice.devSceneName,a.next=7,t.getDeviceData(t.currentDevice.devId);case 7:"1"==t.currentDevice.devScene?t.getParalourData(t.currentDevice.devId):"2"==t.currentDevice.devScene?t.getToiletData(t.currentDevice.devId):"3"==t.currentDevice.devScene&&t.getBedroomData(t.currentDevice.devId);case 8:case"end":return a.stop()}}),a)})))()},closeRealtimeDialog:function(){this.showRealtimeDialog=!1,this.clearDialogData()},clearDialogData:function(){this.currentDevice=void 0,this.sleepValue=void 0,this.devInfo=void 0,this.toiletLineDatas=[],this.bedroomLineDatas=[],this.bedroomLineDatas2=[]},handleReport:function(e){var t=this.formatDate("YYYY-mm-dd",new Date),a="/report/sleep?id=".concat(e.devId,"&date=").concat(t);window.open(a,"_blank")},getDeviceData:function(e){var t=this;return Object(o["a"])(regeneratorRuntime.mark((function a(){var s;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(e){a.next=2;break}return a.abrupt("return");case 2:return a.next=4,t.$api.get("/bms/screen/devSta/getDeviceInfo",{params:{devId:e}});case 4:s=a.sent,t.devInfo=s.data;case 6:case"end":return a.stop()}}),a)})))()},getParalourData:function(e){var t=this;this.$api.get("/bms/screen/devSta/getPersonRealtimeStatusList",{params:{devId:e}}).then((function(e){if("00000"===e.status&&e.data){var a=[];(e.data||[]).filter((function(e){return e.startTime&&e.endTime})).map((function(t,s){var i=Date.parse(new Date(t.startTime.replace(/-/g,"/")))/1e3,n=Date.parse(new Date(t.endTime.replace(/-/g,"/")))/1e3;a.push({startTime:i,endTime:n,sleepState:t.status}),e.data[s+1]&&n!==Date.parse(new Date(e.data[s+1].startTime.replace(/-/g,"/")))/1e3&&a.push({startTime:n,endTime:Date.parse(new Date(e.data[s+1].startTime.replace(/-/g,"/")))/1e3,sleepState:"人员离开"})})),t.sleepValue=a}}))},getToiletData:function(e){var t=this;this.$api.get("/bms/screen/devSta/getToiletStayDurationList",{params:{devId:e}}).then((function(e){if("00000"===e.status&&e.data){var a=[],s=[];(e.data||[]).map((function(e){a.push(t.formatDate("HH:MM",new Date(e.inTime))),s.push(e.duration)})),t.toiletLineDatas={xArrs:a,values:s},console.log("toiletLineDatas",t.toiletLineDatas)}})),this.$api.get("/bms/screen/devSta/getPersonRealtimeStatusList",{params:{devId:e}}).then((function(e){if("00000"===e.status&&e.data){console.log(e.data);var a=[];(e.data||[]).filter((function(e){return e.startTime&&e.endTime})).map((function(t,s){var i=Date.parse(new Date(t.startTime.replace(/-/g,"/")))/1e3,n=Date.parse(new Date(t.endTime.replace(/-/g,"/")))/1e3;a.push({startTime:i,endTime:n,sleepState:t.status}),e.data[s+1]&&n!==Date.parse(new Date(e.data[s+1].startTime.replace(/-/g,"/")))/1e3&&a.push({startTime:n,endTime:Date.parse(new Date(e.data[s+1].startTime.replace(/-/g,"/")))/1e3,sleepState:"人员离开"})})),t.sleepValue=a}}))},getBedroomData:function(e){var t=this,a={};this.$api.get("/bms/screen/devSta/getHeartBreathList",{params:{devId:e}}).then((function(e){if("00000"===e.status&&e.data){var s=[],i=[];(e.data||[]).map((function(e){a[e.createTime]||(s.push([e.createTime.substr(11),e.heartRate]),i.push([e.createTime.substr(11),e.breathRate]),a[e.createTime]=!0)})),t.bedroomLineDatas=s,t.bedroomLineDatas2=i}})),this.$api.get("/bms/screen/devSta/getBedroomPersonRealtimeStatusAndPersonInbedRealtimeStatusList",{params:{devId:e}}).then((function(e){if("00000"===e.status&&e.data){var a=[],s=e.data||[],i=[];s.map((function(e,t){if(t===s.length-1)a.push(e);else if(i.includes(JSON.stringify(e)));else{var n=s[t+1];if("监测到人"===e.status&&"监测到人"===n.status)a.push(e),e.endTime<n.startTime&&a.push({startTime:e.endTime,endTime:n.startTime,status:"人员离开"});else if("人员在床"===e.status&&"监测到人"===n.status)a.push(e),e.endTime<n.startTime?a.push({startTime:e.endTime,endTime:n.startTime,status:"人员离床"}):e.endTime>n.startTime&&i.push(JSON.stringify(n));else if("监测到人"===e.status&&"人员在床"===n.status)e.endTime>=n.startTime||e.endTime<n.startTime?(e.endTime=n.startTime,a.push(e),a.push(n)):(a.push(e),e.endTime<n.startTime&&a.push({startTime:e.endTime,endTime:n.startTime,status:"人员离床"}));else if("人员在床"===e.status&&"人员在床"===n.status){var o=a[a.length-1];o&&"人员在床"!==o.status&&a.push(e),e.endTime<n.startTime&&a.push({startTime:e.endTime,endTime:n.startTime,status:"人员离床"})}}}));var n=Array.from(new Set(a)),o=[];n.map((function(e,t){if(t===n.length-1)o.push(e);else{var a=n[t+1];"人员在床"===e.status&&"监测到人"===a.status||"人员在床"===e.status&&"人员在床"===a.status?(o.push(e),e.endTime<a.startTime&&o.push({startTime:e.endTime,endTime:a.startTime,status:"人员离床"})):o.push(e)}}));var r=Array.from(new Set(o));r.map((function(e,a){e.startTime=10!=="".concat(e.startTime).length?Date.parse(new Date(e.startTime.replace(/-/g,"/")))/1e3:e.startTime,e.endTime=10!=="".concat(e.endTime).length?Date.parse(new Date(e.endTime.replace(/-/g,"/")))/1e3:e.endTime,e.s=t.formatDate("hh:MM:ss",new Date(1e3*parseInt(e.startTime))),e.e=t.formatDate("hh:MM:ss",new Date(1e3*parseInt(e.endTime))),e.sleepState=e.status})),t.sleepValue=r}}))}}},be=ge,ye=(a("f336"),Object(p["a"])(be,s,i,!1,null,"390dd642",null));t["default"]=ye.exports},"5c37":function(e,t,a){"use strict";a("771d")},6062:function(e,t,a){"use strict";var s=a("6d61"),i=a("6566");e.exports=s("Set",(function(e){return function(){return e(this,arguments.length?arguments[0]:void 0)}}),i)},"60c5":function(e,t,a){"use strict";a("60e8")},"60e8":function(e,t,a){},6566:function(e,t,a){"use strict";var s=a("9bf2").f,i=a("7c73"),n=a("e2cc"),o=a("0366"),r=a("19aa"),c=a("2266"),l=a("7dd0"),u=a("2626"),d=a("83ab"),h=a("f183").fastKey,f=a("69f3"),p=f.set,v=f.getterFor;e.exports={getConstructor:function(e,t,a,l){var u=e((function(e,s){r(e,u,t),p(e,{type:t,index:i(null),first:void 0,last:void 0,size:0}),d||(e.size=0),void 0!=s&&c(s,e[l],{that:e,AS_ENTRIES:a})})),f=v(t),m=function(e,t,a){var s,i,n=f(e),o=g(e,t);return o?o.value=a:(n.last=o={index:i=h(t,!0),key:t,value:a,previous:s=n.last,next:void 0,removed:!1},n.first||(n.first=o),s&&(s.next=o),d?n.size++:e.size++,"F"!==i&&(n.index[i]=o)),e},g=function(e,t){var a,s=f(e),i=h(t);if("F"!==i)return s.index[i];for(a=s.first;a;a=a.next)if(a.key==t)return a};return n(u.prototype,{clear:function(){var e=this,t=f(e),a=t.index,s=t.first;while(s)s.removed=!0,s.previous&&(s.previous=s.previous.next=void 0),delete a[s.index],s=s.next;t.first=t.last=void 0,d?t.size=0:e.size=0},delete:function(e){var t=this,a=f(t),s=g(t,e);if(s){var i=s.next,n=s.previous;delete a.index[s.index],s.removed=!0,n&&(n.next=i),i&&(i.previous=n),a.first==s&&(a.first=i),a.last==s&&(a.last=n),d?a.size--:t.size--}return!!s},forEach:function(e){var t,a=f(this),s=o(e,arguments.length>1?arguments[1]:void 0,3);while(t=t?t.next:a.first){s(t.value,t.key,this);while(t&&t.removed)t=t.previous}},has:function(e){return!!g(this,e)}}),n(u.prototype,a?{get:function(e){var t=g(this,e);return t&&t.value},set:function(e,t){return m(this,0===e?0:e,t)}}:{add:function(e){return m(this,e=0===e?0:e,e)}}),d&&s(u.prototype,"size",{get:function(){return f(this).size}}),u},setStrong:function(e,t,a){var s=t+" Iterator",i=v(t),n=v(s);l(e,t,(function(e,t){p(this,{type:s,target:e,state:i(e),kind:t,last:void 0})}),(function(){var e=n(this),t=e.kind,a=e.last;while(a&&a.removed)a=a.previous;return e.target&&(e.last=a=a?a.next:e.state.first)?"keys"==t?{value:a.key,done:!1}:"values"==t?{value:a.value,done:!1}:{value:[a.key,a.value],done:!1}:(e.target=void 0,{value:void 0,done:!0})}),a?"entries":"values",!a,!0),u(t)}}},"6b69":function(e,t,a){"use strict";a("01b9")},"6d61":function(e,t,a){"use strict";var s=a("23e7"),i=a("da84"),n=a("94ca"),o=a("6eeb"),r=a("f183"),c=a("2266"),l=a("19aa"),u=a("861d"),d=a("d039"),h=a("1c7e"),f=a("d44e"),p=a("7156");e.exports=function(e,t,a){var v=-1!==e.indexOf("Map"),m=-1!==e.indexOf("Weak"),g=v?"set":"add",b=i[e],y=b&&b.prototype,w=b,S={},x=function(e){var t=y[e];o(y,e,"add"==e?function(e){return t.call(this,0===e?0:e),this}:"delete"==e?function(e){return!(m&&!u(e))&&t.call(this,0===e?0:e)}:"get"==e?function(e){return m&&!u(e)?void 0:t.call(this,0===e?0:e)}:"has"==e?function(e){return!(m&&!u(e))&&t.call(this,0===e?0:e)}:function(e,a){return t.call(this,0===e?0:e,a),this})},C=n(e,"function"!=typeof b||!(m||y.forEach&&!d((function(){(new b).entries().next()}))));if(C)w=a.getConstructor(t,e,v,g),r.REQUIRED=!0;else if(n(e,!0)){var T=new w,D=T[g](m?{}:-0,1)!=T,_=d((function(){T.has(1)})),k=h((function(e){new b(e)})),I=!m&&d((function(){var e=new b,t=5;while(t--)e[g](t,t);return!e.has(-0)}));k||(w=t((function(t,a){l(t,w,e);var s=p(new b,t,w);return void 0!=a&&c(a,s[g],{that:s,AS_ENTRIES:v}),s})),w.prototype=y,y.constructor=w),(_||I)&&(x("delete"),x("has"),v&&x("get")),(I||D)&&x(g),m&&y.clear&&delete y.clear}return S[e]=w,s({global:!0,forced:w!=b},S),f(w,e),m||a.setStrong(w,e,v),w}},"771d":function(e,t,a){},"806e":function(e,t,a){},"819f":function(e,t,a){"use strict";a("1d2e")},8354:function(e,t,a){},"8b44":function(e,t,a){},b502:function(e,t,a){"use strict";a("806e")},b5c5:function(e,t,a){"use strict";a("2d2c")},c9b7:function(e,t,a){"use strict";function s(e,t){var a,s=t||200;return function(){var t=this,i=arguments;a&&clearTimeout(a),a=setTimeout((function(){a=null,e.apply(t,i)}),s)}}a.d(t,"a",(function(){return s}))},cc62:function(e,t,a){},d7db:function(e,t,a){"use strict";a("cc62")},ddaa:function(e,t,a){},e215:function(e,t,a){"use strict";a("4190")},f336:function(e,t,a){"use strict";a("ddaa")}}]);