(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c033668"],{"206c":function(e,t,a){"use strict";a("fc4a")},4620:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"未处理事件"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("div",{staticClass:"unprocessed-tip"},[e._v(" 未处理: "),a("span",{staticClass:"num"},[e._v(e._s(e.table.pageInfo.total||0))]),e._v("条 "),a("el-button",{staticStyle:{position:"relative",top:"-3px","margin-left":"20px"},attrs:{disabled:!e.handleSelectionList||!e.handleSelectionList.length,type:"primary",size:"mini"},on:{click:e.handleRecordFiled}},[e._v("事件归档")])],1),a("div",{staticClass:"right-btns"},[a("div",{staticClass:"refresh-btn",on:{click:e.fetchDatas}},[a("svg-icon",{attrs:{name:"icon-refresh"}}),a("span",[e._v("刷新")])],1)])]),a("el-table",{ref:"elTable",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center",selectable:e.checkItemFn}}),a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"title",label:"事件标题",minWidth:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"orderTypeName",label:"告警事件",width:"130",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"red-dot"}),e._v(" "+e._s(t.row.orderTypeName)+" "),t.row.tag?a("div",[a("el-tag",{attrs:{size:"mini",type:"danger"}},[e._v(e._s(t.row.tag))])],1):e._e()]}}])}),a("el-table-column",{attrs:{prop:"devCode",label:"关联设备编码",width:"130",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",width:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",width:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"seatName",label:"事件处理人",width:"130",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"事件发生时间",width:"170",align:"center"}}),a("el-table-column",{attrs:{prop:"modifyTime",label:"事件更新时间",width:"170",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",fixed:"right",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(a){return e.handleReceiveOrder(t.row.id)}}},[e._v("立即处理")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,size:e.table.pageInfo.pageSize,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},s=[],i=(a("d81d"),a("d3b7"),{data:function(){return{table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}},setInterval:void 0,handleSelectionList:[]}},beforeDestroy:function(){this.setInterval&&clearInterval(this.setInterval),this.$eventBus.$off("ServicePlatformUnProcessed-Refresh")},mounted:function(){var e=this;this.fetchDatas(),this.$eventBus.$on("ServicePlatformUnProcessed-Refresh",(function(){e.fetchDatas()}))},methods:{handleSelectionChange:function(e){this.handleSelectionList=this.$refs.elTable.selection||[]},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handleReceiveOrder:function(e){var t=this,a=this.$loading({lock:!0,text:"正在处理中, 请稍等...",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"});this.$api.post("/bms/event/receive/".concat(e),{}).then((function(n){"00000"===n.status?(t.fetchUnNum(),a&&a.close(),t.$router.push({name:"servicePlatformEventDetail",params:{id:e}})):(a&&a.close(),t.$message({type:"error",message:n.message||"操作失败!"}))}))},fetchDatas:function(){var e=this;this.handleSelectionList=[],this.$api.get("/bms/event/not-handle/list",{params:{current:this.table.pageInfo.current,pageSize:this.table.pageInfo.pageSize}}).then((function(t){"00000"===t.status&&t.data&&(e.table.datas=t.data,e.table.pageInfo.total=t.page.total),e.fetchUnNum()}))},handleRecordFiled:function(){var e=this;if(this.handleSelectionList&&this.handleSelectionList.length){var t=this.handleSelectionList.map((function(e){return e.id}));this.$confirm("请确认是否要归档当前选中数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(a,n,s){"confirm"===a?(n.confirmButtonLoading=!0,e.$api.post("/bms/event/completeByIds",t).then((function(t){"00000"===t.status?(e.$message({type:"success",message:t.message||"归档成功!"}),e.fetchDatas(),s(!0)):e.$message({type:"success",message:t.message||"操作失败!"})})).catch((function(){s(!1)})).finally((function(){n.confirmButtonLoading=!1}))):s()}}).catch((function(){}))}else this.$message({type:"error",message:"请选择数据!"})},fetchUnNum:function(){var e=this;this.$api.get("/bms/event/not-handle/getCount",{}).then((function(t){"00000"===t.status&&e.$store.commit("menuBadge/setUnprocessedNumber",t.data||0)}))},checkItemFn:function(e){return"0"===e.status||"1"===e.status}}}),l=i,r=(a("206c"),a("2877")),o=Object(r["a"])(l,n,s,!1,null,"969a40cc",null);t["default"]=o.exports},fc4a:function(e,t,a){}}]);