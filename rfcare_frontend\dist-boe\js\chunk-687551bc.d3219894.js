(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-687551bc"],{"03f2":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&s(e,t)}function s(e,t){return s=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},s(e,t)}function i(e){var t=a();return function(){var n,r=c(e);if(t){var l=c(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return o(this,n)}}function o(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?u(e):t}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function c(e){return c=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},c(e)}function h(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=f(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,l=function(){};return{s:l,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,i=!0,o=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return i=e.done,e},e:function(e){o=!0,s=e},f:function(){try{i||null==n["return"]||n["return"]()}finally{if(o)throw s}}}}function f(e,t){if(e){if("string"===typeof e)return d(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?d(e,t):void 0}}function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function m(e,t,n){return t&&p(e.prototype,t),n&&p(e,n),e}var v=n("93be"),g=n("1e8c"),y=n("0790"),T=n("4a05"),S=n("1c8d"),C=n("34eb")("JsSIP:SIPMessage"),E=function(){function e(t,n,r,l,s,i){if(_(this,e),!t||!n||!r)return null;l=l||{},this.ua=r,this.headers={},this.method=t,this.ruri=n,this.body=i,this.extraHeaders=y.cloneArray(s),l.route_set?this.setHeader("route",l.route_set):r.configuration.use_preloaded_route&&this.setHeader("route","<".concat(r.transport.sip_uri,";lr>")),this.setHeader("via",""),this.setHeader("max-forwards",g.MAX_FORWARDS);var o=l.to_uri||n,u=l.to_tag?{tag:l.to_tag}:null,a="undefined"!==typeof l.to_display_name?l.to_display_name:null;this.to=new T(o,a,u),this.setHeader("to",this.to.toString());var c,h=l.from_uri||r.configuration.uri,f={tag:l.from_tag||y.newTag()};c="undefined"!==typeof l.from_display_name?l.from_display_name:r.configuration.display_name?r.configuration.display_name:null,this.from=new T(h,c,f),this.setHeader("from",this.from.toString());var d=l.call_id||r.configuration.jssip_id+y.createRandomToken(15);this.call_id=d,this.setHeader("call-id",d);var p=l.cseq||Math.floor(1e4*Math.random());this.cseq=p,this.setHeader("cseq","".concat(p," ").concat(t))}return m(e,[{key:"setHeader",value:function(e,t){for(var n=new RegExp("^\\s*".concat(e,"\\s*:"),"i"),r=0;r<this.extraHeaders.length;r++)n.test(this.extraHeaders[r])&&this.extraHeaders.splice(r,1);this.headers[y.headerize(e)]=Array.isArray(t)?t:[t]}},{key:"getHeader",value:function(e){var t=this.headers[y.headerize(e)];if(t){if(t[0])return t[0]}else{var n,r=new RegExp("^\\s*".concat(e,"\\s*:"),"i"),l=h(this.extraHeaders);try{for(l.s();!(n=l.n()).done;){var s=n.value;if(r.test(s))return s.substring(s.indexOf(":")+1).trim()}}catch(i){l.e(i)}finally{l.f()}}}},{key:"getHeaders",value:function(e){var t=this.headers[y.headerize(e)],n=[];if(t){var r,l=h(t);try{for(l.s();!(r=l.n()).done;){var s=r.value;n.push(s)}}catch(c){l.e(c)}finally{l.f()}return n}var i,o=new RegExp("^\\s*".concat(e,"\\s*:"),"i"),u=h(this.extraHeaders);try{for(u.s();!(i=u.n()).done;){var a=i.value;o.test(a)&&n.push(a.substring(a.indexOf(":")+1).trim())}}catch(c){u.e(c)}finally{u.f()}return n}},{key:"hasHeader",value:function(e){if(this.headers[y.headerize(e)])return!0;var t,n=new RegExp("^\\s*".concat(e,"\\s*:"),"i"),r=h(this.extraHeaders);try{for(r.s();!(t=r.n()).done;){var l=t.value;if(n.test(l))return!0}}catch(s){r.e(s)}finally{r.f()}return!1}},{key:"parseSDP",value:function(e){return!e&&this.sdp||(this.sdp=v.parse(this.body||"")),this.sdp}},{key:"toString",value:function(){var e="".concat(this.method," ").concat(this.ruri," SIP/2.0\r\n");for(var t in this.headers)if(Object.prototype.hasOwnProperty.call(this.headers,t)){var n,r=h(this.headers[t]);try{for(r.s();!(n=r.n()).done;){var l=n.value;e+="".concat(t,": ").concat(l,"\r\n")}}catch(f){r.e(f)}finally{r.f()}}var s,i=h(this.extraHeaders);try{for(i.s();!(s=i.n()).done;){var o=s.value;e+="".concat(o.trim(),"\r\n")}}catch(f){i.e(f)}finally{i.f()}var u=[];switch(this.method){case g.REGISTER:u.push("path","gruu");break;case g.INVITE:this.ua.configuration.session_timers&&u.push("timer"),(this.ua.contact.pub_gruu||this.ua.contact.temp_gruu)&&u.push("gruu"),u.push("ice","replaces");break;case g.UPDATE:this.ua.configuration.session_timers&&u.push("timer"),u.push("ice");break}u.push("outbound");var a=this.ua.configuration.user_agent||g.USER_AGENT;if(e+="Allow: ".concat(g.ALLOWED_METHODS,"\r\n"),e+="Supported: ".concat(u,"\r\n"),e+="User-Agent: ".concat(a,"\r\n"),this.body){var c=y.str_utf8_length(this.body);e+="Content-Length: ".concat(c,"\r\n\r\n"),e+=this.body}else e+="Content-Length: 0\r\n\r\n";return e}},{key:"clone",value:function(){var t=new e(this.method,this.ruri,this.ua);return Object.keys(this.headers).forEach((function(e){t.headers[e]=this.headers[e].slice()}),this),t.body=this.body,t.extraHeaders=y.cloneArray(this.extraHeaders),t.to=this.to,t.from=this.from,t.call_id=this.call_id,t.cseq=this.cseq,t}}]),e}(),b=function(e){l(n,e);var t=i(n);function n(e,r,l,s,i){var o;return _(this,n),o=t.call(this,g.INVITE,e,r,l,s,i),o.transaction=null,o}return m(n,[{key:"cancel",value:function(e){this.transaction.cancel(e)}},{key:"clone",value:function(){var e=new n(this.ruri,this.ua);return Object.keys(this.headers).forEach((function(t){e.headers[t]=this.headers[t].slice()}),this),e.body=this.body,e.extraHeaders=y.cloneArray(this.extraHeaders),e.to=this.to,e.from=this.from,e.call_id=this.call_id,e.cseq=this.cseq,e.transaction=this.transaction,e}}]),n}(E),A=function(){function e(){_(this,e),this.data=null,this.headers=null,this.method=null,this.via=null,this.via_branch=null,this.call_id=null,this.cseq=null,this.from=null,this.from_tag=null,this.to=null,this.to_tag=null,this.body=null,this.sdp=null}return m(e,[{key:"addHeader",value:function(e,t){var n={raw:t};e=y.headerize(e),this.headers[e]?this.headers[e].push(n):this.headers[e]=[n]}},{key:"getHeader",value:function(e){var t=this.headers[y.headerize(e)];if(t)return t[0]?t[0].raw:void 0}},{key:"getHeaders",value:function(e){var t=this.headers[y.headerize(e)],n=[];if(!t)return[];var r,l=h(t);try{for(l.s();!(r=l.n()).done;){var s=r.value;n.push(s.raw)}}catch(i){l.e(i)}finally{l.f()}return n}},{key:"hasHeader",value:function(e){return!!this.headers[y.headerize(e)]}},{key:"parseHeader",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;if(e=y.headerize(e),this.headers[e]){if(!(t>=this.headers[e].length)){var n=this.headers[e][t],r=n.raw;if(n.parsed)return n.parsed;var l=S.parse(r,e.replace(/-/g,"_"));return-1===l?(this.headers[e].splice(t,1),void C('error parsing "'.concat(e,'" header field with value "').concat(r,'"'))):(n.parsed=l,l)}C('not so many "'.concat(e,'" headers present'))}else C('header "'.concat(e,'" not present'))}},{key:"s",value:function(e,t){return this.parseHeader(e,t)}},{key:"setHeader",value:function(e,t){var n={raw:t};this.headers[y.headerize(e)]=[n]}},{key:"parseSDP",value:function(e){return!e&&this.sdp||(this.sdp=v.parse(this.body||"")),this.sdp}},{key:"toString",value:function(){return this.data}}]),e}(),R=function(e){l(n,e);var t=i(n);function n(e){var r;return _(this,n),r=t.call(this),r.ua=e,r.headers={},r.ruri=null,r.transport=null,r.server_transaction=null,r}return m(n,[{key:"reply",value:function(e,t,n,r,l,s){var i=[],o=this.getHeader("To");if(e=e||null,t=t||null,!e||e<100||e>699)throw new TypeError("Invalid status_code: ".concat(e));if(t&&"string"!==typeof t&&!(t instanceof String))throw new TypeError("Invalid reason_phrase: ".concat(t));t=t||g.REASON_PHRASE[e]||"",n=y.cloneArray(n);var u="SIP/2.0 ".concat(e," ").concat(t,"\r\n");if(this.method===g.INVITE&&e>100&&e<=200){var a,c=this.getHeaders("record-route"),f=h(c);try{for(f.s();!(a=f.n()).done;){var d=a.value;u+="Record-Route: ".concat(d,"\r\n")}}catch(b){f.e(b)}finally{f.f()}}var _,p=this.getHeaders("via"),m=h(p);try{for(m.s();!(_=m.n()).done;){var v=_.value;u+="Via: ".concat(v,"\r\n")}}catch(b){m.e(b)}finally{m.f()}!this.to_tag&&e>100?o+=";tag=".concat(y.newTag()):this.to_tag&&!this.s("to").hasParam("tag")&&(o+=";tag=".concat(this.to_tag)),u+="To: ".concat(o,"\r\n"),u+="From: ".concat(this.getHeader("From"),"\r\n"),u+="Call-ID: ".concat(this.call_id,"\r\n"),u+="CSeq: ".concat(this.cseq," ").concat(this.method,"\r\n");var T,S=h(n);try{for(S.s();!(T=S.n()).done;){var C=T.value;u+="".concat(C.trim(),"\r\n")}}catch(b){S.e(b)}finally{S.f()}switch(this.method){case g.INVITE:this.ua.configuration.session_timers&&i.push("timer"),(this.ua.contact.pub_gruu||this.ua.contact.temp_gruu)&&i.push("gruu"),i.push("ice","replaces");break;case g.UPDATE:this.ua.configuration.session_timers&&i.push("timer"),r&&i.push("ice"),i.push("replaces")}if(i.push("outbound"),this.method===g.OPTIONS?(u+="Allow: ".concat(g.ALLOWED_METHODS,"\r\n"),u+="Accept: ".concat(g.ACCEPTED_BODY_TYPES,"\r\n")):405===e?u+="Allow: ".concat(g.ALLOWED_METHODS,"\r\n"):415===e&&(u+="Accept: ".concat(g.ACCEPTED_BODY_TYPES,"\r\n")),u+="Supported: ".concat(i,"\r\n"),r){var E=y.str_utf8_length(r);u+="Content-Type: application/sdp\r\n",u+="Content-Length: ".concat(E,"\r\n\r\n"),u+=r}else u+="Content-Length: ".concat(0,"\r\n\r\n");this.server_transaction.receiveResponse(e,u,l,s)}},{key:"reply_sl",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=this.getHeaders("via");if(!e||e<100||e>699)throw new TypeError("Invalid status_code: ".concat(e));if(t&&"string"!==typeof t&&!(t instanceof String))throw new TypeError("Invalid reason_phrase: ".concat(t));t=t||g.REASON_PHRASE[e]||"";var r,l="SIP/2.0 ".concat(e," ").concat(t,"\r\n"),s=h(n);try{for(s.s();!(r=s.n()).done;){var i=r.value;l+="Via: ".concat(i,"\r\n")}}catch(u){s.e(u)}finally{s.f()}var o=this.getHeader("To");!this.to_tag&&e>100?o+=";tag=".concat(y.newTag()):this.to_tag&&!this.s("to").hasParam("tag")&&(o+=";tag=".concat(this.to_tag)),l+="To: ".concat(o,"\r\n"),l+="From: ".concat(this.getHeader("From"),"\r\n"),l+="Call-ID: ".concat(this.call_id,"\r\n"),l+="CSeq: ".concat(this.cseq," ").concat(this.method,"\r\n"),l+="Content-Length: ".concat(0,"\r\n\r\n"),this.transport.send(l)}}]),n}(A),w=function(e){l(n,e);var t=i(n);function n(){var e;return _(this,n),e=t.call(this),e.headers={},e.status_code=null,e.reason_phrase=null,e}return n}(A);e.exports={OutgoingRequest:E,InitialOutgoingInviteRequest:b,IncomingRequest:R,IncomingResponse:w}},"05fe":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("7123"),o=n("34eb")("JsSIP:Transport"),u=n("34eb")("JsSIP:ERROR:Transport"),a=n("1e8c");u.log=console.warn.bind(console);var c={STATUS_CONNECTED:0,STATUS_CONNECTING:1,STATUS_DISCONNECTED:2,SOCKET_STATUS_READY:0,SOCKET_STATUS_ERROR:1,recovery_options:{min_interval:a.CONNECTION_RECOVERY_MIN_INTERVAL,max_interval:a.CONNECTION_RECOVERY_MAX_INTERVAL}};e.exports=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:c.recovery_options;if(r(this,e),o("new()"),this.status=c.STATUS_DISCONNECTED,this.socket=null,this.sockets=[],this.recovery_options=n,this.recover_attempts=0,this.recovery_timer=null,this.close_requested=!1,"undefined"===typeof t)throw new TypeError("Invalid argument. undefined 'sockets' argument");t instanceof Array||(t=[t]),t.forEach((function(e){if(!i.isSocket(e.socket))throw new TypeError("Invalid argument. invalid 'JsSIP.Socket' instance");if(e.weight&&!Number(e.weight))throw new TypeError("Invalid argument. 'weight' attribute is not a number");this.sockets.push({socket:e.socket,weight:e.weight||0,status:c.SOCKET_STATUS_READY})}),this),this._getSocket()}return s(e,[{key:"connect",value:function(){o("connect()"),this.isConnected()?o("Transport is already connected"):this.isConnecting()?o("Transport is connecting"):(this.close_requested=!1,this.status=c.STATUS_CONNECTING,this.onconnecting({socket:this.socket,attempts:this.recover_attempts}),this.close_requested||(this.socket.onconnect=this._onConnect.bind(this),this.socket.ondisconnect=this._onDisconnect.bind(this),this.socket.ondata=this._onData.bind(this),this.socket.connect()))}},{key:"disconnect",value:function(){o("close()"),this.close_requested=!0,this.recover_attempts=0,this.status=c.STATUS_DISCONNECTED,null!==this.recovery_timer&&(clearTimeout(this.recovery_timer),this.recovery_timer=null),this.socket.onconnect=function(){},this.socket.ondisconnect=function(){},this.socket.ondata=function(){},this.socket.disconnect(),this.ondisconnect({socket:this.socket,error:!1})}},{key:"send",value:function(e){if(o("send()"),!this.isConnected())return u("unable to send message, transport is not connected"),!1;var t=e.toString();return o("sending message:\n\n".concat(t,"\n")),this.socket.send(t)}},{key:"isConnected",value:function(){return this.status===c.STATUS_CONNECTED}},{key:"isConnecting",value:function(){return this.status===c.STATUS_CONNECTING}},{key:"_reconnect",value:function(){var e=this;this.recover_attempts+=1;var t=Math.floor(Math.random()*Math.pow(2,this.recover_attempts)+1);t<this.recovery_options.min_interval?t=this.recovery_options.min_interval:t>this.recovery_options.max_interval&&(t=this.recovery_options.max_interval),o("reconnection attempt: ".concat(this.recover_attempts,". next connection attempt in ").concat(t," seconds")),this.recovery_timer=setTimeout((function(){e.close_requested||e.isConnected()||e.isConnecting()||(e._getSocket(),e.connect())}),1e3*t)}},{key:"_getSocket",value:function(){var e=[];if(this.sockets.forEach((function(t){t.status!==c.SOCKET_STATUS_ERROR&&(0===e.length?e.push(t):t.weight>e[0].weight?e=[t]:t.weight===e[0].weight&&e.push(t))})),0===e.length)return this.sockets.forEach((function(e){e.status=c.SOCKET_STATUS_READY})),void this._getSocket();var t=Math.floor(Math.random()*e.length);this.socket=e[t].socket}},{key:"_onConnect",value:function(){this.recover_attempts=0,this.status=c.STATUS_CONNECTED,null!==this.recovery_timer&&(clearTimeout(this.recovery_timer),this.recovery_timer=null),this.onconnect({socket:this})}},{key:"_onDisconnect",value:function(e,t,n){this.status=c.STATUS_DISCONNECTED,this.ondisconnect({socket:this.socket,error:e,code:t,reason:n}),this.close_requested||(this.sockets.forEach((function(e){this.socket===e.socket&&(e.status=c.SOCKET_STATUS_ERROR)}),this),this._reconnect(e))}},{key:"_onData",value:function(e){if("\r\n"!==e){if("string"!==typeof e){try{e=String.fromCharCode.apply(null,new Uint8Array(e))}catch(t){return void o("received binary message failed to be converted into string, message discarded")}o("received binary message:\n\n".concat(e,"\n"))}else o("received text message:\n\n".concat(e,"\n"));this.ondata({transport:this,message:e})}else o("received message with CRLF Keep Alive response")}},{key:"via_transport",get:function(){return this.socket.via_transport}},{key:"url",get:function(){return this.socket.url}},{key:"sip_uri",get:function(){return this.socket.sip_uri}}]),e}()},"0790":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=s(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,l=function(){};return{s:l,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(u)throw i}}}}function s(e,t){if(e){if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var o=n("1e8c"),u=n("9cf5"),a=n("1c8d");t.str_utf8_length=function(e){return unescape(encodeURIComponent(e)).length};var c=t.isFunction=function(e){return void 0!==e&&"[object Function]"===Object.prototype.toString.call(e)};t.isString=function(e){return void 0!==e&&"[object String]"===Object.prototype.toString.call(e)},t.isDecimal=function(e){return!isNaN(e)&&parseFloat(e)===parseInt(e,10)},t.isEmpty=function(e){return null===e||""===e||void 0===e||Array.isArray(e)&&0===e.length||"number"===typeof e&&isNaN(e)},t.hasMethods=function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(var l=0,s=n;l<s.length;l++){var i=s[l];if(c(e[i]))return!1}return!0};var h=t.createRandomToken=function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:32,l="";for(t=0;t<e;t++)n=Math.random()*r|0,l+=n.toString(r);return l};t.newTag=function(){return h(10)},t.newUUID=function(){var e="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){var t=16*Math.random()|0,n="x"===e?t:3&t|8;return n.toString(16)}));return e},t.hostType=function(e){if(e)return e=a.parse(e,"host"),-1!==e?e.host_type:void 0};var f=t.escapeUser=function(e){return encodeURIComponent(decodeURIComponent(e)).replace(/%3A/gi,":").replace(/%2B/gi,"+").replace(/%3F/gi,"?").replace(/%2F/gi,"/")};t.normalizeTarget=function(e,t){if(e){if(e instanceof u)return e;if("string"===typeof e){var n,r,l,s=e.split("@");switch(s.length){case 1:if(!t)return;n=e,r=t;break;case 2:n=s[0],r=s[1];break;default:n=s.slice(0,s.length-1).join("@"),r=s[s.length-1]}return n=n.replace(/^(sips?|tel):/i,""),/^[-.()]*\+?[0-9\-.()]+$/.test(n)&&(n=n.replace(/[-.()]/g,"")),e="".concat(o.SIP,":").concat(f(n),"@").concat(r),(l=u.parse(e))?l:void 0}}else;},t.headerize=function(e){var t,n={"Call-Id":"Call-ID",Cseq:"CSeq","Www-Authenticate":"WWW-Authenticate"},r=e.toLowerCase().replace(/_/g,"-").split("-"),l="",s=r.length;for(t=0;t<s;t++)0!==t&&(l+="-"),l+=r[t].charAt(0).toUpperCase()+r[t].substring(1);return n[l]&&(l=n[l]),l},t.sipErrorCause=function(e){for(var t in o.SIP_ERROR_CAUSES)if(-1!==o.SIP_ERROR_CAUSES[t].indexOf(e))return o.causes[t];return o.causes.SIP_FAILURE_CODE},t.getRandomTestNetIP=function(){function e(e,t){return Math.floor(Math.random()*(t-e+1)+e)}return"192.0.2.".concat(e(1,254))},t.calculateMD5=function(e){function t(e,t){return e<<t|e>>>32-t}function n(e,t){var n=2147483648&e,r=2147483648&t,l=1073741824&e,s=1073741824&t,i=(1073741823&e)+(1073741823&t);return l&s?2147483648^i^n^r:l|s?1073741824&i?3221225472^i^n^r:1073741824^i^n^r:i^n^r}function r(e,t,n){return e&t|~e&n}function l(e,t,n){return e&n|t&~n}function s(e,t,n){return e^t^n}function i(e,t,n){return t^(e|~n)}function o(e,l,s,i,o,u,a){return e=n(e,n(n(r(l,s,i),o),a)),n(t(e,u),l)}function u(e,r,s,i,o,u,a){return e=n(e,n(n(l(r,s,i),o),a)),n(t(e,u),r)}function a(e,r,l,i,o,u,a){return e=n(e,n(n(s(r,l,i),o),a)),n(t(e,u),r)}function c(e,r,l,s,o,u,a){return e=n(e,n(n(i(r,l,s),o),a)),n(t(e,u),r)}function h(e){var t,n=e.length,r=n+8,l=(r-r%64)/64,s=16*(l+1),i=new Array(s-1),o=0,u=0;while(u<n)t=(u-u%4)/4,o=u%4*8,i[t]=i[t]|e.charCodeAt(u)<<o,u++;return t=(u-u%4)/4,o=u%4*8,i[t]=i[t]|128<<o,i[s-2]=n<<3,i[s-1]=n>>>29,i}function f(e){var t,n,r="",l="";for(n=0;n<=3;n++)t=e>>>8*n&255,l="0".concat(t.toString(16)),r+=l.substr(l.length-2,2);return r}function d(e){e=e.replace(/\r\n/g,"\n");for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r>127&&r<2048?(t+=String.fromCharCode(r>>6|192),t+=String.fromCharCode(63&r|128)):(t+=String.fromCharCode(r>>12|224),t+=String.fromCharCode(r>>6&63|128),t+=String.fromCharCode(63&r|128))}return t}var _,p,m,v,g,y,T,S,C,E=[],b=7,A=12,R=17,w=22,I=5,O=9,k=14,N=20,P=4,D=11,x=16,U=23,M=6,L=10,q=15,H=21;for(e=d(e),E=h(e),y=1732584193,T=4023233417,S=2562383102,C=271733878,_=0;_<E.length;_+=16)p=y,m=T,v=S,g=C,y=o(y,T,S,C,E[_+0],b,3614090360),C=o(C,y,T,S,E[_+1],A,3905402710),S=o(S,C,y,T,E[_+2],R,606105819),T=o(T,S,C,y,E[_+3],w,3250441966),y=o(y,T,S,C,E[_+4],b,4118548399),C=o(C,y,T,S,E[_+5],A,1200080426),S=o(S,C,y,T,E[_+6],R,2821735955),T=o(T,S,C,y,E[_+7],w,4249261313),y=o(y,T,S,C,E[_+8],b,1770035416),C=o(C,y,T,S,E[_+9],A,2336552879),S=o(S,C,y,T,E[_+10],R,4294925233),T=o(T,S,C,y,E[_+11],w,2304563134),y=o(y,T,S,C,E[_+12],b,1804603682),C=o(C,y,T,S,E[_+13],A,4254626195),S=o(S,C,y,T,E[_+14],R,2792965006),T=o(T,S,C,y,E[_+15],w,1236535329),y=u(y,T,S,C,E[_+1],I,4129170786),C=u(C,y,T,S,E[_+6],O,3225465664),S=u(S,C,y,T,E[_+11],k,643717713),T=u(T,S,C,y,E[_+0],N,3921069994),y=u(y,T,S,C,E[_+5],I,3593408605),C=u(C,y,T,S,E[_+10],O,38016083),S=u(S,C,y,T,E[_+15],k,3634488961),T=u(T,S,C,y,E[_+4],N,3889429448),y=u(y,T,S,C,E[_+9],I,568446438),C=u(C,y,T,S,E[_+14],O,3275163606),S=u(S,C,y,T,E[_+3],k,4107603335),T=u(T,S,C,y,E[_+8],N,1163531501),y=u(y,T,S,C,E[_+13],I,2850285829),C=u(C,y,T,S,E[_+2],O,4243563512),S=u(S,C,y,T,E[_+7],k,1735328473),T=u(T,S,C,y,E[_+12],N,2368359562),y=a(y,T,S,C,E[_+5],P,4294588738),C=a(C,y,T,S,E[_+8],D,2272392833),S=a(S,C,y,T,E[_+11],x,1839030562),T=a(T,S,C,y,E[_+14],U,4259657740),y=a(y,T,S,C,E[_+1],P,2763975236),C=a(C,y,T,S,E[_+4],D,1272893353),S=a(S,C,y,T,E[_+7],x,4139469664),T=a(T,S,C,y,E[_+10],U,3200236656),y=a(y,T,S,C,E[_+13],P,681279174),C=a(C,y,T,S,E[_+0],D,3936430074),S=a(S,C,y,T,E[_+3],x,3572445317),T=a(T,S,C,y,E[_+6],U,76029189),y=a(y,T,S,C,E[_+9],P,3654602809),C=a(C,y,T,S,E[_+12],D,3873151461),S=a(S,C,y,T,E[_+15],x,530742520),T=a(T,S,C,y,E[_+2],U,3299628645),y=c(y,T,S,C,E[_+0],M,4096336452),C=c(C,y,T,S,E[_+7],L,1126891415),S=c(S,C,y,T,E[_+14],q,2878612391),T=c(T,S,C,y,E[_+5],H,4237533241),y=c(y,T,S,C,E[_+12],M,1700485571),C=c(C,y,T,S,E[_+3],L,2399980690),S=c(S,C,y,T,E[_+10],q,4293915773),T=c(T,S,C,y,E[_+1],H,2240044497),y=c(y,T,S,C,E[_+8],M,1873313359),C=c(C,y,T,S,E[_+15],L,4264355552),S=c(S,C,y,T,E[_+6],q,2734768916),T=c(T,S,C,y,E[_+13],H,1309151649),y=c(y,T,S,C,E[_+4],M,4149444226),C=c(C,y,T,S,E[_+11],L,3174756917),S=c(S,C,y,T,E[_+2],q,718787259),T=c(T,S,C,y,E[_+9],H,3951481745),y=n(y,p),T=n(T,m),S=n(S,v),C=n(C,g);var F=f(y)+f(T)+f(S)+f(C);return F.toLowerCase()},t.closeMediaStream=function(e){if(e)try{var t;if(e.getTracks){t=e.getTracks();var n,s=l(t);try{for(s.s();!(n=s.n()).done;){var i=n.value;i.stop()}}catch(d){s.e(d)}finally{s.f()}}else{t=e.getAudioTracks();var o,u=l(t);try{for(u.s();!(o=u.n()).done;){var a=o.value;a.stop()}}catch(d){u.e(d)}finally{u.f()}t=e.getVideoTracks();var c,h=l(t);try{for(h.s();!(c=h.n()).done;){var f=c.value;f.stop()}}catch(d){h.e(d)}finally{h.f()}}}catch(_){"function"!==typeof e.stop&&"object"!==r(e.stop)||e.stop()}},t.cloneArray=function(e){return e&&e.slice()||[]},t.cloneObject=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e&&Object.assign({},e)||t}},"0c32":function(e,t,n){"use strict";function r(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=l(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,s=function(){};return{s:s,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(u)throw i}}}}function l(e,t){if(e){if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=n("1c8d"),o=n("03f2"),u=n("34eb")("JsSIP:ERROR:Parser");function a(e,t){var n=t,r=0,l=0;if(e.substring(n,n+2).match(/(^\r\n)/))return-2;while(0===r){if(l=e.indexOf("\r\n",n),-1===l)return l;!e.substring(l+2,l+4).match(/(^\r\n)/)&&e.charAt(l+2).match(/(^\s+)/)?n=l+2:r=l}return r}function c(e,t,n,l){var s,u=t.indexOf(":",n),a=t.substring(n,u).trim(),c=t.substring(u+1,l).trim();switch(a.toLowerCase()){case"via":case"v":e.addHeader("via",c),1===e.getHeaders("via").length?(s=e.parseHeader("Via"),s&&(e.via=s,e.via_branch=s.branch)):s=0;break;case"from":case"f":e.setHeader("from",c),s=e.parseHeader("from"),s&&(e.from=s,e.from_tag=s.getParam("tag"));break;case"to":case"t":e.setHeader("to",c),s=e.parseHeader("to"),s&&(e.to=s,e.to_tag=s.getParam("tag"));break;case"record-route":if(s=i.parse(c,"Record_Route"),-1===s)s=void 0;else{var h,f=r(s);try{for(f.s();!(h=f.n()).done;){var d=h.value;e.addHeader("record-route",c.substring(d.possition,d.offset)),e.headers["Record-Route"][e.getHeaders("record-route").length-1].parsed=d.parsed}}catch(v){f.e(v)}finally{f.f()}}break;case"call-id":case"i":e.setHeader("call-id",c),s=e.parseHeader("call-id"),s&&(e.call_id=c);break;case"contact":case"m":if(s=i.parse(c,"Contact"),-1===s)s=void 0;else{var _,p=r(s);try{for(p.s();!(_=p.n()).done;){var m=_.value;e.addHeader("contact",c.substring(m.possition,m.offset)),e.headers.Contact[e.getHeaders("contact").length-1].parsed=m.parsed}}catch(v){p.e(v)}finally{p.f()}}break;case"content-length":case"l":e.setHeader("content-length",c),s=e.parseHeader("content-length");break;case"content-type":case"c":e.setHeader("content-type",c),s=e.parseHeader("content-type");break;case"cseq":e.setHeader("cseq",c),s=e.parseHeader("cseq"),s&&(e.cseq=s.value),e instanceof o.IncomingResponse&&(e.method=s.method);break;case"max-forwards":e.setHeader("max-forwards",c),s=e.parseHeader("max-forwards");break;case"www-authenticate":e.setHeader("www-authenticate",c),s=e.parseHeader("www-authenticate");break;case"proxy-authenticate":e.setHeader("proxy-authenticate",c),s=e.parseHeader("proxy-authenticate");break;case"session-expires":case"x":e.setHeader("session-expires",c),s=e.parseHeader("session-expires"),s&&(e.session_expires=s.expires,e.session_expires_refresher=s.refresher);break;case"refer-to":case"r":e.setHeader("refer-to",c),s=e.parseHeader("refer-to"),s&&(e.refer_to=s);break;case"replaces":e.setHeader("replaces",c),s=e.parseHeader("replaces"),s&&(e.replaces=s);break;case"event":case"o":e.setHeader("event",c),s=e.parseHeader("event"),s&&(e.event=s);break;default:e.addHeader(a,c),s=0}return void 0!==s||{error:'error parsing header "'.concat(a,'"')}}u.log=console.warn.bind(console),t.parseMessage=function(e,t){var n,r,l=e.indexOf("\r\n");if(-1!==l){var s=e.substring(0,l),h=i.parse(s,"Request_Response");if(-1!==h){h.status_code?(n=new o.IncomingResponse,n.status_code=h.status_code,n.reason_phrase=h.reason_phrase):(n=new o.IncomingRequest(t),n.method=h.method,n.ruri=h.uri),n.data=e;var f=l+2;while(1){if(l=a(e,f),-2===l){r=f+2;break}if(-1===l)return void u("parseMessage() | malformed message");if(h=c(n,e,f,l),!0!==h)return void u("parseMessage() |",h.error);f=l+2}if(n.hasHeader("content-length")){var d=n.getHeader("content-length");n.body=e.substr(r,d)}else n.body=e.substring(r);return n}u('parseMessage() | error parsing first line of SIP message: "'.concat(s,'"'))}else u("parseMessage() | no CRLF found, not a SIP message")}},1070:function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("0790"),o=n("34eb")("JsSIP:DigestAuthentication"),u=n("34eb")("JsSIP:ERROR:DigestAuthentication");u.log=console.warn.bind(console),e.exports=function(){function e(t){r(this,e),this._credentials=t,this._cnonce=null,this._nc=0,this._ncHex="00000000",this._algorithm=null,this._realm=null,this._nonce=null,this._opaque=null,this._stale=null,this._qop=null,this._method=null,this._uri=null,this._ha1=null,this._response=null}return s(e,[{key:"get",value:function(e){switch(e){case"realm":return this._realm;case"ha1":return this._ha1;default:return void u('get() | cannot get "%s" parameter',e)}}},{key:"authenticate",value:function(e,t){var n=e.method,r=e.ruri,l=e.body,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(this._algorithm=t.algorithm,this._realm=t.realm,this._nonce=t.nonce,this._opaque=t.opaque,this._stale=t.stale,this._algorithm){if("MD5"!==this._algorithm)return u('authenticate() | challenge with Digest algorithm different than "MD5", authentication aborted'),!1}else this._algorithm="MD5";if(!this._nonce)return u("authenticate() | challenge without Digest nonce, authentication aborted"),!1;if(!this._realm)return u("authenticate() | challenge without Digest realm, authentication aborted"),!1;if(!this._credentials.password){if(!this._credentials.ha1)return u("authenticate() | no plain SIP password nor ha1 provided, authentication aborted"),!1;if(this._credentials.realm!==this._realm)return u('authenticate() | no plain SIP password, and stored `realm` does not match the given `realm`, cannot authenticate [stored:"%s", given:"%s"]',this._credentials.realm,this._realm),!1}if(t.qop)if(t.qop.indexOf("auth-int")>-1)this._qop="auth-int";else{if(!(t.qop.indexOf("auth")>-1))return u('authenticate() | challenge without Digest qop different than "auth" or "auth-int", authentication aborted'),!1;this._qop="auth"}else this._qop=null;this._method=n,this._uri=r,this._cnonce=s||i.createRandomToken(12),this._nc+=1;var a,c,h=Number(this._nc).toString(16);return this._ncHex="00000000".substr(0,8-h.length)+h,4294967296===this._nc&&(this._nc=1,this._ncHex="00000001"),this._credentials.password?this._ha1=i.calculateMD5("".concat(this._credentials.username,":").concat(this._realm,":").concat(this._credentials.password)):this._ha1=this._credentials.ha1,"auth"===this._qop?(a="".concat(this._method,":").concat(this._uri),c=i.calculateMD5(a),o('authenticate() | using qop=auth [a2:"%s"]',a),this._response=i.calculateMD5("".concat(this._ha1,":").concat(this._nonce,":").concat(this._ncHex,":").concat(this._cnonce,":auth:").concat(c))):"auth-int"===this._qop?(a="".concat(this._method,":").concat(this._uri,":").concat(i.calculateMD5(l||"")),c=i.calculateMD5(a),o('authenticate() | using qop=auth-int [a2:"%s"]',a),this._response=i.calculateMD5("".concat(this._ha1,":").concat(this._nonce,":").concat(this._ncHex,":").concat(this._cnonce,":auth-int:").concat(c))):null===this._qop&&(a="".concat(this._method,":").concat(this._uri),c=i.calculateMD5(a),o('authenticate() | using qop=null [a2:"%s"]',a),this._response=i.calculateMD5("".concat(this._ha1,":").concat(this._nonce,":").concat(c))),o("authenticate() | response generated"),!0}},{key:"toString",value:function(){var e=[];if(!this._response)throw new Error("response field does not exist, cannot generate Authorization header");return e.push("algorithm=".concat(this._algorithm)),e.push('username="'.concat(this._credentials.username,'"')),e.push('realm="'.concat(this._realm,'"')),e.push('nonce="'.concat(this._nonce,'"')),e.push('uri="'.concat(this._uri,'"')),e.push('response="'.concat(this._response,'"')),this._opaque&&e.push('opaque="'.concat(this._opaque,'"')),this._qop&&(e.push("qop=".concat(this._qop)),e.push('cnonce="'.concat(this._cnonce,'"')),e.push("nc=".concat(this._ncHex))),"Digest ".concat(e.join(", "))}}]),e}()},"13c4":function(e,t,n){"use strict";function r(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=l(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,s=function(){};return{s:s,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(u)throw i}}}}function l(e,t){if(e){if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i=n("0790"),o=n("1e8c"),u=n("1c8d"),a=n("9cf5"),c=n("7123"),h=n("8c52");t.settings={authorization_user:null,password:null,realm:null,ha1:null,display_name:null,uri:null,contact_uri:null,instance_id:null,use_preloaded_route:!1,session_timers:!0,session_timers_refresh_method:o.UPDATE,session_timers_force_refresher:!1,no_answer_timeout:60,register:!0,register_expires:600,registrar_server:null,sockets:null,connection_recovery_max_interval:o.CONNECTION_RECOVERY_MAX_INTERVAL,connection_recovery_min_interval:o.CONNECTION_RECOVERY_MIN_INTERVAL,via_host:"".concat(i.createRandomToken(12),".invalid")};var f={mandatory:{sockets:function(e){var t=[];if(c.isSocket(e))t.push({socket:e});else{if(!Array.isArray(e)||!e.length)return;var n,l=r(e);try{for(l.s();!(n=l.n()).done;){var s=n.value;Object.prototype.hasOwnProperty.call(s,"socket")&&c.isSocket(s.socket)?t.push(s):c.isSocket(s)&&t.push({socket:s})}}catch(i){l.e(i)}finally{l.f()}}return t},uri:function(e){/^sip:/i.test(e)||(e="".concat(o.SIP,":").concat(e));var t=a.parse(e);return t&&t.user?t:void 0}},optional:{authorization_user:function(e){return-1===u.parse('"'.concat(e,'"'),"quoted_string")?void 0:e},authorization_jwt:function(e){if("string"===typeof e)return e},user_agent:function(e){if("string"===typeof e)return e},connection_recovery_max_interval:function(e){if(i.isDecimal(e)){var t=Number(e);if(t>0)return t}},connection_recovery_min_interval:function(e){if(i.isDecimal(e)){var t=Number(e);if(t>0)return t}},contact_uri:function(e){if("string"===typeof e){var t=u.parse(e,"SIP_URI");if(-1!==t)return t}},display_name:function(e){return e},instance_id:function(e){return/^uuid:/i.test(e)&&(e=e.substr(5)),-1===u.parse(e,"uuid")?void 0:e},no_answer_timeout:function(e){if(i.isDecimal(e)){var t=Number(e);if(t>0)return t}},session_timers:function(e){if("boolean"===typeof e)return e},session_timers_refresh_method:function(e){if("string"===typeof e&&(e=e.toUpperCase(),e===o.INVITE||e===o.UPDATE))return e},session_timers_force_refresher:function(e){if("boolean"===typeof e)return e},password:function(e){return String(e)},realm:function(e){return String(e)},ha1:function(e){return String(e)},register:function(e){if("boolean"===typeof e)return e},register_expires:function(e){if(i.isDecimal(e)){var t=Number(e);if(t>0)return t}},registrar_server:function(e){/^sip:/i.test(e)||(e="".concat(o.SIP,":").concat(e));var t=a.parse(e);return t?t.user?void 0:t:void 0},use_preloaded_route:function(e){if("boolean"===typeof e)return e}}};t.load=function(e,t){for(var n in f.mandatory){if(!t.hasOwnProperty(n))throw new h.ConfigurationError(n);var r=t[n],l=f.mandatory[n](r);if(void 0===l)throw new h.ConfigurationError(n,r);e[n]=l}for(var s in f.optional)if(t.hasOwnProperty(s)){var o=t[s];if(i.isEmpty(o))continue;var u=f.optional[s](o);if(void 0===u)throw new h.ConfigurationError(s,o);e[s]=u}}},1468:function(e,t){var n=1e3,r=60*n,l=60*r,s=24*l,i=7*s,o=365.25*s;function u(e){if(e=String(e),!(e.length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var u=parseFloat(t[1]),a=(t[2]||"ms").toLowerCase();switch(a){case"years":case"year":case"yrs":case"yr":case"y":return u*o;case"weeks":case"week":case"w":return u*i;case"days":case"day":case"d":return u*s;case"hours":case"hour":case"hrs":case"hr":case"h":return u*l;case"minutes":case"minute":case"mins":case"min":case"m":return u*r;case"seconds":case"second":case"secs":case"sec":case"s":return u*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return u;default:return}}}}function a(e){var t=Math.abs(e);return t>=s?Math.round(e/s)+"d":t>=l?Math.round(e/l)+"h":t>=r?Math.round(e/r)+"m":t>=n?Math.round(e/n)+"s":e+"ms"}function c(e){var t=Math.abs(e);return t>=s?h(e,t,s,"day"):t>=l?h(e,t,l,"hour"):t>=r?h(e,t,r,"minute"):t>=n?h(e,t,n,"second"):e+" ms"}function h(e,t,n,r){var l=t>=1.5*n;return Math.round(e/n)+" "+r+(l?"s":"")}e.exports=function(e,t){t=t||{};var n=typeof e;if("string"===n&&e.length>0)return u(e);if("number"===n&&isFinite(e))return t.long?c(e):a(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},"1bbf":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function a(e){var t=f();return function(){var n,r=d(e);if(t){var l=d(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var _=n("faa1").EventEmitter,p=n("1e8c"),m=n("609b"),v=n("380f"),g=n("3781e"),y=n("a283"),T=n("05fe"),S=n("0790"),C=n("8c52"),E=n("9cf5"),b=n("0c32"),A=n("03f2"),R=n("e9f0"),w=n("13c4"),I=n("34eb")("JsSIP:UA"),O=n("34eb")("JsSIP:ERROR:UA");O.log=console.warn.bind(console);var k={STATUS_INIT:0,STATUS_READY:1,STATUS_USER_CLOSED:2,STATUS_NOT_READY:3,CONFIGURATION_ERROR:1,NETWORK_ERROR:2};function N(e){this.emit("connecting",e)}function P(e){this._status!==k.STATUS_USER_CLOSED&&(this._status=k.STATUS_READY,this._error=null,this.emit("connected",e),this._dynConfiguration.register&&this._registrator.register())}function D(e){for(var t=["nict","ict","nist","ist"],n=0,r=t;n<r.length;n++){var l=r[n];for(var s in this._transactions[l])Object.prototype.hasOwnProperty.call(this._transactions[l],s)&&this._transactions[l][s].onTransportError()}this.emit("disconnected",e),this._registrator.onTransportClosed(),this._status!==k.STATUS_USER_CLOSED&&(this._status=k.STATUS_NOT_READY,this._error=k.NETWORK_ERROR)}function x(e){var t=e.transport,n=e.message;if(n=b.parseMessage(n,this),n&&!(this._status===k.STATUS_USER_CLOSED&&n instanceof A.IncomingRequest)&&R(n,this,t))if(n instanceof A.IncomingRequest)n.transport=t,this.receiveRequest(n);else if(n instanceof A.IncomingResponse){var r;switch(n.method){case p.INVITE:r=this._transactions.ict[n.via_branch],r&&r.receiveResponse(n);break;case p.ACK:break;default:r=this._transactions.nict[n.via_branch],r&&r.receiveResponse(n);break}}}e.exports=function(e){o(n,e);var t=a(n);function n(e){var r;if(l(this,n),I("new() [configuration:%o]",e),r=t.call(this),r._cache={credentials:{}},r._configuration=Object.assign({},w.settings),r._dynConfiguration={},r._dialogs={},r._applicants={},r._sessions={},r._transport=null,r._contact=null,r._status=k.STATUS_INIT,r._error=null,r._transactions={nist:{},nict:{},ist:{},ict:{}},r._data={},r._closeTimer=null,void 0===e)throw new TypeError("Not enough arguments");try{r._loadConfig(e)}catch(s){throw r._status=k.STATUS_NOT_READY,r._error=k.CONFIGURATION_ERROR,s}return r._registrator=new m(h(r)),r}return i(n,null,[{key:"C",get:function(){return k}}]),i(n,[{key:"start",value:function(){I("start()"),this._status===k.STATUS_INIT?this._transport.connect():this._status===k.STATUS_USER_CLOSED?(I("restarting UA"),null!==this._closeTimer&&(clearTimeout(this._closeTimer),this._closeTimer=null,this._transport.disconnect()),this._status=k.STATUS_INIT,this._transport.connect()):this._status===k.STATUS_READY?I("UA is in READY status, not restarted"):I("ERROR: connection is down, Auto-Recovery system is trying to reconnect"),this._dynConfiguration.register=this._configuration.register}},{key:"register",value:function(){I("register()"),this._dynConfiguration.register=!0,this._registrator.register()}},{key:"unregister",value:function(e){I("unregister()"),this._dynConfiguration.register=!1,this._registrator.unregister(e)}},{key:"registrator",value:function(){return this._registrator}},{key:"isRegistered",value:function(){return this._registrator.registered}},{key:"isConnected",value:function(){return this._transport.isConnected()}},{key:"call",value:function(e,t){I("call()");var n=new v(this);return n.connect(e,t),n}},{key:"sendMessage",value:function(e,t,n){I("sendMessage()");var r=new g(this);return r.send(e,t,n),r}},{key:"terminateSessions",value:function(e){for(var t in I("terminateSessions()"),this._sessions)this._sessions[t].isEnded()||this._sessions[t].terminate(e)}},{key:"stop",value:function(){var e=this;if(I("stop()"),this._dynConfiguration={},this._status!==k.STATUS_USER_CLOSED){this._registrator.close();var t=Object.keys(this._sessions).length;for(var n in this._sessions)if(Object.prototype.hasOwnProperty.call(this._sessions,n)){I("closing session ".concat(n));try{this._sessions[n].terminate()}catch(s){}}for(var r in this._applicants)if(Object.prototype.hasOwnProperty.call(this._applicants,r))try{this._applicants[r].close()}catch(s){}this._status=k.STATUS_USER_CLOSED;var l=Object.keys(this._transactions.nict).length+Object.keys(this._transactions.nist).length+Object.keys(this._transactions.ict).length+Object.keys(this._transactions.ist).length;0===l&&0===t?this._transport.disconnect():this._closeTimer=setTimeout((function(){e._closeTimer=null,e._transport.disconnect()}),2e3)}else I("UA already closed")}},{key:"normalizeTarget",value:function(e){return S.normalizeTarget(e,this._configuration.hostport_params)}},{key:"get",value:function(e){switch(e){case"authorization_user":return this._configuration.authorization_user;case"realm":return this._configuration.realm;case"ha1":return this._configuration.ha1;default:return void O('get() | cannot get "%s" parameter in runtime',e)}}},{key:"set",value:function(e,t){switch(e){case"authorization_user":this._configuration.authorization_user=String(t);break;case"password":this._configuration.password=String(t);break;case"realm":this._configuration.realm=String(t);break;case"ha1":this._configuration.ha1=String(t),this._configuration.password=null;break;case"display_name":this._configuration.display_name=t;break;default:return O('set() | cannot set "%s" parameter in runtime',e),!1}return!0}},{key:"newTransaction",value:function(e){this._transactions[e.type][e.id]=e,this.emit("newTransaction",{transaction:e})}},{key:"destroyTransaction",value:function(e){delete this._transactions[e.type][e.id],this.emit("transactionDestroyed",{transaction:e})}},{key:"newDialog",value:function(e){this._dialogs[e.id]=e}},{key:"destroyDialog",value:function(e){delete this._dialogs[e.id]}},{key:"newMessage",value:function(e,t){this._applicants[e]=e,this.emit("newMessage",t)}},{key:"destroyMessage",value:function(e){delete this._applicants[e]}},{key:"newRTCSession",value:function(e,t){this._sessions[e.id]=e,this.emit("newRTCSession",t)}},{key:"destroyRTCSession",value:function(e){delete this._sessions[e.id]}},{key:"registered",value:function(e){this.emit("registered",e)}},{key:"unregistered",value:function(e){this.emit("unregistered",e)}},{key:"registrationFailed",value:function(e){this.emit("registrationFailed",e)}},{key:"receiveRequest",value:function(e){var t=e.method;if(e.ruri.user!==this._configuration.uri.user&&e.ruri.user!==this._contact.uri.user)return I("Request-URI does not point to us"),void(e.method!==p.ACK&&e.reply_sl(404));if(e.ruri.scheme!==p.SIPS){if(!y.checkTransaction(this,e)){if(t===p.INVITE?new y.InviteServerTransaction(this,this._transport,e):t!==p.ACK&&t!==p.CANCEL&&new y.NonInviteServerTransaction(this,this._transport,e),t===p.OPTIONS)e.reply(200);else if(t===p.MESSAGE){if(0===this.listeners("newMessage").length)return void e.reply(405);var n=new g(this);n.init_incoming(e)}else if(t===p.INVITE&&!e.to_tag&&0===this.listeners("newRTCSession").length)return void e.reply(405);var r,l;if(e.to_tag)r=this._findDialog(e.call_id,e.from_tag,e.to_tag),r?r.receiveRequest(e):t===p.NOTIFY?(l=this._findSession(e),l?l.receiveRequest(e):(I("received NOTIFY request for a non existent subscription"),e.reply(481,"Subscription does not exist"))):t!==p.ACK&&e.reply(481);else switch(t){case p.INVITE:if(window.RTCPeerConnection)if(e.hasHeader("replaces")){var s=e.replaces;r=this._findDialog(s.call_id,s.from_tag,s.to_tag),r?(l=r.owner,l.isEnded()?e.reply(603):l.receiveRequest(e)):e.reply(481)}else l=new v(this),l.init_incoming(e);else O("INVITE received but WebRTC is not supported"),e.reply(488);break;case p.BYE:e.reply(481);break;case p.CANCEL:l=this._findSession(e),l?l.receiveRequest(e):I("received CANCEL request for a non existent session");break;case p.ACK:break;case p.NOTIFY:this.emit("sipEvent",{event:e.event,request:e}),e.reply(200);break;default:e.reply(405);break}}}else e.reply_sl(416)}},{key:"_findSession",value:function(e){var t=e.call_id,n=e.from_tag,r=e.to_tag,l=t+n,s=this._sessions[l],i=t+r,o=this._sessions[i];return s||(o||null)}},{key:"_findDialog",value:function(e,t,n){var r=e+t+n,l=this._dialogs[r];return l||(r=e+n+t,l=this._dialogs[r],l||null)}},{key:"_loadConfig",value:function(e){try{w.load(this._configuration,e)}catch(i){throw i}0===this._configuration.display_name&&(this._configuration.display_name="0"),this._configuration.instance_id||(this._configuration.instance_id=S.newUUID()),this._configuration.jssip_id=S.createRandomToken(5);var t=this._configuration.uri.clone();t.user=null,this._configuration.hostport_params=t.toString().replace(/^sip:/i,"");try{this._transport=new T(this._configuration.sockets,{max_interval:this._configuration.connection_recovery_max_interval,min_interval:this._configuration.connection_recovery_min_interval}),this._transport.onconnecting=N.bind(this),this._transport.onconnect=P.bind(this),this._transport.ondisconnect=D.bind(this),this._transport.ondata=x.bind(this)}catch(i){throw O(i),new C.ConfigurationError("sockets",this._configuration.sockets)}if(delete this._configuration.sockets,this._configuration.authorization_user||(this._configuration.authorization_user=this._configuration.uri.user),!this._configuration.registrar_server){var n=this._configuration.uri.clone();n.user=null,n.clearParams(),n.clearHeaders(),this._configuration.registrar_server=n}this._configuration.no_answer_timeout*=1e3,this._configuration.contact_uri?this._configuration.via_host=this._configuration.contact_uri.host:this._configuration.contact_uri=new E("sip",S.createRandomToken(8),this._configuration.via_host,null,{transport:"ws"}),this._contact={pub_gruu:null,temp_gruu:null,uri:this._configuration.contact_uri,toString:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.anonymous||null,n=e.outbound||null,r="<";return r+=t?this.temp_gruu||"sip:<EMAIL>;transport=ws":this.pub_gruu||this.uri.toString(),!n||(t?this.temp_gruu:this.pub_gruu)||(r+=";ob"),r+=">",r}};var r=["authorization_user","password","realm","ha1","display_name","register"];for(var l in this._configuration)Object.prototype.hasOwnProperty.call(this._configuration,l)&&(-1!==r.indexOf(l)?Object.defineProperty(this._configuration,l,{writable:!0,configurable:!1}):Object.defineProperty(this._configuration,l,{writable:!1,configurable:!1}));for(var s in I("configuration parameters after validation:"),this._configuration)if(Object.prototype.hasOwnProperty.call(w.settings,s))switch(s){case"uri":case"registrar_server":I("- ".concat(s,": ").concat(this._configuration[s]));break;case"password":case"ha1":I("- ".concat(s,": NOT SHOWN"));break;default:I("- ".concat(s,": ").concat(JSON.stringify(this._configuration[s])))}}},{key:"C",get:function(){return k}},{key:"status",get:function(){return this._status}},{key:"contact",get:function(){return this._contact}},{key:"configuration",get:function(){return this._configuration}},{key:"transport",get:function(){return this._transport}}]),n}(_)},"1c8d":function(e,t,n){"use strict";e.exports=function(){function e(e){return'"'+e.replace(/\\/g,"\\\\").replace(/"/g,'\\"').replace(/\x08/g,"\\b").replace(/\t/g,"\\t").replace(/\n/g,"\\n").replace(/\f/g,"\\f").replace(/\r/g,"\\r").replace(/[\x00-\x07\x0B\x0E-\x1F\x80-\uFFFF]/g,escape)+'"'}var t={parse:function(t,r){var l={CRLF:c,DIGIT:h,ALPHA:f,HEXDIG:d,WSP:_,OCTET:p,DQUOTE:m,SP:v,HTAB:g,alphanum:y,reserved:T,unreserved:S,mark:C,escaped:E,LWS:b,SWS:A,HCOLON:R,TEXT_UTF8_TRIM:w,TEXT_UTF8char:I,UTF8_NONASCII:O,UTF8_CONT:k,LHEX:N,token:P,token_nodot:D,separators:x,word:U,STAR:M,SLASH:L,EQUAL:q,LPAREN:H,RPAREN:F,RAQUOT:j,LAQUOT:V,COMMA:G,SEMI:W,COLON:B,LDQUOT:K,RDQUOT:Y,comment:$,ctext:J,quoted_string:z,quoted_string_clean:X,qdtext:Q,quoted_pair:Z,SIP_URI_noparams:ee,SIP_URI:te,uri_scheme:ne,uri_scheme_sips:re,uri_scheme_sip:le,userinfo:se,user:ie,user_unreserved:oe,password:ue,hostport:ae,host:ce,hostname:he,domainlabel:fe,toplabel:de,IPv6reference:_e,IPv6address:pe,h16:me,ls32:ve,IPv4address:ge,dec_octet:ye,port:Te,uri_parameters:Se,uri_parameter:Ce,transport_param:Ee,user_param:be,method_param:Ae,ttl_param:Re,maddr_param:we,lr_param:Ie,other_param:Oe,pname:ke,pvalue:Ne,paramchar:Pe,param_unreserved:De,headers:xe,header:Ue,hname:Me,hvalue:Le,hnv_unreserved:qe,Request_Response:He,Request_Line:Fe,Request_URI:je,absoluteURI:Ve,hier_part:Ge,net_path:We,abs_path:Be,opaque_part:Ke,uric:Ye,uric_no_slash:$e,path_segments:Je,segment:ze,param:Xe,pchar:Qe,scheme:Ze,authority:et,srvr:tt,reg_name:nt,query:rt,SIP_Version:lt,INVITEm:st,ACKm:it,OPTIONSm:ot,BYEm:ut,CANCELm:at,REGISTERm:ct,SUBSCRIBEm:ht,NOTIFYm:ft,REFERm:dt,Method:_t,Status_Line:pt,Status_Code:mt,extension_code:vt,Reason_Phrase:gt,Allow_Events:yt,Call_ID:Tt,Contact:St,contact_param:Ct,name_addr:Et,display_name:bt,contact_params:At,c_p_q:Rt,c_p_expires:wt,delta_seconds:It,qvalue:Ot,generic_param:kt,gen_value:Nt,Content_Disposition:Pt,disp_type:Dt,disp_param:xt,handling_param:Ut,Content_Encoding:Mt,Content_Length:Lt,Content_Type:qt,media_type:Ht,m_type:Ft,discrete_type:jt,composite_type:Vt,extension_token:Gt,x_token:Wt,m_subtype:Bt,m_parameter:Kt,m_value:Yt,CSeq:$t,CSeq_value:Jt,Expires:zt,Event:Xt,event_type:Qt,From:Zt,from_param:en,tag_param:tn,Max_Forwards:nn,Min_Expires:rn,Name_Addr_Header:ln,Proxy_Authenticate:sn,challenge:on,other_challenge:un,auth_param:an,digest_cln:cn,realm:hn,realm_value:fn,domain:dn,URI:_n,nonce:pn,nonce_value:mn,opaque:vn,stale:gn,algorithm:yn,qop_options:Tn,qop_value:Sn,Proxy_Require:Cn,Record_Route:En,rec_route:bn,Reason:An,reason_param:Rn,reason_cause:wn,Require:In,Route:On,route_param:kn,Subscription_State:Nn,substate_value:Pn,subexp_params:Dn,event_reason_value:xn,Subject:Un,Supported:Mn,To:Ln,to_param:qn,Via:Hn,via_param:Fn,via_params:jn,via_ttl:Vn,via_maddr:Gn,via_received:Wn,via_branch:Bn,response_port:Kn,rport:Yn,sent_protocol:$n,protocol_name:Jn,transport:zn,sent_by:Xn,via_host:Qn,via_port:Zn,ttl:er,WWW_Authenticate:tr,Session_Expires:nr,s_e_expires:rr,s_e_params:lr,s_e_refresher:sr,extension_header:ir,header_value:or,message_body:ur,uuid_URI:ar,uuid:cr,hex4:hr,hex8:fr,hex12:dr,Refer_To:_r,Replaces:pr,call_id:mr,replaces_param:vr,to_tag:gr,from_tag:yr,early_flag:Tr};if(void 0!==r){if(void 0===l[r])throw new Error("Invalid rule name: "+e(r)+".")}else r="CRLF";var s=0,i=0,o=0,u=[];function a(e){s<o||(s>o&&(o=s,u=[]),u.push(e))}function c(){var e;return"\r\n"===t.substr(s,2)?(e="\r\n",s+=2):(e=null,0===i&&a('"\\r\\n"')),e}function h(){var e;return/^[0-9]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[0-9]")),e}function f(){var e;return/^[a-zA-Z]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[a-zA-Z]")),e}function d(){var e;return/^[0-9a-fA-F]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[0-9a-fA-F]")),e}function _(){var e;return e=v(),null===e&&(e=g()),e}function p(){var e;return/^[\0-\xFF]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[\\0-\\xFF]")),e}function m(){var e;return/^["]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a('["]')),e}function v(){var e;return 32===t.charCodeAt(s)?(e=" ",s++):(e=null,0===i&&a('" "')),e}function g(){var e;return 9===t.charCodeAt(s)?(e="\t",s++):(e=null,0===i&&a('"\\t"')),e}function y(){var e;return/^[a-zA-Z0-9]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[a-zA-Z0-9]")),e}function T(){var e;return 59===t.charCodeAt(s)?(e=";",s++):(e=null,0===i&&a('";"')),null===e&&(47===t.charCodeAt(s)?(e="/",s++):(e=null,0===i&&a('"/"')),null===e&&(63===t.charCodeAt(s)?(e="?",s++):(e=null,0===i&&a('"?"')),null===e&&(58===t.charCodeAt(s)?(e=":",s++):(e=null,0===i&&a('":"')),null===e&&(64===t.charCodeAt(s)?(e="@",s++):(e=null,0===i&&a('"@"')),null===e&&(38===t.charCodeAt(s)?(e="&",s++):(e=null,0===i&&a('"&"')),null===e&&(61===t.charCodeAt(s)?(e="=",s++):(e=null,0===i&&a('"="')),null===e&&(43===t.charCodeAt(s)?(e="+",s++):(e=null,0===i&&a('"+"')),null===e&&(36===t.charCodeAt(s)?(e="$",s++):(e=null,0===i&&a('"$"')),null===e&&(44===t.charCodeAt(s)?(e=",",s++):(e=null,0===i&&a('","'))))))))))),e}function S(){var e;return e=y(),null===e&&(e=C()),e}function C(){var e;return 45===t.charCodeAt(s)?(e="-",s++):(e=null,0===i&&a('"-"')),null===e&&(95===t.charCodeAt(s)?(e="_",s++):(e=null,0===i&&a('"_"')),null===e&&(46===t.charCodeAt(s)?(e=".",s++):(e=null,0===i&&a('"."')),null===e&&(33===t.charCodeAt(s)?(e="!",s++):(e=null,0===i&&a('"!"')),null===e&&(126===t.charCodeAt(s)?(e="~",s++):(e=null,0===i&&a('"~"')),null===e&&(42===t.charCodeAt(s)?(e="*",s++):(e=null,0===i&&a('"*"')),null===e&&(39===t.charCodeAt(s)?(e="'",s++):(e=null,0===i&&a('"\'"')),null===e&&(40===t.charCodeAt(s)?(e="(",s++):(e=null,0===i&&a('"("')),null===e&&(41===t.charCodeAt(s)?(e=")",s++):(e=null,0===i&&a('")"')))))))))),e}function E(){var e,n,r,l,o;return l=s,o=s,37===t.charCodeAt(s)?(e="%",s++):(e=null,0===i&&a('"%"')),null!==e?(n=d(),null!==n?(r=d(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){return t.join("")}(0,e)),null===e&&(s=l),e}function b(){var e,t,n,r,l,i;r=s,l=s,i=s,e=[],t=_();while(null!==t)e.push(t),t=_();if(null!==e?(t=c(),null!==t?e=[e,t]:(e=null,s=i)):(e=null,s=i),e=null!==e?e:"",null!==e){if(n=_(),null!==n){t=[];while(null!==n)t.push(n),n=_()}else t=null;null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return null!==e&&(e=function(e){return" "}()),null===e&&(s=r),e}function A(){var e;return e=b(),e=null!==e?e:"",e}function R(){var e,n,r,l,o;l=s,o=s,e=[],n=v(),null===n&&(n=g());while(null!==n)e.push(n),n=v(),null===n&&(n=g());return null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return":"}()),null===e&&(s=l),e}function w(){var e,n,r,l,i,o,u;if(i=s,o=s,n=I(),null!==n){e=[];while(null!==n)e.push(n),n=I()}else e=null;if(null!==e){n=[],u=s,r=[],l=b();while(null!==l)r.push(l),l=b();null!==r?(l=I(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);while(null!==r){n.push(r),u=s,r=[],l=b();while(null!==l)r.push(l),l=b();null!==r?(l=I(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u)}null!==n?e=[e,n]:(e=null,s=o)}else e=null,s=o;return null!==e&&(e=function(e){return t.substring(s,e)}(i)),null===e&&(s=i),e}function I(){var e;return/^[!-~]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[!-~]")),null===e&&(e=O()),e}function O(){var e;return/^[\x80-\uFFFF]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[\\x80-\\uFFFF]")),e}function k(){var e;return/^[\x80-\xBF]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[\\x80-\\xBF]")),e}function N(){var e;return e=h(),null===e&&(/^[a-f]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[a-f]"))),e}function P(){var e,n,r;if(r=s,n=y(),null===n&&(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null===n&&(46===t.charCodeAt(s)?(n=".",s++):(n=null,0===i&&a('"."')),null===n&&(33===t.charCodeAt(s)?(n="!",s++):(n=null,0===i&&a('"!"')),null===n&&(37===t.charCodeAt(s)?(n="%",s++):(n=null,0===i&&a('"%"')),null===n&&(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null===n&&(95===t.charCodeAt(s)?(n="_",s++):(n=null,0===i&&a('"_"')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(96===t.charCodeAt(s)?(n="`",s++):(n=null,0===i&&a('"`"')),null===n&&(39===t.charCodeAt(s)?(n="'",s++):(n=null,0===i&&a('"\'"')),null===n&&(126===t.charCodeAt(s)?(n="~",s++):(n=null,0===i&&a('"~"')))))))))))),null!==n){e=[];while(null!==n)e.push(n),n=y(),null===n&&(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null===n&&(46===t.charCodeAt(s)?(n=".",s++):(n=null,0===i&&a('"."')),null===n&&(33===t.charCodeAt(s)?(n="!",s++):(n=null,0===i&&a('"!"')),null===n&&(37===t.charCodeAt(s)?(n="%",s++):(n=null,0===i&&a('"%"')),null===n&&(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null===n&&(95===t.charCodeAt(s)?(n="_",s++):(n=null,0===i&&a('"_"')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(96===t.charCodeAt(s)?(n="`",s++):(n=null,0===i&&a('"`"')),null===n&&(39===t.charCodeAt(s)?(n="'",s++):(n=null,0===i&&a('"\'"')),null===n&&(126===t.charCodeAt(s)?(n="~",s++):(n=null,0===i&&a('"~"'))))))))))))}else e=null;return null!==e&&(e=function(e){return t.substring(s,e)}(r)),null===e&&(s=r),e}function D(){var e,n,r;if(r=s,n=y(),null===n&&(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null===n&&(33===t.charCodeAt(s)?(n="!",s++):(n=null,0===i&&a('"!"')),null===n&&(37===t.charCodeAt(s)?(n="%",s++):(n=null,0===i&&a('"%"')),null===n&&(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null===n&&(95===t.charCodeAt(s)?(n="_",s++):(n=null,0===i&&a('"_"')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(96===t.charCodeAt(s)?(n="`",s++):(n=null,0===i&&a('"`"')),null===n&&(39===t.charCodeAt(s)?(n="'",s++):(n=null,0===i&&a('"\'"')),null===n&&(126===t.charCodeAt(s)?(n="~",s++):(n=null,0===i&&a('"~"'))))))))))),null!==n){e=[];while(null!==n)e.push(n),n=y(),null===n&&(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null===n&&(33===t.charCodeAt(s)?(n="!",s++):(n=null,0===i&&a('"!"')),null===n&&(37===t.charCodeAt(s)?(n="%",s++):(n=null,0===i&&a('"%"')),null===n&&(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null===n&&(95===t.charCodeAt(s)?(n="_",s++):(n=null,0===i&&a('"_"')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(96===t.charCodeAt(s)?(n="`",s++):(n=null,0===i&&a('"`"')),null===n&&(39===t.charCodeAt(s)?(n="'",s++):(n=null,0===i&&a('"\'"')),null===n&&(126===t.charCodeAt(s)?(n="~",s++):(n=null,0===i&&a('"~"')))))))))))}else e=null;return null!==e&&(e=function(e){return t.substring(s,e)}(r)),null===e&&(s=r),e}function x(){var e;return 40===t.charCodeAt(s)?(e="(",s++):(e=null,0===i&&a('"("')),null===e&&(41===t.charCodeAt(s)?(e=")",s++):(e=null,0===i&&a('")"')),null===e&&(60===t.charCodeAt(s)?(e="<",s++):(e=null,0===i&&a('"<"')),null===e&&(62===t.charCodeAt(s)?(e=">",s++):(e=null,0===i&&a('">"')),null===e&&(64===t.charCodeAt(s)?(e="@",s++):(e=null,0===i&&a('"@"')),null===e&&(44===t.charCodeAt(s)?(e=",",s++):(e=null,0===i&&a('","')),null===e&&(59===t.charCodeAt(s)?(e=";",s++):(e=null,0===i&&a('";"')),null===e&&(58===t.charCodeAt(s)?(e=":",s++):(e=null,0===i&&a('":"')),null===e&&(92===t.charCodeAt(s)?(e="\\",s++):(e=null,0===i&&a('"\\\\"')),null===e&&(e=m(),null===e&&(47===t.charCodeAt(s)?(e="/",s++):(e=null,0===i&&a('"/"')),null===e&&(91===t.charCodeAt(s)?(e="[",s++):(e=null,0===i&&a('"["')),null===e&&(93===t.charCodeAt(s)?(e="]",s++):(e=null,0===i&&a('"]"')),null===e&&(63===t.charCodeAt(s)?(e="?",s++):(e=null,0===i&&a('"?"')),null===e&&(61===t.charCodeAt(s)?(e="=",s++):(e=null,0===i&&a('"="')),null===e&&(123===t.charCodeAt(s)?(e="{",s++):(e=null,0===i&&a('"{"')),null===e&&(125===t.charCodeAt(s)?(e="}",s++):(e=null,0===i&&a('"}"')),null===e&&(e=v(),null===e&&(e=g())))))))))))))))))),e}function U(){var e,n,r;if(r=s,n=y(),null===n&&(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null===n&&(46===t.charCodeAt(s)?(n=".",s++):(n=null,0===i&&a('"."')),null===n&&(33===t.charCodeAt(s)?(n="!",s++):(n=null,0===i&&a('"!"')),null===n&&(37===t.charCodeAt(s)?(n="%",s++):(n=null,0===i&&a('"%"')),null===n&&(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null===n&&(95===t.charCodeAt(s)?(n="_",s++):(n=null,0===i&&a('"_"')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(96===t.charCodeAt(s)?(n="`",s++):(n=null,0===i&&a('"`"')),null===n&&(39===t.charCodeAt(s)?(n="'",s++):(n=null,0===i&&a('"\'"')),null===n&&(126===t.charCodeAt(s)?(n="~",s++):(n=null,0===i&&a('"~"')),null===n&&(40===t.charCodeAt(s)?(n="(",s++):(n=null,0===i&&a('"("')),null===n&&(41===t.charCodeAt(s)?(n=")",s++):(n=null,0===i&&a('")"')),null===n&&(60===t.charCodeAt(s)?(n="<",s++):(n=null,0===i&&a('"<"')),null===n&&(62===t.charCodeAt(s)?(n=">",s++):(n=null,0===i&&a('">"')),null===n&&(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null===n&&(92===t.charCodeAt(s)?(n="\\",s++):(n=null,0===i&&a('"\\\\"')),null===n&&(n=m(),null===n&&(47===t.charCodeAt(s)?(n="/",s++):(n=null,0===i&&a('"/"')),null===n&&(91===t.charCodeAt(s)?(n="[",s++):(n=null,0===i&&a('"["')),null===n&&(93===t.charCodeAt(s)?(n="]",s++):(n=null,0===i&&a('"]"')),null===n&&(63===t.charCodeAt(s)?(n="?",s++):(n=null,0===i&&a('"?"')),null===n&&(123===t.charCodeAt(s)?(n="{",s++):(n=null,0===i&&a('"{"')),null===n&&(125===t.charCodeAt(s)?(n="}",s++):(n=null,0===i&&a('"}"'))))))))))))))))))))))))),null!==n){e=[];while(null!==n)e.push(n),n=y(),null===n&&(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null===n&&(46===t.charCodeAt(s)?(n=".",s++):(n=null,0===i&&a('"."')),null===n&&(33===t.charCodeAt(s)?(n="!",s++):(n=null,0===i&&a('"!"')),null===n&&(37===t.charCodeAt(s)?(n="%",s++):(n=null,0===i&&a('"%"')),null===n&&(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null===n&&(95===t.charCodeAt(s)?(n="_",s++):(n=null,0===i&&a('"_"')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(96===t.charCodeAt(s)?(n="`",s++):(n=null,0===i&&a('"`"')),null===n&&(39===t.charCodeAt(s)?(n="'",s++):(n=null,0===i&&a('"\'"')),null===n&&(126===t.charCodeAt(s)?(n="~",s++):(n=null,0===i&&a('"~"')),null===n&&(40===t.charCodeAt(s)?(n="(",s++):(n=null,0===i&&a('"("')),null===n&&(41===t.charCodeAt(s)?(n=")",s++):(n=null,0===i&&a('")"')),null===n&&(60===t.charCodeAt(s)?(n="<",s++):(n=null,0===i&&a('"<"')),null===n&&(62===t.charCodeAt(s)?(n=">",s++):(n=null,0===i&&a('">"')),null===n&&(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null===n&&(92===t.charCodeAt(s)?(n="\\",s++):(n=null,0===i&&a('"\\\\"')),null===n&&(n=m(),null===n&&(47===t.charCodeAt(s)?(n="/",s++):(n=null,0===i&&a('"/"')),null===n&&(91===t.charCodeAt(s)?(n="[",s++):(n=null,0===i&&a('"["')),null===n&&(93===t.charCodeAt(s)?(n="]",s++):(n=null,0===i&&a('"]"')),null===n&&(63===t.charCodeAt(s)?(n="?",s++):(n=null,0===i&&a('"?"')),null===n&&(123===t.charCodeAt(s)?(n="{",s++):(n=null,0===i&&a('"{"')),null===n&&(125===t.charCodeAt(s)?(n="}",s++):(n=null,0===i&&a('"}"')))))))))))))))))))))))))}else e=null;return null!==e&&(e=function(e){return t.substring(s,e)}(r)),null===e&&(s=r),e}function M(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(42===t.charCodeAt(s)?(n="*",s++):(n=null,0===i&&a('"*"')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return"*"}()),null===e&&(s=l),e}function L(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(47===t.charCodeAt(s)?(n="/",s++):(n=null,0===i&&a('"/"')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return"/"}()),null===e&&(s=l),e}function q(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return"="}()),null===e&&(s=l),e}function H(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(40===t.charCodeAt(s)?(n="(",s++):(n=null,0===i&&a('"("')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return"("}()),null===e&&(s=l),e}function F(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(41===t.charCodeAt(s)?(n=")",s++):(n=null,0===i&&a('")"')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return")"}()),null===e&&(s=l),e}function j(){var e,n,r,l;return r=s,l=s,62===t.charCodeAt(s)?(e=">",s++):(e=null,0===i&&a('">"')),null!==e?(n=A(),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e){return">"}()),null===e&&(s=r),e}function V(){var e,n,r,l;return r=s,l=s,e=A(),null!==e?(60===t.charCodeAt(s)?(n="<",s++):(n=null,0===i&&a('"<"')),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e){return"<"}()),null===e&&(s=r),e}function G(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(44===t.charCodeAt(s)?(n=",",s++):(n=null,0===i&&a('","')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return","}()),null===e&&(s=l),e}function W(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(59===t.charCodeAt(s)?(n=";",s++):(n=null,0===i&&a('";"')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return";"}()),null===e&&(s=l),e}function B(){var e,n,r,l,o;return l=s,o=s,e=A(),null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=A(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return":"}()),null===e&&(s=l),e}function K(){var e,t,n,r;return n=s,r=s,e=A(),null!==e?(t=m(),null!==t?e=[e,t]:(e=null,s=r)):(e=null,s=r),null!==e&&(e=function(e){return'"'}()),null===e&&(s=n),e}function Y(){var e,t,n,r;return n=s,r=s,e=m(),null!==e?(t=A(),null!==t?e=[e,t]:(e=null,s=r)):(e=null,s=r),null!==e&&(e=function(e){return'"'}()),null===e&&(s=n),e}function $(){var e,t,n,r;if(r=s,e=H(),null!==e){t=[],n=J(),null===n&&(n=Z(),null===n&&(n=$()));while(null!==n)t.push(n),n=J(),null===n&&(n=Z(),null===n&&(n=$()));null!==t?(n=F(),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)}else e=null,s=r;return e}function J(){var e;return/^[!-']/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[!-']")),null===e&&(/^[*-[]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[*-[]")),null===e&&(/^[\]-~]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[\\]-~]")),null===e&&(e=O(),null===e&&(e=b())))),e}function z(){var e,n,r,l,i,o;if(i=s,o=s,e=A(),null!==e)if(n=m(),null!==n){r=[],l=Q(),null===l&&(l=Z());while(null!==l)r.push(l),l=Q(),null===l&&(l=Z());null!==r?(l=m(),null!==l?e=[e,n,r,l]:(e=null,s=o)):(e=null,s=o)}else e=null,s=o;else e=null,s=o;return null!==e&&(e=function(e){return t.substring(s,e)}(i)),null===e&&(s=i),e}function X(){var e,n,r,l,i,o;if(i=s,o=s,e=A(),null!==e)if(n=m(),null!==n){r=[],l=Q(),null===l&&(l=Z());while(null!==l)r.push(l),l=Q(),null===l&&(l=Z());null!==r?(l=m(),null!==l?e=[e,n,r,l]:(e=null,s=o)):(e=null,s=o)}else e=null,s=o;else e=null,s=o;return null!==e&&(e=function(e){var n=t.substring(s,e).trim();return n.substring(1,n.length-1).replace(/\\([\x00-\x09\x0b-\x0c\x0e-\x7f])/g,"$1")}(i)),null===e&&(s=i),e}function Q(){var e;return e=b(),null===e&&(33===t.charCodeAt(s)?(e="!",s++):(e=null,0===i&&a('"!"')),null===e&&(/^[#-[]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[#-[]")),null===e&&(/^[\]-~]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[\\]-~]")),null===e&&(e=O())))),e}function Z(){var e,n,r;return r=s,92===t.charCodeAt(s)?(e="\\",s++):(e=null,0===i&&a('"\\\\"')),null!==e?(/^[\0-\t]/.test(t.charAt(s))?(n=t.charAt(s),s++):(n=null,0===i&&a("[\\0-\\t]")),null===n&&(/^[\x0B-\f]/.test(t.charAt(s))?(n=t.charAt(s),s++):(n=null,0===i&&a("[\\x0B-\\f]")),null===n&&(/^[\x0E-]/.test(t.charAt(s))?(n=t.charAt(s),s++):(n=null,0===i&&a("[\\x0E-]")))),null!==n?e=[e,n]:(e=null,s=r)):(e=null,s=r),e}function ee(){var e,n,r,l,o,u;return o=s,u=s,e=ne(),null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=se(),r=null!==r?r:"",null!==r?(l=ae(),null!==l?e=[e,n,r,l]:(e=null,s=u)):(e=null,s=u)):(e=null,s=u)):(e=null,s=u),null!==e&&(e=function(e){try{Ar.uri=new Er(Ar.scheme,Ar.user,Ar.host,Ar.port),delete Ar.scheme,delete Ar.user,delete Ar.host,delete Ar.host_type,delete Ar.port}catch(t){Ar=-1}}()),null===e&&(s=o),e}function te(){var e,n,l,o,u,c,h,f;return h=s,f=s,e=ne(),null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(l=se(),l=null!==l?l:"",null!==l?(o=ae(),null!==o?(u=Se(),null!==u?(c=xe(),c=null!==c?c:"",null!==c?e=[e,n,l,o,u,c]:(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f),null!==e&&(e=function(e){try{Ar.uri=new Er(Ar.scheme,Ar.user,Ar.host,Ar.port,Ar.uri_params,Ar.uri_headers),delete Ar.scheme,delete Ar.user,delete Ar.host,delete Ar.host_type,delete Ar.port,delete Ar.uri_params,"SIP_URI"===r&&(Ar=Ar.uri)}catch(t){Ar=-1}}()),null===e&&(s=h),e}function ne(){var e;return e=re(),null===e&&(e=le()),e}function re(){var e,n;return n=s,"sips"===t.substr(s,4).toLowerCase()?(e=t.substr(s,4),s+=4):(e=null,0===i&&a('"sips"')),null!==e&&(e=function(e,t){Ar.scheme=t.toLowerCase()}(0,e)),null===e&&(s=n),e}function le(){var e,n;return n=s,"sip"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"sip"')),null!==e&&(e=function(e,t){Ar.scheme=t.toLowerCase()}(0,e)),null===e&&(s=n),e}function se(){var e,n,r,l,o,u;return l=s,o=s,e=ie(),null!==e?(u=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=ue(),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u),n=null!==n?n:"",null!==n?(64===t.charCodeAt(s)?(r="@",s++):(r=null,0===i&&a('"@"')),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){Ar.user=decodeURIComponent(t.substring(s-1,e))}(l)),null===e&&(s=l),e}function ie(){var e,t;if(t=S(),null===t&&(t=E(),null===t&&(t=oe())),null!==t){e=[];while(null!==t)e.push(t),t=S(),null===t&&(t=E(),null===t&&(t=oe()))}else e=null;return e}function oe(){var e;return 38===t.charCodeAt(s)?(e="&",s++):(e=null,0===i&&a('"&"')),null===e&&(61===t.charCodeAt(s)?(e="=",s++):(e=null,0===i&&a('"="')),null===e&&(43===t.charCodeAt(s)?(e="+",s++):(e=null,0===i&&a('"+"')),null===e&&(36===t.charCodeAt(s)?(e="$",s++):(e=null,0===i&&a('"$"')),null===e&&(44===t.charCodeAt(s)?(e=",",s++):(e=null,0===i&&a('","')),null===e&&(59===t.charCodeAt(s)?(e=";",s++):(e=null,0===i&&a('";"')),null===e&&(63===t.charCodeAt(s)?(e="?",s++):(e=null,0===i&&a('"?"')),null===e&&(47===t.charCodeAt(s)?(e="/",s++):(e=null,0===i&&a('"/"'))))))))),e}function ue(){var e,n,r;r=s,e=[],n=S(),null===n&&(n=E(),null===n&&(38===t.charCodeAt(s)?(n="&",s++):(n=null,0===i&&a('"&"')),null===n&&(61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(36===t.charCodeAt(s)?(n="$",s++):(n=null,0===i&&a('"$"')),null===n&&(44===t.charCodeAt(s)?(n=",",s++):(n=null,0===i&&a('","'))))))));while(null!==n)e.push(n),n=S(),null===n&&(n=E(),null===n&&(38===t.charCodeAt(s)?(n="&",s++):(n=null,0===i&&a('"&"')),null===n&&(61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')),null===n&&(36===t.charCodeAt(s)?(n="$",s++):(n=null,0===i&&a('"$"')),null===n&&(44===t.charCodeAt(s)?(n=",",s++):(n=null,0===i&&a('","'))))))));return null!==e&&(e=function(e){Ar.password=t.substring(s,e)}(r)),null===e&&(s=r),e}function ae(){var e,n,r,l,o;return l=s,e=ce(),null!==e?(o=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=Te(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),e}function ce(){var e,n;return n=s,e=he(),null===e&&(e=ge(),null===e&&(e=_e())),null!==e&&(e=function(e){return Ar.host=t.substring(s,e).toLowerCase(),Ar.host}(n)),null===e&&(s=n),e}function he(){var e,n,r,l,o,u;l=s,o=s,e=[],u=s,n=fe(),null!==n?(46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."')),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u);while(null!==n)e.push(n),u=s,n=fe(),null!==n?(46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."')),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u);return null!==e?(n=de(),null!==n?(46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."')),r=null!==r?r:"",null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return Ar.host_type="domain",t.substring(s,e)}(l)),null===e&&(s=l),e}function fe(){var e,n,r,l;if(l=s,e=y(),null!==e){n=[],r=y(),null===r&&(45===t.charCodeAt(s)?(r="-",s++):(r=null,0===i&&a('"-"')),null===r&&(95===t.charCodeAt(s)?(r="_",s++):(r=null,0===i&&a('"_"'))));while(null!==r)n.push(r),r=y(),null===r&&(45===t.charCodeAt(s)?(r="-",s++):(r=null,0===i&&a('"-"')),null===r&&(95===t.charCodeAt(s)?(r="_",s++):(r=null,0===i&&a('"_"'))));null!==n?e=[e,n]:(e=null,s=l)}else e=null,s=l;return e}function de(){var e,n,r,l;if(l=s,e=f(),null!==e){n=[],r=y(),null===r&&(45===t.charCodeAt(s)?(r="-",s++):(r=null,0===i&&a('"-"')),null===r&&(95===t.charCodeAt(s)?(r="_",s++):(r=null,0===i&&a('"_"'))));while(null!==r)n.push(r),r=y(),null===r&&(45===t.charCodeAt(s)?(r="-",s++):(r=null,0===i&&a('"-"')),null===r&&(95===t.charCodeAt(s)?(r="_",s++):(r=null,0===i&&a('"_"'))));null!==n?e=[e,n]:(e=null,s=l)}else e=null,s=l;return e}function _e(){var e,n,r,l,o;return l=s,o=s,91===t.charCodeAt(s)?(e="[",s++):(e=null,0===i&&a('"["')),null!==e?(n=pe(),null!==n?(93===t.charCodeAt(s)?(r="]",s++):(r=null,0===i&&a('"]"')),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){return Ar.host_type="IPv6",t.substring(s,e)}(l)),null===e&&(s=l),e}function pe(){var e,n,r,l,o,u,c,h,f,d,_,p,m,v,g,y;return v=s,g=s,e=me(),null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?(58===t.charCodeAt(s)?(l=":",s++):(l=null,0===i&&a('":"')),null!==l?(o=me(),null!==o?(58===t.charCodeAt(s)?(u=":",s++):(u=null,0===i&&a('":"')),null!==u?(c=me(),null!==c?(58===t.charCodeAt(s)?(h=":",s++):(h=null,0===i&&a('":"')),null!==h?(f=me(),null!==f?(58===t.charCodeAt(s)?(d=":",s++):(d=null,0===i&&a('":"')),null!==d?(_=me(),null!==_?(58===t.charCodeAt(s)?(p=":",s++):(p=null,0===i&&a('":"')),null!==p?(m=ve(),null!==m?e=[e,n,r,l,o,u,c,h,f,d,_,p,m]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=me(),null!==n?(58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?(58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?(58===t.charCodeAt(s)?(c=":",s++):(c=null,0===i&&a('":"')),null!==c?(h=me(),null!==h?(58===t.charCodeAt(s)?(f=":",s++):(f=null,0===i&&a('":"')),null!==f?(d=me(),null!==d?(58===t.charCodeAt(s)?(_=":",s++):(_=null,0===i&&a('":"')),null!==_?(p=ve(),null!==p?e=[e,n,r,l,o,u,c,h,f,d,_,p]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=me(),null!==n?(58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?(58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?(58===t.charCodeAt(s)?(c=":",s++):(c=null,0===i&&a('":"')),null!==c?(h=me(),null!==h?(58===t.charCodeAt(s)?(f=":",s++):(f=null,0===i&&a('":"')),null!==f?(d=ve(),null!==d?e=[e,n,r,l,o,u,c,h,f,d]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=me(),null!==n?(58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?(58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?(58===t.charCodeAt(s)?(c=":",s++):(c=null,0===i&&a('":"')),null!==c?(h=ve(),null!==h?e=[e,n,r,l,o,u,c,h]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=me(),null!==n?(58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?(58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=ve(),null!==u?e=[e,n,r,l,o,u]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=me(),null!==n?(58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=ve(),null!==l?e=[e,n,r,l]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=ve(),null!==n?e=[e,n]:(e=null,s=g)):(e=null,s=g),null===e&&(g=s,"::"===t.substr(s,2)?(e="::",s+=2):(e=null,0===i&&a('"::"')),null!==e?(n=me(),null!==n?e=[e,n]:(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?("::"===t.substr(s,2)?(n="::",s+=2):(n=null,0===i&&a('"::"')),null!==n?(r=me(),null!==r?(58===t.charCodeAt(s)?(l=":",s++):(l=null,0===i&&a('":"')),null!==l?(o=me(),null!==o?(58===t.charCodeAt(s)?(u=":",s++):(u=null,0===i&&a('":"')),null!==u?(c=me(),null!==c?(58===t.charCodeAt(s)?(h=":",s++):(h=null,0===i&&a('":"')),null!==h?(f=me(),null!==f?(58===t.charCodeAt(s)?(d=":",s++):(d=null,0===i&&a('":"')),null!==d?(_=ve(),null!==_?e=[e,n,r,l,o,u,c,h,f,d,_]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?(y=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?n=[n,r]:(n=null,s=y)):(n=null,s=y),n=null!==n?n:"",null!==n?("::"===t.substr(s,2)?(r="::",s+=2):(r=null,0===i&&a('"::"')),null!==r?(l=me(),null!==l?(58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?(58===t.charCodeAt(s)?(c=":",s++):(c=null,0===i&&a('":"')),null!==c?(h=me(),null!==h?(58===t.charCodeAt(s)?(f=":",s++):(f=null,0===i&&a('":"')),null!==f?(d=ve(),null!==d?e=[e,n,r,l,o,u,c,h,f,d]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?(y=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?n=[n,r]:(n=null,s=y)):(n=null,s=y),n=null!==n?n:"",null!==n?(y=s,58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?r=[r,l]:(r=null,s=y)):(r=null,s=y),r=null!==r?r:"",null!==r?("::"===t.substr(s,2)?(l="::",s+=2):(l=null,0===i&&a('"::"')),null!==l?(o=me(),null!==o?(58===t.charCodeAt(s)?(u=":",s++):(u=null,0===i&&a('":"')),null!==u?(c=me(),null!==c?(58===t.charCodeAt(s)?(h=":",s++):(h=null,0===i&&a('":"')),null!==h?(f=ve(),null!==f?e=[e,n,r,l,o,u,c,h,f]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?(y=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?n=[n,r]:(n=null,s=y)):(n=null,s=y),n=null!==n?n:"",null!==n?(y=s,58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?r=[r,l]:(r=null,s=y)):(r=null,s=y),r=null!==r?r:"",null!==r?(y=s,58===t.charCodeAt(s)?(l=":",s++):(l=null,0===i&&a('":"')),null!==l?(o=me(),null!==o?l=[l,o]:(l=null,s=y)):(l=null,s=y),l=null!==l?l:"",null!==l?("::"===t.substr(s,2)?(o="::",s+=2):(o=null,0===i&&a('"::"')),null!==o?(u=me(),null!==u?(58===t.charCodeAt(s)?(c=":",s++):(c=null,0===i&&a('":"')),null!==c?(h=ve(),null!==h?e=[e,n,r,l,o,u,c,h]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?(y=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?n=[n,r]:(n=null,s=y)):(n=null,s=y),n=null!==n?n:"",null!==n?(y=s,58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?r=[r,l]:(r=null,s=y)):(r=null,s=y),r=null!==r?r:"",null!==r?(y=s,58===t.charCodeAt(s)?(l=":",s++):(l=null,0===i&&a('":"')),null!==l?(o=me(),null!==o?l=[l,o]:(l=null,s=y)):(l=null,s=y),l=null!==l?l:"",null!==l?(y=s,58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?o=[o,u]:(o=null,s=y)):(o=null,s=y),o=null!==o?o:"",null!==o?("::"===t.substr(s,2)?(u="::",s+=2):(u=null,0===i&&a('"::"')),null!==u?(c=ve(),null!==c?e=[e,n,r,l,o,u,c]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?(y=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?n=[n,r]:(n=null,s=y)):(n=null,s=y),n=null!==n?n:"",null!==n?(y=s,58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?r=[r,l]:(r=null,s=y)):(r=null,s=y),r=null!==r?r:"",null!==r?(y=s,58===t.charCodeAt(s)?(l=":",s++):(l=null,0===i&&a('":"')),null!==l?(o=me(),null!==o?l=[l,o]:(l=null,s=y)):(l=null,s=y),l=null!==l?l:"",null!==l?(y=s,58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?o=[o,u]:(o=null,s=y)):(o=null,s=y),o=null!==o?o:"",null!==o?(y=s,58===t.charCodeAt(s)?(u=":",s++):(u=null,0===i&&a('":"')),null!==u?(c=me(),null!==c?u=[u,c]:(u=null,s=y)):(u=null,s=y),u=null!==u?u:"",null!==u?("::"===t.substr(s,2)?(c="::",s+=2):(c=null,0===i&&a('"::"')),null!==c?(h=me(),null!==h?e=[e,n,r,l,o,u,c,h]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g),null===e&&(g=s,e=me(),null!==e?(y=s,58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?n=[n,r]:(n=null,s=y)):(n=null,s=y),n=null!==n?n:"",null!==n?(y=s,58===t.charCodeAt(s)?(r=":",s++):(r=null,0===i&&a('":"')),null!==r?(l=me(),null!==l?r=[r,l]:(r=null,s=y)):(r=null,s=y),r=null!==r?r:"",null!==r?(y=s,58===t.charCodeAt(s)?(l=":",s++):(l=null,0===i&&a('":"')),null!==l?(o=me(),null!==o?l=[l,o]:(l=null,s=y)):(l=null,s=y),l=null!==l?l:"",null!==l?(y=s,58===t.charCodeAt(s)?(o=":",s++):(o=null,0===i&&a('":"')),null!==o?(u=me(),null!==u?o=[o,u]:(o=null,s=y)):(o=null,s=y),o=null!==o?o:"",null!==o?(y=s,58===t.charCodeAt(s)?(u=":",s++):(u=null,0===i&&a('":"')),null!==u?(c=me(),null!==c?u=[u,c]:(u=null,s=y)):(u=null,s=y),u=null!==u?u:"",null!==u?(y=s,58===t.charCodeAt(s)?(c=":",s++):(c=null,0===i&&a('":"')),null!==c?(h=me(),null!==h?c=[c,h]:(c=null,s=y)):(c=null,s=y),c=null!==c?c:"",null!==c?("::"===t.substr(s,2)?(h="::",s+=2):(h=null,0===i&&a('"::"')),null!==h?e=[e,n,r,l,o,u,c,h]:(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g)):(e=null,s=g))))))))))))))),null!==e&&(e=function(e){return Ar.host_type="IPv6",t.substring(s,e)}(v)),null===e&&(s=v),e}function me(){var e,t,n,r,l;return l=s,e=d(),null!==e?(t=d(),t=null!==t?t:"",null!==t?(n=d(),n=null!==n?n:"",null!==n?(r=d(),r=null!==r?r:"",null!==r?e=[e,t,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function ve(){var e,n,r,l;return l=s,e=me(),null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=me(),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),null===e&&(e=ge()),e}function ge(){var e,n,r,l,o,u,c,h,f;return h=s,f=s,e=ye(),null!==e?(46===t.charCodeAt(s)?(n=".",s++):(n=null,0===i&&a('"."')),null!==n?(r=ye(),null!==r?(46===t.charCodeAt(s)?(l=".",s++):(l=null,0===i&&a('"."')),null!==l?(o=ye(),null!==o?(46===t.charCodeAt(s)?(u=".",s++):(u=null,0===i&&a('"."')),null!==u?(c=ye(),null!==c?e=[e,n,r,l,o,u,c]:(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f)):(e=null,s=f),null!==e&&(e=function(e){return Ar.host_type="IPv4",t.substring(s,e)}(h)),null===e&&(s=h),e}function ye(){var e,n,r,l;return l=s,"25"===t.substr(s,2)?(e="25",s+=2):(e=null,0===i&&a('"25"')),null!==e?(/^[0-5]/.test(t.charAt(s))?(n=t.charAt(s),s++):(n=null,0===i&&a("[0-5]")),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null===e&&(l=s,50===t.charCodeAt(s)?(e="2",s++):(e=null,0===i&&a('"2"')),null!==e?(/^[0-4]/.test(t.charAt(s))?(n=t.charAt(s),s++):(n=null,0===i&&a("[0-4]")),null!==n?(r=h(),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),null===e&&(l=s,49===t.charCodeAt(s)?(e="1",s++):(e=null,0===i&&a('"1"')),null!==e?(n=h(),null!==n?(r=h(),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),null===e&&(l=s,/^[1-9]/.test(t.charAt(s))?(e=t.charAt(s),s++):(e=null,0===i&&a("[1-9]")),null!==e?(n=h(),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null===e&&(e=h())))),e}function Te(){var e,t,n,r,l,i,o;return i=s,o=s,e=h(),e=null!==e?e:"",null!==e?(t=h(),t=null!==t?t:"",null!==t?(n=h(),n=null!==n?n:"",null!==n?(r=h(),r=null!==r?r:"",null!==r?(l=h(),l=null!==l?l:"",null!==l?e=[e,t,n,r,l]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){return t=parseInt(t.join("")),Ar.port=t,t}(0,e)),null===e&&(s=i),e}function Se(){var e,n,r,l;e=[],l=s,59===t.charCodeAt(s)?(n=";",s++):(n=null,0===i&&a('";"')),null!==n?(r=Ce(),null!==r?n=[n,r]:(n=null,s=l)):(n=null,s=l);while(null!==n)e.push(n),l=s,59===t.charCodeAt(s)?(n=";",s++):(n=null,0===i&&a('";"')),null!==n?(r=Ce(),null!==r?n=[n,r]:(n=null,s=l)):(n=null,s=l);return e}function Ce(){var e;return e=Ee(),null===e&&(e=be(),null===e&&(e=Ae(),null===e&&(e=Re(),null===e&&(e=we(),null===e&&(e=Ie(),null===e&&(e=Oe())))))),e}function Ee(){var e,n,r,l;return r=s,l=s,"transport="===t.substr(s,10).toLowerCase()?(e=t.substr(s,10),s+=10):(e=null,0===i&&a('"transport="')),null!==e?("udp"===t.substr(s,3).toLowerCase()?(n=t.substr(s,3),s+=3):(n=null,0===i&&a('"udp"')),null===n&&("tcp"===t.substr(s,3).toLowerCase()?(n=t.substr(s,3),s+=3):(n=null,0===i&&a('"tcp"')),null===n&&("sctp"===t.substr(s,4).toLowerCase()?(n=t.substr(s,4),s+=4):(n=null,0===i&&a('"sctp"')),null===n&&("tls"===t.substr(s,3).toLowerCase()?(n=t.substr(s,3),s+=3):(n=null,0===i&&a('"tls"')),null===n&&(n=P())))),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t){Ar.uri_params||(Ar.uri_params={}),Ar.uri_params["transport"]=t.toLowerCase()}(0,e[1])),null===e&&(s=r),e}function be(){var e,n,r,l;return r=s,l=s,"user="===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"user="')),null!==e?("phone"===t.substr(s,5).toLowerCase()?(n=t.substr(s,5),s+=5):(n=null,0===i&&a('"phone"')),null===n&&("ip"===t.substr(s,2).toLowerCase()?(n=t.substr(s,2),s+=2):(n=null,0===i&&a('"ip"')),null===n&&(n=P())),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t){Ar.uri_params||(Ar.uri_params={}),Ar.uri_params["user"]=t.toLowerCase()}(0,e[1])),null===e&&(s=r),e}function Ae(){var e,n,r,l;return r=s,l=s,"method="===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"method="')),null!==e?(n=_t(),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t){Ar.uri_params||(Ar.uri_params={}),Ar.uri_params["method"]=t}(0,e[1])),null===e&&(s=r),e}function Re(){var e,n,r,l;return r=s,l=s,"ttl="===t.substr(s,4).toLowerCase()?(e=t.substr(s,4),s+=4):(e=null,0===i&&a('"ttl="')),null!==e?(n=er(),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t){Ar.params||(Ar.params={}),Ar.params["ttl"]=t}(0,e[1])),null===e&&(s=r),e}function we(){var e,n,r,l;return r=s,l=s,"maddr="===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"maddr="')),null!==e?(n=ce(),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t){Ar.uri_params||(Ar.uri_params={}),Ar.uri_params["maddr"]=t}(0,e[1])),null===e&&(s=r),e}function Ie(){var e,n,r,l,o,u;return l=s,o=s,"lr"===t.substr(s,2).toLowerCase()?(e=t.substr(s,2),s+=2):(e=null,0===i&&a('"lr"')),null!==e?(u=s,61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){Ar.uri_params||(Ar.uri_params={}),Ar.uri_params["lr"]=void 0}()),null===e&&(s=l),e}function Oe(){var e,n,r,l,o,u;return l=s,o=s,e=ke(),null!==e?(u=s,61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null!==n?(r=Ne(),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t,n){Ar.uri_params||(Ar.uri_params={}),n="undefined"===typeof n?void 0:n[1],Ar.uri_params[t.toLowerCase()]=n}(0,e[0],e[1])),null===e&&(s=l),e}function ke(){var e,t,n;if(n=s,t=Pe(),null!==t){e=[];while(null!==t)e.push(t),t=Pe()}else e=null;return null!==e&&(e=function(e,t){return t.join("")}(0,e)),null===e&&(s=n),e}function Ne(){var e,t,n;if(n=s,t=Pe(),null!==t){e=[];while(null!==t)e.push(t),t=Pe()}else e=null;return null!==e&&(e=function(e,t){return t.join("")}(0,e)),null===e&&(s=n),e}function Pe(){var e;return e=De(),null===e&&(e=S(),null===e&&(e=E())),e}function De(){var e;return 91===t.charCodeAt(s)?(e="[",s++):(e=null,0===i&&a('"["')),null===e&&(93===t.charCodeAt(s)?(e="]",s++):(e=null,0===i&&a('"]"')),null===e&&(47===t.charCodeAt(s)?(e="/",s++):(e=null,0===i&&a('"/"')),null===e&&(58===t.charCodeAt(s)?(e=":",s++):(e=null,0===i&&a('":"')),null===e&&(38===t.charCodeAt(s)?(e="&",s++):(e=null,0===i&&a('"&"')),null===e&&(43===t.charCodeAt(s)?(e="+",s++):(e=null,0===i&&a('"+"')),null===e&&(36===t.charCodeAt(s)?(e="$",s++):(e=null,0===i&&a('"$"')))))))),e}function xe(){var e,n,r,l,o,u,c;if(u=s,63===t.charCodeAt(s)?(e="?",s++):(e=null,0===i&&a('"?"')),null!==e)if(n=Ue(),null!==n){r=[],c=s,38===t.charCodeAt(s)?(l="&",s++):(l=null,0===i&&a('"&"')),null!==l?(o=Ue(),null!==o?l=[l,o]:(l=null,s=c)):(l=null,s=c);while(null!==l)r.push(l),c=s,38===t.charCodeAt(s)?(l="&",s++):(l=null,0===i&&a('"&"')),null!==l?(o=Ue(),null!==o?l=[l,o]:(l=null,s=c)):(l=null,s=c);null!==r?e=[e,n,r]:(e=null,s=u)}else e=null,s=u;else e=null,s=u;return e}function Ue(){var e,n,r,l,o;return l=s,o=s,e=Me(),null!==e?(61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null!==n?(r=Le(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t,n){t=t.join("").toLowerCase(),n=n.join(""),Ar.uri_headers||(Ar.uri_headers={}),Ar.uri_headers[t]?Ar.uri_headers[t].push(n):Ar.uri_headers[t]=[n]}(0,e[0],e[2])),null===e&&(s=l),e}function Me(){var e,t;if(t=qe(),null===t&&(t=S(),null===t&&(t=E())),null!==t){e=[];while(null!==t)e.push(t),t=qe(),null===t&&(t=S(),null===t&&(t=E()))}else e=null;return e}function Le(){var e,t;e=[],t=qe(),null===t&&(t=S(),null===t&&(t=E()));while(null!==t)e.push(t),t=qe(),null===t&&(t=S(),null===t&&(t=E()));return e}function qe(){var e;return 91===t.charCodeAt(s)?(e="[",s++):(e=null,0===i&&a('"["')),null===e&&(93===t.charCodeAt(s)?(e="]",s++):(e=null,0===i&&a('"]"')),null===e&&(47===t.charCodeAt(s)?(e="/",s++):(e=null,0===i&&a('"/"')),null===e&&(63===t.charCodeAt(s)?(e="?",s++):(e=null,0===i&&a('"?"')),null===e&&(58===t.charCodeAt(s)?(e=":",s++):(e=null,0===i&&a('":"')),null===e&&(43===t.charCodeAt(s)?(e="+",s++):(e=null,0===i&&a('"+"')),null===e&&(36===t.charCodeAt(s)?(e="$",s++):(e=null,0===i&&a('"$"')))))))),e}function He(){var e;return e=pt(),null===e&&(e=Fe()),e}function Fe(){var e,t,n,r,l,i;return i=s,e=_t(),null!==e?(t=v(),null!==t?(n=je(),null!==n?(r=v(),null!==r?(l=lt(),null!==l?e=[e,t,n,r,l]:(e=null,s=i)):(e=null,s=i)):(e=null,s=i)):(e=null,s=i)):(e=null,s=i),e}function je(){var e;return e=te(),null===e&&(e=Ve()),e}function Ve(){var e,n,r,l;return l=s,e=Ze(),null!==e?(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null!==n?(r=Ge(),null===r&&(r=Ke()),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function Ge(){var e,n,r,l,o;return l=s,e=We(),null===e&&(e=Be()),null!==e?(o=s,63===t.charCodeAt(s)?(n="?",s++):(n=null,0===i&&a('"?"')),null!==n?(r=rt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),e}function We(){var e,n,r,l;return l=s,"//"===t.substr(s,2)?(e="//",s+=2):(e=null,0===i&&a('"//"')),null!==e?(n=et(),null!==n?(r=Be(),r=null!==r?r:"",null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function Be(){var e,n,r;return r=s,47===t.charCodeAt(s)?(e="/",s++):(e=null,0===i&&a('"/"')),null!==e?(n=Je(),null!==n?e=[e,n]:(e=null,s=r)):(e=null,s=r),e}function Ke(){var e,t,n,r;if(r=s,e=$e(),null!==e){t=[],n=Ye();while(null!==n)t.push(n),n=Ye();null!==t?e=[e,t]:(e=null,s=r)}else e=null,s=r;return e}function Ye(){var e;return e=T(),null===e&&(e=S(),null===e&&(e=E())),e}function $e(){var e;return e=S(),null===e&&(e=E(),null===e&&(59===t.charCodeAt(s)?(e=";",s++):(e=null,0===i&&a('";"')),null===e&&(63===t.charCodeAt(s)?(e="?",s++):(e=null,0===i&&a('"?"')),null===e&&(58===t.charCodeAt(s)?(e=":",s++):(e=null,0===i&&a('":"')),null===e&&(64===t.charCodeAt(s)?(e="@",s++):(e=null,0===i&&a('"@"')),null===e&&(38===t.charCodeAt(s)?(e="&",s++):(e=null,0===i&&a('"&"')),null===e&&(61===t.charCodeAt(s)?(e="=",s++):(e=null,0===i&&a('"="')),null===e&&(43===t.charCodeAt(s)?(e="+",s++):(e=null,0===i&&a('"+"')),null===e&&(36===t.charCodeAt(s)?(e="$",s++):(e=null,0===i&&a('"$"')),null===e&&(44===t.charCodeAt(s)?(e=",",s++):(e=null,0===i&&a('","')))))))))))),e}function Je(){var e,n,r,l,o,u;if(o=s,e=ze(),null!==e){n=[],u=s,47===t.charCodeAt(s)?(r="/",s++):(r=null,0===i&&a('"/"')),null!==r?(l=ze(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);while(null!==r)n.push(r),u=s,47===t.charCodeAt(s)?(r="/",s++):(r=null,0===i&&a('"/"')),null!==r?(l=ze(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);null!==n?e=[e,n]:(e=null,s=o)}else e=null,s=o;return e}function ze(){var e,n,r,l,o,u;o=s,e=[],n=Qe();while(null!==n)e.push(n),n=Qe();if(null!==e){n=[],u=s,59===t.charCodeAt(s)?(r=";",s++):(r=null,0===i&&a('";"')),null!==r?(l=Xe(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);while(null!==r)n.push(r),u=s,59===t.charCodeAt(s)?(r=";",s++):(r=null,0===i&&a('";"')),null!==r?(l=Xe(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);null!==n?e=[e,n]:(e=null,s=o)}else e=null,s=o;return e}function Xe(){var e,t;e=[],t=Qe();while(null!==t)e.push(t),t=Qe();return e}function Qe(){var e;return e=S(),null===e&&(e=E(),null===e&&(58===t.charCodeAt(s)?(e=":",s++):(e=null,0===i&&a('":"')),null===e&&(64===t.charCodeAt(s)?(e="@",s++):(e=null,0===i&&a('"@"')),null===e&&(38===t.charCodeAt(s)?(e="&",s++):(e=null,0===i&&a('"&"')),null===e&&(61===t.charCodeAt(s)?(e="=",s++):(e=null,0===i&&a('"="')),null===e&&(43===t.charCodeAt(s)?(e="+",s++):(e=null,0===i&&a('"+"')),null===e&&(36===t.charCodeAt(s)?(e="$",s++):(e=null,0===i&&a('"$"')),null===e&&(44===t.charCodeAt(s)?(e=",",s++):(e=null,0===i&&a('","')))))))))),e}function Ze(){var e,n,r,l,o;if(l=s,o=s,e=f(),null!==e){n=[],r=f(),null===r&&(r=h(),null===r&&(43===t.charCodeAt(s)?(r="+",s++):(r=null,0===i&&a('"+"')),null===r&&(45===t.charCodeAt(s)?(r="-",s++):(r=null,0===i&&a('"-"')),null===r&&(46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."'))))));while(null!==r)n.push(r),r=f(),null===r&&(r=h(),null===r&&(43===t.charCodeAt(s)?(r="+",s++):(r=null,0===i&&a('"+"')),null===r&&(45===t.charCodeAt(s)?(r="-",s++):(r=null,0===i&&a('"-"')),null===r&&(46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."'))))));null!==n?e=[e,n]:(e=null,s=o)}else e=null,s=o;return null!==e&&(e=function(e){Ar.scheme=t.substring(s,e)}(l)),null===e&&(s=l),e}function et(){var e;return e=tt(),null===e&&(e=nt()),e}function tt(){var e,n,r,l;return r=s,l=s,e=se(),null!==e?(64===t.charCodeAt(s)?(n="@",s++):(n=null,0===i&&a('"@"')),null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),e=null!==e?e:"",null!==e?(n=ae(),null!==n?e=[e,n]:(e=null,s=r)):(e=null,s=r),e=null!==e?e:"",e}function nt(){var e,n;if(n=S(),null===n&&(n=E(),null===n&&(36===t.charCodeAt(s)?(n="$",s++):(n=null,0===i&&a('"$"')),null===n&&(44===t.charCodeAt(s)?(n=",",s++):(n=null,0===i&&a('","')),null===n&&(59===t.charCodeAt(s)?(n=";",s++):(n=null,0===i&&a('";"')),null===n&&(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null===n&&(64===t.charCodeAt(s)?(n="@",s++):(n=null,0===i&&a('"@"')),null===n&&(38===t.charCodeAt(s)?(n="&",s++):(n=null,0===i&&a('"&"')),null===n&&(61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"'))))))))))),null!==n){e=[];while(null!==n)e.push(n),n=S(),null===n&&(n=E(),null===n&&(36===t.charCodeAt(s)?(n="$",s++):(n=null,0===i&&a('"$"')),null===n&&(44===t.charCodeAt(s)?(n=",",s++):(n=null,0===i&&a('","')),null===n&&(59===t.charCodeAt(s)?(n=";",s++):(n=null,0===i&&a('";"')),null===n&&(58===t.charCodeAt(s)?(n=":",s++):(n=null,0===i&&a('":"')),null===n&&(64===t.charCodeAt(s)?(n="@",s++):(n=null,0===i&&a('"@"')),null===n&&(38===t.charCodeAt(s)?(n="&",s++):(n=null,0===i&&a('"&"')),null===n&&(61===t.charCodeAt(s)?(n="=",s++):(n=null,0===i&&a('"="')),null===n&&(43===t.charCodeAt(s)?(n="+",s++):(n=null,0===i&&a('"+"')))))))))))}else e=null;return e}function rt(){var e,t;e=[],t=Ye();while(null!==t)e.push(t),t=Ye();return e}function lt(){var e,n,r,l,o,u,c,f;if(c=s,f=s,"sip"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"SIP"')),null!==e)if(47===t.charCodeAt(s)?(n="/",s++):(n=null,0===i&&a('"/"')),null!==n){if(l=h(),null!==l){r=[];while(null!==l)r.push(l),l=h()}else r=null;if(null!==r)if(46===t.charCodeAt(s)?(l=".",s++):(l=null,0===i&&a('"."')),null!==l){if(u=h(),null!==u){o=[];while(null!==u)o.push(u),u=h()}else o=null;null!==o?e=[e,n,r,l,o]:(e=null,s=f)}else e=null,s=f;else e=null,s=f}else e=null,s=f;else e=null,s=f;return null!==e&&(e=function(e){Ar.sip_version=t.substring(s,e)}(c)),null===e&&(s=c),e}function st(){var e;return"INVITE"===t.substr(s,6)?(e="INVITE",s+=6):(e=null,0===i&&a('"INVITE"')),e}function it(){var e;return"ACK"===t.substr(s,3)?(e="ACK",s+=3):(e=null,0===i&&a('"ACK"')),e}function ot(){var e;return"OPTIONS"===t.substr(s,7)?(e="OPTIONS",s+=7):(e=null,0===i&&a('"OPTIONS"')),e}function ut(){var e;return"BYE"===t.substr(s,3)?(e="BYE",s+=3):(e=null,0===i&&a('"BYE"')),e}function at(){var e;return"CANCEL"===t.substr(s,6)?(e="CANCEL",s+=6):(e=null,0===i&&a('"CANCEL"')),e}function ct(){var e;return"REGISTER"===t.substr(s,8)?(e="REGISTER",s+=8):(e=null,0===i&&a('"REGISTER"')),e}function ht(){var e;return"SUBSCRIBE"===t.substr(s,9)?(e="SUBSCRIBE",s+=9):(e=null,0===i&&a('"SUBSCRIBE"')),e}function ft(){var e;return"NOTIFY"===t.substr(s,6)?(e="NOTIFY",s+=6):(e=null,0===i&&a('"NOTIFY"')),e}function dt(){var e;return"REFER"===t.substr(s,5)?(e="REFER",s+=5):(e=null,0===i&&a('"REFER"')),e}function _t(){var e,n;return n=s,e=st(),null===e&&(e=it(),null===e&&(e=ot(),null===e&&(e=ut(),null===e&&(e=at(),null===e&&(e=ct(),null===e&&(e=ht(),null===e&&(e=ft(),null===e&&(e=dt(),null===e&&(e=P()))))))))),null!==e&&(e=function(e){return Ar.method=t.substring(s,e),Ar.method}(n)),null===e&&(s=n),e}function pt(){var e,t,n,r,l,i;return i=s,e=lt(),null!==e?(t=v(),null!==t?(n=mt(),null!==n?(r=v(),null!==r?(l=gt(),null!==l?e=[e,t,n,r,l]:(e=null,s=i)):(e=null,s=i)):(e=null,s=i)):(e=null,s=i)):(e=null,s=i),e}function mt(){var e,t;return t=s,e=vt(),null!==e&&(e=function(e,t){Ar.status_code=parseInt(t.join(""))}(0,e)),null===e&&(s=t),e}function vt(){var e,t,n,r;return r=s,e=h(),null!==e?(t=h(),null!==t?(n=h(),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)):(e=null,s=r),e}function gt(){var e,n,r;r=s,e=[],n=T(),null===n&&(n=S(),null===n&&(n=E(),null===n&&(n=O(),null===n&&(n=k(),null===n&&(n=v(),null===n&&(n=g()))))));while(null!==n)e.push(n),n=T(),null===n&&(n=S(),null===n&&(n=E(),null===n&&(n=O(),null===n&&(n=k(),null===n&&(n=v(),null===n&&(n=g()))))));return null!==e&&(e=function(e){Ar.reason_phrase=t.substring(s,e)}(r)),null===e&&(s=r),e}function yt(){var e,t,n,r,l,i;if(l=s,e=Qt(),null!==e){t=[],i=s,n=G(),null!==n?(r=Qt(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=Qt(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function Tt(){var e,n,r,l,o,u;return l=s,o=s,e=U(),null!==e?(u=s,64===t.charCodeAt(s)?(n="@",s++):(n=null,0===i&&a('"@"')),null!==n?(r=U(),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){Ar=t.substring(s,e)}(l)),null===e&&(s=l),e}function St(){var e,t,n,r,l,i,o;if(l=s,e=M(),null===e)if(i=s,e=Ct(),null!==e){t=[],o=s,n=G(),null!==n?(r=Ct(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=G(),null!==n?(r=Ct(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){var t,n;for(n=Ar.multi_header.length,t=0;t<n;t++)if(null===Ar.multi_header[t].parsed){Ar=null;break}Ar=null!==Ar?Ar.multi_header:-1}()),null===e&&(s=l),e}function Ct(){var e,t,n,r,l,i,o;if(l=s,i=s,e=ee(),null===e&&(e=Et()),null!==e){t=[],o=s,n=W(),null!==n?(r=At(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=W(),null!==n?(r=At(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){var t;Ar.multi_header||(Ar.multi_header=[]);try{t=new br(Ar.uri,Ar.display_name,Ar.params),delete Ar.uri,delete Ar.display_name,delete Ar.params}catch(n){t=null}Ar.multi_header.push({possition:s,offset:e,parsed:t})}(l)),null===e&&(s=l),e}function Et(){var e,t,n,r,l;return l=s,e=bt(),e=null!==e?e:"",null!==e?(t=V(),null!==t?(n=te(),null!==n?(r=j(),null!==r?e=[e,t,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function bt(){var e,t,n,r,l,i,o;if(l=s,i=s,e=P(),null!==e){t=[],o=s,n=b(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=b(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null===e&&(e=X()),null!==e&&(e=function(e,t){Ar.display_name="string"===typeof t?t:t[1].reduce((function(e,t){return e+t[0]+t[1]}),t[0])}(0,e)),null===e&&(s=l),e}function At(){var e;return e=Rt(),null===e&&(e=wt(),null===e&&(e=kt())),e}function Rt(){var e,n,r,l,o;return l=s,o=s,"q"===t.substr(s,1).toLowerCase()?(e=t.substr(s,1),s++):(e=null,0===i&&a('"q"')),null!==e?(n=q(),null!==n?(r=Ot(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.params||(Ar.params={}),Ar.params["q"]=t}(0,e[2])),null===e&&(s=l),e}function wt(){var e,n,r,l,o;return l=s,o=s,"expires"===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"expires"')),null!==e?(n=q(),null!==n?(r=It(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.params||(Ar.params={}),Ar.params["expires"]=t}(0,e[2])),null===e&&(s=l),e}function It(){var e,t,n;if(n=s,t=h(),null!==t){e=[];while(null!==t)e.push(t),t=h()}else e=null;return null!==e&&(e=function(e,t){return parseInt(t.join(""))}(0,e)),null===e&&(s=n),e}function Ot(){var e,n,r,l,o,u,c,f;return u=s,c=s,48===t.charCodeAt(s)?(e="0",s++):(e=null,0===i&&a('"0"')),null!==e?(f=s,46===t.charCodeAt(s)?(n=".",s++):(n=null,0===i&&a('"."')),null!==n?(r=h(),r=null!==r?r:"",null!==r?(l=h(),l=null!==l?l:"",null!==l?(o=h(),o=null!==o?o:"",null!==o?n=[n,r,l,o]:(n=null,s=f)):(n=null,s=f)):(n=null,s=f)):(n=null,s=f),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=c)):(e=null,s=c),null!==e&&(e=function(e){return parseFloat(t.substring(s,e))}(u)),null===e&&(s=u),e}function kt(){var e,t,n,r,l,i;return r=s,l=s,e=P(),null!==e?(i=s,t=q(),null!==t?(n=Nt(),null!==n?t=[t,n]:(t=null,s=i)):(t=null,s=i),t=null!==t?t:"",null!==t?e=[e,t]:(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t,n){Ar.params||(Ar.params={}),n="undefined"===typeof n?void 0:n[1],Ar.params[t.toLowerCase()]=n}(0,e[0],e[1])),null===e&&(s=r),e}function Nt(){var e;return e=P(),null===e&&(e=ce(),null===e&&(e=z())),e}function Pt(){var e,t,n,r,l,i;if(l=s,e=Dt(),null!==e){t=[],i=s,n=W(),null!==n?(r=xt(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=W(),null!==n?(r=xt(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function Dt(){var e;return"render"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"render"')),null===e&&("session"===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"session"')),null===e&&("icon"===t.substr(s,4).toLowerCase()?(e=t.substr(s,4),s+=4):(e=null,0===i&&a('"icon"')),null===e&&("alert"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"alert"')),null===e&&(e=P())))),e}function xt(){var e;return e=Ut(),null===e&&(e=kt()),e}function Ut(){var e,n,r,l;return l=s,"handling"===t.substr(s,8).toLowerCase()?(e=t.substr(s,8),s+=8):(e=null,0===i&&a('"handling"')),null!==e?(n=q(),null!==n?("optional"===t.substr(s,8).toLowerCase()?(r=t.substr(s,8),s+=8):(r=null,0===i&&a('"optional"')),null===r&&("required"===t.substr(s,8).toLowerCase()?(r=t.substr(s,8),s+=8):(r=null,0===i&&a('"required"')),null===r&&(r=P())),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function Mt(){var e,t,n,r,l,i;if(l=s,e=P(),null!==e){t=[],i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function Lt(){var e,t,n;if(n=s,t=h(),null!==t){e=[];while(null!==t)e.push(t),t=h()}else e=null;return null!==e&&(e=function(e,t){Ar=parseInt(t.join(""))}(0,e)),null===e&&(s=n),e}function qt(){var e,n;return n=s,e=Ht(),null!==e&&(e=function(e){Ar=t.substring(s,e)}(n)),null===e&&(s=n),e}function Ht(){var e,t,n,r,l,i,o,u;if(o=s,e=Ft(),null!==e)if(t=L(),null!==t)if(n=Bt(),null!==n){r=[],u=s,l=W(),null!==l?(i=Kt(),null!==i?l=[l,i]:(l=null,s=u)):(l=null,s=u);while(null!==l)r.push(l),u=s,l=W(),null!==l?(i=Kt(),null!==i?l=[l,i]:(l=null,s=u)):(l=null,s=u);null!==r?e=[e,t,n,r]:(e=null,s=o)}else e=null,s=o;else e=null,s=o;else e=null,s=o;return e}function Ft(){var e;return e=jt(),null===e&&(e=Vt()),e}function jt(){var e;return"text"===t.substr(s,4).toLowerCase()?(e=t.substr(s,4),s+=4):(e=null,0===i&&a('"text"')),null===e&&("image"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"image"')),null===e&&("audio"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"audio"')),null===e&&("video"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"video"')),null===e&&("application"===t.substr(s,11).toLowerCase()?(e=t.substr(s,11),s+=11):(e=null,0===i&&a('"application"')),null===e&&(e=Gt()))))),e}function Vt(){var e;return"message"===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"message"')),null===e&&("multipart"===t.substr(s,9).toLowerCase()?(e=t.substr(s,9),s+=9):(e=null,0===i&&a('"multipart"')),null===e&&(e=Gt())),e}function Gt(){var e;return e=P(),null===e&&(e=Wt()),e}function Wt(){var e,n,r;return r=s,"x-"===t.substr(s,2).toLowerCase()?(e=t.substr(s,2),s+=2):(e=null,0===i&&a('"x-"')),null!==e?(n=P(),null!==n?e=[e,n]:(e=null,s=r)):(e=null,s=r),e}function Bt(){var e;return e=Gt(),null===e&&(e=P()),e}function Kt(){var e,t,n,r;return r=s,e=P(),null!==e?(t=q(),null!==t?(n=Yt(),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)):(e=null,s=r),e}function Yt(){var e;return e=P(),null===e&&(e=z()),e}function $t(){var e,t,n,r;return r=s,e=Jt(),null!==e?(t=b(),null!==t?(n=_t(),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)):(e=null,s=r),e}function Jt(){var e,t,n;if(n=s,t=h(),null!==t){e=[];while(null!==t)e.push(t),t=h()}else e=null;return null!==e&&(e=function(e,t){Ar.value=parseInt(t.join(""))}(0,e)),null===e&&(s=n),e}function zt(){var e,t;return t=s,e=It(),null!==e&&(e=function(e,t){Ar=t}(0,e)),null===e&&(s=t),e}function Xt(){var e,t,n,r,l,i,o;if(l=s,i=s,e=Qt(),null!==e){t=[],o=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e,t){Ar.event=t.join("").toLowerCase()}(0,e[0])),null===e&&(s=l),e}function Qt(){var e,n,r,l,o,u;if(o=s,e=D(),null!==e){n=[],u=s,46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."')),null!==r?(l=D(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);while(null!==r)n.push(r),u=s,46===t.charCodeAt(s)?(r=".",s++):(r=null,0===i&&a('"."')),null!==r?(l=D(),null!==l?r=[r,l]:(r=null,s=u)):(r=null,s=u);null!==n?e=[e,n]:(e=null,s=o)}else e=null,s=o;return e}function Zt(){var e,t,n,r,l,i,o;if(l=s,i=s,e=ee(),null===e&&(e=Et()),null!==e){t=[],o=s,n=W(),null!==n?(r=en(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=W(),null!==n?(r=en(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){var t=Ar.tag;try{Ar=new br(Ar.uri,Ar.display_name,Ar.params),t&&Ar.setParam("tag",t)}catch(n){Ar=-1}}()),null===e&&(s=l),e}function en(){var e;return e=tn(),null===e&&(e=kt()),e}function tn(){var e,n,r,l,o;return l=s,o=s,"tag"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"tag"')),null!==e?(n=q(),null!==n?(r=P(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.tag=t}(0,e[2])),null===e&&(s=l),e}function nn(){var e,t,n;if(n=s,t=h(),null!==t){e=[];while(null!==t)e.push(t),t=h()}else e=null;return null!==e&&(e=function(e,t){Ar=parseInt(t.join(""))}(0,e)),null===e&&(s=n),e}function rn(){var e,t;return t=s,e=It(),null!==e&&(e=function(e,t){Ar=t}(0,e)),null===e&&(s=t),e}function ln(){var e,t,n,r,l,i,o,u,a,c;u=s,a=s,e=[],t=bt();while(null!==t)e.push(t),t=bt();if(null!==e)if(t=V(),null!==t)if(n=te(),null!==n)if(r=j(),null!==r){l=[],c=s,i=W(),null!==i?(o=kt(),null!==o?i=[i,o]:(i=null,s=c)):(i=null,s=c);while(null!==i)l.push(i),c=s,i=W(),null!==i?(o=kt(),null!==o?i=[i,o]:(i=null,s=c)):(i=null,s=c);null!==l?e=[e,t,n,r,l]:(e=null,s=a)}else e=null,s=a;else e=null,s=a;else e=null,s=a;else e=null,s=a;return null!==e&&(e=function(e){try{Ar=new br(Ar.uri,Ar.display_name,Ar.params)}catch(t){Ar=-1}}()),null===e&&(s=u),e}function sn(){var e;return e=on(),e}function on(){var e,n,r,l,o,u,c,h;if(c=s,"digest"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"Digest"')),null!==e)if(n=b(),null!==n)if(r=cn(),null!==r){l=[],h=s,o=G(),null!==o?(u=cn(),null!==u?o=[o,u]:(o=null,s=h)):(o=null,s=h);while(null!==o)l.push(o),h=s,o=G(),null!==o?(u=cn(),null!==u?o=[o,u]:(o=null,s=h)):(o=null,s=h);null!==l?e=[e,n,r,l]:(e=null,s=c)}else e=null,s=c;else e=null,s=c;else e=null,s=c;return null===e&&(e=un()),e}function un(){var e,t,n,r,l,i,o,u;if(o=s,e=P(),null!==e)if(t=b(),null!==t)if(n=an(),null!==n){r=[],u=s,l=G(),null!==l?(i=an(),null!==i?l=[l,i]:(l=null,s=u)):(l=null,s=u);while(null!==l)r.push(l),u=s,l=G(),null!==l?(i=an(),null!==i?l=[l,i]:(l=null,s=u)):(l=null,s=u);null!==r?e=[e,t,n,r]:(e=null,s=o)}else e=null,s=o;else e=null,s=o;else e=null,s=o;return e}function an(){var e,t,n,r;return r=s,e=P(),null!==e?(t=q(),null!==t?(n=P(),null===n&&(n=z()),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)):(e=null,s=r),e}function cn(){var e;return e=hn(),null===e&&(e=dn(),null===e&&(e=pn(),null===e&&(e=vn(),null===e&&(e=gn(),null===e&&(e=yn(),null===e&&(e=Tn(),null===e&&(e=an()))))))),e}function hn(){var e,n,r,l;return l=s,"realm"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"realm"')),null!==e?(n=q(),null!==n?(r=fn(),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function fn(){var e,t;return t=s,e=X(),null!==e&&(e=function(e,t){Ar.realm=t}(0,e)),null===e&&(s=t),e}function dn(){var e,n,r,l,o,u,c,h,f;if(h=s,"domain"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"domain"')),null!==e)if(n=q(),null!==n)if(r=K(),null!==r)if(l=_n(),null!==l){if(o=[],f=s,c=v(),null!==c){u=[];while(null!==c)u.push(c),c=v()}else u=null;null!==u?(c=_n(),null!==c?u=[u,c]:(u=null,s=f)):(u=null,s=f);while(null!==u){if(o.push(u),f=s,c=v(),null!==c){u=[];while(null!==c)u.push(c),c=v()}else u=null;null!==u?(c=_n(),null!==c?u=[u,c]:(u=null,s=f)):(u=null,s=f)}null!==o?(u=Y(),null!==u?e=[e,n,r,l,o,u]:(e=null,s=h)):(e=null,s=h)}else e=null,s=h;else e=null,s=h;else e=null,s=h;else e=null,s=h;return e}function _n(){var e;return e=Ve(),null===e&&(e=Be()),e}function pn(){var e,n,r,l;return l=s,"nonce"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"nonce"')),null!==e?(n=q(),null!==n?(r=mn(),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function mn(){var e,t;return t=s,e=X(),null!==e&&(e=function(e,t){Ar.nonce=t}(0,e)),null===e&&(s=t),e}function vn(){var e,n,r,l,o;return l=s,o=s,"opaque"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"opaque"')),null!==e?(n=q(),null!==n?(r=X(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.opaque=t}(0,e[2])),null===e&&(s=l),e}function gn(){var e,n,r,l,o;return l=s,"stale"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"stale"')),null!==e?(n=q(),null!==n?(o=s,"true"===t.substr(s,4).toLowerCase()?(r=t.substr(s,4),s+=4):(r=null,0===i&&a('"true"')),null!==r&&(r=function(e){Ar.stale=!0}()),null===r&&(s=o),null===r&&(o=s,"false"===t.substr(s,5).toLowerCase()?(r=t.substr(s,5),s+=5):(r=null,0===i&&a('"false"')),null!==r&&(r=function(e){Ar.stale=!1}()),null===r&&(s=o)),null!==r?e=[e,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function yn(){var e,n,r,l,o;return l=s,o=s,"algorithm"===t.substr(s,9).toLowerCase()?(e=t.substr(s,9),s+=9):(e=null,0===i&&a('"algorithm"')),null!==e?(n=q(),null!==n?("md5"===t.substr(s,3).toLowerCase()?(r=t.substr(s,3),s+=3):(r=null,0===i&&a('"MD5"')),null===r&&("md5-sess"===t.substr(s,8).toLowerCase()?(r=t.substr(s,8),s+=8):(r=null,0===i&&a('"MD5-sess"')),null===r&&(r=P())),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.algorithm=t.toUpperCase()}(0,e[2])),null===e&&(s=l),e}function Tn(){var e,n,r,l,o,u,c,h,f,d;if(h=s,"qop"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"qop"')),null!==e)if(n=q(),null!==n)if(r=K(),null!==r){if(f=s,l=Sn(),null!==l){o=[],d=s,44===t.charCodeAt(s)?(u=",",s++):(u=null,0===i&&a('","')),null!==u?(c=Sn(),null!==c?u=[u,c]:(u=null,s=d)):(u=null,s=d);while(null!==u)o.push(u),d=s,44===t.charCodeAt(s)?(u=",",s++):(u=null,0===i&&a('","')),null!==u?(c=Sn(),null!==c?u=[u,c]:(u=null,s=d)):(u=null,s=d);null!==o?l=[l,o]:(l=null,s=f)}else l=null,s=f;null!==l?(o=Y(),null!==o?e=[e,n,r,l,o]:(e=null,s=h)):(e=null,s=h)}else e=null,s=h;else e=null,s=h;else e=null,s=h;return e}function Sn(){var e,n;return n=s,"auth-int"===t.substr(s,8).toLowerCase()?(e=t.substr(s,8),s+=8):(e=null,0===i&&a('"auth-int"')),null===e&&("auth"===t.substr(s,4).toLowerCase()?(e=t.substr(s,4),s+=4):(e=null,0===i&&a('"auth"')),null===e&&(e=P())),null!==e&&(e=function(e,t){Ar.qop||(Ar.qop=[]),Ar.qop.push(t.toLowerCase())}(0,e)),null===e&&(s=n),e}function Cn(){var e,t,n,r,l,i;if(l=s,e=P(),null!==e){t=[],i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function En(){var e,t,n,r,l,i,o;if(l=s,i=s,e=bn(),null!==e){t=[],o=s,n=G(),null!==n?(r=bn(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=G(),null!==n?(r=bn(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){var t,n;for(n=Ar.multi_header.length,t=0;t<n;t++)if(null===Ar.multi_header[t].parsed){Ar=null;break}Ar=null!==Ar?Ar.multi_header:-1}()),null===e&&(s=l),e}function bn(){var e,t,n,r,l,i,o;if(l=s,i=s,e=Et(),null!==e){t=[],o=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){var t;Ar.multi_header||(Ar.multi_header=[]);try{t=new br(Ar.uri,Ar.display_name,Ar.params),delete Ar.uri,delete Ar.display_name,delete Ar.params}catch(n){t=null}Ar.multi_header.push({possition:s,offset:e,parsed:t})}(l)),null===e&&(s=l),e}function An(){var e,n,r,l,o,u,c;if(o=s,u=s,"sip"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"SIP"')),null===e&&(e=P()),null!==e){n=[],c=s,r=W(),null!==r?(l=Rn(),null!==l?r=[r,l]:(r=null,s=c)):(r=null,s=c);while(null!==r)n.push(r),c=s,r=W(),null!==r?(l=Rn(),null!==l?r=[r,l]:(r=null,s=c)):(r=null,s=c);null!==n?e=[e,n]:(e=null,s=u)}else e=null,s=u;return null!==e&&(e=function(e,t){if(Ar.protocol=t.toLowerCase(),Ar.params||(Ar.params={}),Ar.params.text&&'"'===Ar.params.text[0]){var n=Ar.params.text;Ar.text=n.substring(1,n.length-1),delete Ar.params.text}}(0,e[0])),null===e&&(s=o),e}function Rn(){var e;return e=wn(),null===e&&(e=kt()),e}function wn(){var e,n,r,l,o,u;if(o=s,u=s,"cause"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"cause"')),null!==e)if(n=q(),null!==n){if(l=h(),null!==l){r=[];while(null!==l)r.push(l),l=h()}else r=null;null!==r?e=[e,n,r]:(e=null,s=u)}else e=null,s=u;else e=null,s=u;return null!==e&&(e=function(e,t){Ar.cause=parseInt(t.join(""))}(0,e[2])),null===e&&(s=o),e}function In(){var e,t,n,r,l,i;if(l=s,e=P(),null!==e){t=[],i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function On(){var e,t,n,r,l,i;if(l=s,e=kn(),null!==e){t=[],i=s,n=G(),null!==n?(r=kn(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=kn(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function kn(){var e,t,n,r,l,i;if(l=s,e=Et(),null!==e){t=[],i=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function Nn(){var e,t,n,r,l,i;if(l=s,e=Pn(),null!==e){t=[],i=s,n=W(),null!==n?(r=Dn(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=W(),null!==n?(r=Dn(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function Pn(){var e,n;return n=s,"active"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"active"')),null===e&&("pending"===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"pending"')),null===e&&("terminated"===t.substr(s,10).toLowerCase()?(e=t.substr(s,10),s+=10):(e=null,0===i&&a('"terminated"')),null===e&&(e=P()))),null!==e&&(e=function(e){Ar.state=t.substring(s,e)}(n)),null===e&&(s=n),e}function Dn(){var e,n,r,l,o;return l=s,o=s,"reason"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"reason"')),null!==e?(n=q(),null!==n?(r=xn(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){"undefined"!==typeof t&&(Ar.reason=t)}(0,e[2])),null===e&&(s=l),null===e&&(l=s,o=s,"expires"===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"expires"')),null!==e?(n=q(),null!==n?(r=It(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){"undefined"!==typeof t&&(Ar.expires=t)}(0,e[2])),null===e&&(s=l),null===e&&(l=s,o=s,"retry_after"===t.substr(s,11).toLowerCase()?(e=t.substr(s,11),s+=11):(e=null,0===i&&a('"retry_after"')),null!==e?(n=q(),null!==n?(r=It(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){"undefined"!==typeof t&&(Ar.retry_after=t)}(0,e[2])),null===e&&(s=l),null===e&&(e=kt()))),e}function xn(){var e;return"deactivated"===t.substr(s,11).toLowerCase()?(e=t.substr(s,11),s+=11):(e=null,0===i&&a('"deactivated"')),null===e&&("probation"===t.substr(s,9).toLowerCase()?(e=t.substr(s,9),s+=9):(e=null,0===i&&a('"probation"')),null===e&&("rejected"===t.substr(s,8).toLowerCase()?(e=t.substr(s,8),s+=8):(e=null,0===i&&a('"rejected"')),null===e&&("timeout"===t.substr(s,7).toLowerCase()?(e=t.substr(s,7),s+=7):(e=null,0===i&&a('"timeout"')),null===e&&("giveup"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"giveup"')),null===e&&("noresource"===t.substr(s,10).toLowerCase()?(e=t.substr(s,10),s+=10):(e=null,0===i&&a('"noresource"')),null===e&&("invariant"===t.substr(s,9).toLowerCase()?(e=t.substr(s,9),s+=9):(e=null,0===i&&a('"invariant"')),null===e&&(e=P()))))))),e}function Un(){var e;return e=w(),e=null!==e?e:"",e}function Mn(){var e,t,n,r,l,i;if(l=s,e=P(),null!==e){t=[],i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=P(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e=null!==e?e:"",e}function Ln(){var e,t,n,r,l,i,o;if(l=s,i=s,e=ee(),null===e&&(e=Et()),null!==e){t=[],o=s,n=W(),null!==n?(r=qn(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=W(),null!==n?(r=qn(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){var t=Ar.tag;try{Ar=new br(Ar.uri,Ar.display_name,Ar.params),t&&Ar.setParam("tag",t)}catch(n){Ar=-1}}()),null===e&&(s=l),e}function qn(){var e;return e=tn(),null===e&&(e=kt()),e}function Hn(){var e,t,n,r,l,i;if(l=s,e=Fn(),null!==e){t=[],i=s,n=G(),null!==n?(r=Fn(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=G(),null!==n?(r=Fn(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function Fn(){var e,t,n,r,l,i,o,u;if(o=s,e=$n(),null!==e)if(t=b(),null!==t)if(n=Xn(),null!==n){r=[],u=s,l=W(),null!==l?(i=jn(),null!==i?l=[l,i]:(l=null,s=u)):(l=null,s=u);while(null!==l)r.push(l),u=s,l=W(),null!==l?(i=jn(),null!==i?l=[l,i]:(l=null,s=u)):(l=null,s=u);null!==r?e=[e,t,n,r]:(e=null,s=o)}else e=null,s=o;else e=null,s=o;else e=null,s=o;return e}function jn(){var e;return e=Vn(),null===e&&(e=Gn(),null===e&&(e=Wn(),null===e&&(e=Bn(),null===e&&(e=Kn(),null===e&&(e=kt()))))),e}function Vn(){var e,n,r,l,o;return l=s,o=s,"ttl"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"ttl"')),null!==e?(n=q(),null!==n?(r=er(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.ttl=t}(0,e[2])),null===e&&(s=l),e}function Gn(){var e,n,r,l,o;return l=s,o=s,"maddr"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"maddr"')),null!==e?(n=q(),null!==n?(r=ce(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.maddr=t}(0,e[2])),null===e&&(s=l),e}function Wn(){var e,n,r,l,o;return l=s,o=s,"received"===t.substr(s,8).toLowerCase()?(e=t.substr(s,8),s+=8):(e=null,0===i&&a('"received"')),null!==e?(n=q(),null!==n?(r=ge(),null===r&&(r=pe()),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.received=t}(0,e[2])),null===e&&(s=l),e}function Bn(){var e,n,r,l,o;return l=s,o=s,"branch"===t.substr(s,6).toLowerCase()?(e=t.substr(s,6),s+=6):(e=null,0===i&&a('"branch"')),null!==e?(n=q(),null!==n?(r=P(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.branch=t}(0,e[2])),null===e&&(s=l),e}function Kn(){var e,n,r,l,o;return l=s,"rport"===t.substr(s,5).toLowerCase()?(e=t.substr(s,5),s+=5):(e=null,0===i&&a('"rport"')),null!==e?(o=s,n=q(),null!==n?(r=Yn(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=l)):(e=null,s=l),e}function Yn(){var e,t,n,r,l,i,o;return i=s,o=s,e=h(),e=null!==e?e:"",null!==e?(t=h(),t=null!==t?t:"",null!==t?(n=h(),n=null!==n?n:"",null!==n?(r=h(),r=null!==r?r:"",null!==r?(l=h(),l=null!==l?l:"",null!==l?e=[e,t,n,r,l]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.rport=parseInt(t.join(""))}(0,e)),null===e&&(s=i),e}function $n(){var e,t,n,r,l,i;return i=s,e=Jn(),null!==e?(t=L(),null!==t?(n=P(),null!==n?(r=L(),null!==r?(l=zn(),null!==l?e=[e,t,n,r,l]:(e=null,s=i)):(e=null,s=i)):(e=null,s=i)):(e=null,s=i)):(e=null,s=i),e}function Jn(){var e,n;return n=s,"sip"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"SIP"')),null===e&&(e=P()),null!==e&&(e=function(e,t){Ar.protocol=t}(0,e)),null===e&&(s=n),e}function zn(){var e,n;return n=s,"udp"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"UDP"')),null===e&&("tcp"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"TCP"')),null===e&&("tls"===t.substr(s,3).toLowerCase()?(e=t.substr(s,3),s+=3):(e=null,0===i&&a('"TLS"')),null===e&&("sctp"===t.substr(s,4).toLowerCase()?(e=t.substr(s,4),s+=4):(e=null,0===i&&a('"SCTP"')),null===e&&(e=P())))),null!==e&&(e=function(e,t){Ar.transport=t}(0,e)),null===e&&(s=n),e}function Xn(){var e,t,n,r,l;return r=s,e=Qn(),null!==e?(l=s,t=B(),null!==t?(n=Zn(),null!==n?t=[t,n]:(t=null,s=l)):(t=null,s=l),t=null!==t?t:"",null!==t?e=[e,t]:(e=null,s=r)):(e=null,s=r),e}function Qn(){var e,n;return n=s,e=ge(),null===e&&(e=_e(),null===e&&(e=he())),null!==e&&(e=function(e){Ar.host=t.substring(s,e)}(n)),null===e&&(s=n),e}function Zn(){var e,t,n,r,l,i,o;return i=s,o=s,e=h(),e=null!==e?e:"",null!==e?(t=h(),t=null!==t?t:"",null!==t?(n=h(),n=null!==n?n:"",null!==n?(r=h(),r=null!==r?r:"",null!==r?(l=h(),l=null!==l?l:"",null!==l?e=[e,t,n,r,l]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.port=parseInt(t.join(""))}(0,e)),null===e&&(s=i),e}function er(){var e,t,n,r,l;return r=s,l=s,e=h(),null!==e?(t=h(),t=null!==t?t:"",null!==t?(n=h(),n=null!==n?n:"",null!==n?e=[e,t,n]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l),null!==e&&(e=function(e,t){return parseInt(t.join(""))}(0,e)),null===e&&(s=r),e}function tr(){var e;return e=on(),e}function nr(){var e,t,n,r,l,i;if(l=s,e=rr(),null!==e){t=[],i=s,n=W(),null!==n?(r=lr(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=W(),null!==n?(r=lr(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function rr(){var e,t;return t=s,e=It(),null!==e&&(e=function(e,t){Ar.expires=t}(0,e)),null===e&&(s=t),e}function lr(){var e;return e=sr(),null===e&&(e=kt()),e}function sr(){var e,n,r,l,o;return l=s,o=s,"refresher"===t.substr(s,9).toLowerCase()?(e=t.substr(s,9),s+=9):(e=null,0===i&&a('"refresher"')),null!==e?(n=q(),null!==n?("uac"===t.substr(s,3).toLowerCase()?(r=t.substr(s,3),s+=3):(r=null,0===i&&a('"uac"')),null===r&&("uas"===t.substr(s,3).toLowerCase()?(r=t.substr(s,3),s+=3):(r=null,0===i&&a('"uas"'))),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.refresher=t.toLowerCase()}(0,e[2])),null===e&&(s=l),e}function ir(){var e,t,n,r;return r=s,e=P(),null!==e?(t=R(),null!==t?(n=or(),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)):(e=null,s=r),e}function or(){var e,t;e=[],t=I(),null===t&&(t=k(),null===t&&(t=b()));while(null!==t)e.push(t),t=I(),null===t&&(t=k(),null===t&&(t=b()));return e}function ur(){var e,t;e=[],t=p();while(null!==t)e.push(t),t=p();return e}function ar(){var e,n,r;return r=s,"uuid:"===t.substr(s,5)?(e="uuid:",s+=5):(e=null,0===i&&a('"uuid:"')),null!==e?(n=cr(),null!==n?e=[e,n]:(e=null,s=r)):(e=null,s=r),e}function cr(){var e,n,r,l,o,u,c,h,f,d,_;return d=s,_=s,e=fr(),null!==e?(45===t.charCodeAt(s)?(n="-",s++):(n=null,0===i&&a('"-"')),null!==n?(r=hr(),null!==r?(45===t.charCodeAt(s)?(l="-",s++):(l=null,0===i&&a('"-"')),null!==l?(o=hr(),null!==o?(45===t.charCodeAt(s)?(u="-",s++):(u=null,0===i&&a('"-"')),null!==u?(c=hr(),null!==c?(45===t.charCodeAt(s)?(h="-",s++):(h=null,0===i&&a('"-"')),null!==h?(f=dr(),null!==f?e=[e,n,r,l,o,u,c,h,f]:(e=null,s=_)):(e=null,s=_)):(e=null,s=_)):(e=null,s=_)):(e=null,s=_)):(e=null,s=_)):(e=null,s=_)):(e=null,s=_)):(e=null,s=_),null!==e&&(e=function(e,n){Ar=t.substring(s+5,e)}(d,e[0])),null===e&&(s=d),e}function hr(){var e,t,n,r,l;return l=s,e=d(),null!==e?(t=d(),null!==t?(n=d(),null!==n?(r=d(),null!==r?e=[e,t,n,r]:(e=null,s=l)):(e=null,s=l)):(e=null,s=l)):(e=null,s=l),e}function fr(){var e,t,n;return n=s,e=hr(),null!==e?(t=hr(),null!==t?e=[e,t]:(e=null,s=n)):(e=null,s=n),e}function dr(){var e,t,n,r;return r=s,e=hr(),null!==e?(t=hr(),null!==t?(n=hr(),null!==n?e=[e,t,n]:(e=null,s=r)):(e=null,s=r)):(e=null,s=r),e}function _r(){var e,t,n,r,l,i,o;if(l=s,i=s,e=ee(),null===e&&(e=Et()),null!==e){t=[],o=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);while(null!==n)t.push(n),o=s,n=W(),null!==n?(r=kt(),null!==r?n=[n,r]:(n=null,s=o)):(n=null,s=o);null!==t?e=[e,t]:(e=null,s=i)}else e=null,s=i;return null!==e&&(e=function(e){try{Ar=new br(Ar.uri,Ar.display_name,Ar.params)}catch(t){Ar=-1}}()),null===e&&(s=l),e}function pr(){var e,t,n,r,l,i;if(l=s,e=mr(),null!==e){t=[],i=s,n=W(),null!==n?(r=vr(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);while(null!==n)t.push(n),i=s,n=W(),null!==n?(r=vr(),null!==r?n=[n,r]:(n=null,s=i)):(n=null,s=i);null!==t?e=[e,t]:(e=null,s=l)}else e=null,s=l;return e}function mr(){var e,n,r,l,o,u;return l=s,o=s,e=U(),null!==e?(u=s,64===t.charCodeAt(s)?(n="@",s++):(n=null,0===i&&a('"@"')),null!==n?(r=U(),null!==r?n=[n,r]:(n=null,s=u)):(n=null,s=u),n=null!==n?n:"",null!==n?e=[e,n]:(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e){Ar.call_id=t.substring(s,e)}(l)),null===e&&(s=l),e}function vr(){var e;return e=gr(),null===e&&(e=yr(),null===e&&(e=Tr(),null===e&&(e=kt()))),e}function gr(){var e,n,r,l,o;return l=s,o=s,"to-tag"===t.substr(s,6)?(e="to-tag",s+=6):(e=null,0===i&&a('"to-tag"')),null!==e?(n=q(),null!==n?(r=P(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.to_tag=t}(0,e[2])),null===e&&(s=l),e}function yr(){var e,n,r,l,o;return l=s,o=s,"from-tag"===t.substr(s,8)?(e="from-tag",s+=8):(e=null,0===i&&a('"from-tag"')),null!==e?(n=q(),null!==n?(r=P(),null!==r?e=[e,n,r]:(e=null,s=o)):(e=null,s=o)):(e=null,s=o),null!==e&&(e=function(e,t){Ar.from_tag=t}(0,e[2])),null===e&&(s=l),e}function Tr(){var e,n;return n=s,"early-only"===t.substr(s,10)?(e="early-only",s+=10):(e=null,0===i&&a('"early-only"')),null!==e&&(e=function(e){Ar.early_only=!0}()),null===e&&(s=n),e}function Sr(e){e.sort();for(var t=null,n=[],r=0;r<e.length;r++)e[r]!==t&&(n.push(e[r]),t=e[r]);return n}function Cr(){for(var e=1,n=1,r=!1,l=0;l<Math.max(s,o);l++){var i=t.charAt(l);"\n"===i?(r||e++,n=1,r=!1):"\r"===i||"\u2028"===i||"\u2029"===i?(e++,n=1,r=!0):(n++,r=!1)}return{line:e,column:n}}var Er=n("9cf5"),br=n("4a05"),Ar={},Rr=l[r]();if(null===Rr||s!==t.length){var wr=Math.max(s,o),Ir=wr<t.length?t.charAt(wr):null,Or=Cr();return new this.SyntaxError(Sr(u),Ir,wr,Or.line,Or.column),-1}return Ar},toSource:function(){return this._source},SyntaxError:function(t,n,r,l,s){function i(t,n){var r,l;switch(t.length){case 0:r="end of input";break;case 1:r=t[0];break;default:r=t.slice(0,t.length-1).join(", ")+" or "+t[t.length-1]}return l=n?e(n):"end of input","Expected "+r+" but "+l+" found."}this.name="SyntaxError",this.expected=t,this.found=n,this.message=i(t,n),this.offset=r,this.line=l,this.column=s}};return t.SyntaxError.prototype=Error.prototype,t}()},"1e00":function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"test-sip"},[n("el-switch",{attrs:{"active-text":"打开日志","inactive-text":"关闭日志"},model:{value:e.logFlag,callback:function(t){e.logFlag=t},expression:"logFlag"}}),n("div",{staticClass:"step"},[n("h2",[e._v("步骤 1：输入自己的分机号（1001-1019）")]),n("div",{staticClass:"step-box"},[n("el-input",{staticClass:"input-box",attrs:{placeholder:"请输入自己的分机号（1001-1010）",disabled:null!==e.localStream},model:{value:e.userExtension,callback:function(t){e.userExtension=t},expression:"userExtension"}}),n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:!e.userExtension||e.isRegisted},on:{click:e.registerUser}},[e._v(" 注册 ")])],1)]),n("div",[e._v("BOE客户专用内容")]),e._m(0),n("div",{staticClass:"step"},[n("h2",[e._v("步骤 2：输入要呼叫的分机号（1001-1019）")]),n("div",{staticClass:"step-box"},[n("el-input",{staticClass:"input-box",attrs:{placeholder:"请输入要呼叫的分机号（1001-1010）",disabled:!e.isRegisted},model:{value:e.targetExtension,callback:function(t){e.targetExtension=t},expression:"targetExtension"}}),n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:!e.targetExtension||null!==e.currentSession},on:{click:function(t){return e.startCall(!1)}}},[e._v(" 拨打语音电话 ")]),n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:!e.targetExtension||null!==e.currentSession},on:{click:function(t){return e.startCall(!0)}}},[e._v(" 拨打视频电话 ")])],1)]),n("div",{staticClass:"step"},[n("h2",[e._v("其他操作")]),n("div",{staticClass:"step-box"},[n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:null==e.currentSession},on:{click:e.hangUpCall}},[e._v(" 挂断 ")]),n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:!e.isRegisted},on:{click:e.unregisterUser}},[e._v(" 取消注册 ")]),e.localStream?n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:e.currentSession},on:{click:e.stopLocalMedia}},[e._v(" 停止测试本地设备 ")]):n("el-button",{staticClass:"step-button",attrs:{type:"primary",disabled:null!==e.currentSession},on:{click:e.captureLocalMedia}},[e._v(" 测试本地设备 ")])],1)]),e._m(1),e._m(2)],1)},l=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"default-section"},[n("p",[e._v("默认版本功能")])])},function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"step"},[n("h2",[e._v("音频：")]),n("div",{staticClass:"step-box"},[n("audio",{attrs:{id:"audio",autoplay:""}})])])},function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"step"},[n("h2",[e._v("视频：")]),n("div",{staticClass:"step-box"},[n("video",{attrs:{id:"meVideo",playsinline:"",autoplay:""}}),n("video",{attrs:{id:"remoteVideo",playsinline:"",autoplay:""}})])])}],s=n("5530"),i=(n("d3b7"),n("3ca3"),n("ddb0"),n("2b3d"),n("b0c0"),n("159b"),n("99af"),n("caad"),n("2532"),n("9715")),o=n.n(i),u={name:"TestSip",data:function(){return{logFlag:!0,userExtension:"1005",targetExtension:"1008",userAgent:null,password:"ocvLA4u03fdChK7g",serverIp:"fs.rfcare.cn",isRegisted:!1,localStream:null,incomingSession:null,outgoingSession:null,currentSession:null,myHangup:!1,audio:null,meVideo:null,remoteVideo:null,constraints:{audio:!0,video:{width:{max:1280},height:{max:720}}}}},computed:{ws_url:function(){return"wss://".concat(this.serverIp,":7443")}},watch:{logFlag:{handler:function(e,t){e?o.a.debug.enable("JsSIP:*"):o.a.debug.disable("JsSIP:*")},immediate:!0}},mounted:function(){this.audio=document.getElementById("audio"),this.meVideo=document.getElementById("meVideo"),this.remoteVideo=document.getElementById("remoteVideo")},methods:{captureLocalMedia:function(){var e=this;console.log("获取到本地音频/视频"),navigator.mediaDevices.getUserMedia(this.constraints).then((function(t){console.log("获取到本地媒体流"),e.localStream=t,"srcObject"in e.audio?e.audio.srcObject=t:e.audio.src=window.URL.createObjectURL(t),t.getVideoTracks().length>0&&("srcObject"in e.meVideo?e.meVideo.srcObject=t:e.meVideo.src=window.URL.createObjectURL(t))})).catch((function(t){e.$message("获取用户媒体设备错误: "+t.name)}))},stopLocalMedia:function(){this.localStream&&(this.localStream.getTracks().forEach((function(e){return e.stop()})),this.localStream=null,this.clearMedia("audio"),this.clearMedia("meVideo"))},isValidExtension:function(e){var t=parseInt(e,10);return t>=1001&&t<=1019},registerUser:function(){var e=this,t={sockets:[new o.a.WebSocketInterface(this.ws_url)],uri:"sip:".concat(this.userExtension,"@").concat(this.serverIp,";transport=ws"),password:this.password,contact_uri:"sip:".concat(this.userExtension,"@").concat(this.serverIp,";transport=ws"),display_name:this.userExtension,register:!0,session_timers:!1};this.userAgent=new o.a.UA(t),this.userAgent.on("connecting",(function(){return console.log("WebSocket 连接中")})),this.userAgent.on("connected",(function(){return console.log("WebSocket 连接成功")})),this.userAgent.on("disconnected",(function(){return console.log("WebSocket 断开连接")})),this.userAgent.on("registered",(function(){e.isRegisted=!0,console.log("用户代理注册成功")})),this.userAgent.on("unregistered",(function(){e.isRegisted=!1,console.log("用户代理取消注册")})),this.userAgent.on("registrationFailed",(function(t){e.$message("用户代理注册失败: ".concat(t.cause))})),this.userAgent.on("newRTCSession",(function(t){console.log("新会话: ",t),"remote"==t.originator?(console.log("接听到来电"),console.log(t.data),e.incomingSession=t.session,e.sipEventBind(t)):(console.log("打电话"),console.log(t),e.outgoingSession=t.session,e.outgoingSession.on("connecting",(function(t){console.info("onConnecting - ",t.request),e.currentSession=e.outgoingSession,e.outgoingSession=null})),e.outgoingSession.connection.addEventListener("track",(function(t){console.log("接收到远端track:",t.track),e.trackHandle(t.track,t.streams[0])})))})),this.userAgent.start(),console.log("用户代理启动")},sipEventBind:function(e,t){var n=this;e.session.on("accepted",(function(){console.log("onAccepted - ",e),"remote"==e.originator&&null==n.currentSession&&(n.currentSession=n.incomingSession,n.incomingSession=null,console.log("setCurrentSession：",n.currentSession))})),e.session.on("sdp",(function(e){console.log("onSDP,  ",e.sdp),console.log("onSDP 2,  ",e.sdp)})),e.session.on("progress",(function(){if(console.log(e),console.log("onProgress - ",e.originator),"remote"==e.originator){console.log("onProgress, response - ",e.response);var t=e.request.body.includes("m=video");n.$confirm("是否接听?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.session.answer({mediaConstraints:{audio:!0,video:t}})})).catch((function(){n.hangUpCall()}))}})),e.session.on("peerconnection",(function(){console.log("onPeerconnection - ",e.peerconnection),"remote"==e.originator&&null==n.currentSession&&e.session.connection.addEventListener("track",(function(e){console.info("接收到远端track:",e.track),n.trackHandle(e.track,e.streams[0])}))})),e.session.on("confirmed",(function(){console.log("onConfirmed - ",e),"remote"==e.originator&&null==n.currentSession&&(n.currentSession=n.incomingSession,n.incomingSession=null,console.log("setCurrentSession - ",n.currentSession))})),e.session.on("ended",(function(){n.endedHandle(),console.log("call ended：",e)})),e.session.on("failed",(function(e){n.$message("会话失败"),console.error("会话失败：",e)}))},trackHandle:function(e,t){var n=this,r=function(){navigator.mediaDevices.getUserMedia(Object(s["a"])(Object(s["a"])({},n.constraints),{},{audio:!1})).then((function(e){n.meVideo.srcObject=e})).catch((function(e){that.$message("".concat(e.name,"：").concat(e.message))}))};"video"===e.kind?(this.remoteVideo.srcObject=t,r()):"audio"===e.kind&&(this.audio.srcObject=t)},endedHandle:function(){this.clearMedia("meVideo"),this.clearMedia("remoteVideo"),this.clearMedia("audio"),this.myHangup?this.$message("通话结束"):this.$message("对方已挂断!"),this.myHangup=!1,this.currentSession=null},startCall:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.userAgent)try{var n={progress:function(e){return console.log("call is in progress")},failed:function(t){console.error(t),e.$message("call failed with cause: ".concat(t.cause))},ended:function(t){e.endedHandle(),console.log("call ended with cause: ".concat(t.cause))},confirmed:function(e){return console.log("call confirmed")}};console.log("this.userAgent.call"),this.outgoingSession=this.userAgent.call("sip:".concat(this.targetExtension,"@").concat(this.serverIp),{mediaConstraints:{audio:!0,video:t},eventHandlers:n})}catch(r){this.$message("呼叫失败"),console.error("呼叫失败：",r)}else this.$message("用户代理未初始化")},hangUpCall:function(){this.myHangup=!0,this.outgoingSession=this.userAgent.terminateSessions(),this.currentSession=null},clearMedia:function(e){var t=this[e].srcObject;if(t)for(var n=t.getTracks(),r=0;r<n.length;r++)n[r].stop();this[e].srcObject=null},unregisterUser:function(){console.log("取消注册"),this.userAgent.unregister(),this.resetState()},resetState:function(){this.userExtension="",this.targetExtension="",this.isRegisted=!1}}},a=u,c=(n("33ac"),n("2877")),h=Object(c["a"])(a,r,l,!1,null,"50a69c0f",null);t["default"]=h.exports},"1e8c":function(e,t,n){"use strict";var r=n("d251");e.exports={USER_AGENT:"".concat(r.title," ").concat(r.version),SIP:"sip",SIPS:"sips",causes:{CONNECTION_ERROR:"Connection Error",REQUEST_TIMEOUT:"Request Timeout",SIP_FAILURE_CODE:"SIP Failure Code",INTERNAL_ERROR:"Internal Error",BUSY:"Busy",REJECTED:"Rejected",REDIRECTED:"Redirected",UNAVAILABLE:"Unavailable",NOT_FOUND:"Not Found",ADDRESS_INCOMPLETE:"Address Incomplete",INCOMPATIBLE_SDP:"Incompatible SDP",MISSING_SDP:"Missing SDP",AUTHENTICATION_ERROR:"Authentication Error",BYE:"Terminated",WEBRTC_ERROR:"WebRTC Error",CANCELED:"Canceled",NO_ANSWER:"No Answer",EXPIRES:"Expires",NO_ACK:"No ACK",DIALOG_ERROR:"Dialog Error",USER_DENIED_MEDIA_ACCESS:"User Denied Media Access",BAD_MEDIA_DESCRIPTION:"Bad Media Description",RTP_TIMEOUT:"RTP Timeout"},SIP_ERROR_CAUSES:{REDIRECTED:[300,301,302,305,380],BUSY:[486,600],REJECTED:[403,603],NOT_FOUND:[404,604],UNAVAILABLE:[480,410,408,430],ADDRESS_INCOMPLETE:[484,424],INCOMPATIBLE_SDP:[488,606],AUTHENTICATION_ERROR:[401,407]},ACK:"ACK",BYE:"BYE",CANCEL:"CANCEL",INFO:"INFO",INVITE:"INVITE",MESSAGE:"MESSAGE",NOTIFY:"NOTIFY",OPTIONS:"OPTIONS",REGISTER:"REGISTER",REFER:"REFER",UPDATE:"UPDATE",SUBSCRIBE:"SUBSCRIBE",DTMF_TRANSPORT:{INFO:"INFO",RFC2833:"RFC2833"},REASON_PHRASE:{100:"Trying",180:"Ringing",181:"Call Is Being Forwarded",182:"Queued",183:"Session Progress",199:"Early Dialog Terminated",200:"OK",202:"Accepted",204:"No Notification",300:"Multiple Choices",301:"Moved Permanently",302:"Moved Temporarily",305:"Use Proxy",380:"Alternative Service",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",410:"Gone",412:"Conditional Request Failed",413:"Request Entity Too Large",414:"Request-URI Too Long",415:"Unsupported Media Type",416:"Unsupported URI Scheme",417:"Unknown Resource-Priority",420:"Bad Extension",421:"Extension Required",422:"Session Interval Too Small",423:"Interval Too Brief",424:"Bad Location Information",428:"Use Identity Header",429:"Provide Referrer Identity",430:"Flow Failed",433:"Anonymity Disallowed",436:"Bad Identity-Info",437:"Unsupported Certificate",438:"Invalid Identity Header",439:"First Hop Lacks Outbound Support",440:"Max-Breadth Exceeded",469:"Bad Info Package",470:"Consent Needed",478:"Unresolvable Destination",480:"Temporarily Unavailable",481:"Call/Transaction Does Not Exist",482:"Loop Detected",483:"Too Many Hops",484:"Address Incomplete",485:"Ambiguous",486:"Busy Here",487:"Request Terminated",488:"Not Acceptable Here",489:"Bad Event",491:"Request Pending",493:"Undecipherable",494:"Security Agreement Required",500:"JsSIP Internal Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Server Time-out",505:"Version Not Supported",513:"Message Too Large",580:"Precondition Failure",600:"Busy Everywhere",603:"Decline",604:"Does Not Exist Anywhere",606:"Not Acceptable"},ALLOWED_METHODS:"INVITE,ACK,CANCEL,BYE,UPDATE,MESSAGE,OPTIONS,REFER,INFO,NOTIFY",ACCEPTED_BODY_TYPES:"application/sdp, application/dtmf-relay",MAX_FORWARDS:69,SESSION_EXPIRES:90,MIN_SESSION_EXPIRES:60,CONNECTION_RECOVERY_MAX_INTERVAL:30,CONNECTION_RECOVERY_MIN_INTERVAL:2}},2437:function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function a(e){var t=f();return function(){var n,r=d(e);if(t){var l=d(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var _=n("faa1").EventEmitter,p=n("1e8c"),m=n("1c8d"),v=n("0790"),g=n("34eb")("JsSIP:RTCSession:ReferSubscriber");e.exports=function(e){o(n,e);var t=a(n);function n(e){var r;return l(this,n),r=t.call(this),r._id=null,r._session=e,r}return i(n,[{key:"sendRefer",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};g("sendRefer()");var r=v.cloneArray(n.extraHeaders),l=v.cloneObject(n.eventHandlers);for(var s in l)Object.prototype.hasOwnProperty.call(l,s)&&this.on(s,l[s]);var i=null;n.replaces&&(i=n.replaces._request.call_id,i+=";to-tag=".concat(n.replaces._to_tag),i+=";from-tag=".concat(n.replaces._from_tag),i=encodeURIComponent(i));var o="Refer-To: <".concat(e).concat(i?"?Replaces=".concat(i):"",">");r.push(o);var u="Referred-By: <".concat(this._session._ua._configuration.uri._scheme,":").concat(this._session._ua._configuration.uri._user,"@").concat(this._session._ua._configuration.uri._host,">");r.push(u),r.push("Contact: ".concat(this._session.contact));var a=this._session.sendRequest(p.REFER,{extraHeaders:r,eventHandlers:{onSuccessResponse:function(e){t._requestSucceeded(e)},onErrorResponse:function(e){t._requestFailed(e,p.causes.REJECTED)},onTransportError:function(){t._requestFailed(null,p.causes.CONNECTION_ERROR)},onRequestTimeout:function(){t._requestFailed(null,p.causes.REQUEST_TIMEOUT)},onDialogError:function(){t._requestFailed(null,p.causes.DIALOG_ERROR)}}});this._id=a.cseq}},{key:"receiveNotify",value:function(e){if(g("receiveNotify()"),e.body){var t=m.parse(e.body.trim(),"Status_Line");if(-1!==t)switch(!0){case/^100$/.test(t.status_code):this.emit("trying",{request:e,status_line:t});break;case/^1[0-9]{2}$/.test(t.status_code):this.emit("progress",{request:e,status_line:t});break;case/^2[0-9]{2}$/.test(t.status_code):this.emit("accepted",{request:e,status_line:t});break;default:this.emit("failed",{request:e,status_line:t});break}else g('receiveNotify() | error parsing NOTIFY body: "'.concat(e.body,'"'))}}},{key:"_requestSucceeded",value:function(e){g("REFER succeeded"),g('emit "requestSucceeded"'),this.emit("requestSucceeded",{response:e})}},{key:"_requestFailed",value:function(e,t){g("REFER failed"),g('emit "requestFailed"'),this.emit("requestFailed",{response:e||null,cause:t})}},{key:"id",get:function(){return this._id}}]),n}(_)},"28e8":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("1e8c"),o=n("34eb")("JsSIP:RTCSession:ReferNotifier"),u={event_type:"refer",body_type:"message/sipfrag;version=2.0",expires:300};e.exports=function(){function e(t,n,l){r(this,e),this._session=t,this._id=n,this._expires=l||u.expires,this._active=!0,this.notify(100)}return s(e,[{key:"notify",value:function(e,t){var n;(o("notify()"),!1!==this._active)&&(t=t||i.REASON_PHRASE[e]||"",n=e>=200?"terminated;reason=noresource":"active;expires=".concat(this._expires),this._session.sendRequest(i.NOTIFY,{extraHeaders:["Event: ".concat(u.event_type,";id=").concat(this._id),"Subscription-State: ".concat(n),"Content-Type: ".concat(u.body_type)],body:"SIP/2.0 ".concat(e," ").concat(t),eventHandlers:{onErrorResponse:function(){this._active=!1}}}))}}]),e}()},"2f68":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("1e8c"),o=n("a283"),u=n("380f"),a=n("c9ab"),c={onRequestTimeout:function(){},onTransportError:function(){},onSuccessResponse:function(){},onErrorResponse:function(){},onAuthenticated:function(){},onDialogError:function(){}};e.exports=function(){function e(t,n,l){for(var s in r(this,e),this._dialog=t,this._ua=t._ua,this._request=n,this._eventHandlers=l,this._reattempt=!1,this._reattemptTimer=null,c)Object.prototype.hasOwnProperty.call(c,s)&&(this._eventHandlers[s]||(this._eventHandlers[s]=c[s]))}return s(e,[{key:"send",value:function(){var e=this,t=new a(this._ua,this._request,{onRequestTimeout:function(){e._eventHandlers.onRequestTimeout()},onTransportError:function(){e._eventHandlers.onTransportError()},onAuthenticated:function(t){e._eventHandlers.onAuthenticated(t)},onReceiveResponse:function(t){e._receiveResponse(t)}});if(t.send(),(this._request.method===i.INVITE||this._request.method===i.UPDATE&&this._request.body)&&t.clientTransaction.state!==o.C.STATUS_TERMINATED){this._dialog.uac_pending_reply=!0;var n=function n(){t.clientTransaction.state!==o.C.STATUS_ACCEPTED&&t.clientTransaction.state!==o.C.STATUS_COMPLETED&&t.clientTransaction.state!==o.C.STATUS_TERMINATED||(t.clientTransaction.removeListener("stateChanged",n),e._dialog.uac_pending_reply=!1)};t.clientTransaction.on("stateChanged",n)}}},{key:"_receiveResponse",value:function(e){var t=this;408===e.status_code||481===e.status_code?this._eventHandlers.onDialogError(e):e.method===i.INVITE&&491===e.status_code?this._reattempt?e.status_code>=200&&e.status_code<300?this._eventHandlers.onSuccessResponse(e):e.status_code>=300&&this._eventHandlers.onErrorResponse(e):(this._request.cseq=this._dialog.local_seqnum+=1,this._reattemptTimer=setTimeout((function(){t._dialog.owner.status!==u.C.STATUS_TERMINATED&&(t._reattempt=!0,t._request_sender.send())}),1e3)):e.status_code>=200&&e.status_code<300?this._eventHandlers.onSuccessResponse(e):e.status_code>=300&&this._eventHandlers.onErrorResponse(e)}},{key:"request",get:function(){return this._request}}]),e}()},"33ac":function(e,t,n){"use strict";n("df29")},"34eb":function(e,t,n){(function(r){function l(){return!("undefined"===typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"===typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!==typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!==typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))}function s(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;t.splice(1,0,n,"color: inherit");let r=0,l=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(r++,"%c"===e&&(l=r))}),t.splice(l,0,n)}function i(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(n){}}function o(){let e;try{e=t.storage.getItem("debug")}catch(n){}return!e&&"undefined"!==typeof r&&"env"in r&&(e=Object({NODE_ENV:"production",VUE_APP_API_ROOT:"/api/",VUE_APP_CDN:"OFF",VUE_APP_COMPANY:"boe",VUE_APP_DEBUG_TOOL:"",VUE_APP_ENABLE_ADVANCED_REPORTS:"true",VUE_APP_ENABLE_CUSTOM_DASHBOARD:"true",VUE_APP_ENABLE_MULTI_TENANT:"false",VUE_APP_GZIP:"ON",VUE_APP_LOGO_URL:"/images/logo-boe.png",VUE_APP_THEME_COLOR:"#1890ff",VUE_APP_TITLE:"BOE 服务管理系统",VUE_APP_WS_API_ROOT:"wss://boe.rfcare.cn",BASE_URL:"/"}).DEBUG),e}function u(){try{return localStorage}catch(e){}}t.formatArgs=s,t.save=i,t.load=o,t.useColors=l,t.storage=u(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=n("dc90")(t);const{formatters:a}=e.exports;a.j=function(e){try{return JSON.stringify(e)}catch(t){return"[UnexpectedJSONParseError]: "+t.message}}}).call(this,n("4362"))},"3781e":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function a(e){var t=f();return function(){var n,r=d(e);if(t){var l=d(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var _=n("faa1").EventEmitter,p=n("1e8c"),m=n("03f2"),v=n("0790"),g=n("c9ab"),y=n("8c52"),T=n("34eb")("JsSIP:Message");e.exports=function(e){o(n,e);var t=a(n);function n(e){var r;return l(this,n),r=t.call(this),r._ua=e,r._request=null,r._closed=!1,r._direction=null,r._local_identity=null,r._remote_identity=null,r._is_replied=!1,r._data={},r}return i(n,[{key:"send",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},l=e;if(void 0===e||void 0===t)throw new TypeError("Not enough arguments");if(e=this._ua.normalizeTarget(e),!e)throw new TypeError("Invalid target: ".concat(l));var s=v.cloneArray(r.extraHeaders),i=v.cloneObject(r.eventHandlers),o=r.contentType||"text/plain";for(var u in i)Object.prototype.hasOwnProperty.call(i,u)&&this.on(u,i[u]);s.push("Content-Type: ".concat(o)),this._request=new m.OutgoingRequest(p.MESSAGE,e,this._ua,null,s),t&&(this._request.body=t);var a=new g(this._ua,this._request,{onRequestTimeout:function(){n._onRequestTimeout()},onTransportError:function(){n._onTransportError()},onReceiveResponse:function(e){n._receiveResponse(e)}});this._newMessage("local",this._request),a.send()}},{key:"init_incoming",value:function(e){this._request=e,this._newMessage("remote",e),this._is_replied||(this._is_replied=!0,e.reply(200)),this._close()}},{key:"accept",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=v.cloneArray(e.extraHeaders),n=e.body;if("incoming"!==this._direction)throw new y.NotSupportedError('"accept" not supported for outgoing Message');if(this._is_replied)throw new Error("incoming Message already replied");this._is_replied=!0,this._request.reply(200,null,t,n)}},{key:"reject",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.status_code||480,n=e.reason_phrase,r=v.cloneArray(e.extraHeaders),l=e.body;if("incoming"!==this._direction)throw new y.NotSupportedError('"reject" not supported for outgoing Message');if(this._is_replied)throw new Error("incoming Message already replied");if(t<300||t>=700)throw new TypeError("Invalid status_code: ".concat(t));this._is_replied=!0,this._request.reply(t,n,r,l)}},{key:"_receiveResponse",value:function(e){if(!this._closed)switch(!0){case/^1[0-9]{2}$/.test(e.status_code):break;case/^2[0-9]{2}$/.test(e.status_code):this._succeeded("remote",e);break;default:var t=v.sipErrorCause(e.status_code);this._failed("remote",e,t);break}}},{key:"_onRequestTimeout",value:function(){this._closed||this._failed("system",null,p.causes.REQUEST_TIMEOUT)}},{key:"_onTransportError",value:function(){this._closed||this._failed("system",null,p.causes.CONNECTION_ERROR)}},{key:"_close",value:function(){this._closed=!0,this._ua.destroyMessage(this)}},{key:"_newMessage",value:function(e,t){"remote"===e?(this._direction="incoming",this._local_identity=t.to,this._remote_identity=t.from):"local"===e&&(this._direction="outgoing",this._local_identity=t.from,this._remote_identity=t.to),this._ua.newMessage(this,{originator:e,message:this,request:t})}},{key:"_failed",value:function(e,t,n){T("MESSAGE failed"),this._close(),T('emit "failed"'),this.emit("failed",{originator:e,response:t||null,cause:n})}},{key:"_succeeded",value:function(e,t){T("MESSAGE succeeded"),this._close(),T('emit "succeeded"'),this.emit("succeeded",{originator:e,response:t})}},{key:"direction",get:function(){return this._direction}},{key:"local_identity",get:function(){return this._local_identity}},{key:"remote_identity",get:function(){return this._remote_identity}}]),n}(_)},"380f":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=s(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,l=function(){};return{s:l,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:l}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(u)throw i}}}}function s(e,t){if(e){if("string"===typeof e)return i(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?i(e,t):void 0}}function i(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function a(e,t,n){return t&&u(e.prototype,t),n&&u(e,n),e}function c(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&h(e,t)}function h(e,t){return h=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},h(e,t)}function f(e){var t=p();return function(){var n,r=m(e);if(t){var l=m(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return d(this,n)}}function d(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?_(e):t}function _(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function p(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function m(e){return m=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},m(e)}var v=n("faa1").EventEmitter,g=n("93be"),y=n("1e8c"),T=n("8c52"),S=n("a283"),C=n("0790"),E=n("488a"),b=n("03f2"),A=n("88fe"),R=n("c9ab"),w=n("50d3"),I=n("60da6"),O=n("28e8"),k=n("2437"),N=n("9cf5"),P=n("34eb")("JsSIP:RTCSession"),D=n("34eb")("JsSIP:ERROR:RTCSession");D.log=console.warn.bind(console);var x={STATUS_NULL:0,STATUS_INVITE_SENT:1,STATUS_1XX_RECEIVED:2,STATUS_INVITE_RECEIVED:3,STATUS_WAITING_FOR_ANSWER:4,STATUS_ANSWERED:5,STATUS_WAITING_FOR_ACK:6,STATUS_CANCELED:7,STATUS_TERMINATED:8,STATUS_CONFIRMED:9},U=["audio","video"];e.exports=function(e){c(n,e);var t=f(n);function n(e){var r;return o(this,n),P("new"),r=t.call(this),r._id=null,r._ua=e,r._status=x.STATUS_NULL,r._dialog=null,r._earlyDialogs={},r._contact=null,r._from_tag=null,r._to_tag=null,r._connection=null,r._connectionPromiseQueue=Promise.resolve(),r._request=null,r._is_canceled=!1,r._cancel_reason="",r._is_confirmed=!1,r._late_sdp=!1,r._rtcOfferConstraints=null,r._rtcAnswerConstraints=null,r._localMediaStream=null,r._localMediaStreamLocallyGenerated=!1,r._rtcReady=!0,r._timers={ackTimer:null,expiresTimer:null,invite2xxTimer:null,userNoAnswerTimer:null},r._direction=null,r._local_identity=null,r._remote_identity=null,r._start_time=null,r._end_time=null,r._tones=null,r._audioMuted=!1,r._videoMuted=!1,r._localHold=!1,r._remoteHold=!1,r._sessionTimers={enabled:r._ua.configuration.session_timers,refreshMethod:r._ua.configuration.session_timers_refresh_method,defaultExpires:y.SESSION_EXPIRES,currentExpires:null,running:!1,refresher:!1,timer:null},r._referSubscribers={},r._data={},r}return a(n,null,[{key:"C",get:function(){return x}}]),a(n,[{key:"isInProgress",value:function(){switch(this._status){case x.STATUS_NULL:case x.STATUS_INVITE_SENT:case x.STATUS_1XX_RECEIVED:case x.STATUS_INVITE_RECEIVED:case x.STATUS_WAITING_FOR_ANSWER:return!0;default:return!1}}},{key:"isEstablished",value:function(){switch(this._status){case x.STATUS_ANSWERED:case x.STATUS_WAITING_FOR_ACK:case x.STATUS_CONFIRMED:return!0;default:return!1}}},{key:"isEnded",value:function(){switch(this._status){case x.STATUS_CANCELED:case x.STATUS_TERMINATED:return!0;default:return!1}}},{key:"isMuted",value:function(){return{audio:this._audioMuted,video:this._videoMuted}}},{key:"isOnHold",value:function(){return{local:this._localHold,remote:this._remoteHold}}},{key:"connect",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0;P("connect()");var r=e,l=C.cloneObject(t.eventHandlers),s=C.cloneArray(t.extraHeaders),i=C.cloneObject(t.mediaConstraints,{audio:!0,video:!0}),o=t.mediaStream||null,u=C.cloneObject(t.pcConfig,{iceServers:[]}),a=t.rtcConstraints||null,c=t.rtcOfferConstraints||null;if(this._rtcOfferConstraints=c,this._rtcAnswerConstraints=t.rtcAnswerConstraints||null,this._data=t.data||this._data,void 0===e)throw new TypeError("Not enough arguments");if(this._status!==x.STATUS_NULL)throw new T.InvalidStateError(this._status);if(!window.RTCPeerConnection)throw new T.NotSupportedError("WebRTC not supported");if(e=this._ua.normalizeTarget(e),!e)throw new TypeError("Invalid target: ".concat(r));for(var h in this._sessionTimers.enabled&&C.isDecimal(t.sessionTimersExpires)&&(t.sessionTimersExpires>=y.MIN_SESSION_EXPIRES?this._sessionTimers.defaultExpires=t.sessionTimersExpires:this._sessionTimers.defaultExpires=y.SESSION_EXPIRES),l)Object.prototype.hasOwnProperty.call(l,h)&&this.on(h,l[h]);this._from_tag=C.newTag();var f=t.anonymous||!1,d={from_tag:this._from_tag};this._contact=this._ua.contact.toString({anonymous:f,outbound:!0}),f&&(d.from_display_name="Anonymous",d.from_uri=new N("sip","anonymous","anonymous.invalid"),s.push("P-Preferred-Identity: ".concat(this._ua.configuration.uri.toString())),s.push("Privacy: id")),s.push("Contact: ".concat(this._contact)),s.push("Content-Type: application/sdp"),this._sessionTimers.enabled&&s.push("Session-Expires: ".concat(this._sessionTimers.defaultExpires).concat(this._ua.configuration.session_timers_force_refresher?";refresher=uac":"")),this._request=new b.InitialOutgoingInviteRequest(e,this._ua,d,s),this._id=this._request.call_id+this._from_tag,this._createRTCConnection(u,a),this._direction="outgoing",this._local_identity=this._request.from,this._remote_identity=this._request.to,n&&n(this),this._newRTCSession("local",this._request),this._sendInitialRequest(i,c,o)}},{key:"init_incoming",value:function(e,t){var n,r=this;P("init_incoming()");var l=e.hasHeader("Content-Type")?e.getHeader("Content-Type").toLowerCase():void 0;e.body&&"application/sdp"!==l?e.reply(415):(this._status=x.STATUS_INVITE_RECEIVED,this._from_tag=e.from_tag,this._id=e.call_id+this._from_tag,this._request=e,this._contact=this._ua.contact.toString(),e.hasHeader("expires")&&(n=1e3*e.getHeader("expires")),e.to_tag=C.newTag(),this._createDialog(e,"UAS",!0)?(e.body?this._late_sdp=!1:this._late_sdp=!0,this._status=x.STATUS_WAITING_FOR_ANSWER,this._timers.userNoAnswerTimer=setTimeout((function(){e.reply(408),r._failed("local",null,y.causes.NO_ANSWER)}),this._ua.configuration.no_answer_timeout),n&&(this._timers.expiresTimer=setTimeout((function(){r._status===x.STATUS_WAITING_FOR_ANSWER&&(e.reply(487),r._failed("system",null,y.causes.EXPIRES))}),n)),this._direction="incoming",this._local_identity=e.to,this._remote_identity=e.from,t&&t(this),this._newRTCSession("remote",e),this._status!==x.STATUS_TERMINATED&&(e.reply(180,null,["Contact: ".concat(this._contact)]),this._progress("local",null))):e.reply(500,"Missing Contact header field"))}},{key:"answer",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};P("answer()");var n,r=this._request,s=C.cloneArray(t.extraHeaders),i=C.cloneObject(t.mediaConstraints),o=t.mediaStream||null,u=C.cloneObject(t.pcConfig,{iceServers:[]}),a=t.rtcConstraints||null,c=t.rtcAnswerConstraints||null,h=C.cloneObject(t.rtcOfferConstraints),f=!1,d=!1,_=!1,p=!1;if(this._rtcAnswerConstraints=c,this._rtcOfferConstraints=t.rtcOfferConstraints||null,this._data=t.data||this._data,"incoming"!==this._direction)throw new T.NotSupportedError('"answer" not supported for outgoing RTCSession');if(this._status!==x.STATUS_WAITING_FOR_ANSWER)throw new T.InvalidStateError(this._status);if(this._sessionTimers.enabled&&C.isDecimal(t.sessionTimersExpires)&&(t.sessionTimersExpires>=y.MIN_SESSION_EXPIRES?this._sessionTimers.defaultExpires=t.sessionTimersExpires:this._sessionTimers.defaultExpires=y.SESSION_EXPIRES),this._status=x.STATUS_ANSWERED,this._createDialog(r,"UAS")){clearTimeout(this._timers.userNoAnswerTimer),s.unshift("Contact: ".concat(this._contact));var m=r.parseSDP();Array.isArray(m.media)||(m.media=[m.media]);var v,g=l(m.media);try{for(g.s();!(v=g.n()).done;){var S=v.value;"audio"===S.type&&(f=!0,S.direction&&"sendrecv"!==S.direction||(_=!0)),"video"===S.type&&(d=!0,S.direction&&"sendrecv"!==S.direction||(p=!0))}}catch(O){g.e(O)}finally{g.f()}if(o&&!1===i.audio){n=o.getAudioTracks();var E,b=l(n);try{for(b.s();!(E=b.n()).done;){var A=E.value;o.removeTrack(A)}}catch(O){b.e(O)}finally{b.f()}}if(o&&!1===i.video){n=o.getVideoTracks();var R,w=l(n);try{for(w.s();!(R=w.n()).done;){var I=R.value;o.removeTrack(I)}}catch(O){w.e(O)}finally{w.f()}}o||void 0!==i.audio||(i.audio=_),o||void 0!==i.video||(i.video=p),o||f||h.offerToReceiveAudio||(i.audio=!1),o||d||h.offerToReceiveVideo||(i.video=!1),this._createRTCConnection(u,a),Promise.resolve().then((function(){return o||(i.audio||i.video?(e._localMediaStreamLocallyGenerated=!0,navigator.mediaDevices.getUserMedia(i)["catch"]((function(t){if(e._status===x.STATUS_TERMINATED)throw new Error("terminated");throw r.reply(480),e._failed("local",null,y.causes.USER_DENIED_MEDIA_ACCESS),D('emit "getusermediafailed" [error:%o]',t),e.emit("getusermediafailed",t),new Error("getUserMedia() failed")}))):void 0)})).then((function(t){if(e._status===x.STATUS_TERMINATED)throw new Error("terminated");e._localMediaStream=t,t&&t.getTracks().forEach((function(n){e._connection.addTrack(n,t)}))})).then((function(){if(!e._late_sdp){var t={originator:"remote",type:"offer",sdp:r.body};P('emit "sdp"'),e.emit("sdp",t);var n=new RTCSessionDescription({type:"offer",sdp:t.sdp});return e._connectionPromiseQueue=e._connectionPromiseQueue.then((function(){return e._connection.setRemoteDescription(n)}))["catch"]((function(t){throw r.reply(488),e._failed("system",null,y.causes.WEBRTC_ERROR),D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',t),e.emit("peerconnection:setremotedescriptionfailed",t),new Error("peerconnection.setRemoteDescription() failed")})),e._connectionPromiseQueue}})).then((function(){if(e._status===x.STATUS_TERMINATED)throw new Error("terminated");return e._connecting(r),e._late_sdp?e._createLocalDescription("offer",e._rtcOfferConstraints)["catch"]((function(){throw r.reply(500),new Error("_createLocalDescription() failed")})):e._createLocalDescription("answer",c)["catch"]((function(){throw r.reply(500),new Error("_createLocalDescription() failed")}))})).then((function(t){if(e._status===x.STATUS_TERMINATED)throw new Error("terminated");e._handleSessionTimersInIncomingRequest(r,s),r.reply(200,null,s,t,(function(){e._status=x.STATUS_WAITING_FOR_ACK,e._setInvite2xxTimer(r,t),e._setACKTimer(),e._accepted("local")}),(function(){e._failed("system",null,y.causes.CONNECTION_ERROR)}))}))["catch"]((function(t){e._status!==x.STATUS_TERMINATED&&D(t)}))}else r.reply(500,"Error creating dialog")}},{key:"terminate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};P("terminate()");var n,r=t.cause||y.causes.BYE,l=C.cloneArray(t.extraHeaders),s=t.body,i=t.status_code,o=t.reason_phrase;if(this._status===x.STATUS_TERMINATED)throw new T.InvalidStateError(this._status);switch(this._status){case x.STATUS_NULL:case x.STATUS_INVITE_SENT:case x.STATUS_1XX_RECEIVED:if(P("canceling session"),i&&(i<200||i>=700))throw new TypeError("Invalid status_code: ".concat(i));i&&(o=o||y.REASON_PHRASE[i]||"",n="SIP ;cause=".concat(i,' ;text="').concat(o,'"')),this._status===x.STATUS_NULL||this._status===x.STATUS_INVITE_SENT?(this._is_canceled=!0,this._cancel_reason=n):this._status===x.STATUS_1XX_RECEIVED&&this._request.cancel(n),this._status=x.STATUS_CANCELED,this._failed("local",null,y.causes.CANCELED);break;case x.STATUS_WAITING_FOR_ANSWER:case x.STATUS_ANSWERED:if(P("rejecting session"),i=i||480,i<300||i>=700)throw new TypeError("Invalid status_code: ".concat(i));this._request.reply(i,o,l,s),this._failed("local",null,y.causes.REJECTED);break;case x.STATUS_WAITING_FOR_ACK:case x.STATUS_CONFIRMED:if(P("terminating session"),o=t.reason_phrase||y.REASON_PHRASE[i]||"",i&&(i<200||i>=700))throw new TypeError("Invalid status_code: ".concat(i));if(i&&l.push("Reason: SIP ;cause=".concat(i,'; text="').concat(o,'"')),this._status===x.STATUS_WAITING_FOR_ACK&&"incoming"===this._direction&&this._request.server_transaction.state!==S.C.STATUS_TERMINATED){var u=this._dialog;this.receiveRequest=function(t){var n=t.method;n===y.ACK&&(e.sendRequest(y.BYE,{extraHeaders:l,body:s}),u.terminate())},this._request.server_transaction.on("stateChanged",(function(){e._request.server_transaction.state===S.C.STATUS_TERMINATED&&(e.sendRequest(y.BYE,{extraHeaders:l,body:s}),u.terminate())})),this._ended("local",null,r),this._dialog=u,this._ua.newDialog(u)}else this.sendRequest(y.BYE,{extraHeaders:l,body:s}),this._ended("local",null,r)}}},{key:"sendDTMF",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};P("sendDTMF() | tones: %s",e);var n=0,r=t.duration||null,l=t.interToneGap||null,s=t.transportType||y.DTMF_TRANSPORT.INFO;if(void 0===e)throw new TypeError("Not enough arguments");if(this._status!==x.STATUS_CONFIRMED&&this._status!==x.STATUS_WAITING_FOR_ACK)throw new T.InvalidStateError(this._status);if(s!==y.DTMF_TRANSPORT.INFO&&s!==y.DTMF_TRANSPORT.RFC2833)throw new TypeError("invalid transportType: ".concat(s));if("number"===typeof e&&(e=e.toString()),!e||"string"!==typeof e||!e.match(/^[0-9A-DR#*,]+$/i))throw new TypeError("Invalid tones: ".concat(e));if(r&&!C.isDecimal(r))throw new TypeError("Invalid tone duration: ".concat(r));if(r?r<w.C.MIN_DURATION?(P('"duration" value is lower than the minimum allowed, setting it to '.concat(w.C.MIN_DURATION," milliseconds")),r=w.C.MIN_DURATION):r>w.C.MAX_DURATION?(P('"duration" value is greater than the maximum allowed, setting it to '.concat(w.C.MAX_DURATION," milliseconds")),r=w.C.MAX_DURATION):r=Math.abs(r):r=w.C.DEFAULT_DURATION,t.duration=r,l&&!C.isDecimal(l))throw new TypeError("Invalid interToneGap: ".concat(l));if(l?l<w.C.MIN_INTER_TONE_GAP?(P('"interToneGap" value is lower than the minimum allowed, setting it to '.concat(w.C.MIN_INTER_TONE_GAP," milliseconds")),l=w.C.MIN_INTER_TONE_GAP):l=Math.abs(l):l=w.C.DEFAULT_INTER_TONE_GAP,s!==y.DTMF_TRANSPORT.RFC2833)this._tones?this._tones+=e:(this._tones=e,o.call(this));else{var i=this._getDTMFRTPSender();i&&(e=i.toneBuffer+e,i.insertDTMF(e,r,l))}function o(){var e,s=this;if(this._status===x.STATUS_TERMINATED||!this._tones||n>=this._tones.length)this._tones=null;else{var i=this._tones[n];if(n+=1,","===i)e=2e3;else{var u=new w(this);t.eventHandlers={onFailed:function(){s._tones=null}},u.send(i,t),e=r+l}setTimeout(o.bind(this),e)}}}},{key:"sendInfo",value:function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(P("sendInfo()"),this._status!==x.STATUS_CONFIRMED&&this._status!==x.STATUS_WAITING_FOR_ACK)throw new T.InvalidStateError(this._status);var r=new I(this);r.send(e,t,n)}},{key:"mute",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{audio:!0,video:!1};P("mute()");var t=!1,n=!1;!1===this._audioMuted&&e.audio&&(t=!0,this._audioMuted=!0,this._toggleMuteAudio(!0)),!1===this._videoMuted&&e.video&&(n=!0,this._videoMuted=!0,this._toggleMuteVideo(!0)),!0!==t&&!0!==n||this._onmute({audio:t,video:n})}},{key:"unmute",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{audio:!0,video:!0};P("unmute()");var t=!1,n=!1;!0===this._audioMuted&&e.audio&&(t=!0,this._audioMuted=!1,!1===this._localHold&&this._toggleMuteAudio(!1)),!0===this._videoMuted&&e.video&&(n=!0,this._videoMuted=!1,!1===this._localHold&&this._toggleMuteVideo(!1)),!0!==t&&!0!==n||this._onunmute({audio:t,video:n})}},{key:"hold",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(P("hold()"),this._status!==x.STATUS_WAITING_FOR_ACK&&this._status!==x.STATUS_CONFIRMED)return!1;if(!0===this._localHold)return!1;if(!this._isReadyToReOffer())return!1;this._localHold=!0,this._onhold("local");var r={succeeded:function(){n&&n()},failed:function(){e.terminate({cause:y.causes.WEBRTC_ERROR,status_code:500,reason_phrase:"Hold Failed"})}};return t.useUpdate?this._sendUpdate({sdpOffer:!0,eventHandlers:r,extraHeaders:t.extraHeaders}):this._sendReinvite({eventHandlers:r,extraHeaders:t.extraHeaders}),!0}},{key:"unhold",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;if(P("unhold()"),this._status!==x.STATUS_WAITING_FOR_ACK&&this._status!==x.STATUS_CONFIRMED)return!1;if(!1===this._localHold)return!1;if(!this._isReadyToReOffer())return!1;this._localHold=!1,this._onunhold("local");var r={succeeded:function(){n&&n()},failed:function(){e.terminate({cause:y.causes.WEBRTC_ERROR,status_code:500,reason_phrase:"Unhold Failed"})}};return t.useUpdate?this._sendUpdate({sdpOffer:!0,eventHandlers:r,extraHeaders:t.extraHeaders}):this._sendReinvite({eventHandlers:r,extraHeaders:t.extraHeaders}),!0}},{key:"renegotiate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;P("renegotiate()");var r=t.rtcOfferConstraints||null;if(this._status!==x.STATUS_WAITING_FOR_ACK&&this._status!==x.STATUS_CONFIRMED)return!1;if(!this._isReadyToReOffer())return!1;var l={succeeded:function(){n&&n()},failed:function(){e.terminate({cause:y.causes.WEBRTC_ERROR,status_code:500,reason_phrase:"Media Renegotiation Failed"})}};return this._setLocalMediaStatus(),t.useUpdate?this._sendUpdate({sdpOffer:!0,eventHandlers:l,rtcOfferConstraints:r,extraHeaders:t.extraHeaders}):this._sendReinvite({eventHandlers:l,rtcOfferConstraints:r,extraHeaders:t.extraHeaders}),!0}},{key:"refer",value:function(e,t){var n=this;P("refer()");var r=e;if(this._status!==x.STATUS_WAITING_FOR_ACK&&this._status!==x.STATUS_CONFIRMED)return!1;if(e=this._ua.normalizeTarget(e),!e)throw new TypeError("Invalid target: ".concat(r));var l=new k(this);l.sendRefer(e,t);var s=l.id;return this._referSubscribers[s]=l,l.on("requestFailed",(function(){delete n._referSubscribers[s]})),l.on("accepted",(function(){delete n._referSubscribers[s]})),l.on("failed",(function(){delete n._referSubscribers[s]})),l}},{key:"sendRequest",value:function(e,t){return P("sendRequest()"),this._dialog.sendRequest(e,t)}},{key:"receiveRequest",value:function(e){var t=this;if(P("receiveRequest()"),e.method===y.CANCEL)this._status!==x.STATUS_WAITING_FOR_ANSWER&&this._status!==x.STATUS_ANSWERED||(this._status=x.STATUS_CANCELED,this._request.reply(487),this._failed("remote",e,y.causes.CANCELED));else switch(e.method){case y.ACK:if(this._status!==x.STATUS_WAITING_FOR_ACK)return;if(this._status=x.STATUS_CONFIRMED,clearTimeout(this._timers.ackTimer),clearTimeout(this._timers.invite2xxTimer),this._late_sdp){if(!e.body){this.terminate({cause:y.causes.MISSING_SDP,status_code:400});break}var n={originator:"remote",type:"answer",sdp:e.body};P('emit "sdp"'),this.emit("sdp",n);var r=new RTCSessionDescription({type:"answer",sdp:n.sdp});this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return t._connection.setRemoteDescription(r)})).then((function(){t._is_confirmed||t._confirmed("remote",e)}))["catch"]((function(e){t.terminate({cause:y.causes.BAD_MEDIA_DESCRIPTION,status_code:488}),D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',e),t.emit("peerconnection:setremotedescriptionfailed",e)}))}else this._is_confirmed||this._confirmed("remote",e);break;case y.BYE:this._status===x.STATUS_CONFIRMED||this._status===x.STATUS_WAITING_FOR_ACK?(e.reply(200),this._ended("remote",e,y.causes.BYE)):this._status===x.STATUS_INVITE_RECEIVED||this._status===x.STATUS_WAITING_FOR_ANSWER?(e.reply(200),this._request.reply(487,"BYE Received"),this._ended("remote",e,y.causes.BYE)):e.reply(403,"Wrong Status");break;case y.INVITE:this._status===x.STATUS_CONFIRMED?e.hasHeader("replaces")?this._receiveReplaces(e):this._receiveReinvite(e):e.reply(403,"Wrong Status");break;case y.INFO:if(this._status===x.STATUS_1XX_RECEIVED||this._status===x.STATUS_WAITING_FOR_ANSWER||this._status===x.STATUS_ANSWERED||this._status===x.STATUS_WAITING_FOR_ACK||this._status===x.STATUS_CONFIRMED){var l=e.hasHeader("Content-Type")?e.getHeader("Content-Type").toLowerCase():void 0;l&&l.match(/^application\/dtmf-relay/i)?new w(this).init_incoming(e):void 0!==l?new I(this).init_incoming(e):e.reply(415)}else e.reply(403,"Wrong Status");break;case y.UPDATE:this._status===x.STATUS_CONFIRMED?this._receiveUpdate(e):e.reply(403,"Wrong Status");break;case y.REFER:this._status===x.STATUS_CONFIRMED?this._receiveRefer(e):e.reply(403,"Wrong Status");break;case y.NOTIFY:this._status===x.STATUS_CONFIRMED?this._receiveNotify(e):e.reply(403,"Wrong Status");break;default:e.reply(501)}}},{key:"onTransportError",value:function(){D("onTransportError()"),this._status!==x.STATUS_TERMINATED&&this.terminate({status_code:500,reason_phrase:y.causes.CONNECTION_ERROR,cause:y.causes.CONNECTION_ERROR})}},{key:"onRequestTimeout",value:function(){D("onRequestTimeout()"),this._status!==x.STATUS_TERMINATED&&this.terminate({status_code:408,reason_phrase:y.causes.REQUEST_TIMEOUT,cause:y.causes.REQUEST_TIMEOUT})}},{key:"onDialogError",value:function(){D("onDialogError()"),this._status!==x.STATUS_TERMINATED&&this.terminate({status_code:500,reason_phrase:y.causes.DIALOG_ERROR,cause:y.causes.DIALOG_ERROR})}},{key:"newDTMF",value:function(e){P("newDTMF()"),this.emit("newDTMF",e)}},{key:"newInfo",value:function(e){P("newInfo()"),this.emit("newInfo",e)}},{key:"_isReadyToReOffer",value:function(){return this._rtcReady?this._dialog?!0!==this._dialog.uac_pending_reply&&!0!==this._dialog.uas_pending_reply||(P("_isReadyToReOffer() | there is another INVITE/UPDATE transaction in progress"),!1):(P("_isReadyToReOffer() | session not established yet"),!1):(P("_isReadyToReOffer() | internal WebRTC status not ready"),!1)}},{key:"_close",value:function(){if(P("close()"),this._status!==x.STATUS_TERMINATED){if(this._status=x.STATUS_TERMINATED,this._connection)try{this._connection.close()}catch(r){D("close() | error closing the RTCPeerConnection: %o",r)}for(var e in this._localMediaStream&&this._localMediaStreamLocallyGenerated&&(P("close() | closing local MediaStream"),C.closeMediaStream(this._localMediaStream)),this._timers)Object.prototype.hasOwnProperty.call(this._timers,e)&&clearTimeout(this._timers[e]);for(var t in clearTimeout(this._sessionTimers.timer),this._dialog&&(this._dialog.terminate(),delete this._dialog),this._earlyDialogs)Object.prototype.hasOwnProperty.call(this._earlyDialogs,t)&&(this._earlyDialogs[t].terminate(),delete this._earlyDialogs[t]);for(var n in this._referSubscribers)Object.prototype.hasOwnProperty.call(this._referSubscribers,n)&&delete this._referSubscribers[n];this._ua.destroyRTCSession(this)}}},{key:"_setInvite2xxTimer",value:function(e,t){var n=E.T1;function r(){this._status===x.STATUS_WAITING_FOR_ACK&&(e.reply(200,null,["Contact: ".concat(this._contact)],t),n<E.T2&&(n*=2,n>E.T2&&(n=E.T2)),this._timers.invite2xxTimer=setTimeout(r.bind(this),n))}this._timers.invite2xxTimer=setTimeout(r.bind(this),n)}},{key:"_setACKTimer",value:function(){var e=this;this._timers.ackTimer=setTimeout((function(){e._status===x.STATUS_WAITING_FOR_ACK&&(P("no ACK received, terminating the session"),clearTimeout(e._timers.invite2xxTimer),e.sendRequest(y.BYE),e._ended("remote",null,y.causes.NO_ACK))}),E.TIMER_H)}},{key:"_createRTCConnection",value:function(e,t){var n=this;this._connection=new RTCPeerConnection(e,t),this._connection.addEventListener("iceconnectionstatechange",(function(){var e=n._connection.iceConnectionState;"failed"===e&&n.terminate({cause:y.causes.RTP_TIMEOUT,status_code:408,reason_phrase:y.causes.RTP_TIMEOUT})})),P('emit "peerconnection"'),this.emit("peerconnection",{peerconnection:this._connection})}},{key:"_createLocalDescription",value:function(e,t){var n=this;if(P("createLocalDescription()"),"offer"!==e&&"answer"!==e)throw new Error('createLocalDescription() | invalid type "'.concat(e,'"'));var r=this._connection;return this._rtcReady=!1,Promise.resolve().then((function(){return"offer"===e?r.createOffer(t)["catch"]((function(e){return D('emit "peerconnection:createofferfailed" [error:%o]',e),n.emit("peerconnection:createofferfailed",e),Promise.reject(e)})):r.createAnswer(t)["catch"]((function(e){return D('emit "peerconnection:createanswerfailed" [error:%o]',e),n.emit("peerconnection:createanswerfailed",e),Promise.reject(e)}))})).then((function(e){return r.setLocalDescription(e)["catch"]((function(e){return n._rtcReady=!0,D('emit "peerconnection:setlocaldescriptionfailed" [error:%o]',e),n.emit("peerconnection:setlocaldescriptionfailed",e),Promise.reject(e)}))})).then((function(){if("complete"===r.iceGatheringState&&(!t||!t.iceRestart)){n._rtcReady=!0;var l={originator:"local",type:e,sdp:r.localDescription.sdp};return P('emit "sdp"'),n.emit("sdp",l),Promise.resolve(l.sdp)}return new Promise((function(t){var l,s,i=!1,o=function(){r.removeEventListener("icecandidate",l),r.removeEventListener("icegatheringstatechange",s),i=!0,n._rtcReady=!0;var o={originator:"local",type:e,sdp:r.localDescription.sdp};P('emit "sdp"'),n.emit("sdp",o),t(o.sdp)};r.addEventListener("icecandidate",l=function(e){var t=e.candidate;t?n.emit("icecandidate",{candidate:t,ready:o}):i||o()}),r.addEventListener("icegatheringstatechange",s=function(){"complete"!==r.iceGatheringState||i||o()})}))}))}},{key:"_createDialog",value:function(e,t,n){var r="UAS"===t?e.to_tag:e.from_tag,l="UAS"===t?e.from_tag:e.to_tag,s=e.call_id+r+l,i=this._earlyDialogs[s];if(n)return!!i||(i=new A(this,e,t,A.C.STATUS_EARLY),i.error?(P(i.error),this._failed("remote",e,y.causes.INTERNAL_ERROR),!1):(this._earlyDialogs[s]=i,!0));if(this._from_tag=e.from_tag,this._to_tag=e.to_tag,i)return i.update(e,t),this._dialog=i,delete this._earlyDialogs[s],!0;var o=new A(this,e,t);return o.error?(P(o.error),this._failed("remote",e,y.causes.INTERNAL_ERROR),!1):(this._dialog=o,!0)}},{key:"_receiveReinvite",value:function(e){var t=this;P("receiveReinvite()");var n=e.hasHeader("Content-Type")?e.getHeader("Content-Type").toLowerCase():void 0,r={request:e,callback:void 0,reject:s.bind(this)},l=!1;function s(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l=!0;var n=t.status_code||403,r=t.reason_phrase||"",s=C.cloneArray(t.extraHeaders);if(this._status!==x.STATUS_CONFIRMED)return!1;if(n<300||n>=700)throw new TypeError("Invalid status_code: ".concat(n));e.reply(n,r,s)}if(this.emit("reinvite",r),!l){if(this._late_sdp=!1,!e.body)return this._late_sdp=!0,this._remoteHold&&(this._remoteHold=!1,this._onunhold("remote")),void(this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return t._createLocalDescription("offer",t._rtcOfferConstraints)})).then((function(e){i.call(t,e)}))["catch"]((function(){e.reply(500)})));if("application/sdp"!==n)return P("invalid Content-Type"),void e.reply(415);this._processInDialogSdpOffer(e).then((function(e){t._status!==x.STATUS_TERMINATED&&i.call(t,e)}))["catch"]((function(e){D(e)}))}function i(t){var n=this,l=["Contact: ".concat(this._contact)];this._handleSessionTimersInIncomingRequest(e,l),this._late_sdp&&(t=this._mangleOffer(t)),e.reply(200,null,l,t,(function(){n._status=x.STATUS_WAITING_FOR_ACK,n._setInvite2xxTimer(e,t),n._setACKTimer()})),"function"===typeof r.callback&&r.callback()}}},{key:"_receiveUpdate",value:function(e){var t=this;P("receiveUpdate()");var n=e.hasHeader("Content-Type")?e.getHeader("Content-Type").toLowerCase():void 0,r={request:e,callback:void 0,reject:s.bind(this)},l=!1;function s(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};l=!0;var n=t.status_code||403,r=t.reason_phrase||"",s=C.cloneArray(t.extraHeaders);if(this._status!==x.STATUS_CONFIRMED)return!1;if(n<300||n>=700)throw new TypeError("Invalid status_code: ".concat(n));e.reply(n,r,s)}if(this.emit("update",r),!l)if(e.body){if("application/sdp"!==n)return P("invalid Content-Type"),void e.reply(415);this._processInDialogSdpOffer(e).then((function(e){t._status!==x.STATUS_TERMINATED&&i.call(t,e)}))["catch"]((function(e){D(e)}))}else i.call(this,null);function i(t){var n=["Contact: ".concat(this._contact)];this._handleSessionTimersInIncomingRequest(e,n),e.reply(200,null,n,t),"function"===typeof r.callback&&r.callback()}}},{key:"_processInDialogSdpOffer",value:function(e){var t=this;P("_processInDialogSdpOffer()");var n,r=e.parseSDP(),s=!1,i=l(r.media);try{for(i.s();!(n=i.n()).done;){var o=n.value;if(-1!==U.indexOf(o.type)){var u=o.direction||r.direction||"sendrecv";if("sendonly"!==u&&"inactive"!==u){s=!1;break}s=!0}}}catch(h){i.e(h)}finally{i.f()}var a={originator:"remote",type:"offer",sdp:e.body};P('emit "sdp"'),this.emit("sdp",a);var c=new RTCSessionDescription({type:"offer",sdp:a.sdp});return this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){if(t._status===x.STATUS_TERMINATED)throw new Error("terminated");return t._connection.setRemoteDescription(c)["catch"]((function(n){throw e.reply(488),D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',n),t.emit("peerconnection:setremotedescriptionfailed",n),n}))})).then((function(){if(t._status===x.STATUS_TERMINATED)throw new Error("terminated");!0===t._remoteHold&&!1===s?(t._remoteHold=!1,t._onunhold("remote")):!1===t._remoteHold&&!0===s&&(t._remoteHold=!0,t._onhold("remote"))})).then((function(){if(t._status===x.STATUS_TERMINATED)throw new Error("terminated");return t._createLocalDescription("answer",t._rtcAnswerConstraints)["catch"]((function(t){throw e.reply(500),D('emit "peerconnection:createtelocaldescriptionfailed" [error:%o]',t),t}))}))["catch"]((function(e){D("_processInDialogSdpOffer() failed [error: %o]",e)})),this._connectionPromiseQueue}},{key:"_receiveRefer",value:function(e){var t=this;if(P("receiveRefer()"),!e.refer_to)return P("no Refer-To header field present in REFER"),void e.reply(400);if(e.refer_to.uri.scheme!==y.SIP)return P("Refer-To header field points to a non-SIP URI scheme"),void e.reply(416);e.reply(202);var r=new O(this,e.cseq);function l(t){var l=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(t="function"===typeof t?t:null,this._status!==x.STATUS_WAITING_FOR_ACK&&this._status!==x.STATUS_CONFIRMED)return!1;var s=new n(this._ua);if(s.on("progress",(function(e){var t=e.response;r.notify(t.status_code,t.reason_phrase)})),s.on("accepted",(function(e){var t=e.response;r.notify(t.status_code,t.reason_phrase)})),s.on("_failed",(function(e){var t=e.message,n=e.cause;t?r.notify(t.status_code,t.reason_phrase):r.notify(487,n)})),e.refer_to.uri.hasHeader("replaces")){var i=decodeURIComponent(e.refer_to.uri.getHeader("replaces"));l.extraHeaders=C.cloneArray(l.extraHeaders),l.extraHeaders.push("Replaces: ".concat(i))}s.connect(e.refer_to.uri.toAor(),l,t)}function s(){r.notify(603)}P('emit "refer"'),this.emit("refer",{request:e,accept:function(e,n){l.call(t,e,n)},reject:function(){s.call(t)}})}},{key:"_receiveNotify",value:function(e){switch(P("receiveNotify()"),e.event||e.reply(400),e.event.event){case"refer":var t,n;if(e.event.params&&e.event.params.id)t=e.event.params.id,n=this._referSubscribers[t];else{if(1!==Object.keys(this._referSubscribers).length)return void e.reply(400,"Missing event id parameter");n=this._referSubscribers[Object.keys(this._referSubscribers)[0]]}if(!n)return void e.reply(481,"Subscription does not exist");n.receiveNotify(e),e.reply(200);break;default:e.reply(489)}}},{key:"_receiveReplaces",value:function(e){var t=this;function r(t){var r=this;if(this._status!==x.STATUS_WAITING_FOR_ACK&&this._status!==x.STATUS_CONFIRMED)return!1;var l=new n(this._ua);l.on("confirmed",(function(){r.terminate()})),l.init_incoming(e,t)}function l(){P("Replaced INVITE rejected by the user"),e.reply(486)}P("receiveReplaces()"),this.emit("replaces",{request:e,accept:function(e){r.call(t,e)},reject:function(){l.call(t)}})}},{key:"_sendInitialRequest",value:function(e,t,n){var r=this,l=new R(this._ua,this._request,{onRequestTimeout:function(){r.onRequestTimeout()},onTransportError:function(){r.onTransportError()},onAuthenticated:function(e){r._request=e},onReceiveResponse:function(e){r._receiveInviteResponse(e)}});Promise.resolve().then((function(){return n||(e.audio||e.video?(r._localMediaStreamLocallyGenerated=!0,navigator.mediaDevices.getUserMedia(e)["catch"]((function(e){if(r._status===x.STATUS_TERMINATED)throw new Error("terminated");throw r._failed("local",null,y.causes.USER_DENIED_MEDIA_ACCESS),D('emit "getusermediafailed" [error:%o]',e),r.emit("getusermediafailed",e),e}))):void 0)})).then((function(e){if(r._status===x.STATUS_TERMINATED)throw new Error("terminated");return r._localMediaStream=e,e&&e.getTracks().forEach((function(t){r._connection.addTrack(t,e)})),r._connecting(r._request),r._createLocalDescription("offer",t)["catch"]((function(e){throw r._failed("local",null,y.causes.WEBRTC_ERROR),e}))})).then((function(e){if(r._is_canceled||r._status===x.STATUS_TERMINATED)throw new Error("terminated");r._request.body=e,r._status=x.STATUS_INVITE_SENT,P('emit "sending" [request:%o]',r._request),r.emit("sending",{request:r._request}),l.send()}))["catch"]((function(e){r._status!==x.STATUS_TERMINATED&&D(e)}))}},{key:"_getDTMFRTPSender",value:function(){var e=this._connection.getSenders().find((function(e){return e.track&&"audio"===e.track.kind}));if(e&&e.dtmf)return e.dtmf;D("sendDTMF() | no local audio track to send DTMF with")}},{key:"_receiveInviteResponse",value:function(e){var t=this;if(P("receiveInviteResponse()"),this._dialog&&e.status_code>=200&&e.status_code<=299){if(this._dialog.id.call_id===e.call_id&&this._dialog.id.local_tag===e.from_tag&&this._dialog.id.remote_tag===e.to_tag)return void this.sendRequest(y.ACK);var n=new A(this,e,"UAC");return void 0!==n.error?void P(n.error):(this.sendRequest(y.ACK),void this.sendRequest(y.BYE))}if(this._is_canceled)e.status_code>=100&&e.status_code<200?this._request.cancel(this._cancel_reason):e.status_code>=200&&e.status_code<299&&this._acceptAndTerminate(e);else if(this._status===x.STATUS_INVITE_SENT||this._status===x.STATUS_1XX_RECEIVED)switch(!0){case/^100$/.test(e.status_code):this._status=x.STATUS_1XX_RECEIVED;break;case/^1[0-9]{2}$/.test(e.status_code):if(!e.to_tag){P("1xx response received without to tag");break}if(e.hasHeader("contact")&&!this._createDialog(e,"UAC",!0))break;if(this._status=x.STATUS_1XX_RECEIVED,!e.body){this._progress("remote",e);break}var r={originator:"remote",type:"answer",sdp:e.body};P('emit "sdp"'),this.emit("sdp",r);var l=new RTCSessionDescription({type:"answer",sdp:r.sdp});this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return t._connection.setRemoteDescription(l)})).then((function(){return t._progress("remote",e)}))["catch"]((function(e){D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',e),t.emit("peerconnection:setremotedescriptionfailed",e)}));break;case/^2[0-9]{2}$/.test(e.status_code):if(this._status=x.STATUS_CONFIRMED,!e.body){this._acceptAndTerminate(e,400,y.causes.MISSING_SDP),this._failed("remote",e,y.causes.BAD_MEDIA_DESCRIPTION);break}if(!this._createDialog(e,"UAC"))break;var s={originator:"remote",type:"answer",sdp:e.body};P('emit "sdp"'),this.emit("sdp",s);var i=new RTCSessionDescription({type:"answer",sdp:s.sdp});this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){if("stable"===t._connection.signalingState)return t._connection.createOffer(t._rtcOfferConstraints).then((function(e){return t._connection.setLocalDescription(e)}))["catch"]((function(n){t._acceptAndTerminate(e,500,n.toString()),t._failed("local",e,y.causes.WEBRTC_ERROR)}))})).then((function(){t._connection.setRemoteDescription(i).then((function(){t._handleSessionTimersInIncomingResponse(e),t._accepted("remote",e),t.sendRequest(y.ACK),t._confirmed("local",null)}))["catch"]((function(n){t._acceptAndTerminate(e,488,"Not Acceptable Here"),t._failed("remote",e,y.causes.BAD_MEDIA_DESCRIPTION),D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',n),t.emit("peerconnection:setremotedescriptionfailed",n)}))}));break;default:var o=C.sipErrorCause(e.status_code);this._failed("remote",e,o)}}},{key:"_sendReinvite",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};P("sendReinvite()");var n=C.cloneArray(t.extraHeaders),r=C.cloneObject(t.eventHandlers),l=t.rtcOfferConstraints||this._rtcOfferConstraints||null,s=!1;function i(e){var t=this;if(this._status!==x.STATUS_TERMINATED&&(this.sendRequest(y.ACK),!s))if(this._handleSessionTimersInIncomingResponse(e),e.body)if(e.hasHeader("Content-Type")&&"application/sdp"===e.getHeader("Content-Type").toLowerCase()){var n={originator:"remote",type:"answer",sdp:e.body};P('emit "sdp"'),this.emit("sdp",n);var l=new RTCSessionDescription({type:"answer",sdp:n.sdp});this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return t._connection.setRemoteDescription(l)})).then((function(){r.succeeded&&r.succeeded(e)}))["catch"]((function(e){o.call(t),D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',e),t.emit("peerconnection:setremotedescriptionfailed",e)}))}else o.call(this);else o.call(this)}function o(e){r.failed&&r.failed(e)}n.push("Contact: ".concat(this._contact)),n.push("Content-Type: application/sdp"),this._sessionTimers.running&&n.push("Session-Expires: ".concat(this._sessionTimers.currentExpires,";refresher=").concat(this._sessionTimers.refresher?"uac":"uas")),this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return e._createLocalDescription("offer",l)})).then((function(t){t=e._mangleOffer(t);var r={originator:"local",type:"offer",sdp:t};P('emit "sdp"'),e.emit("sdp",r),e.sendRequest(y.INVITE,{extraHeaders:n,body:t,eventHandlers:{onSuccessResponse:function(t){i.call(e,t),s=!0},onErrorResponse:function(t){o.call(e,t)},onTransportError:function(){e.onTransportError()},onRequestTimeout:function(){e.onRequestTimeout()},onDialogError:function(){e.onDialogError()}}})}))["catch"]((function(){o()}))}},{key:"_sendUpdate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};P("sendUpdate()");var n=C.cloneArray(t.extraHeaders),r=C.cloneObject(t.eventHandlers),l=t.rtcOfferConstraints||this._rtcOfferConstraints||null,s=t.sdpOffer||!1,i=!1;function o(e){var t=this;if(this._status!==x.STATUS_TERMINATED&&!i)if(this._handleSessionTimersInIncomingResponse(e),s){if(!e.body)return void u.call(this);if(!e.hasHeader("Content-Type")||"application/sdp"!==e.getHeader("Content-Type").toLowerCase())return void u.call(this);var n={originator:"remote",type:"answer",sdp:e.body};P('emit "sdp"'),this.emit("sdp",n);var l=new RTCSessionDescription({type:"answer",sdp:n.sdp});this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return t._connection.setRemoteDescription(l)})).then((function(){r.succeeded&&r.succeeded(e)}))["catch"]((function(e){u.call(t),D('emit "peerconnection:setremotedescriptionfailed" [error:%o]',e),t.emit("peerconnection:setremotedescriptionfailed",e)}))}else r.succeeded&&r.succeeded(e)}function u(e){r.failed&&r.failed(e)}n.push("Contact: ".concat(this._contact)),this._sessionTimers.running&&n.push("Session-Expires: ".concat(this._sessionTimers.currentExpires,";refresher=").concat(this._sessionTimers.refresher?"uac":"uas")),s?(n.push("Content-Type: application/sdp"),this._connectionPromiseQueue=this._connectionPromiseQueue.then((function(){return e._createLocalDescription("offer",l)})).then((function(t){t=e._mangleOffer(t);var r={originator:"local",type:"offer",sdp:t};P('emit "sdp"'),e.emit("sdp",r),e.sendRequest(y.UPDATE,{extraHeaders:n,body:t,eventHandlers:{onSuccessResponse:function(t){o.call(e,t),i=!0},onErrorResponse:function(t){u.call(e,t)},onTransportError:function(){e.onTransportError()},onRequestTimeout:function(){e.onRequestTimeout()},onDialogError:function(){e.onDialogError()}}})}))["catch"]((function(){u.call(e)}))):this.sendRequest(y.UPDATE,{extraHeaders:n,eventHandlers:{onSuccessResponse:function(t){o.call(e,t)},onErrorResponse:function(t){u.call(e,t)},onTransportError:function(){e.onTransportError()},onRequestTimeout:function(){e.onRequestTimeout()},onDialogError:function(){e.onDialogError()}}})}},{key:"_acceptAndTerminate",value:function(e,t,n){P("acceptAndTerminate()");var r=[];t&&(n=n||y.REASON_PHRASE[t]||"",r.push("Reason: SIP ;cause=".concat(t,'; text="').concat(n,'"'))),(this._dialog||this._createDialog(e,"UAC"))&&(this.sendRequest(y.ACK),this.sendRequest(y.BYE,{extraHeaders:r})),this._status=x.STATUS_TERMINATED}},{key:"_mangleOffer",value:function(e){if(!this._localHold&&!this._remoteHold)return e;if(e=g.parse(e),this._localHold&&!this._remoteHold){P("mangleOffer() | me on hold, mangling offer");var t,n=l(e.media);try{for(n.s();!(t=n.n()).done;){var r=t.value;-1!==U.indexOf(r.type)&&(r.direction?"sendrecv"===r.direction?r.direction="sendonly":"recvonly"===r.direction&&(r.direction="inactive"):r.direction="sendonly")}}catch(h){n.e(h)}finally{n.f()}}else if(this._localHold&&this._remoteHold){P("mangleOffer() | both on hold, mangling offer");var s,i=l(e.media);try{for(i.s();!(s=i.n()).done;){var o=s.value;-1!==U.indexOf(o.type)&&(o.direction="inactive")}}catch(h){i.e(h)}finally{i.f()}}else if(this._remoteHold){P("mangleOffer() | remote on hold, mangling offer");var u,a=l(e.media);try{for(a.s();!(u=a.n()).done;){var c=u.value;-1!==U.indexOf(c.type)&&(c.direction?"sendrecv"===c.direction?c.direction="recvonly":"recvonly"===c.direction&&(c.direction="inactive"):c.direction="recvonly")}}catch(h){a.e(h)}finally{a.f()}}return g.write(e)}},{key:"_setLocalMediaStatus",value:function(){var e=!0,t=!0;(this._localHold||this._remoteHold)&&(e=!1,t=!1),this._audioMuted&&(e=!1),this._videoMuted&&(t=!1),this._toggleMuteAudio(!e),this._toggleMuteVideo(!t)}},{key:"_handleSessionTimersInIncomingRequest",value:function(e,t){var n;this._sessionTimers.enabled&&(e.session_expires&&e.session_expires>=y.MIN_SESSION_EXPIRES?(this._sessionTimers.currentExpires=e.session_expires,n=e.session_expires_refresher||"uas"):(this._sessionTimers.currentExpires=this._sessionTimers.defaultExpires,n="uas"),t.push("Session-Expires: ".concat(this._sessionTimers.currentExpires,";refresher=").concat(n)),this._sessionTimers.refresher="uas"===n,this._runSessionTimer())}},{key:"_handleSessionTimersInIncomingResponse",value:function(e){var t;this._sessionTimers.enabled&&(e.session_expires&&e.session_expires>=y.MIN_SESSION_EXPIRES?(this._sessionTimers.currentExpires=e.session_expires,t=e.session_expires_refresher||"uac"):(this._sessionTimers.currentExpires=this._sessionTimers.defaultExpires,t="uac"),this._sessionTimers.refresher="uac"===t,this._runSessionTimer())}},{key:"_runSessionTimer",value:function(){var e=this,t=this._sessionTimers.currentExpires;this._sessionTimers.running=!0,clearTimeout(this._sessionTimers.timer),this._sessionTimers.refresher?this._sessionTimers.timer=setTimeout((function(){e._status!==x.STATUS_TERMINATED&&(P("runSessionTimer() | sending session refresh request"),e._sessionTimers.refreshMethod===y.UPDATE?e._sendUpdate():e._sendReinvite())}),500*t):this._sessionTimers.timer=setTimeout((function(){e._status!==x.STATUS_TERMINATED&&(D("runSessionTimer() | timer expired, terminating the session"),e.terminate({cause:y.causes.REQUEST_TIMEOUT,status_code:408,reason_phrase:"Session Timer Expired"}))}),1100*t)}},{key:"_toggleMuteAudio",value:function(e){var t,n=this._connection.getSenders().filter((function(e){return e.track&&"audio"===e.track.kind})),r=l(n);try{for(r.s();!(t=r.n()).done;){var s=t.value;s.track.enabled=!e}}catch(i){r.e(i)}finally{r.f()}}},{key:"_toggleMuteVideo",value:function(e){var t,n=this._connection.getSenders().filter((function(e){return e.track&&"video"===e.track.kind})),r=l(n);try{for(r.s();!(t=r.n()).done;){var s=t.value;s.track.enabled=!e}}catch(i){r.e(i)}finally{r.f()}}},{key:"_newRTCSession",value:function(e,t){P("newRTCSession()"),this._ua.newRTCSession(this,{originator:e,session:this,request:t})}},{key:"_connecting",value:function(e){P("session connecting"),P('emit "connecting"'),this.emit("connecting",{request:e})}},{key:"_progress",value:function(e,t){P("session progress"),P('emit "progress"'),this.emit("progress",{originator:e,response:t||null})}},{key:"_accepted",value:function(e,t){P("session accepted"),this._start_time=new Date,P('emit "accepted"'),this.emit("accepted",{originator:e,response:t||null})}},{key:"_confirmed",value:function(e,t){P("session confirmed"),this._is_confirmed=!0,P('emit "confirmed"'),this.emit("confirmed",{originator:e,ack:t||null})}},{key:"_ended",value:function(e,t,n){P("session ended"),this._end_time=new Date,this._close(),P('emit "ended"'),this.emit("ended",{originator:e,message:t||null,cause:n})}},{key:"_failed",value:function(e,t,n){P("session failed"),P('emit "_failed"'),this.emit("_failed",{originator:e,message:t||null,cause:n}),this._close(),P('emit "failed"'),this.emit("failed",{originator:e,message:t||null,cause:n})}},{key:"_onhold",value:function(e){P("session onhold"),this._setLocalMediaStatus(),P('emit "hold"'),this.emit("hold",{originator:e})}},{key:"_onunhold",value:function(e){P("session onunhold"),this._setLocalMediaStatus(),P('emit "unhold"'),this.emit("unhold",{originator:e})}},{key:"_onmute",value:function(e){var t=e.audio,n=e.video;P("session onmute"),this._setLocalMediaStatus(),P('emit "muted"'),this.emit("muted",{audio:t,video:n})}},{key:"_onunmute",value:function(e){var t=e.audio,n=e.video;P("session onunmute"),this._setLocalMediaStatus(),P('emit "unmuted"'),this.emit("unmuted",{audio:t,video:n})}},{key:"C",get:function(){return x}},{key:"causes",get:function(){return y.causes}},{key:"id",get:function(){return this._id}},{key:"connection",get:function(){return this._connection}},{key:"contact",get:function(){return this._contact}},{key:"direction",get:function(){return this._direction}},{key:"local_identity",get:function(){return this._local_identity}},{key:"remote_identity",get:function(){return this._remote_identity}},{key:"start_time",get:function(){return this._start_time}},{key:"end_time",get:function(){return this._end_time}},{key:"data",get:function(){return this._data},set:function(e){this._data=e}},{key:"status",get:function(){return this._status}}]),n}(v)},"3baa":function(e,t){var n=e.exports={v:[{name:"version",reg:/^(\d*)$/}],o:[{name:"origin",reg:/^(\S*) (\d*) (\d*) (\S*) IP(\d) (\S*)/,names:["username","sessionId","sessionVersion","netType","ipVer","address"],format:"%s %s %d %s IP%d %s"}],s:[{name:"name"}],i:[{name:"description"}],u:[{name:"uri"}],e:[{name:"email"}],p:[{name:"phone"}],z:[{name:"timezones"}],r:[{name:"repeats"}],t:[{name:"timing",reg:/^(\d*) (\d*)/,names:["start","stop"],format:"%d %d"}],c:[{name:"connection",reg:/^IN IP(\d) (\S*)/,names:["version","ip"],format:"IN IP%d %s"}],b:[{push:"bandwidth",reg:/^(TIAS|AS|CT|RR|RS):(\d*)/,names:["type","limit"],format:"%s:%s"}],m:[{reg:/^(\w*) (\d*) ([\w/]*)(?: (.*))?/,names:["type","port","protocol","payloads"],format:"%s %d %s %s"}],a:[{push:"rtp",reg:/^rtpmap:(\d*) ([\w\-.]*)(?:\s*\/(\d*)(?:\s*\/(\S*))?)?/,names:["payload","codec","rate","encoding"],format:function(e){return e.encoding?"rtpmap:%d %s/%s/%s":e.rate?"rtpmap:%d %s/%s":"rtpmap:%d %s"}},{push:"fmtp",reg:/^fmtp:(\d*) ([\S| ]*)/,names:["payload","config"],format:"fmtp:%d %s"},{name:"control",reg:/^control:(.*)/,format:"control:%s"},{name:"rtcp",reg:/^rtcp:(\d*)(?: (\S*) IP(\d) (\S*))?/,names:["port","netType","ipVer","address"],format:function(e){return null!=e.address?"rtcp:%d %s IP%d %s":"rtcp:%d"}},{push:"rtcpFbTrrInt",reg:/^rtcp-fb:(\*|\d*) trr-int (\d*)/,names:["payload","value"],format:"rtcp-fb:%s trr-int %d"},{push:"rtcpFb",reg:/^rtcp-fb:(\*|\d*) ([\w-_]*)(?: ([\w-_]*))?/,names:["payload","type","subtype"],format:function(e){return null!=e.subtype?"rtcp-fb:%s %s %s":"rtcp-fb:%s %s"}},{push:"ext",reg:/^extmap:(\d+)(?:\/(\w+))?(?: (urn:ietf:params:rtp-hdrext:encrypt))? (\S*)(?: (\S*))?/,names:["value","direction","encrypt-uri","uri","config"],format:function(e){return"extmap:%d"+(e.direction?"/%s":"%v")+(e["encrypt-uri"]?" %s":"%v")+" %s"+(e.config?" %s":"")}},{name:"extmapAllowMixed",reg:/^(extmap-allow-mixed)/},{push:"crypto",reg:/^crypto:(\d*) ([\w_]*) (\S*)(?: (\S*))?/,names:["id","suite","config","sessionConfig"],format:function(e){return null!=e.sessionConfig?"crypto:%d %s %s %s":"crypto:%d %s %s"}},{name:"setup",reg:/^setup:(\w*)/,format:"setup:%s"},{name:"connectionType",reg:/^connection:(new|existing)/,format:"connection:%s"},{name:"mid",reg:/^mid:([^\s]*)/,format:"mid:%s"},{name:"msid",reg:/^msid:(.*)/,format:"msid:%s"},{name:"ptime",reg:/^ptime:(\d*(?:\.\d*)*)/,format:"ptime:%d"},{name:"maxptime",reg:/^maxptime:(\d*(?:\.\d*)*)/,format:"maxptime:%d"},{name:"direction",reg:/^(sendrecv|recvonly|sendonly|inactive)/},{name:"icelite",reg:/^(ice-lite)/},{name:"iceUfrag",reg:/^ice-ufrag:(\S*)/,format:"ice-ufrag:%s"},{name:"icePwd",reg:/^ice-pwd:(\S*)/,format:"ice-pwd:%s"},{name:"fingerprint",reg:/^fingerprint:(\S*) (\S*)/,names:["type","hash"],format:"fingerprint:%s %s"},{push:"candidates",reg:/^candidate:(\S*) (\d*) (\S*) (\d*) (\S*) (\d*) typ (\S*)(?: raddr (\S*) rport (\d*))?(?: tcptype (\S*))?(?: generation (\d*))?(?: network-id (\d*))?(?: network-cost (\d*))?/,names:["foundation","component","transport","priority","ip","port","type","raddr","rport","tcptype","generation","network-id","network-cost"],format:function(e){var t="candidate:%s %d %s %d %s %d typ %s";return t+=null!=e.raddr?" raddr %s rport %d":"%v%v",t+=null!=e.tcptype?" tcptype %s":"%v",null!=e.generation&&(t+=" generation %d"),t+=null!=e["network-id"]?" network-id %d":"%v",t+=null!=e["network-cost"]?" network-cost %d":"%v",t}},{name:"endOfCandidates",reg:/^(end-of-candidates)/},{name:"remoteCandidates",reg:/^remote-candidates:(.*)/,format:"remote-candidates:%s"},{name:"iceOptions",reg:/^ice-options:(\S*)/,format:"ice-options:%s"},{push:"ssrcs",reg:/^ssrc:(\d*) ([\w_-]*)(?::(.*))?/,names:["id","attribute","value"],format:function(e){var t="ssrc:%d";return null!=e.attribute&&(t+=" %s",null!=e.value&&(t+=":%s")),t}},{push:"ssrcGroups",reg:/^ssrc-group:([\x21\x23\x24\x25\x26\x27\x2A\x2B\x2D\x2E\w]*) (.*)/,names:["semantics","ssrcs"],format:"ssrc-group:%s %s"},{name:"msidSemantic",reg:/^msid-semantic:\s?(\w*) (\S*)/,names:["semantic","token"],format:"msid-semantic: %s %s"},{push:"groups",reg:/^group:(\w*) (.*)/,names:["type","mids"],format:"group:%s %s"},{name:"rtcpMux",reg:/^(rtcp-mux)/},{name:"rtcpRsize",reg:/^(rtcp-rsize)/},{name:"sctpmap",reg:/^sctpmap:([\w_/]*) (\S*)(?: (\S*))?/,names:["sctpmapNumber","app","maxMessageSize"],format:function(e){return null!=e.maxMessageSize?"sctpmap:%s %s %s":"sctpmap:%s %s"}},{name:"xGoogleFlag",reg:/^x-google-flag:([^\s]*)/,format:"x-google-flag:%s"},{push:"rids",reg:/^rid:([\d\w]+) (\w+)(?: ([\S| ]*))?/,names:["id","direction","params"],format:function(e){return e.params?"rid:%s %s %s":"rid:%s %s"}},{push:"imageattrs",reg:new RegExp("^imageattr:(\\d+|\\*)[\\s\\t]+(send|recv)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*)(?:[\\s\\t]+(recv|send)[\\s\\t]+(\\*|\\[\\S+\\](?:[\\s\\t]+\\[\\S+\\])*))?"),names:["pt","dir1","attrs1","dir2","attrs2"],format:function(e){return"imageattr:%s %s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast",reg:new RegExp("^simulcast:(send|recv) ([a-zA-Z0-9\\-_~;,]+)(?:\\s?(send|recv) ([a-zA-Z0-9\\-_~;,]+))?$"),names:["dir1","list1","dir2","list2"],format:function(e){return"simulcast:%s %s"+(e.dir2?" %s %s":"")}},{name:"simulcast_03",reg:/^simulcast:[\s\t]+([\S+\s\t]+)$/,names:["value"],format:"simulcast: %s"},{name:"framerate",reg:/^framerate:(\d+(?:$|\.\d+))/,format:"framerate:%s"},{name:"sourceFilter",reg:/^source-filter: *(excl|incl) (\S*) (IP4|IP6|\*) (\S*) (.*)/,names:["filterMode","netType","addressTypes","destAddress","srcList"],format:"source-filter: %s %s %s %s %s"},{name:"bundleOnly",reg:/^(bundle-only)/},{name:"label",reg:/^label:(.+)/,format:"label:%s"},{name:"sctpPort",reg:/^sctp-port:(\d+)$/,format:"sctp-port:%s"},{name:"maxMessageSize",reg:/^max-message-size:(\d+)$/,format:"max-message-size:%s"},{push:"tsRefClocks",reg:/^ts-refclk:([^\s=]*)(?:=(\S*))?/,names:["clksrc","clksrcExt"],format:function(e){return"ts-refclk:%s"+(null!=e.clksrcExt?"=%s":"")}},{name:"mediaClk",reg:/^mediaclk:(?:id=(\S*))? *([^\s=]*)(?:=(\S*))?(?: *rate=(\d+)\/(\d+))?/,names:["id","mediaClockName","mediaClockValue","rateNumerator","rateDenominator"],format:function(e){var t="mediaclk:";return t+=null!=e.id?"id=%s %s":"%v%s",t+=null!=e.mediaClockValue?"=%s":"",t+=null!=e.rateNumerator?" rate=%s":"",t+=null!=e.rateDenominator?"/%s":"",t}},{name:"keywords",reg:/^keywds:(.+)$/,format:"keywds:%s"},{name:"content",reg:/^content:(.+)/,format:"content:%s"},{name:"bfcpFloorCtrl",reg:/^floorctrl:(c-only|s-only|c-s)/,format:"floorctrl:%s"},{name:"bfcpConfId",reg:/^confid:(\d+)/,format:"confid:%s"},{name:"bfcpUserId",reg:/^userid:(\d+)/,format:"userid:%s"},{name:"bfcpFloorId",reg:/^floorid:(.+) (?:m-stream|mstrm):(.+)/,names:["id","mStream"],format:"floorid:%s mstrm:%s"},{push:"invalid",names:["value"]}]};Object.keys(n).forEach((function(e){var t=n[e];t.forEach((function(e){e.reg||(e.reg=/(.*)/),e.format||(e.format="%s")}))}))},"488a":function(e,t,n){"use strict";var r=500,l=4e3,s=5e3;e.exports={T1:r,T2:l,T4:s,TIMER_B:64*r,TIMER_D:0*r,TIMER_F:64*r,TIMER_H:64*r,TIMER_I:0*r,TIMER_J:0*r,TIMER_K:0*s,TIMER_L:64*r,TIMER_M:64*r,PROVISIONAL_RESPONSE_INTERVAL:6e4}},"4a05":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("9cf5"),o=n("1c8d");e.exports=function(){function e(t,n,l){if(r(this,e),!t||!(t instanceof i))throw new TypeError('missing or invalid "uri" parameter');for(var s in this._uri=t,this._parameters={},this.display_name=n,l)Object.prototype.hasOwnProperty.call(l,s)&&this.setParam(s,l[s])}return s(e,null,[{key:"parse",value:function(e){return e=o.parse(e,"Name_Addr_Header"),-1!==e?e:void 0}}]),s(e,[{key:"setParam",value:function(e,t){e&&(this._parameters[e.toLowerCase()]="undefined"===typeof t||null===t?null:t.toString())}},{key:"getParam",value:function(e){if(e)return this._parameters[e.toLowerCase()]}},{key:"hasParam",value:function(e){if(e)return!!this._parameters.hasOwnProperty(e.toLowerCase())}},{key:"deleteParam",value:function(e){if(e=e.toLowerCase(),this._parameters.hasOwnProperty(e)){var t=this._parameters[e];return delete this._parameters[e],t}}},{key:"clearParams",value:function(){this._parameters={}}},{key:"clone",value:function(){return new e(this._uri.clone(),this._display_name,JSON.parse(JSON.stringify(this._parameters)))}},{key:"_quote",value:function(e){return e.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}},{key:"toString",value:function(){var e=this._display_name?'"'.concat(this._quote(this._display_name),'" '):"";for(var t in e+="<".concat(this._uri.toString(),">"),this._parameters)Object.prototype.hasOwnProperty.call(this._parameters,t)&&(e+=";".concat(t),null!==this._parameters[t]&&(e+="=".concat(this._parameters[t])));return e}},{key:"uri",get:function(){return this._uri}},{key:"display_name",get:function(){return this._display_name},set:function(e){this._display_name=0===e?"0":e}}]),e}()},"50d3":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function a(e){var t=f();return function(){var n,r=d(e);if(t){var l=d(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var _=n("faa1").EventEmitter,p=n("1e8c"),m=n("8c52"),v=n("0790"),g=n("34eb")("JsSIP:RTCSession:DTMF"),y=n("34eb")("JsSIP:ERROR:RTCSession:DTMF");y.log=console.warn.bind(console);var T={MIN_DURATION:70,MAX_DURATION:6e3,DEFAULT_DURATION:100,MIN_INTER_TONE_GAP:50,DEFAULT_INTER_TONE_GAP:500};e.exports=function(e){o(n,e);var t=a(n);function n(e){var r;return l(this,n),r=t.call(this),r._session=e,r._direction=null,r._tone=null,r._duration=null,r._request=null,r}return i(n,[{key:"send",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(void 0===e)throw new TypeError("Not enough arguments");if(this._direction="outgoing",this._session.status!==this._session.C.STATUS_CONFIRMED&&this._session.status!==this._session.C.STATUS_WAITING_FOR_ACK)throw new m.InvalidStateError(this._session.status);var r=v.cloneArray(n.extraHeaders);if(this.eventHandlers=v.cloneObject(n.eventHandlers),"string"===typeof e)e=e.toUpperCase();else{if("number"!==typeof e)throw new TypeError("Invalid tone: ".concat(e));e=e.toString()}if(!e.match(/^[0-9A-DR#*]$/))throw new TypeError("Invalid tone: ".concat(e));this._tone=e,this._duration=n.duration,r.push("Content-Type: application/dtmf-relay");var l="Signal=".concat(this._tone,"\r\n");l+="Duration=".concat(this._duration),this._session.newDTMF({originator:"local",dtmf:this,request:this._request}),this._session.sendRequest(p.INFO,{extraHeaders:r,eventHandlers:{onSuccessResponse:function(e){t.emit("succeeded",{originator:"remote",response:e})},onErrorResponse:function(e){t.eventHandlers.onFailed&&t.eventHandlers.onFailed(),t.emit("failed",{originator:"remote",response:e})},onRequestTimeout:function(){t._session.onRequestTimeout()},onTransportError:function(){t._session.onTransportError()},onDialogError:function(){t._session.onDialogError()}},body:l})}},{key:"init_incoming",value:function(e){var t=/^(Signal\s*?=\s*?)([0-9A-D#*]{1})(\s)?.*/,n=/^(Duration\s?=\s?)([0-9]{1,4})(\s)?.*/;if(this._direction="incoming",this._request=e,e.reply(200),e.body){var r=e.body.split("\n");r.length>=1&&t.test(r[0])&&(this._tone=r[0].replace(t,"$2")),r.length>=2&&n.test(r[1])&&(this._duration=parseInt(r[1].replace(n,"$2"),10))}this._duration||(this._duration=T.DEFAULT_DURATION),this._tone?this._session.newDTMF({originator:"remote",dtmf:this,request:e}):g("invalid INFO DTMF received, discarded")}},{key:"tone",get:function(){return this._tone}},{key:"duration",get:function(){return this._duration}}]),n}(_),e.exports.C=T},"609b":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("0790"),o=n("1e8c"),u=n("03f2"),a=n("c9ab"),c=n("34eb")("JsSIP:Registrator"),h=10;e.exports=function(){function e(t,n){r(this,e);var l=1;this._ua=t,this._transport=n,this._registrar=t.configuration.registrar_server,this._expires=t.configuration.register_expires,this._call_id=i.createRandomToken(22),this._cseq=0,this._to_uri=t.configuration.uri,this._registrationTimer=null,this._registering=!1,this._registered=!1,this._contact=this._ua.contact.toString(),this._contact+=";+sip.ice",this._extraHeaders=[],this._extraContactParams="",l&&(this._contact+=";reg-id=".concat(l),this._contact+=';+sip.instance="<urn:uuid:'.concat(this._ua.configuration.instance_id,'>"'))}return s(e,[{key:"setExtraHeaders",value:function(e){Array.isArray(e)||(e=[]),this._extraHeaders=e.slice()}},{key:"setExtraContactParams",value:function(e){for(var t in e instanceof Object||(e={}),this._extraContactParams="",e)if(Object.prototype.hasOwnProperty.call(e,t)){var n=e[t];this._extraContactParams+=";".concat(t),n&&(this._extraContactParams+="=".concat(n))}}},{key:"register",value:function(){var e=this;if(this._registering)c("Register request in progress...");else{var t=this._extraHeaders.slice();t.push("Contact: ".concat(this._contact,";expires=").concat(this._expires).concat(this._extraContactParams)),t.push("Expires: ".concat(this._expires));var n=new u.OutgoingRequest(o.REGISTER,this._registrar,this._ua,{to_uri:this._to_uri,call_id:this._call_id,cseq:this._cseq+=1},t),r=new a(this._ua,n,{onRequestTimeout:function(){e._registrationFailure(null,o.causes.REQUEST_TIMEOUT)},onTransportError:function(){e._registrationFailure(null,o.causes.CONNECTION_ERROR)},onAuthenticated:function(){e._cseq+=1},onReceiveResponse:function(t){if(t.cseq===e._cseq)switch(null!==e._registrationTimer&&(clearTimeout(e._registrationTimer),e._registrationTimer=null),!0){case/^1[0-9]{2}$/.test(t.status_code):break;case/^2[0-9]{2}$/.test(t.status_code):if(e._registering=!1,!t.hasHeader("Contact")){c("no Contact header in response to REGISTER, response ignored");break}var n=t.headers["Contact"].reduce((function(e,t){return e.concat(t.parsed)}),[]),r=n.find((function(t){return t.uri.user===e._ua.contact.uri.user}));if(!r){c("no Contact header pointing to us, response ignored");break}var l=r.getParam("expires");!l&&t.hasHeader("expires")&&(l=t.getHeader("expires")),l||(l=e._expires),l=Number(l),l<h&&(l=h);var s=l>64?1e3*l/2+Math.floor(1e3*(l/2-32)*Math.random()):1e3*l-5e3;e._registrationTimer=setTimeout((function(){e._registrationTimer=null,0===e._ua.listeners("registrationExpiring").length?e.register():e._ua.emit("registrationExpiring")}),s),r.hasParam("temp-gruu")&&(e._ua.contact.temp_gruu=r.getParam("temp-gruu").replace(/"/g,"")),r.hasParam("pub-gruu")&&(e._ua.contact.pub_gruu=r.getParam("pub-gruu").replace(/"/g,"")),e._registered||(e._registered=!0,e._ua.registered({response:t}));break;case/^423$/.test(t.status_code):t.hasHeader("min-expires")?(e._expires=Number(t.getHeader("min-expires")),e._expires<h&&(e._expires=h),e.register()):(c("423 response received for REGISTER without Min-Expires"),e._registrationFailure(t,o.causes.SIP_FAILURE_CODE));break;default:var u=i.sipErrorCause(t.status_code);e._registrationFailure(t,u)}}});this._registering=!0,r.send()}}},{key:"unregister",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._registered){this._registered=!1,null!==this._registrationTimer&&(clearTimeout(this._registrationTimer),this._registrationTimer=null);var n=this._extraHeaders.slice();t.all?n.push("Contact: *".concat(this._extraContactParams)):n.push("Contact: ".concat(this._contact,";expires=0").concat(this._extraContactParams)),n.push("Expires: 0");var r=new u.OutgoingRequest(o.REGISTER,this._registrar,this._ua,{to_uri:this._to_uri,call_id:this._call_id,cseq:this._cseq+=1},n),l=new a(this._ua,r,{onRequestTimeout:function(){e._unregistered(null,o.causes.REQUEST_TIMEOUT)},onTransportError:function(){e._unregistered(null,o.causes.CONNECTION_ERROR)},onAuthenticated:function(){e._cseq+=1},onReceiveResponse:function(t){switch(!0){case/^1[0-9]{2}$/.test(t.status_code):break;case/^2[0-9]{2}$/.test(t.status_code):e._unregistered(t);break;default:var n=i.sipErrorCause(t.status_code);e._unregistered(t,n)}}});l.send()}else c("already unregistered")}},{key:"close",value:function(){this._registered&&this.unregister()}},{key:"onTransportClosed",value:function(){this._registering=!1,null!==this._registrationTimer&&(clearTimeout(this._registrationTimer),this._registrationTimer=null),this._registered&&(this._registered=!1,this._ua.unregistered({}))}},{key:"_registrationFailure",value:function(e,t){this._registering=!1,this._ua.registrationFailed({response:e||null,cause:t}),this._registered&&(this._registered=!1,this._ua.unregistered({response:e||null,cause:t}))}},{key:"_unregistered",value:function(e,t){this._registering=!1,this._registered=!1,this._ua.unregistered({response:e||null,cause:t||null})}},{key:"registered",get:function(){return this._registered}}]),e}()},"60da6":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function a(e){var t=f();return function(){var n,r=d(e);if(t){var l=d(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var _=n("faa1").EventEmitter,p=n("34eb")("JsSIP:ERROR:RTCSession:Info");p.log=console.warn.bind(console);var m=n("1e8c"),v=n("8c52"),g=n("0790");e.exports=function(e){o(n,e);var t=a(n);function n(e){var r;return l(this,n),r=t.call(this),r._session=e,r._direction=null,r._contentType=null,r._body=null,r}return i(n,[{key:"send",value:function(e,t){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(this._direction="outgoing",void 0===e)throw new TypeError("Not enough arguments");if(this._session.status!==this._session.C.STATUS_CONFIRMED&&this._session.status!==this._session.C.STATUS_WAITING_FOR_ACK)throw new v.InvalidStateError(this._session.status);this._contentType=e,this._body=t;var l=g.cloneArray(r.extraHeaders);l.push("Content-Type: ".concat(e)),this._session.newInfo({originator:"local",info:this,request:this.request}),this._session.sendRequest(m.INFO,{extraHeaders:l,eventHandlers:{onSuccessResponse:function(e){n.emit("succeeded",{originator:"remote",response:e})},onErrorResponse:function(e){n.emit("failed",{originator:"remote",response:e})},onTransportError:function(){n._session.onTransportError()},onRequestTimeout:function(){n._session.onRequestTimeout()},onDialogError:function(){n._session.onDialogError()}},body:t})}},{key:"init_incoming",value:function(e){this._direction="incoming",this.request=e,e.reply(200),this._contentType=e.hasHeader("Content-Type")?e.getHeader("Content-Type").toLowerCase():void 0,this._body=e.body,this._session.newInfo({originator:"remote",info:this,request:e})}},{key:"contentType",get:function(){return this._contentType}},{key:"body",get:function(){return this._body}}]),n}(_)},"69e4":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("1c8d"),o=n("34eb")("JsSIP:WebSocketInterface"),u=n("34eb")("JsSIP:ERROR:WebSocketInterface");u.log=console.warn.bind(console),e.exports=function(){function e(t){r(this,e),o('new() [url:"%s"]',t),this._url=t,this._sip_uri=null,this._via_transport=null,this._ws=null;var n=i.parse(t,"absoluteURI");if(-1===n)throw u("invalid WebSocket URI: ".concat(t)),new TypeError("Invalid argument: ".concat(t));if("wss"!==n.scheme&&"ws"!==n.scheme)throw u("invalid WebSocket URI scheme: ".concat(n.scheme)),new TypeError("Invalid argument: ".concat(t));this._sip_uri="sip:".concat(n.host).concat(n.port?":".concat(n.port):"",";transport=ws"),this._via_transport=n.scheme.toUpperCase()}return s(e,[{key:"connect",value:function(){if(o("connect()"),this.isConnected())o("WebSocket ".concat(this._url," is already connected"));else if(this.isConnecting())o("WebSocket ".concat(this._url," is connecting"));else{this._ws&&this.disconnect(),o("connecting to WebSocket ".concat(this._url));try{this._ws=new WebSocket(this._url,"sip"),this._ws.binaryType="arraybuffer",this._ws.onopen=this._onOpen.bind(this),this._ws.onclose=this._onClose.bind(this),this._ws.onmessage=this._onMessage.bind(this),this._ws.onerror=this._onError.bind(this)}catch(e){this._onError(e)}}}},{key:"disconnect",value:function(){o("disconnect()"),this._ws&&(this._ws.onopen=function(){},this._ws.onclose=function(){},this._ws.onmessage=function(){},this._ws.onerror=function(){},this._ws.close(),this._ws=null)}},{key:"send",value:function(e){return o("send()"),this.isConnected()?(this._ws.send(e),!0):(u("unable to send message, WebSocket is not open"),!1)}},{key:"isConnected",value:function(){return this._ws&&this._ws.readyState===this._ws.OPEN}},{key:"isConnecting",value:function(){return this._ws&&this._ws.readyState===this._ws.CONNECTING}},{key:"_onOpen",value:function(){o("WebSocket ".concat(this._url," connected")),this.onconnect()}},{key:"_onClose",value:function(e){var t=e.wasClean,n=e.code,r=e.reason;o("WebSocket ".concat(this._url," closed")),!1===t&&o("WebSocket abrupt disconnection");var l={socket:this,error:!t,code:n,reason:r};this.ondisconnect(l)}},{key:"_onMessage",value:function(e){var t=e.data;o("received WebSocket message"),this.ondata(t)}},{key:"_onError",value:function(e){u("WebSocket ".concat(this._url," error: ").concat(e))}},{key:"via_transport",get:function(){return this._via_transport},set:function(e){this._via_transport=e.toUpperCase()}},{key:"sip_uri",get:function(){return this._sip_uri}},{key:"url",get:function(){return this._url}}]),e}()},7123:function(e,t,n){"use strict";var r=n("0790"),l=n("1c8d"),s=n("34eb")("JsSIP:ERROR:Socket");s.log=console.warn.bind(console),t.isSocket=function(e){if(Array.isArray(e))return!1;if("undefined"===typeof e)return s("undefined JsSIP.Socket instance"),!1;try{if(!r.isString(e.url))throw s("missing or invalid JsSIP.Socket url property"),new Error;if(!r.isString(e.via_transport))throw s("missing or invalid JsSIP.Socket via_transport property"),new Error;if(-1===l.parse(e.sip_uri,"SIP_URI"))throw s("missing or invalid JsSIP.Socket sip_uri property"),new Error}catch(t){return!1}try{["connect","disconnect","send"].forEach((function(t){if(!r.isFunction(e[t]))throw s("missing or invalid JsSIP.Socket method: ".concat(t)),new Error}))}catch(t){return!1}return!0}},"88fe":function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("03f2"),o=n("1e8c"),u=n("a283"),a=n("2f68"),c=n("0790"),h=n("34eb")("JsSIP:Dialog"),f={STATUS_EARLY:1,STATUS_CONFIRMED:2};e.exports=function(){function e(t,n,l){var s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:f.STATUS_CONFIRMED;if(r(this,e),this._owner=t,this._ua=t._ua,this._uac_pending_reply=!1,this._uas_pending_reply=!1,!n.hasHeader("contact"))return{error:"unable to create a Dialog without Contact header field"};n instanceof i.IncomingResponse&&(s=n.status_code<200?f.STATUS_EARLY:f.STATUS_CONFIRMED);var o=n.parseHeader("contact");"UAS"===l?(this._id={call_id:n.call_id,local_tag:n.to_tag,remote_tag:n.from_tag,toString:function(){return this.call_id+this.local_tag+this.remote_tag}},this._state=s,this._remote_seqnum=n.cseq,this._local_uri=n.parseHeader("to").uri,this._remote_uri=n.parseHeader("from").uri,this._remote_target=o.uri,this._route_set=n.getHeaders("record-route"),this._ack_seqnum=this._remote_seqnum):"UAC"===l&&(this._id={call_id:n.call_id,local_tag:n.from_tag,remote_tag:n.to_tag,toString:function(){return this.call_id+this.local_tag+this.remote_tag}},this._state=s,this._local_seqnum=n.cseq,this._local_uri=n.parseHeader("from").uri,this._remote_uri=n.parseHeader("to").uri,this._remote_target=o.uri,this._route_set=n.getHeaders("record-route").reverse(),this._ack_seqnum=null),this._ua.newDialog(this),h("new ".concat(l," dialog created with status ").concat(this._state===f.STATUS_EARLY?"EARLY":"CONFIRMED"))}return s(e,null,[{key:"C",get:function(){return f}}]),s(e,[{key:"update",value:function(e,t){this._state=f.STATUS_CONFIRMED,h("dialog ".concat(this._id.toString(),"  changed to CONFIRMED state")),"UAC"===t&&(this._route_set=e.getHeaders("record-route").reverse())}},{key:"terminate",value:function(){h("dialog ".concat(this._id.toString()," deleted")),this._ua.destroyDialog(this)}},{key:"sendRequest",value:function(e){var t=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=c.cloneArray(n.extraHeaders),l=c.cloneObject(n.eventHandlers),s=n.body||null,i=this._createRequest(e,r,s);l.onAuthenticated=function(){t._local_seqnum+=1};var o=new a(this,i,l);return o.send(),i}},{key:"receiveRequest",value:function(e){this._checkInDialogRequest(e)&&(e.method===o.ACK&&null!==this._ack_seqnum?this._ack_seqnum=null:e.method===o.INVITE&&(this._ack_seqnum=e.cseq),this._owner.receiveRequest(e))}},{key:"_createRequest",value:function(e,t,n){t=c.cloneArray(t),this._local_seqnum||(this._local_seqnum=Math.floor(1e4*Math.random()));var r=e===o.CANCEL||e===o.ACK?this._local_seqnum:this._local_seqnum+=1,l=new i.OutgoingRequest(e,this._remote_target,this._ua,{cseq:r,call_id:this._id.call_id,from_uri:this._local_uri,from_tag:this._id.local_tag,to_uri:this._remote_uri,to_tag:this._id.remote_tag,route_set:this._route_set},t,n);return l}},{key:"_checkInDialogRequest",value:function(e){var t=this;if(this._remote_seqnum)if(e.cseq<this._remote_seqnum){if(e.method!==o.ACK)return e.reply(500),!1;if(null===this._ack_seqnum||e.cseq!==this._ack_seqnum)return!1}else e.cseq>this._remote_seqnum&&(this._remote_seqnum=e.cseq);else this._remote_seqnum=e.cseq;if(e.method===o.INVITE||e.method===o.UPDATE&&e.body){if(!0===this._uac_pending_reply)e.reply(491);else{if(!0===this._uas_pending_reply){var n=1+(10*Math.random()|0);return e.reply(500,null,["Retry-After:".concat(n)]),!1}this._uas_pending_reply=!0;var r=function n(){e.server_transaction.state!==u.C.STATUS_ACCEPTED&&e.server_transaction.state!==u.C.STATUS_COMPLETED&&e.server_transaction.state!==u.C.STATUS_TERMINATED||(e.server_transaction.removeListener("stateChanged",n),t._uas_pending_reply=!1)};e.server_transaction.on("stateChanged",r)}e.hasHeader("contact")&&e.server_transaction.on("stateChanged",(function(){e.server_transaction.state===u.C.STATUS_ACCEPTED&&(t._remote_target=e.parseHeader("contact").uri)}))}else e.method===o.NOTIFY&&e.hasHeader("contact")&&e.server_transaction.on("stateChanged",(function(){e.server_transaction.state===u.C.STATUS_COMPLETED&&(t._remote_target=e.parseHeader("contact").uri)}));return!0}},{key:"id",get:function(){return this._id}},{key:"local_seqnum",get:function(){return this._local_seqnum},set:function(e){this._local_seqnum=e}},{key:"owner",get:function(){return this._owner}},{key:"uac_pending_reply",get:function(){return this._uac_pending_reply},set:function(e){this._uac_pending_reply=e}},{key:"uas_pending_reply",get:function(){return this._uas_pending_reply}}]),e}()},"8c52":function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&d(e,t)}function i(e){var t=h();return function(){var n,r=_(e);if(t){var l=_(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return o(this,n)}}function o(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?u(e):t}function u(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(e){var t="function"===typeof Map?new Map:void 0;return a=function(e){if(null===e||!f(e))return e;if("function"!==typeof e)throw new TypeError("Super expression must either be null or a function");if("undefined"!==typeof t){if(t.has(e))return t.get(e);t.set(e,n)}function n(){return c(e,arguments,_(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),d(n,e)},a(e)}function c(e,t,n){return c=h()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var l=Function.bind.apply(e,r),s=new l;return n&&d(s,n.prototype),s},c.apply(null,arguments)}function h(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function f(e){return-1!==Function.toString.call(e).indexOf("[native code]")}function d(e,t){return d=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},d(e,t)}function _(e){return _=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},_(e)}var p=function(e){s(n,e);var t=i(n);function n(e,r){var s;return l(this,n),s=t.call(this),s.code=1,s.name="CONFIGURATION_ERROR",s.parameter=e,s.value=r,s.message=s.value?"Invalid value ".concat(JSON.stringify(s.value),' for parameter "').concat(s.parameter,'"'):"Missing parameter: ".concat(s.parameter),s}return n}(a(Error)),m=function(e){s(n,e);var t=i(n);function n(e){var r;return l(this,n),r=t.call(this),r.code=2,r.name="INVALID_STATE_ERROR",r.status=e,r.message="Invalid status: ".concat(e),r}return n}(a(Error)),v=function(e){s(n,e);var t=i(n);function n(e){var r;return l(this,n),r=t.call(this),r.code=3,r.name="NOT_SUPPORTED_ERROR",r.message=e,r}return n}(a(Error)),g=function(e){s(n,e);var t=i(n);function n(e){var r;return l(this,n),r=t.call(this),r.code=4,r.name="NOT_READY_ERROR",r.message=e,r}return n}(a(Error));e.exports={ConfigurationError:p,InvalidStateError:m,NotSupportedError:v,NotReadyError:g}},"93be":function(e,t,n){var r=n("95e8"),l=n("f0bc");t.write=l,t.parse=r.parse,t.parseParams=r.parseParams,t.parseFmtpConfig=r.parseFmtpConfig,t.parsePayloads=r.parsePayloads,t.parseRemoteCandidates=r.parseRemoteCandidates,t.parseImageAttributes=r.parseImageAttributes,t.parseSimulcastStreamList=r.parseSimulcastStreamList},"95e8":function(e,t,n){var r=function(e){return String(Number(e))===e?Number(e):e},l=function(e,t,n,l){if(l&&!n)t[l]=r(e[1]);else for(var s=0;s<n.length;s+=1)null!=e[s+1]&&(t[n[s]]=r(e[s+1]))},s=function(e,t,n){var r=e.name&&e.names;e.push&&!t[e.push]?t[e.push]=[]:r&&!t[e.name]&&(t[e.name]={});var s=e.push?{}:r?t[e.name]:t;l(n.match(e.reg),s,e.names,e.name),e.push&&t[e.push].push(s)},i=n("3baa"),o=RegExp.prototype.test.bind(/^([a-z])=(.*)/);t.parse=function(e){var t={},n=[],r=t;return e.split(/(\r\n|\r|\n)/).filter(o).forEach((function(e){var t=e[0],l=e.slice(2);"m"===t&&(n.push({rtp:[],fmtp:[]}),r=n[n.length-1]);for(var o=0;o<(i[t]||[]).length;o+=1){var u=i[t][o];if(u.reg.test(l))return s(u,r,l)}})),t.media=n,t};var u=function(e,t){var n=t.split(/=(.+)/,2);return 2===n.length?e[n[0]]=r(n[1]):1===n.length&&t.length>1&&(e[n[0]]=void 0),e};t.parseParams=function(e){return e.split(/;\s?/).reduce(u,{})},t.parseFmtpConfig=t.parseParams,t.parsePayloads=function(e){return e.toString().split(" ").map(Number)},t.parseRemoteCandidates=function(e){for(var t=[],n=e.split(" ").map(r),l=0;l<n.length;l+=3)t.push({component:n[l],ip:n[l+1],port:n[l+2]});return t},t.parseImageAttributes=function(e){return e.split(" ").map((function(e){return e.substring(1,e.length-1).split(",").reduce(u,{})}))},t.parseSimulcastStreamList=function(e){return e.split(";").map((function(e){return e.split(",").map((function(e){var t,n=!1;return"~"!==e[0]?t=r(e):(t=r(e.substring(1,e.length)),n=!0),{scid:t,paused:n}}))}))}},9715:function(e,t,n){"use strict";var r=n("d251"),l=n("1e8c"),s=n("8c52"),i=n("0790"),o=n("1bbf"),u=n("9cf5"),a=n("4a05"),c=n("1c8d"),h=n("69e4"),f=n("34eb")("JsSIP");f("version %s",r.version),e.exports={C:l,Exceptions:s,Utils:i,UA:o,URI:u,NameAddrHeader:a,WebSocketInterface:h,Grammar:c,debug:n("34eb"),get name(){return r.title},get version(){return r.version}}},"9cf5":function(e,t,n){"use strict";function r(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=l(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,s=function(){};return{s:s,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(u)throw i}}}}function l(e,t){if(e){if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function u(e,t,n){return t&&o(e.prototype,t),n&&o(e,n),e}var a=n("1e8c"),c=n("0790"),h=n("1c8d");e.exports=function(){function e(t,n,r,l){var s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{};if(i(this,e),!r)throw new TypeError('missing or invalid "host" parameter');for(var u in this._parameters={},this._headers={},this._scheme=t||a.SIP,this._user=n,this._host=r,this._port=l,s)Object.prototype.hasOwnProperty.call(s,u)&&this.setParam(u,s[u]);for(var c in o)Object.prototype.hasOwnProperty.call(o,c)&&this.setHeader(c,o[c])}return u(e,null,[{key:"parse",value:function(e){return e=h.parse(e,"SIP_URI"),-1!==e?e:void 0}}]),u(e,[{key:"setParam",value:function(e,t){e&&(this._parameters[e.toLowerCase()]="undefined"===typeof t||null===t?null:t.toString())}},{key:"getParam",value:function(e){if(e)return this._parameters[e.toLowerCase()]}},{key:"hasParam",value:function(e){if(e)return!!this._parameters.hasOwnProperty(e.toLowerCase())}},{key:"deleteParam",value:function(e){if(e=e.toLowerCase(),this._parameters.hasOwnProperty(e)){var t=this._parameters[e];return delete this._parameters[e],t}}},{key:"clearParams",value:function(){this._parameters={}}},{key:"setHeader",value:function(e,t){this._headers[c.headerize(e)]=Array.isArray(t)?t:[t]}},{key:"getHeader",value:function(e){if(e)return this._headers[c.headerize(e)]}},{key:"hasHeader",value:function(e){if(e)return!!this._headers.hasOwnProperty(c.headerize(e))}},{key:"deleteHeader",value:function(e){if(e=c.headerize(e),this._headers.hasOwnProperty(e)){var t=this._headers[e];return delete this._headers[e],t}}},{key:"clearHeaders",value:function(){this._headers={}}},{key:"clone",value:function(){return new e(this._scheme,this._user,this._host,this._port,JSON.parse(JSON.stringify(this._parameters)),JSON.parse(JSON.stringify(this._headers)))}},{key:"toString",value:function(){var e=[],t="".concat(this._scheme,":");for(var n in this._user&&(t+="".concat(c.escapeUser(this._user),"@")),t+=this._host,(this._port||0===this._port)&&(t+=":".concat(this._port)),this._parameters)Object.prototype.hasOwnProperty.call(this._parameters,n)&&(t+=";".concat(n),null!==this._parameters[n]&&(t+="=".concat(this._parameters[n])));for(var l in this._headers)if(Object.prototype.hasOwnProperty.call(this._headers,l)){var s,i=r(this._headers[l]);try{for(i.s();!(s=i.n()).done;){var o=s.value;e.push("".concat(l,"=").concat(o))}}catch(u){i.e(u)}finally{i.f()}}return e.length>0&&(t+="?".concat(e.join("&"))),t}},{key:"toAor",value:function(e){var t="".concat(this._scheme,":");return this._user&&(t+="".concat(c.escapeUser(this._user),"@")),t+=this._host,e&&(this._port||0===this._port)&&(t+=":".concat(this._port)),t}},{key:"scheme",get:function(){return this._scheme},set:function(e){this._scheme=e.toLowerCase()}},{key:"user",get:function(){return this._user},set:function(e){this._user=e}},{key:"host",get:function(){return this._host},set:function(e){this._host=e.toLowerCase()}},{key:"port",get:function(){return this._port},set:function(e){this._port=0===e?e:parseInt(e,10)||null}}]),e}()},a283:function(e,t,n){"use strict";function r(e){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"===typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},r(e)}function l(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function i(e,t,n){return t&&s(e.prototype,t),n&&s(e,n),e}function o(e,t){if("function"!==typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&u(e,t)}function u(e,t){return u=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},u(e,t)}function a(e){var t=f();return function(){var n,r=d(e);if(t){var l=d(this).constructor;n=Reflect.construct(r,arguments,l)}else n=r.apply(this,arguments);return c(this,n)}}function c(e,t){return!t||"object"!==r(t)&&"function"!==typeof t?h(e):t}function h(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function f(){if("undefined"===typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"===typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(e){return!1}}function d(e){return d=Object.setPrototypeOf?Object.getPrototypeOf:function(e){return e.__proto__||Object.getPrototypeOf(e)},d(e)}var _=n("faa1").EventEmitter,p=n("1e8c"),m=n("03f2"),v=n("488a"),g=n("34eb")("JsSIP:NonInviteClientTransaction"),y=n("34eb")("JsSIP:InviteClientTransaction"),T=n("34eb")("JsSIP:AckClientTransaction"),S=n("34eb")("JsSIP:NonInviteServerTransaction"),C=n("34eb")("JsSIP:InviteServerTransaction"),E={STATUS_TRYING:1,STATUS_PROCEEDING:2,STATUS_CALLING:3,STATUS_ACCEPTED:4,STATUS_COMPLETED:5,STATUS_TERMINATED:6,STATUS_CONFIRMED:7,NON_INVITE_CLIENT:"nict",NON_INVITE_SERVER:"nist",INVITE_CLIENT:"ict",INVITE_SERVER:"ist"},b=function(e){o(n,e);var t=a(n);function n(e,r,s,i){var o;l(this,n),o=t.call(this),o.type=E.NON_INVITE_CLIENT,o.id="z9hG4bK".concat(Math.floor(1e7*Math.random())),o.ua=e,o.transport=r,o.request=s,o.eventHandlers=i;var u="SIP/2.0/".concat(r.via_transport);return u+=" ".concat(e.configuration.via_host,";branch=").concat(o.id),o.request.setHeader("via",u),o.ua.newTransaction(h(o)),o}return i(n,[{key:"stateChanged",value:function(e){this.state=e,this.emit("stateChanged")}},{key:"send",value:function(){var e=this;this.stateChanged(E.STATUS_TRYING),this.F=setTimeout((function(){e.timer_F()}),v.TIMER_F),this.transport.send(this.request)||this.onTransportError()}},{key:"onTransportError",value:function(){g("transport error occurred, deleting transaction ".concat(this.id)),clearTimeout(this.F),clearTimeout(this.K),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this),this.eventHandlers.onTransportError()}},{key:"timer_F",value:function(){g("Timer F expired for transaction ".concat(this.id)),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this),this.eventHandlers.onRequestTimeout()}},{key:"timer_K",value:function(){this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this)}},{key:"receiveResponse",value:function(e){var t=this,n=e.status_code;if(n<200)switch(this.state){case E.STATUS_TRYING:case E.STATUS_PROCEEDING:this.stateChanged(E.STATUS_PROCEEDING),this.eventHandlers.onReceiveResponse(e);break}else switch(this.state){case E.STATUS_TRYING:case E.STATUS_PROCEEDING:this.stateChanged(E.STATUS_COMPLETED),clearTimeout(this.F),408===n?this.eventHandlers.onRequestTimeout():this.eventHandlers.onReceiveResponse(e),this.K=setTimeout((function(){t.timer_K()}),v.TIMER_K);break;case E.STATUS_COMPLETED:break}}},{key:"C",get:function(){return E}}]),n}(_),A=function(e){o(n,e);var t=a(n);function n(e,r,s,i){var o;l(this,n),o=t.call(this),o.type=E.INVITE_CLIENT,o.id="z9hG4bK".concat(Math.floor(1e7*Math.random())),o.ua=e,o.transport=r,o.request=s,o.eventHandlers=i,s.transaction=h(o);var u="SIP/2.0/".concat(r.via_transport);return u+=" ".concat(e.configuration.via_host,";branch=").concat(o.id),o.request.setHeader("via",u),o.ua.newTransaction(h(o)),o}return i(n,[{key:"stateChanged",value:function(e){this.state=e,this.emit("stateChanged")}},{key:"send",value:function(){var e=this;this.stateChanged(E.STATUS_CALLING),this.B=setTimeout((function(){e.timer_B()}),v.TIMER_B),this.transport.send(this.request)||this.onTransportError()}},{key:"onTransportError",value:function(){clearTimeout(this.B),clearTimeout(this.D),clearTimeout(this.M),this.state!==E.STATUS_ACCEPTED&&(y("transport error occurred, deleting transaction ".concat(this.id)),this.eventHandlers.onTransportError()),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this)}},{key:"timer_M",value:function(){y("Timer M expired for transaction ".concat(this.id)),this.state===E.STATUS_ACCEPTED&&(clearTimeout(this.B),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this))}},{key:"timer_B",value:function(){y("Timer B expired for transaction ".concat(this.id)),this.state===E.STATUS_CALLING&&(this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this),this.eventHandlers.onRequestTimeout())}},{key:"timer_D",value:function(){y("Timer D expired for transaction ".concat(this.id)),clearTimeout(this.B),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this)}},{key:"sendACK",value:function(e){var t=this,n=new m.OutgoingRequest(p.ACK,this.request.ruri,this.ua,{route_set:this.request.getHeaders("route"),call_id:this.request.getHeader("call-id"),cseq:this.request.cseq});n.setHeader("from",this.request.getHeader("from")),n.setHeader("via",this.request.getHeader("via")),n.setHeader("to",e.getHeader("to")),this.D=setTimeout((function(){t.timer_D()}),v.TIMER_D),this.transport.send(n)}},{key:"cancel",value:function(e){if(this.state===E.STATUS_PROCEEDING){var t=new m.OutgoingRequest(p.CANCEL,this.request.ruri,this.ua,{route_set:this.request.getHeaders("route"),call_id:this.request.getHeader("call-id"),cseq:this.request.cseq});t.setHeader("from",this.request.getHeader("from")),t.setHeader("via",this.request.getHeader("via")),t.setHeader("to",this.request.getHeader("to")),e&&t.setHeader("reason",e),this.transport.send(t)}}},{key:"receiveResponse",value:function(e){var t=this,n=e.status_code;if(n>=100&&n<=199)switch(this.state){case E.STATUS_CALLING:this.stateChanged(E.STATUS_PROCEEDING),this.eventHandlers.onReceiveResponse(e);break;case E.STATUS_PROCEEDING:this.eventHandlers.onReceiveResponse(e);break}else if(n>=200&&n<=299)switch(this.state){case E.STATUS_CALLING:case E.STATUS_PROCEEDING:this.stateChanged(E.STATUS_ACCEPTED),this.M=setTimeout((function(){t.timer_M()}),v.TIMER_M),this.eventHandlers.onReceiveResponse(e);break;case E.STATUS_ACCEPTED:this.eventHandlers.onReceiveResponse(e);break}else if(n>=300&&n<=699)switch(this.state){case E.STATUS_CALLING:case E.STATUS_PROCEEDING:this.stateChanged(E.STATUS_COMPLETED),this.sendACK(e),this.eventHandlers.onReceiveResponse(e);break;case E.STATUS_COMPLETED:this.sendACK(e);break}}},{key:"C",get:function(){return E}}]),n}(_),R=function(e){o(n,e);var t=a(n);function n(e,r,s,i){var o;l(this,n),o=t.call(this),o.id="z9hG4bK".concat(Math.floor(1e7*Math.random())),o.transport=r,o.request=s,o.eventHandlers=i;var u="SIP/2.0/".concat(r.via_transport);return u+=" ".concat(e.configuration.via_host,";branch=").concat(o.id),o.request.setHeader("via",u),o}return i(n,[{key:"send",value:function(){this.transport.send(this.request)||this.onTransportError()}},{key:"onTransportError",value:function(){T("transport error occurred for transaction ".concat(this.id)),this.eventHandlers.onTransportError()}},{key:"C",get:function(){return E}}]),n}(_),w=function(e){o(n,e);var t=a(n);function n(e,r,s){var i;return l(this,n),i=t.call(this),i.type=E.NON_INVITE_SERVER,i.id=s.via_branch,i.ua=e,i.transport=r,i.request=s,i.last_response="",s.server_transaction=h(i),i.state=E.STATUS_TRYING,e.newTransaction(h(i)),i}return i(n,[{key:"stateChanged",value:function(e){this.state=e,this.emit("stateChanged")}},{key:"timer_J",value:function(){S("Timer J expired for transaction ".concat(this.id)),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this)}},{key:"onTransportError",value:function(){this.transportError||(this.transportError=!0,S("transport error occurred, deleting transaction ".concat(this.id)),clearTimeout(this.J),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this))}},{key:"receiveResponse",value:function(e,t,n,r){var l=this;if(100===e)switch(this.state){case E.STATUS_TRYING:this.stateChanged(E.STATUS_PROCEEDING),this.transport.send(t)||this.onTransportError();break;case E.STATUS_PROCEEDING:this.last_response=t,this.transport.send(t)?n&&n():(this.onTransportError(),r&&r());break}else if(e>=200&&e<=699)switch(this.state){case E.STATUS_TRYING:case E.STATUS_PROCEEDING:this.stateChanged(E.STATUS_COMPLETED),this.last_response=t,this.J=setTimeout((function(){l.timer_J()}),v.TIMER_J),this.transport.send(t)?n&&n():(this.onTransportError(),r&&r());break;case E.STATUS_COMPLETED:break}}},{key:"C",get:function(){return E}}]),n}(_),I=function(e){o(n,e);var t=a(n);function n(e,r,s){var i;return l(this,n),i=t.call(this),i.type=E.INVITE_SERVER,i.id=s.via_branch,i.ua=e,i.transport=r,i.request=s,i.last_response="",s.server_transaction=h(i),i.state=E.STATUS_PROCEEDING,e.newTransaction(h(i)),i.resendProvisionalTimer=null,s.reply(100),i}return i(n,[{key:"stateChanged",value:function(e){this.state=e,this.emit("stateChanged")}},{key:"timer_H",value:function(){C("Timer H expired for transaction ".concat(this.id)),this.state===E.STATUS_COMPLETED&&C("ACK not received, dialog will be terminated"),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this)}},{key:"timer_I",value:function(){this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this)}},{key:"timer_L",value:function(){C("Timer L expired for transaction ".concat(this.id)),this.state===E.STATUS_ACCEPTED&&(this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this))}},{key:"onTransportError",value:function(){this.transportError||(this.transportError=!0,C("transport error occurred, deleting transaction ".concat(this.id)),null!==this.resendProvisionalTimer&&(clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=null),clearTimeout(this.L),clearTimeout(this.H),clearTimeout(this.I),this.stateChanged(E.STATUS_TERMINATED),this.ua.destroyTransaction(this))}},{key:"resend_provisional",value:function(){this.transport.send(this.last_response)||this.onTransportError()}},{key:"receiveResponse",value:function(e,t,n,r){var l=this;if(e>=100&&e<=199)switch(this.state){case E.STATUS_PROCEEDING:this.transport.send(t)||this.onTransportError(),this.last_response=t;break}if(e>100&&e<=199&&this.state===E.STATUS_PROCEEDING)null===this.resendProvisionalTimer&&(this.resendProvisionalTimer=setInterval((function(){l.resend_provisional()}),v.PROVISIONAL_RESPONSE_INTERVAL));else if(e>=200&&e<=299)switch(this.state){case E.STATUS_PROCEEDING:this.stateChanged(E.STATUS_ACCEPTED),this.last_response=t,this.L=setTimeout((function(){l.timer_L()}),v.TIMER_L),null!==this.resendProvisionalTimer&&(clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=null);case E.STATUS_ACCEPTED:this.transport.send(t)?n&&n():(this.onTransportError(),r&&r());break}else if(e>=300&&e<=699)switch(this.state){case E.STATUS_PROCEEDING:null!==this.resendProvisionalTimer&&(clearInterval(this.resendProvisionalTimer),this.resendProvisionalTimer=null),this.transport.send(t)?(this.stateChanged(E.STATUS_COMPLETED),this.H=setTimeout((function(){l.timer_H()}),v.TIMER_H),n&&n()):(this.onTransportError(),r&&r());break}}},{key:"C",get:function(){return E}}]),n}(_);function O(e,t){var n,r=e._transactions;switch(t.method){case p.INVITE:if(n=r.ist[t.via_branch],n){switch(n.state){case E.STATUS_PROCEEDING:n.transport.send(n.last_response);break;case E.STATUS_ACCEPTED:break}return!0}break;case p.ACK:if(n=r.ist[t.via_branch],!n)return!1;if(n.state===E.STATUS_ACCEPTED)return!1;if(n.state===E.STATUS_COMPLETED)return n.state=E.STATUS_CONFIRMED,n.I=setTimeout((function(){n.timer_I()}),v.TIMER_I),!0;break;case p.CANCEL:return n=r.ist[t.via_branch],n?(t.reply_sl(200),n.state!==E.STATUS_PROCEEDING):(t.reply_sl(481),!0);default:if(n=r.nist[t.via_branch],n){switch(n.state){case E.STATUS_TRYING:break;case E.STATUS_PROCEEDING:case E.STATUS_COMPLETED:n.transport.send(n.last_response);break}return!0}break}}e.exports={C:E,NonInviteClientTransaction:b,InviteClientTransaction:A,AckClientTransaction:R,NonInviteServerTransaction:w,InviteServerTransaction:I,checkTransaction:O}},c9ab:function(e,t,n){"use strict";function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function l(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function s(e,t,n){return t&&l(e.prototype,t),n&&l(e,n),e}var i=n("1e8c"),o=n("1070"),u=n("a283"),a=n("34eb")("JsSIP:RequestSender"),c={onRequestTimeout:function(){},onTransportError:function(){},onReceiveResponse:function(){},onAuthenticated:function(){}};e.exports=function(){function e(t,n,l){for(var s in r(this,e),this._ua=t,this._eventHandlers=l,this._method=n.method,this._request=n,this._auth=null,this._challenged=!1,this._staled=!1,c)Object.prototype.hasOwnProperty.call(c,s)&&(this._eventHandlers[s]||(this._eventHandlers[s]=c[s]));t.status!==t.C.STATUS_USER_CLOSED||this._method===i.BYE&&this._method===i.ACK||this._eventHandlers.onTransportError()}return s(e,[{key:"send",value:function(){var e=this,t={onRequestTimeout:function(){e._eventHandlers.onRequestTimeout()},onTransportError:function(){e._eventHandlers.onTransportError()},onReceiveResponse:function(t){e._receiveResponse(t)}};switch(this._method){case"INVITE":this.clientTransaction=new u.InviteClientTransaction(this._ua,this._ua.transport,this._request,t);break;case"ACK":this.clientTransaction=new u.AckClientTransaction(this._ua,this._ua.transport,this._request,t);break;default:this.clientTransaction=new u.NonInviteClientTransaction(this._ua,this._ua.transport,this._request,t)}this._ua._configuration.authorization_jwt&&this._request.setHeader("Authorization",this._ua._configuration.authorization_jwt),this.clientTransaction.send()}},{key:"_receiveResponse",value:function(e){var t,n,r=e.status_code;if(401!==r&&407!==r||null===this._ua.configuration.password&&null===this._ua.configuration.ha1)this._eventHandlers.onReceiveResponse(e);else{if(401===e.status_code?(t=e.parseHeader("www-authenticate"),n="authorization"):(t=e.parseHeader("proxy-authenticate"),n="proxy-authorization"),!t)return a("".concat(e.status_code," with wrong or missing challenge, cannot authenticate")),void this._eventHandlers.onReceiveResponse(e);if(!this._challenged||!this._staled&&!0===t.stale){if(this._auth||(this._auth=new o({username:this._ua.configuration.authorization_user,password:this._ua.configuration.password,realm:this._ua.configuration.realm,ha1:this._ua.configuration.ha1})),!this._auth.authenticate(this._request,t))return void this._eventHandlers.onReceiveResponse(e);this._challenged=!0,this._ua.set("realm",this._auth.get("realm")),this._ua.set("ha1",this._auth.get("ha1")),t.stale&&(this._staled=!0),this._request=this._request.clone(),this._request.cseq+=1,this._request.setHeader("cseq","".concat(this._request.cseq," ").concat(this._method)),this._request.setHeader(n,this._auth.toString()),this._eventHandlers.onAuthenticated(this._request),this.send()}else this._eventHandlers.onReceiveResponse(e)}}}]),e}()},d251:function(e){e.exports=JSON.parse('{"name":"jssip","title":"JsSIP","description":"the Javascript SIP library","version":"3.6.1","homepage":"https://jssip.net","author":"José Luis Millán <<EMAIL>> (https://github.com/jmillan)","contributors":["Iñaki Baz Castillo <<EMAIL>> (https://github.com/ibc)"],"types":"lib/JsSIP.d.ts","main":"lib-es5/JsSIP.js","keywords":["sip","websocket","webrtc","node","browser","library"],"license":"MIT","repository":{"type":"git","url":"https://github.com/versatica/JsSIP.git"},"bugs":{"url":"https://github.com/versatica/JsSIP/issues"},"dependencies":{"@types/debug":"^4.1.5","@types/node":"^14.0.6","debug":"^4.1.1","events":"^3.1.0","sdp-transform":"^2.14.0"},"devDependencies":{"@babel/core":"^7.9.6","@babel/preset-env":"^7.9.6","ansi-colors":"^3.2.4","browserify":"^16.5.1","eslint":"^5.16.0","fancy-log":"^1.3.3","gulp":"^4.0.2","gulp-babel":"^8.0.0","gulp-eslint":"^5.0.0","gulp-expect-file":"^1.0.2","gulp-header":"^2.0.9","gulp-nodeunit-runner":"^0.2.2","gulp-plumber":"^1.2.1","gulp-rename":"^1.4.0","gulp-uglify-es":"^1.0.4","pegjs":"^0.7.0","vinyl-buffer":"^1.0.1","vinyl-source-stream":"^2.0.0"},"scripts":{"lint":"gulp lint","test":"gulp test","prepublishOnly":"gulp babel"}}')},dc90:function(e,t,n){function r(e){function t(e){let t=0;for(let n=0;n<e.length;n++)t=(t<<5)-t+e.charCodeAt(n),t|=0;return r.colors[Math.abs(t)%r.colors.length]}function r(e){let t,n=null;function s(...e){if(!s.enabled)return;const n=s,l=Number(new Date),i=l-(t||l);n.diff=i,n.prev=t,n.curr=l,t=l,e[0]=r.coerce(e[0]),"string"!==typeof e[0]&&e.unshift("%O");let o=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(t,l)=>{if("%%"===t)return"%";o++;const s=r.formatters[l];if("function"===typeof s){const r=e[o];t=s.call(n,r),e.splice(o,1),o--}return t}),r.formatArgs.call(n,e);const u=n.log||r.log;u.apply(n,e)}return s.namespace=e,s.useColors=r.useColors(),s.color=r.selectColor(e),s.extend=l,s.destroy=r.destroy,Object.defineProperty(s,"enabled",{enumerable:!0,configurable:!1,get:()=>null===n?r.enabled(e):n,set:e=>{n=e}}),"function"===typeof r.init&&r.init(s),s}function l(e,t){const n=r(this.namespace+("undefined"===typeof t?":":t)+e);return n.log=this.log,n}function s(e){let t;r.save(e),r.names=[],r.skips=[];const n=("string"===typeof e?e:"").split(/[\s,]+/),l=n.length;for(t=0;t<l;t++)n[t]&&(e=n[t].replace(/\*/g,".*?"),"-"===e[0]?r.skips.push(new RegExp("^"+e.substr(1)+"$")):r.names.push(new RegExp("^"+e+"$")))}function i(){const e=[...r.names.map(u),...r.skips.map(u).map(e=>"-"+e)].join(",");return r.enable(""),e}function o(e){if("*"===e[e.length-1])return!0;let t,n;for(t=0,n=r.skips.length;t<n;t++)if(r.skips[t].test(e))return!1;for(t=0,n=r.names.length;t<n;t++)if(r.names[t].test(e))return!0;return!1}function u(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}function a(e){return e instanceof Error?e.stack||e.message:e}function c(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return r.debug=r,r.default=r,r.coerce=a,r.disable=i,r.enable=s,r.enabled=o,r.humanize=n("1468"),r.destroy=c,Object.keys(e).forEach(t=>{r[t]=e[t]}),r.names=[],r.skips=[],r.formatters={},r.selectColor=t,r.enable(r.load()),r}e.exports=r},df29:function(e,t,n){},e9f0:function(e,t,n){"use strict";function r(e,t){var n;if("undefined"===typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=l(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,s=function(){};return{s:s,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:s}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,o=!0,u=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return o=e.done,e},e:function(e){u=!0,i=e},f:function(){try{o||null==n["return"]||n["return"]()}finally{if(u)throw i}}}}function l(e,t){if(e){if("string"===typeof e)return s(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?s(e,t):void 0}}function s(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var i,o,u,a=n("1e8c"),c=n("03f2"),h=n("0790"),f=n("34eb")("JsSIP:sanityCheck"),d=[C],_=[m,v,g,y],p=[T,S];function m(){if("sip"!==i.s("to").uri.scheme)return E(416),!1}function v(){if(!i.to_tag&&i.call_id.substr(0,5)===o.configuration.jssip_id)return E(482),!1}function g(){var e=h.str_utf8_length(i.body),t=i.getHeader("content-length");if(e<t)return E(400),!1}function y(){var e,t=i.from_tag,n=i.call_id,r=i.cseq;if(!i.to_tag)if(i.method===a.INVITE){if(o._transactions.ist[i.via_branch])return!1;for(var l in o._transactions.ist)if(Object.prototype.hasOwnProperty.call(o._transactions.ist,l)&&(e=o._transactions.ist[l],e.request.from_tag===t&&e.request.call_id===n&&e.request.cseq===r))return E(482),!1}else{if(o._transactions.nist[i.via_branch])return!1;for(var s in o._transactions.nist)if(Object.prototype.hasOwnProperty.call(o._transactions.nist,s)&&(e=o._transactions.nist[s],e.request.from_tag===t&&e.request.call_id===n&&e.request.cseq===r))return E(482),!1}}function T(){if(i.getHeaders("via").length>1)return f("more than one Via header field present in the response, dropping the response"),!1}function S(){var e=h.str_utf8_length(i.body),t=i.getHeader("content-length");if(e<t)return f("message body length is lower than the value in Content-Length header field, dropping the response"),!1}function C(){for(var e=["from","to","call_id","cseq","via"],t=0,n=e;t<n.length;t++){var r=n[t];if(!i.hasHeader(r))return f("missing mandatory header field : ".concat(r,", dropping the response")),!1}}function E(e){var t,n,l=i.getHeaders("via"),s="SIP/2.0 ".concat(e," ").concat(a.REASON_PHRASE[e],"\r\n"),o=r(l);try{for(o.s();!(n=o.n()).done;){var c=n.value;s+="Via: ".concat(c,"\r\n")}}catch(f){o.e(f)}finally{o.f()}t=i.getHeader("To"),i.to_tag||(t+=";tag=".concat(h.newTag())),s+="To: ".concat(t,"\r\n"),s+="From: ".concat(i.getHeader("From"),"\r\n"),s+="Call-ID: ".concat(i.call_id,"\r\n"),s+="CSeq: ".concat(i.cseq," ").concat(i.method,"\r\n"),s+="\r\n",u.send(s)}e.exports=function(e,t,n){i=e,o=t,u=n;var l,s=r(d);try{for(s.s();!(l=s.n()).done;){var a=l.value;if(!1===a())return!1}}catch(T){s.e(T)}finally{s.f()}if(i instanceof c.IncomingRequest){var h,f=r(_);try{for(f.s();!(h=f.n()).done;){var m=h.value;if(!1===m())return!1}}catch(T){f.e(T)}finally{f.f()}}else if(i instanceof c.IncomingResponse){var v,g=r(p);try{for(g.s();!(v=g.n()).done;){var y=v.value;if(!1===y())return!1}}catch(T){g.e(T)}finally{g.f()}}return!0}},f0bc:function(e,t,n){var r=n("3baa"),l=/%[sdv%]/g,s=function(e){var t=1,n=arguments,r=n.length;return e.replace(l,(function(e){if(t>=r)return e;var l=n[t];switch(t+=1,e){case"%%":return"%";case"%s":return String(l);case"%d":return Number(l);case"%v":return""}}))},i=function(e,t,n){var r=t.format instanceof Function?t.format(t.push?n:n[t.name]):t.format,l=[e+"="+r];if(t.names)for(var i=0;i<t.names.length;i+=1){var o=t.names[i];t.name?l.push(n[t.name][o]):l.push(n[t.names[i]])}else l.push(n[t.name]);return s.apply(null,l)},o=["v","o","s","i","u","e","p","c","b","t","r","z","a"],u=["i","c","b","a"];e.exports=function(e,t){t=t||{},null==e.version&&(e.version=0),null==e.name&&(e.name=" "),e.media.forEach((function(e){null==e.payloads&&(e.payloads="")}));var n=t.outerOrder||o,l=t.innerOrder||u,s=[];return n.forEach((function(t){r[t].forEach((function(n){n.name in e&&null!=e[n.name]?s.push(i(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach((function(e){s.push(i(t,n,e))}))}))})),e.media.forEach((function(e){s.push(i("m",r.m[0],e)),l.forEach((function(t){r[t].forEach((function(n){n.name in e&&null!=e[n.name]?s.push(i(t,n,e)):n.push in e&&null!=e[n.push]&&e[n.push].forEach((function(e){s.push(i(t,n,e))}))}))}))})),s.join("\r\n")+"\r\n"}},faa1:function(e,t,n){"use strict";var r,l="object"===typeof Reflect?Reflect:null,s=l&&"function"===typeof l.apply?l.apply:function(e,t,n){return Function.prototype.apply.call(e,t,n)};function i(e){console&&console.warn&&console.warn(e)}r=l&&"function"===typeof l.ownKeys?l.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var o=Number.isNaN||function(e){return e!==e};function u(){u.init.call(this)}e.exports=u,e.exports.once=T,u.EventEmitter=u,u.prototype._events=void 0,u.prototype._eventsCount=0,u.prototype._maxListeners=void 0;var a=10;function c(e){if("function"!==typeof e)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function h(e){return void 0===e._maxListeners?u.defaultMaxListeners:e._maxListeners}function f(e,t,n,r){var l,s,o;if(c(n),s=e._events,void 0===s?(s=e._events=Object.create(null),e._eventsCount=0):(void 0!==s.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),s=e._events),o=s[t]),void 0===o)o=s[t]=n,++e._eventsCount;else if("function"===typeof o?o=s[t]=r?[n,o]:[o,n]:r?o.unshift(n):o.push(n),l=h(e),l>0&&o.length>l&&!o.warned){o.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=o.length,i(u)}return e}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function _(e,t,n){var r={fired:!1,wrapFn:void 0,target:e,type:t,listener:n},l=d.bind(r);return l.listener=n,r.wrapFn=l,l}function p(e,t,n){var r=e._events;if(void 0===r)return[];var l=r[t];return void 0===l?[]:"function"===typeof l?n?[l.listener||l]:[l]:n?y(l):v(l,l.length)}function m(e){var t=this._events;if(void 0!==t){var n=t[e];if("function"===typeof n)return 1;if(void 0!==n)return n.length}return 0}function v(e,t){for(var n=new Array(t),r=0;r<t;++r)n[r]=e[r];return n}function g(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}function y(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}function T(e,t){return new Promise((function(n,r){function l(n){e.removeListener(t,s),r(n)}function s(){"function"===typeof e.removeListener&&e.removeListener("error",l),n([].slice.call(arguments))}C(e,t,s,{once:!0}),"error"!==t&&S(e,l,{once:!0})}))}function S(e,t,n){"function"===typeof e.on&&C(e,"error",t,n)}function C(e,t,n,r){if("function"===typeof e.on)r.once?e.once(t,n):e.on(t,n);else{if("function"!==typeof e.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,(function l(s){r.once&&e.removeEventListener(t,l),n(s)}))}}Object.defineProperty(u,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!==typeof e||e<0||o(e))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),u.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},u.prototype.setMaxListeners=function(e){if("number"!==typeof e||e<0||o(e))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},u.prototype.getMaxListeners=function(){return h(this)},u.prototype.emit=function(e){for(var t=[],n=1;n<arguments.length;n++)t.push(arguments[n]);var r="error"===e,l=this._events;if(void 0!==l)r=r&&void 0===l.error;else if(!r)return!1;if(r){var i;if(t.length>0&&(i=t[0]),i instanceof Error)throw i;var o=new Error("Unhandled error."+(i?" ("+i.message+")":""));throw o.context=i,o}var u=l[e];if(void 0===u)return!1;if("function"===typeof u)s(u,this,t);else{var a=u.length,c=v(u,a);for(n=0;n<a;++n)s(c[n],this,t)}return!0},u.prototype.addListener=function(e,t){return f(this,e,t,!1)},u.prototype.on=u.prototype.addListener,u.prototype.prependListener=function(e,t){return f(this,e,t,!0)},u.prototype.once=function(e,t){return c(t),this.on(e,_(this,e,t)),this},u.prototype.prependOnceListener=function(e,t){return c(t),this.prependListener(e,_(this,e,t)),this},u.prototype.removeListener=function(e,t){var n,r,l,s,i;if(c(t),r=this._events,void 0===r)return this;if(n=r[e],void 0===n)return this;if(n===t||n.listener===t)0===--this._eventsCount?this._events=Object.create(null):(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!==typeof n){for(l=-1,s=n.length-1;s>=0;s--)if(n[s]===t||n[s].listener===t){i=n[s].listener,l=s;break}if(l<0)return this;0===l?n.shift():g(n,l),1===n.length&&(r[e]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",e,i||t)}return this},u.prototype.off=u.prototype.removeListener,u.prototype.removeAllListeners=function(e){var t,n,r;if(n=this._events,void 0===n)return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[e]&&(0===--this._eventsCount?this._events=Object.create(null):delete n[e]),this;if(0===arguments.length){var l,s=Object.keys(n);for(r=0;r<s.length;++r)l=s[r],"removeListener"!==l&&this.removeAllListeners(l);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(t=n[e],"function"===typeof t)this.removeListener(e,t);else if(void 0!==t)for(r=t.length-1;r>=0;r--)this.removeListener(e,t[r]);return this},u.prototype.listeners=function(e){return p(this,e,!0)},u.prototype.rawListeners=function(e){return p(this,e,!1)},u.listenerCount=function(e,t){return"function"===typeof e.listenerCount?e.listenerCount(t):m.call(e,t)},u.prototype.listenerCount=m,u.prototype.eventNames=function(){return this._eventsCount>0?r(this._events):[]}}}]);