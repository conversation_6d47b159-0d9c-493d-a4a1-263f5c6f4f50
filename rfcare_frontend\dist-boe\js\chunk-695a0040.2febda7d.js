(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-695a0040"],{"4f9a":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("page-header",{attrs:{title:"服务记录明细"}}),a("page-main",{attrs:{title:"会员信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[t._v(" 会员ID："+t._s(t.vo.pmid)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 会员姓名："+t._s(t.vo.name)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 会员手机号："+t._s(t.vo.phone)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 证件号码："+t._s(t.vo.idcardCode)+" ")])],1)],1),a("page-main",{attrs:{title:"服务信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[t._v(" 报销时间："+t._s(t.vo.toReimburseTime)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 本次垫付："+t._s(t.vo.advanceAmount)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 剩余可用垫付额度："+t._s(t.vo.remainAmount)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 报警位置："+t._s(t.vo.alertLocation)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 报警时间："+t._s(t.vo.alertTime)+" ")]),a("el-col",{attrs:{md:8}},[t._v(" 出险经过："+t._s(t.vo.handlingDesc)+" ")])],1)],1),a("page-main",{attrs:{title:"事件处理记录"}},[t.vo.orderHandleHisVOList?a("el-row",[a("el-table",{ref:"table",staticClass:"list-table",staticStyle:{width:"100%"},attrs:{data:t.vo.orderHandleHisVOList,stripe:""}},[a("el-table-column",{attrs:{prop:"createTime",label:"事件",align:"center"}}),a("el-table-column",{attrs:{prop:"logObjectName",label:"操作对象",align:"center"}}),a("el-table-column",{attrs:{prop:"relationTypeDesc",label:"关系类别",align:"center"}}),a("el-table-column",{attrs:{prop:"logResult",label:"操作结果",align:"center"}})],1)],1):t._e()],1)],1)},l=[],s=a("1da1"),n=(a("96cf"),{data:function(){return{dict:{stations:[]},vo:{}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(t){var e=this;return Object(s["a"])(regeneratorRuntime.mark((function a(){var r,l,s,n;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,e.$api.get("/bms/worryfree/detail/".concat(t));case 2:r=a.sent,l=r.status,s=r.message,n=r.data,"00000"===l?e.vo=n||{}:e.$message.error(s);case 7:case"end":return a.stop()}}),a)})))()},toEventDetail:function(t){1==t.size()&&this.$router.push({name:"servicePlatformEventDetail",params:{eventId:eventId}})}}}),o=n,i=(a("5184"),a("2877")),c=Object(i["a"])(o,r,l,!1,null,"2324b714",null);e["default"]=c.exports},5184:function(t,e,a){"use strict";a("5f91")},"5f91":function(t,e,a){}}]);