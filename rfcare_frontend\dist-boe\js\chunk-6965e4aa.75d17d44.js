(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6965e4aa"],{"74ec":function(t,e,a){"use strict";a("a855")},a855:function(t,e,a){},c48a:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("page-header",{attrs:{title:"服务站分配设备"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("div",{staticClass:"unprocessed-tip"},[t._v(" 正在为 "),a("span",{staticClass:"num"},[t._v(t._s(t.vo.orgName))]),t._v("机构下"),a("span",{staticClass:"num"},[t._v(t._s(t.vo.name))]),t._v(" 服务站分配设备： ")]),a("el-button",{attrs:{type:"primary",size:"small",disabled:!t.handleSelectionList||!t.handleSelectionList.length},on:{click:t.handleConfirm}},[t._v("设备分配")])],1),a("el-table",{ref:"elTable",staticClass:"list-table",attrs:{data:t.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center",selectable:t.checkItemFn}}),a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",align:"center"}}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"激活状态",wdth:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"运行状态",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"chambName",label:"关联管家",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"入库时间",width:"170",align:"center"}}),a("el-table-column",{attrs:{prop:"activeTime",label:"激活时间",width:"170",align:"center"}})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":t.table.pageInfo.current,total:t.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":t.handlePageCurrentChange,"size-change":t.handlePageSizeChange}}),a("el-button",{on:{click:function(e){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)},i=[],s=a("5530"),l=(a("99af"),a("d81d"),a("d3b7"),{data:function(){return{stationName:void 0,table:{datas:[],pageInfo:{current:1,total:0}},vo:{},handleSelectionList:[]}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{handleSelectionChange:function(t){this.handleSelectionList=this.$refs.elTable.selection||[]},handlePageCurrentChange:function(t){this.table.pageInfo.current=t,this.fetchDatas()},handlePageSizeChange:function(t){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=t,this.fetchDatas()},fetchDetail:function(t){var e=this;t&&this.$api.get("/bms/station/getStationInfo/".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(e.vo=t.data,e.fetchDatas())}))},fetchDatas:function(){var t=this;this.handleSelectionList=[],this.$api.get("/bms/station/listDeviceNonStation?stationId=".concat(this.vo.id,"&orgId=").concat(this.vo.orgId),{params:Object(s["a"])({},this.table.pageInfo)}).then((function(e){"00000"===e.status&&e.data&&(t.table.datas=e.data,t.table.pageInfo.total=e.page.total)}))},handleConfirm:function(){var t=this;if(this.handleSelectionList&&this.handleSelectionList.length){var e=this.handleSelectionList.map((function(e){return{id:parseInt(e.id),stationId:parseInt(t.$route.params.id)}}));this.$confirm("请确认是否要分配当前选中数据","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(a,n,i){"confirm"===a?(n.confirmButtonLoading=!0,t.$api.post("/bms/station/distributeDevice",e).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"分配成功!"}),t.handlePageCurrentChange(1),i(!0)):t.$message({type:"success",message:e.message||"操作失败!"})})).catch((function(){i(!1)})).finally((function(){n.confirmButtonLoading=!1}))):i()}}).catch((function(){}))}else this.$message({type:"error",message:"请选择数据!"})},checkItemFn:function(t){return!0}}}),o=l,c=(a("74ec"),a("2877")),r=Object(c["a"])(o,n,i,!1,null,"0a9c4467",null);e["default"]=r.exports}}]);