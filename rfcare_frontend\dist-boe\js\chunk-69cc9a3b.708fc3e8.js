(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-69cc9a3b"],{"0406":function(t,a,e){},"660a":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",[e("page-header",{attrs:{title:"详情查看"}}),e("page-main",{attrs:{title:"基础信息"}},[e("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[e("el-col",{attrs:{md:8}},[e("span",{staticClass:"label-field"},[t._v("机构编号:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.orgCode))])]),e("el-col",{attrs:{md:8}},[e("span",{staticClass:"label-field"},[t._v("机构名称:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.name))])]),e("el-col",{attrs:{md:8}},[e("span",{staticClass:"label-field"},[t._v("机构简称:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.shortName))])]),e("el-col",{attrs:{md:8}},[e("span",{staticClass:"label-field"},[t._v("企业ID:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.companyId))])]),e("el-col",{attrs:{md:16}},[e("span",{staticClass:"label-field"},[t._v("企业KEY:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.companyKey))])]),e("el-col",{attrs:{md:16}},[e("span",{staticClass:"label-field"},[t._v("外部url:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.externalUrl))])]),e("el-col",{attrs:{md:16}},[e("span",{staticClass:"label-field",staticStyle:{width:"140px"}},[t._v("是否推广增值服务:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s("1"===t.reqForm.spreadServer?"是":"否"))])])],1)],1),e("page-main",{attrs:{title:"系统资料"}},[e("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[e("el-col",{attrs:{md:24}},[e("span",{staticClass:"label-field"},[t._v("系统名称:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.systemName||"-"))])]),e("el-col",{attrs:{md:24}},[e("span",{staticClass:"label-field"},[t._v("企业LOGO:")]),t._v(" "),t.reqForm.logo?e("el-image",{style:"width: 80px;height: 80px;vertical-align: text-top;",attrs:{src:t.reqForm.logo,fit:"fill"}}):t._e()],1)],1)],1),e("page-main",{attrs:{title:"机构账号"}},[e("div",{staticStyle:{position:"absolute",right:"20px","z-index":"10"}},[e("el-button",{attrs:{type:"primary",size:"small",disabled:!t.reqForm.phone},nativeOn:{click:function(a){return t.handleResetPwd(t.reqForm.sysUserVO.id,t.reqForm.name)}}},[t._v("重置密码")])],1),e("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[e("el-col",{attrs:{md:12}},[e("span",{staticClass:"label-field"},[t._v("用户名:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.userName))])]),e("el-col",{attrs:{md:12}},[e("span",{staticClass:"label-field"},[t._v("手机号:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.phone))])]),e("el-col",{attrs:{md:12}},[e("span",{staticClass:"label-field"},[t._v("邮箱:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.email))])]),e("el-col",{attrs:{md:12}},[e("span",{staticClass:"label-field"},[t._v("服务有效期:")]),e("span",{staticClass:"value-field"},[t._v(t._s(t.reqForm.servStartDate&&t.reqForm.servEndDate?t.reqForm.servStartDate+"~"+t.reqForm.servEndDate:"")+" "+t._s(t.reqForm.servStartDate&&!t.reqForm.servEndDate?t.reqForm.servStartDate+"~":"")+" "+t._s(!t.reqForm.servStartDate&&t.reqForm.servEndDate?"~"+t.reqForm.servStartDate:""))])]),e("el-col",{attrs:{md:12}},[e("span",{staticClass:"label-field"},[t._v("默认密码:")]),t._v(" "),e("span",{staticClass:"value-field"},[t._v("123456")])])],1)],1),e("fixed-action-bar",[e("el-button",{on:{click:function(a){return t.$router.go(-1)}}},[t._v("返回")])],1)],1)},l=[],r=(e("d3b7"),{data:function(){return{reqForm:{}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(t){var a=this;t&&this.$api.get("/bms/org/getOrganizationInfo/".concat(t),{}).then((function(t){"00000"===t.status&&t.data&&(t.data&&!t.data.sysUserVO&&(t.data.sysUserVO={}),t.data.userName=t.data.sysUserVO.userName,t.data.phone=t.data.sysUserVO.phone,t.data.email=t.data.sysUserVO.email,a.reqForm=t.data)}))},handleResetPwd:function(t,a){var e=this;this.$confirm("是否重置该机构 [".concat(a||"-","] 的密码?"),"提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(a,s,l){"confirm"===a?(s.confirmButtonLoading=!0,e.$api.post("/bms/org/resetOrgAccountPassword/".concat(t),{}).then((function(t){e.$message({type:"success",message:"重置成功!"}),l(!0)})).catch((function(){l(!1)})).finally((function(){s.confirmButtonLoading=!1}))):l()}}).catch((function(){}))}}}),i=r,n=(e("858b"),e("2877")),o=Object(n["a"])(i,s,l,!1,null,"70a814b8",null);a["default"]=o.exports},"858b":function(t,a,e){"use strict";e("0406")}}]);