(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6a4c9664"],{"3dd6":function(e,t,r){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},8739:function(e,t,r){"use strict";r("e956")},c97f:function(e,t,r){"use strict";r("3dd6")},e1a4:function(e,t,r){"use strict";function i(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function s(e){var t=/^\d{11}$/;return t.test(e)}function a(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function o(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function n(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function l(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function c(e){return l(e)||n(e)}r.d(t,"b",(function(){return i})),r.d(t,"c",(function(){return s})),r.d(t,"d",(function(){return a})),r.d(t,"a",(function(){return o})),r.d(t,"e",(function(){return c}))},e956:function(e,t,r){},f057:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{"padding-bottom":"80px"}},[r("page-header",{attrs:{title:(e.$route.params.id?"编辑":"新增")+"服务信息"}}),r("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.rules,"label-position":"top","label-width":"80px",size:"small"}},[r("page-main",{attrs:{title:"基础信息"}},[r("el-row",{staticStyle:{padding:"20px","box-sizing":"border-box",width:"780px",margin:"0 auto"},attrs:{gutter:20}},[r("el-col",{attrs:{md:12}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务名称",prop:"serverName"}},[r("el-input",{attrs:{placeholder:"请输入服务名称"},model:{value:e.reqForm.serverName,callback:function(t){e.$set(e.reqForm,"serverName",t)},expression:"reqForm.serverName"}})],1)],1),r("el-col",{attrs:{md:12}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务类别",prop:"serverType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.reqForm.serverType,callback:function(t){e.$set(e.reqForm,"serverType",t)},expression:"reqForm.serverType"}},e._l(e.dict.serverTypes,(function(e){return r("el-option",{key:e.dimCode+"",attrs:{label:e.name,value:e.dimCode+""}})})),1)],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务介绍",prop:"serverDesc"}},[r("editor",{attrs:{id:"descEditor",init:e.descEditor},model:{value:e.reqForm.serverDesc,callback:function(t){e.$set(e.reqForm,"serverDesc",t)},expression:"reqForm.serverDesc"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务icon",prop:"serverIcon"}},[r("ImageUpload",{attrs:{url:e.reqForm.serverIcon||void 0,action:e.baseURL+"/bms/server/img/upload",headers:{token:e.$store.state.user.token}},on:{"on-success":e.handleLogoChange,"on-remove":function(t){e.reqForm.serverIcon=void 0}}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"是否需要激活",prop:"requiresActive"}},[r("el-tooltip",{staticClass:"item",attrs:{effect:"dark",content:"判断使用服务时，是否需要同意服务协议",placement:"bottom-start"}},[r("el-badge",{staticStyle:{"font-size":"20px","font-weight":"900",position:"relative",top:"-40px",left:"90px"},attrs:{value:"?"}})],1),r("el-radio",{attrs:{label:"0"},model:{value:e.reqForm.requiresActive,callback:function(t){e.$set(e.reqForm,"requiresActive",t)},expression:"reqForm.requiresActive"}},[e._v("是")]),r("el-radio",{attrs:{label:"1"},model:{value:e.reqForm.requiresActive,callback:function(t){e.$set(e.reqForm,"requiresActive",t)},expression:"reqForm.requiresActive"}},[e._v("否")])],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"精简协议",prop:"simpleAgreement"}},[r("editor",{attrs:{id:"simpleAgreementEditor",init:e.simpleAgreementEditor},model:{value:e.reqForm.simpleAgreement,callback:function(t){e.$set(e.reqForm,"simpleAgreement",t)},expression:"reqForm.simpleAgreement"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务协议",prop:"agreement"}},[r("editor",{attrs:{id:"agreementEditor",init:e.agreementEditor},model:{value:e.reqForm.agreement,callback:function(t){e.$set(e.reqForm,"agreement",t)},expression:"reqForm.agreement"}})],1)],1)],1)],1),r("page-main",{attrs:{title:"服务价格"}},[r("div",{staticClass:"service-list"},[e._l(e.reqForm.serverPriceVOList,(function(t,i){return r("el-row",{key:i,staticClass:"service-item",attrs:{gutter:20}},[i>0?r("i",{staticClass:"el-icon-delete",staticStyle:{position:"absolute",top:"20px",right:"20px",color:"red",cursor:"pointer"},on:{click:function(t){return e.handleRemovePriceAction(i)}}}):e._e(),r("el-col",{attrs:{span:12}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"计费方式",prop:"serverPriceVOList."+i+".billingMethod",rules:[{required:!0,message:"请选择计费方式",trigger:"change"}]}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.billingMethod,callback:function(r){e.$set(t,"billingMethod",r)},expression:"item.billingMethod"}},["Y"!==t.billingMethod&&e.billingMethodMapping["Y"]?e._e():r("el-option",{attrs:{label:"按年",value:"Y"}}),"Q"!==t.billingMethod&&e.billingMethodMapping["Q"]?e._e():r("el-option",{attrs:{label:"按季",value:"Q"}}),"M"!==t.billingMethod&&e.billingMethodMapping["M"]?e._e():r("el-option",{attrs:{label:"按月",value:"M"}})],1)],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"单价",prop:"serverPriceVOList."+i+".unitPrice",rules:[{required:!0,message:"请输入单价",trigger:"blur"}]}},[r("el-input",{attrs:{type:"number",placeholder:"请输入单价"},model:{value:t.unitPrice,callback:function(r){e.$set(t,"unitPrice",r)},expression:"item.unitPrice"}},[r("template",{slot:"append"},[e._v("元")])],2)],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"优惠价",prop:"discountPrice"}},[r("el-input",{attrs:{type:"number",placeholder:"请输入优惠价"},model:{value:t.discountPrice,callback:function(r){e.$set(t,"discountPrice",r)},expression:"item.discountPrice"}},[r("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1)})),e.reqForm.serverPriceVOList.length<3?r("div",{staticStyle:{"margin-top":"20px","margin-left":"-10px"}},[r("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleAddPriceAction}},[e._v("添加价格信息")])],1):e._e()],2)])],1),r("fixed-action-bar",[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)},s=[],a=(r("d81d"),r("4de4"),r("a434"),r("365c")),o=(r("e1a4"),r("ca72")),n=(r("030f"),{language_url:"/tinymce/langs/zh_CN.js",language:"zh_CN",skin_url:"/tinymce/skins/ui/oxide",branding:!1,plugins:"link code table lists"}),l={components:{Editor:o["a"]},data:function(){var e=this,t=function(t,r,i){if("0"===e.reqForm.requiresActive&&!r)return i(new Error('需要激活为"是",请填写精简协议'));i()},r=function(t,r,i){if("0"===e.reqForm.requiresActive&&!r)return i(new Error('需要激活为"是",请填写服务协议'));i()};return{baseURL:a["a"],keyword:"",dict:{serverTypes:[]},reqForm:{id:void 0,serverName:void 0,serverType:void 0,serverDesc:void 0,serverIcon:void 0,requiresActive:"1",simpleAgreement:null,agreement:null,serverPriceVOList:[{billingMethod:void 0,unitPrice:void 0,discountPrice:void 0}]},rules:{serverName:[{required:!0,trigger:"blur",message:"请填写服务名称"}],serverType:[{required:!0,trigger:"change",message:"请填写服务类型"}],simpleAgreement:[{validator:t}],agreement:[{validator:r}]},submitLoading:!1,descEditor:Object.assign({selector:"#descEditor"},n),simpleAgreementEditor:Object.assign({selector:"#simpleAgreementEditor"},n),agreementEditor:Object.assign({selector:"#agreementEditor"},n),switchProps:{activeValue:"1",inactiveValue:"0",activeText:"是",inactiveText:"否"}}},mounted:function(){this.fetchLiveTypeDict(),this.fetchDetail(this.$route.params.id)},computed:{billingMethodMapping:function(){var e={};return(this.reqForm.serverPriceVOList||[]).map((function(t){e[t.billingMethod]=!0})),e}},methods:{handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))},handleLogoChange:function(e){e&&e.data?this.reqForm.serverIcon=e.data:this.reqForm.serverIcon=""},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/server/getServerInfo/".concat(e),{}).then((function(e){if("00000"===e.status&&e.data){if(e.data.serverPriceVOList&&e.data.serverPriceVOList.length){var r=[null,null,null];e.data.serverPriceVOList.map((function(e){"Y"===e.billingMethod?r[0]=e:"Q"===e.billingMethod?r[1]=e:"M"===e.billingMethod&&(r[2]=e)})),e.data.serverPriceVOList=r.filter((function(e){return e}))}t.reqForm=e.data}}))},fetchLiveTypeDict:function(){var e=this;this.$api.get("/dict/queryDictItemsByType",{params:{typeCode:"server_type"}}).then((function(t){"00000"===t.status&&t.data&&(e.dict.serverTypes=t.data)}))},handleAddPriceAction:function(){this.reqForm.serverPriceVOList.push({billingMethod:void 0,unitPrice:void 0})},handleRemovePriceAction:function(e){this.reqForm.serverPriceVOList.splice(e,1)},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var r=JSON.parse(JSON.stringify(t.reqForm)),i=t.$route.params.id?t.$api.post("/bms/server/updateServer",r):t.$api.post("/bms/server/addServer",r);i.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),t.submitLoading=!1,setTimeout((function(){t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},c=l,d=(r("c97f"),r("8739"),r("2877")),u=Object(d["a"])(c,i,s,!1,null,"364242bb",null);t["default"]=u.exports}}]);