(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6ec3bedc"],{"499d":function(e,t,i){"use strict";i.r(t);var r=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",{staticStyle:{"padding-bottom":"80px"}},[i("page-header",{attrs:{title:"配置价格"}}),i("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},["org"!==e.$store.state.user.member.role?i("page-main",{attrs:{title:"基础信息"}},[i("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px",width:"820px",margin:"0 auto"},attrs:{gutter:20}},[i("el-col",{attrs:{md:24}},[i("span",{staticClass:"label-field"},[e._v("服务名称:")]),e._v(" "),i("span",{staticClass:"value-field"},[e._v(e._s(e.serviceVo.serverName))])]),i("el-col",{attrs:{md:24}},[i("span",{staticClass:"label-field"},[e._v("服务介绍:")]),e._v(" "),i("span",{staticClass:"value-field"},[e._v(e._s(e.serviceVo.serverDesc))])]),i("el-col",{attrs:{md:24}},[i("span",{staticClass:"label-field",staticStyle:{position:"relative"}},[i("span",{staticStyle:{position:"absolute",left:"-14px",top:"0",color:"#f56c6c"}},[e._v("*")]),e._v("所属机构:")]),i("el-form-item",{staticStyle:{display:"inline-block"},attrs:{prop:"orgId"}},[i("el-select",{attrs:{size:"small",disabled:void 0!=e.reqForm.id,filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.reqForm.orgId,callback:function(t){e.$set(e.reqForm,"orgId",t)},expression:"reqForm.orgId"}},e._l(e.dict.orgs,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1)],1):e._e(),i("page-main",{attrs:{title:"服务价格"}},[i("div",{staticClass:"service-list"},[e._l(e.reqForm.serverOrgCfgPriceVOList,(function(t,r){return i("el-row",{key:r,staticClass:"service-item",attrs:{gutter:20}},[r>0?i("i",{staticClass:"el-icon-delete",staticStyle:{position:"absolute",top:"20px",right:"20px",color:"red",cursor:"pointer"},on:{click:function(t){return e.handleRemovePriceAction(r)}}}):e._e(),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"计费方式",prop:"serverOrgCfgPriceVOList."+r+".billingMethod",rules:[{required:!0,message:"请选择计费方式",trigger:"change"}]}},[i("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.billingMethod,callback:function(i){e.$set(t,"billingMethod",i)},expression:"item.billingMethod"}},["Y"!==t.billingMethod&&e.billingMethodMapping["Y"]?e._e():i("el-option",{attrs:{label:"按年",value:"Y"}}),"Q"!==t.billingMethod&&e.billingMethodMapping["Q"]?e._e():i("el-option",{attrs:{label:"按季",value:"Q"}}),"M"!==t.billingMethod&&e.billingMethodMapping["M"]?e._e():i("el-option",{attrs:{label:"按月",value:"M"}})],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"单价",prop:"serverOrgCfgPriceVOList."+r+".unitPrice",rules:[{required:!0,message:"请输入单价",trigger:"blur"}]}},[i("el-input",{attrs:{type:"number",placeholder:"请输入单价"},model:{value:t.unitPrice,callback:function(i){e.$set(t,"unitPrice",i)},expression:"item.unitPrice"}},[i("template",{slot:"append"},[e._v("元")])],2)],1)],1)],1)})),e.reqForm.serverOrgCfgPriceVOList.length<3?i("div",{staticStyle:{"margin-top":"20px","margin-left":"-10px"}},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.handleAddPriceAction}},[e._v("添加价格信息")])],1):e._e()],2)])],1),i("fixed-action-bar",[i("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)},s=[],a=(i("d81d"),i("a434"),i("365c")),o={data:function(){return{baseURL:a["a"],dict:{orgs:[]},reqForm:{id:void 0,serverId:void 0,orgId:void 0,serverOrgCfgPriceVOList:[{billingMethod:void 0,unitPrice:void 0}]},serviceVo:{},reqFormRules:{orgId:[{required:!0,message:"请选择所属机构",trigger:"change"}]},submitLoading:!1}},computed:{billingMethodMapping:function(){var e={};return(this.reqForm.serverOrgCfgPriceVOList||[]).map((function(t){e[t.billingMethod]=!0})),e}},mounted:function(){this.handleFetchOrgs(),this.$route.params.serviceId?(this.reqForm.serverId=this.$route.params.serviceId,this.fetchServiceDetail(this.$route.params.serviceId)):this.fetchDetail(this.$route.params.id)},methods:{fetchServiceDetail:function(e){var t=this;e&&this.$api.get("/bms/server/getServerInfo/".concat(this.reqForm.serverId),{}).then((function(e){"00000"===e.status&&e.data&&(t.serviceVo=e.data)}))},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/server/getServerOrgCfgInfo//".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data,t.fetchServiceDetail(e.data.serverId))}))},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))},handleAddPriceAction:function(){this.reqForm.serverOrgCfgPriceVOList.push({billingMethod:void 0,unitPrice:void 0})},handleRemovePriceAction:function(e){this.reqForm.serverOrgCfgPriceVOList.splice(e,1)},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var i=JSON.parse(JSON.stringify(t.reqForm)),r=t.$route.params.id?t.$api.post("/bms/server/updateServerOrgCfg",i):t.$api.post("/bms/server/addServerOrgCfg",i);r.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),t.submitLoading=!1,setTimeout((function(){t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},l=o,n=(i("a140"),i("2877")),c=Object(n["a"])(l,r,s,!1,null,"c3cd8068",null);t["default"]=c.exports},a140:function(e,t,i){"use strict";i("ae34")},ae34:function(e,t,i){}}]);