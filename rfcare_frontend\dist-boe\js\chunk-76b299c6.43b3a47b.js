(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-76b299c6"],{"1c82":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务订单"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"80px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"订单编号:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.orderNo,callback:function(t){e.$set(e.search,"orderNo",t)},expression:"search.orderNo"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"客户:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.customerName,callback:function(t){e.$set(e.search,"customerName",t)},expression:"search.customerName"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"所属机构:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.search.orgId,callback:function(t){e.$set(e.search,"orgId",t)},expression:"search.orgId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.orgs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"订单状态:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},model:{value:e.search.payStatus,callback:function(t){e.$set(e.search,"payStatus",t)},expression:"search.payStatus"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(Object.entries(e.$store.state.dict.payStatus),(function(e,t){return a("el-option",{key:t,attrs:{value:e[0],label:e[1]}})}))],2)],1),a("el-form-item",{staticStyle:{"margin-left":"20px",width:"80%"},attrs:{label:"订单类型:"}},[a("el-select",{staticStyle:{width:"80%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:e.search.orderType,callback:function(t){e.$set(e.search,"orderType",t)},expression:"search.orderType"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(Object.entries(e.$store.state.dict.orderType),(function(e,t){return a("el-option",{key:t,attrs:{value:e[0],label:e[1]}})}))],2)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{directives:[{name:"auth",rawName:"v-auth",value:["self.system"],expression:"[ 'self.system' ]"}],staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"serviceOrderAdd"})}}},[e._v("新增订单")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"orderNo",label:"订单编号",width:"190",align:"center"}}),a("el-table-column",{attrs:{prop:"orderTypeCName",label:"订单类型",align:"center"}}),a("el-table-column",{attrs:{prop:"customerName",label:"客户",align:"center"}}),a("el-table-column",{attrs:{prop:"orgCName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"buyTypeCName",label:"购买类型",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"payStatusCName",label:"订单状态",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("span",{style:{color:e.dict.statusColor[r.payStatus]||"#999"}},[e._v(e._s(r.payStatusCName))])]}}])}),"system"===e.$store.state.user.member.role?a("el-table-column",{attrs:{prop:"createUserName",label:"创建人",width:"150",align:"center"}}):e._e(),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"170",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return["1"===r.payStatus&&"org"===r.orderType?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:["self.system"],expression:"[ 'self.system' ]"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"serviceOrderEdit",params:{id:r.id}})}}},[e._v("维护")]):e._e(),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"serviceOrderInfo",params:{id:r.id}})}}},[e._v("详情查看")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},l=[],s=a("5530"),n=(a("ac1f"),a("841c"),{data:function(){return{dict:{orgs:[],statusColor:{1:"#ff3609",2:"#25c525",3:"#999"}},search:{orderNo:void 0,customerName:void 0,orgId:void 0,payStatus:void 0,orderType:void 0},table:{datas:[],pageInfo:{current:1,total:0}}}},mounted:function(){this.search.orgId=this.$route.params.id,this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},resetForm:function(){Object.assign(this.$data.search,this.$options.data().search),this.search.orgId=this.$route.params.id,this.fetchDatas()},fetchDatas:function(e){var t=this;this.$api.get("/bms/server-order/listOrder",{params:Object(s["a"])(Object(s["a"])({},this.search),this.table.pageInfo)}).then((function(e){"00000"===e.status&&e.data?(t.table.datas=e.data,t.table.pageInfo.total=e.page.total):(t.table.datas=[],t.table.pageInfo.total=0)}))},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))}}}),i=n,o=(a("8a35"),a("2877")),c=Object(o["a"])(i,r,l,!1,null,"d86aa8da",null);t["default"]=c.exports},"8a35":function(e,t,a){"use strict";a("aa8c")},aa8c:function(e,t,a){}}]);