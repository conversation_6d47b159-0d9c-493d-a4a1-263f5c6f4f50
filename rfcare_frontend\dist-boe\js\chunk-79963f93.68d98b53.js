(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-79963f93"],{"0676":function(e,t,a){},4960:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"机构管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"110px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"机构名称:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.name,callback:function(t){e.$set(e.search,"name",t)},expression:"search.name"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"系统名称:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.systemName,callback:function(t){e.$set(e.search,"systemName",t)},expression:"search.systemName"}})],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},on:{click:e.handleSyncOrg}},[e._v("同步机构")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"orgCode",label:"机构编码",width:"140",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"机构名称",minWidth:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"shortName",label:"机构简称",minWidth:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"companyId",label:"企业ID",width:"190",align:"center"}}),a("el-table-column",{attrs:{prop:"stationCount",label:"服务站",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationListByOrg",params:{orgId:n.id}})}}},[e._v(e._s(n.stationCount||0))])]}}])}),a("el-table-column",{attrs:{prop:"devCount",label:"设备服务",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"orgDeviceList",params:{id:n.id}})}}},[e._v(e._s(n.devCount||0))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"服务有效期",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.servStartDate&&a.servEndDate?a.servStartDate+"~"+a.servEndDate:"")+" "+e._s(a.servStartDate&&!a.servEndDate?a.servStartDate+"~":"")+" "+e._s(!a.servStartDate&&a.servEndDate?"~"+a.servStartDate:"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"170",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["0"===n.validStatus?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"danger",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(n.id,1)}}},[e._v("禁用")]):e._e(),"1"===n.validStatus?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(n.id,0)}}},[e._v("启用")]):e._e(),a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationOrgEdit",params:{id:n.id}})}}},[e._v("维护")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationOrgInfo",params:{id:n.id}})}}},[e._v("详情查看")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},r=[],s=a("5530"),i=(a("ac1f"),a("841c"),a("d3b7"),{data:function(){return{dict:{stations:[]},search:{name:void 0,systemName:void 0},table:{datas:[],pageInfo:{current:1,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handleRadioChange:function(e){this.search.status=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/org/listOrganization",{params:Object(s["a"])(Object(s["a"])({},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleChangeStatus:function(e,t){var a=this;e&&void 0!=t&&null!=t&&this.$api.post("/bms/org/invalidOrganization",{id:e,validStatus:t}).then((function(e){"00000"===e.status?(a.$message({type:"success",message:"更新成功!"}),a.fetchDatas()):a.$message({type:"error",message:e.message||"操作失败!"})}))},resetForm:function(){Object.assign(this.$data.search,this.$options.data().search),this.fetchDatas()},handleSyncOrg:function(){var e=this;this.$confirm("是否同步dmp系统的机构信息?","提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(t,a,n){"confirm"===t?(a.confirmButtonLoading=!0,e.$api.get("/bms/org/synOrganization",{}).then((function(t){e.$message({type:"success",message:"同步成功!"}),e.handlePageCurrentChange(1),n(!0)})).catch((function(){n(!1)})).finally((function(){a.confirmButtonLoading=!1}))):n()}}).catch((function(){}))}}}),l=i,o=(a("9ec3"),a("2877")),c=Object(o["a"])(l,n,r,!1,null,"42b57b60",null);t["default"]=c.exports},"9ec3":function(e,t,a){"use strict";a("0676")}}]);