(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7e208fb6"],{"0350":function(t,e,s){"use strict";s("7547")},"04ff":function(t,e,s){"use strict";s("8a6f")},"2d2c":function(t,e,s){},"3b0e":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"older-analysis-content flex-col"},[s("el-row",{staticClass:"flex-row items-center"},[s("el-col",{staticClass:"older-analysis-top flex-row items-center",attrs:{span:6}},[s("div",{staticClass:"older-analysis-icon-block flex-col justify-around"},[s("span",{staticClass:"iconfont icon-person-nan bed-icon",staticStyle:{color:"#088bfe"}}),s("span",{staticClass:"person-num"},[t._v(t._s(t.data.manNum||0)+"人")])]),s("div",{staticClass:"older-analysis-icon-block flex-col justify-around"},[s("span",{staticClass:"iconfont icon-person-nv bed-icon",staticStyle:{color:"#f34336"}}),s("span",{staticClass:"person-num"},[t._v(t._s(t.data.womanNum||0)+"人")])])]),s("el-col",{staticClass:"older-analysis flex-row items-center justify-around",attrs:{span:18}},[s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"older-analysis-label"},[t._v("≤60岁")]),s("span",{staticClass:"older-analysis-value"},[t._v(t._s(t.data.age60||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"older-analysis-label"},[t._v("61-70岁")]),s("span",{staticClass:"older-analysis-value"},[t._v(t._s(t.data.age6170||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"older-analysis-label"},[t._v("71-80岁")]),s("span",{staticClass:"older-analysis-value"},[t._v(t._s(t.data.age7180||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"older-analysis-label"},[t._v("81-90岁")]),s("span",{staticClass:"older-analysis-value"},[t._v(t._s(t.data.age8190||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"older-analysis-label"},[t._v(">90岁")]),s("span",{staticClass:"older-analysis-value"},[t._v(t._s(t.data.age90||0))])])])],1),t.headers&&t.headers.length>0?s("div",{staticClass:"bed-list flex-col"},[s("ScrollingTable",{attrs:{data:t.list,headers:t.headers,rowNum:4}})],1):t._e()],1)},i=[],a=(s("d193"),{name:"OlderAnalysis",props:{data:{type:Object,required:!0},list:{type:Array,required:!1},headers:{type:Array,required:!1}},data:function(){return{}},mounted:function(){},methods:{}}),c=a,r=(s("0350"),s("2877")),o=Object(r["a"])(c,n,i,!1,null,"e910270e",null);e["a"]=o.exports},4277:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"bg-container flex-col"},[s("div",{staticClass:"bg flex-col"}),s("div",{staticClass:"main flex-col"},[s("div",{staticClass:"top flex-col"},[s("div",{staticClass:"page-title flex-col"},[s("span",{staticClass:"text"},[t._v(t._s(t.title))])])]),s("div",{staticClass:"curr-time"},[s("span",[t._v(t._s(t.currentTime))])]),t.path?s("div",{staticClass:"notice",on:{click:t.openNotice}},[t.noticeStatus?s("span",{staticClass:"icon iconfont icon-shengyin notice-icon"}):s("span",{staticClass:"icon iconfont icon-shengyinguanbi notice-icon"})]):t._e(),t._t("default")],2),s("el-dialog",{staticClass:"event-dialog",attrs:{title:"报警消息",visible:t.dialogTableVisible},on:{"update:visible":function(e){t.dialogTableVisible=e}}},[s("el-row",{attrs:{gutter:20,justify:"space-around"}},[s("el-col",{attrs:{span:20}},[s("div",[t._v("总计："+t._s(t.eventList.length))])]),s("el-col",{attrs:{span:4}},[s("div",[s("el-button",{attrs:{type:"primary",size:"mini"},on:{click:t.removeAll}},[t._v("全部知道了")])],1)])],1),s("el-table",{staticStyle:{"margin-top":"12px"},attrs:{data:t.eventList}},[s("el-table-column",{attrs:{property:"devName",label:"设备名称",width:"180"}}),s("el-table-column",{attrs:{property:"orderType",label:"类型",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){var s=e.row;return[t._v(" "+t._s(t.getOrderTypeName(s.orderType))+" ")]}}])}),s("el-table-column",{attrs:{property:"createTime",label:"发生时间"}}),s("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[s("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(s){return t.remove(e.row.id)}}},[t._v("知道了")])]}}])})],1)],1)],1)},i=[],a=s("2909"),c=s("1da1"),r=(s("96cf"),s("4de4"),s("99af"),s("5a0c")),o=s.n(r),l=s("5530");function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function h(t,e){for(var s=0;s<e.length;s++){var n=e[s];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function d(t,e,s){return e&&h(t.prototype,e),s&&h(t,s),t}s("a434");var f=function(){function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};u(this,t),this.config=Object(l["a"])({lang:"zh-cn",voiceURI:"Microsoft Huihui - Chinese (Simplified, PRC)",volume:.7,rate:.7,pitch:1,repeatCount:3,onMessage:null},e),this.isEnabled=!1,this.speech=null,this.eventQueue=[],this.currentSpeakText=null,this.currentSpeakCount=0,this.batchSpeak=!1,this.init()}return d(t,[{key:"init",value:function(){var t=this;if("undefined"===typeof SpeechSynthesisUtterance)return this.showMessage("您的浏览器不支持TTS语音功能"),!1;try{return this.speech=new SpeechSynthesisUtterance,this.speech.lang=this.config.lang,this.speech.voiceURI=this.config.voiceURI,this.speech.volume=this.config.volume,this.speech.rate=this.config.rate,this.speech.pitch=this.config.pitch,this.speech.onend=function(){t.handleSpeechEnd()},console.log("TTS初始化成功"),!0}catch(e){return console.error("TTS初始化失败:",e),this.showMessage("TTS初始化失败"),!1}}},{key:"handleSpeechEnd",value:function(){if(this.currentSpeakText){if(this.currentSpeakCount+=1,this.currentSpeakCount<this.config.repeatCount)return console.log("第".concat(this.currentSpeakCount+1,"次重复播报")),void this.playNotice(this.currentSpeakText);this.currentSpeakText=null,this.batchSpeak||this.eventQueue.splice(0,1),this.playNextNotice()}}},{key:"toggleNotice",value:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(){var e,s=arguments;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=s.length>0&&void 0!==s[0]?s[0]:null,this.isEnabled=null===e?!this.isEnabled:e,!this.isEnabled){t.next=8;break}return t.next=5,this.playNotice("已开启语音通知");case 5:this.showMessage("已开启语音通知!"),t.next=9;break;case 8:this.showMessage("语音通知已关闭!");case 9:return t.abrupt("return",this.isEnabled);case 10:case"end":return t.stop()}}),t,this)})));function e(){return t.apply(this,arguments)}return e}()},{key:"speakText",value:function(t){this.isEnabled&&t&&this.speech&&(this.speech.text=t,console.log("开始播放:",t),window.speechSynthesis.speak(this.speech),console.log("播放命令已发送"))}},{key:"cancel",value:function(){window.speechSynthesis&&window.speechSynthesis.cancel(),this.currentSpeakText=null,this.currentSpeakCount=0}},{key:"playNotice",value:function(){var t=Object(c["a"])(regeneratorRuntime.mark((function t(e){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(this.isEnabled){t.next=3;break}return console.log("已关闭语音通知!"),t.abrupt("return");case 3:this.speakText(e);case 4:case"end":return t.stop()}}),t,this)})));function e(e){return t.apply(this,arguments)}return e}()},{key:"playNextNotice",value:function(){if(this.eventQueue.length>=3)return this.batchSpeak=!0,void this.speakText("最近发生".concat(this.eventQueue.length,"次告警"));if(this.batchSpeak=!1,window.speechSynthesis&&window.speechSynthesis.speaking)console.log("当前已有正在播放的语音!");else{var t=this.eventQueue[0];if(t){console.log("播放事件:",t);var e=this.buildEventText(t);this.currentSpeakCount=0,this.currentSpeakText=e,this.playNotice(e)}}}},{key:"buildEventText",value:function(t){return"".concat(t.devName,"发生").concat(this.getOrderTypeName(t.orderType),"告警")}},{key:"getOrderTypeName",value:function(t){var e={21:"跌倒",22:"久滞",23:"坠床",31:"主动呼叫"};return e[t]||"未知"}},{key:"addEvent",value:function(t){this.eventQueue.push(t)}},{key:"removeAllEvents",value:function(){this.eventQueue=[],this.cancel()}},{key:"getQueueLength",value:function(){return this.eventQueue.length}},{key:"getStatus",value:function(){return{isEnabled:this.isEnabled,queueLength:this.eventQueue.length,isPlaying:!!window.speechSynthesis&&window.speechSynthesis.speaking,currentText:this.currentSpeakText,currentCount:this.currentSpeakCount}}},{key:"showMessage",value:function(t){this.config.onMessage&&"function"===typeof this.config.onMessage?this.config.onMessage(t):console.log("TTS消息:",t)}},{key:"destroy",value:function(){this.cancel(),this.eventQueue=[],this.currentSpeakText=null,this.currentSpeakCount=0,this.speech=null,console.log("TTS实例已销毁")}}]),t}(),p=f,v={props:{title:String,path:String},computed:{noticeStatus:function(){return!!this.ttsManager&&this.ttsManager.isEnabled}},data:function(){return{currentTime:"",socket:void 0,eventList:[],dialogTableVisible:!1,ttsManager:null,wsConnected:!1,wsReconnectAttempts:0,wsMaxReconnectAttempts:5,wsReconnectInterval:3e3,wsHeartbeatTimer:null,wsHeartbeatInterval:3e4,wsReconnectTimer:null}},watch:{path:function(t){this.path=t,this.closeWS(),this.initWS()}},mounted:function(){var t=this;this.setInterval=setInterval((function(){t.currentTime=o()().format("YYYY-MM-DD HH:mm:ss")}),1e3),this.ttsManager=new p({onMessage:function(e){t.$message(e)}}),console.log("path:"+this.path),this.path&&this.initWS()},beforeDestroy:function(){this.closeWS(),this.ttsManager&&this.ttsManager.destroy()},methods:{initWS:function(){if(void 0!==WebSocket)if(this.path)try{console.log("正在建立WebSocket连接:",this.path),this.socket=new WebSocket(this.path),this.socket.onopen=this.open,this.socket.onerror=this.error,this.socket.onmessage=this.getMessage,this.socket.onclose=this.close}catch(t){console.error("WebSocket连接创建失败:",t),this.scheduleReconnect()}else console.warn("WebSocket路径为空，无法建立连接");else this.$message("您的浏览器不支持socket")},open:function(){console.log("WebSocket连接成功"),this.wsConnected=!0,this.wsReconnectAttempts=0,this.startHeartbeat()},error:function(t){console.error("WebSocket连接错误:",t),this.wsConnected=!1,this.stopHeartbeat(),this.scheduleReconnect()},getMessage:function(t){console.log("getMessage:",t);try{var e=JSON.parse(t.data);if("heartbeat"===e.type||"pong"===e.type)return void console.log("收到心跳响应");if(e.data){var s=e.data,n=s.wsdata,i=s.type;if(console.log("wsdata:",n),"EVENT_ORDER"==i&&this.noticeStatus){this.dialogTableVisible||(this.dialogTableVisible=!0);var a=this.eventList.length;this.eventList.push(n),this.ttsManager&&(this.ttsManager.addEvent(n),0==a&&this.ttsManager.playNextNotice())}console.log(n)}}catch(c){console.error("解析WebSocket消息失败:",c,t.data)}},send:function(){var t="";this.socket.send(t)},close:function(t){console.log("WebSocket连接已关闭:",t),this.wsConnected=!1,this.stopHeartbeat(),1e3!==t.code&&(console.log("WebSocket异常关闭，准备重连..."),this.scheduleReconnect())},remove:function(t){this.eventList=this.eventList.filter((function(e){return e.id!=t})),0==this.eventList.length&&(this.dialogTableVisible=!1)},removeAll:function(){this.eventList=[],this.dialogTableVisible=!1,this.ttsManager&&this.ttsManager.removeAllEvents()},openNotice:function(){var t=this;return Object(c["a"])(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!t.ttsManager){e.next=3;break}return e.next=3,t.ttsManager.toggleNotice();case 3:case"end":return e.stop()}}),e)})))()},cancel:function(){this.ttsManager&&this.ttsManager.cancel()},playNextNotice:function(){this.ttsManager&&(this.ttsManager.eventQueue=Object(a["a"])(this.eventList),this.ttsManager.playNextNotice()),0==this.eventList.length&&(this.dialogTableVisible=!1)},getOrderTypeName:function(t){switch(t){case"21":return"跌倒";case"22":return"久滞";case"23":return"坠床";case"24":return"离床";case"31":return"主动呼叫";case"32":return"户外呼叫"}},closeWS:function(){this.stopHeartbeat(),this.clearReconnectTimer(),this.socket&&(this.socket.onopen=null,this.socket.onmessage=null,this.socket.onerror=null,this.socket.onclose=null,this.socket.readyState===WebSocket.OPEN&&this.socket.close(1e3,"正常关闭"),this.socket=null),this.wsConnected=!1},startHeartbeat:function(){var t=this;this.stopHeartbeat(),this.wsHeartbeatTimer=setInterval((function(){if(t.socket&&t.socket.readyState===WebSocket.OPEN)try{t.socket.send(JSON.stringify({type:"heartbeat",timestamp:Date.now()})),console.log("发送心跳消息")}catch(e){console.error("发送心跳消息失败:",e),t.scheduleReconnect()}else console.warn("WebSocket连接不可用，停止心跳"),t.stopHeartbeat(),t.scheduleReconnect()}),this.wsHeartbeatInterval)},stopHeartbeat:function(){this.wsHeartbeatTimer&&(clearInterval(this.wsHeartbeatTimer),this.wsHeartbeatTimer=null)},scheduleReconnect:function(){var t=this;if(this.wsReconnectAttempts>=this.wsMaxReconnectAttempts)return console.error("WebSocket重连次数已达上限，停止重连"),void this.$message.error("WebSocket连接失败，请刷新页面重试");this.clearReconnectTimer();var e=Math.min(this.wsReconnectInterval*Math.pow(2,this.wsReconnectAttempts),3e4);console.log("WebSocket将在".concat(e,"ms后进行第").concat(this.wsReconnectAttempts+1,"次重连")),this.wsReconnectTimer=setTimeout((function(){t.wsReconnectAttempts++,t.initWS()}),e)},clearReconnectTimer:function(){this.wsReconnectTimer&&(clearTimeout(this.wsReconnectTimer),this.wsReconnectTimer=null)}}},b=v,g=(s("b5c5"),s("2877")),m=Object(g["a"])(b,n,i,!1,null,"47ca0bc3",null);e["a"]=m.exports},4584:function(t,e,s){},"560a":function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"card-widget flex-col"},[s("div",{staticClass:"card-top flex-row align-center justify-between"},[s("div",{staticClass:"flex-row align-center"},[s("span",{staticClass:"iconfont icon-bofang1 title-icon"}),s("div",{staticClass:"title"},[t._v(t._s(t.title))])]),t.rightText?s("span",{staticClass:"right-text"},[t._v(t._s(t.rightText))]):t._e(),t.rightBtn?s("span",{staticClass:"right-btn",on:{click:function(e){return t.$emit("rightClick")}}},[t._v(t._s(t.rightBtn))]):t._e()]),s("div",{staticClass:"card-content flex-col"},[t._t("default")],2),s("div",{staticClass:"card-bottom"})])},i=[],a={name:"Card",props:{title:{type:String,required:!0},rightText:{type:String,required:!1},rightBtn:{type:String,required:!1}},data:function(){return{}},mounted:function(){},methods:{}},c=a,r=(s("a2fc"),s("2877")),o=Object(r["a"])(c,n,i,!1,null,"82dc13ac",null);e["a"]=o.exports},"5d09":function(t,e,s){"use strict";s.d(e,"a",(function(){return n})),s.d(e,"d",(function(){return i})),s.d(e,"b",(function(){return a})),s.d(e,"c",(function(){return c}));var n={left:"2%",right:"2%",bottom:"2%",top:"10%",containLabel:!0},i={trigger:"item",backgroundColor:"rgba(50, 50, 50, 0.8)",borderWidth:0,textStyle:{color:"#fff"}},a={bottom:"5%",itemWidth:10,itemHeight:10,textStyle:{color:"#fff",fontSize:12}},c=function(t){return{text:t,left:"10%",top:"0%",textStyle:{color:"#fff",fontSize:13}}}},"6b34":function(t,e,s){"use strict";s("4584")},7547:function(t,e,s){},"8a6f":function(t,e,s){},a2fc:function(t,e,s){"use strict";s("e2e4")},b41a:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"service-order-content flex-col"},[s("el-row",{staticClass:"flex-row items-center"},[s("el-col",{attrs:{span:12}},[t.data?s("div",{ref:"chart",staticStyle:{width:"100%",height:"180px"}}):t._e()]),s("el-col",{attrs:{span:12}},[t.data2?s("div",{ref:"chart2",staticStyle:{width:"100%",height:"180px"}}):t._e()])],1)],1)},i=[],a=s("ade3"),c=(s("a9e3"),s("313e")),r=s("5d09"),o={name:"ServiceOrder",props:{chamberNum:{type:Number,default:0},olderNum:{type:Number,default:0},data:{type:Object,required:!0},data2:{type:Object,required:!0}},data:function(){return{chart:null,chart2:null}},watch:{chamberNum:function(t,e){this.chamberNum=t,this.initChart()},olderNum:function(t,e){this.olderNum=t,this.initChart2()},data:function(t,e){this.data=t,this.initChart()},data2:function(t,e){this.data2=t,this.initChart2()}},mounted:function(){var t=this;this.initChart(),this.initChart2(),window.addEventListener("resize",(function(){t.chart&&t.chart.resize(),t.chart2&&t.chart2.resize()}))},methods:{initChart:function(){var t,e,s,n,i,o,l=(o={darkMode:!0,grid:r["a"],title:r["c"]("会员总人数".concat(this.olderNum))},Object(a["a"])(o,"grid",{left:"2%",right:"2%",bottom:"42%",top:"10%",containLabel:!0}),Object(a["a"])(o,"tooltip",r["d"]),Object(a["a"])(o,"legend",{bottom:"5%",itemWidth:10,itemHeight:10,textStyle:{color:"#fff",fontSize:12}}),Object(a["a"])(o,"series",[{type:"pie",radius:["40%","60%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:2},label:{show:!1,position:"center"},labelLine:{show:!1},data:[{value:((null===(t=this.data)||void 0===t?void 0:t.status0)||0)+((null===(e=this.data)||void 0===e?void 0:e.status1)||0)+((null===(s=this.data)||void 0===s?void 0:s.status2)||0)+((null===(n=this.data)||void 0===n?void 0:n.status3)||0),name:"服务中"},{value:(null===(i=this.data)||void 0===i?void 0:i.status4)||0,name:"已完成"}]}]),o);this.chart2=c["init"](this.$refs.chart),this.chart2.setOption(l)},initChart2:function(){var t,e,s,n,i=(n={grid:r["a"],title:r["c"]("管家总人数".concat(this.chamberNum))},Object(a["a"])(n,"grid",{left:"2%",right:"2%",bottom:"42%",top:"10%",containLabel:!0}),Object(a["a"])(n,"tooltip",r["d"]),Object(a["a"])(n,"legend",{bottom:"5%",itemWidth:10,itemHeight:10,textStyle:{color:"#fff",fontSize:12}}),Object(a["a"])(n,"series",[{type:"pie",radius:["40%","60%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:2},label:{show:!1,position:"center"},labelLine:{show:!1},data:[{value:(null===(t=this.data2)||void 0===t?void 0:t.statusNum0)||0,name:"未处理"},{value:(null===(e=this.data2)||void 0===e?void 0:e.statusNum1)||0,name:"处理中"},{value:(null===(s=this.data2)||void 0===s?void 0:s.statusNum2)||0,name:"已处理"}]}]),n);this.chart1=c["init"](this.$refs.chart2),this.chart1.setOption(i)}}},l=o,u=(s("b83f"),s("2877")),h=Object(u["a"])(l,n,i,!1,null,"571864dc",null);e["a"]=h.exports},b5c5:function(t,e,s){"use strict";s("2d2c")},b83f:function(t,e,s){"use strict";s("ff07")},c7d3:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"bednum-content flex-col"},[s("el-row",{staticClass:"flex-row items-center"},[s("el-col",{attrs:{span:3}},[s("div",{staticClass:"bednum-icon-block"},[s("span",{staticClass:"iconfont icon-chuang bed-icon"})])]),s("el-col",{staticClass:"bed-cnt flex-row items-center justify-around",attrs:{span:21}},[s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"bed-cnt-label"},[t._v("床位总数")]),s("span",{staticClass:"bed-cnt-value"},[t._v(t._s(t.data.bedCount||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"bed-cnt-label"},[t._v("已使用")]),s("span",{staticClass:"bed-cnt-value"},[t._v(t._s(t.data.usedCount||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"bed-cnt-label"},[t._v("空闲")]),s("span",{staticClass:"bed-cnt-value"},[t._v(t._s(t.data.freeCount||0))])]),s("div",{staticClass:"flex-col justify-center"},[s("span",{staticClass:"bed-cnt-label"},[t._v("使用率")]),s("span",{staticClass:"bed-cnt-value"},[t._v(t._s(t.data.usedRate||"-"))])])])],1),t.headers&&t.headers.length>0?s("div",{staticClass:"bed-list flex-col"},[s("ScrollingTable",{attrs:{data:t.list,headers:t.headers,rowNum:4}})],1):t._e()],1)},i=[],a=(s("d193"),{name:"BedNum",props:{data:{type:Object,required:!0},list:{type:Array,required:!1},headers:{type:Array,required:!1}},data:function(){return{}},mounted:function(){},methods:{}}),c=a,r=(s("6b34"),s("2877")),o=Object(r["a"])(c,n,i,!1,null,"e78afb84",null);e["a"]=o.exports},c9b7:function(t,e,s){"use strict";function n(t,e){var s,n=e||200;return function(){var e=this,i=arguments;s&&clearTimeout(s),s=setTimeout((function(){s=null,t.apply(e,i)}),n)}}s.d(e,"a",(function(){return n}))},d821:function(t,e,s){"use strict";var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"device-monitor-content flex-col"},[s("el-row",{staticClass:"flex-row items-center"},[s("el-col",{attrs:{span:24}},[s("div",{ref:"chart",staticStyle:{width:"100%",height:"180px"}})])],1)],1)},i=[],a=(s("d81d"),s("313e")),c=s("5d09"),r={name:"DeviceMonitor",props:{data:{type:Array,required:!0}},data:function(){return{chart:null}},watch:{data:function(t,e){this.data=t,this.initChart()}},mounted:function(){var t=this;this.initChart(),window.addEventListener("resize",(function(){t.chart&&t.chart.resize()}))},methods:{initChart:function(){var t={darkMode:!0,grid:c["a"],tooltip:c["d"],legend:{itemWidth:10,itemHeight:10,textStyle:{color:"#fff",fontSize:12}},xAxis:{type:"category",axisTick:{show:!1}},yAxis:{splitLine:{show:!1},type:"value"},dataset:{dimensions:["product","总数","已激活","在线","离线"],source:this.formatData(this.data)},series:[{type:"bar"},{type:"bar"},{type:"bar"},{type:"bar"}]};this.chart=a["init"](this.$refs.chart),this.chart.setOption(t)},formatData:function(t){var e=this,s=t.map((function(t){return{product:e.getType(t.deviceType),"总数":t.total,"已激活":t.active,"在线":t.online,"离线":t.offline}}));return s},getType:function(t){switch(t){case"1":return"与安宝Mini";case"2":return"与安宝Pro";case"3":return"随身宝";case"4":return"急救呼";case"5":return"PRI"}}}},o=r,l=(s("04ff"),s("2877")),u=Object(l["a"])(o,n,i,!1,null,"8cb4e28e",null);e["a"]=u.exports},e2e4:function(t,e,s){},ff07:function(t,e,s){}}]);