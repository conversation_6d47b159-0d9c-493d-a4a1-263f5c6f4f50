(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7f68a383"],{"64da":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务信息"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"110px","label-position":"left"},nativeOn:{submit:function(e){e.preventDefault()}}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务名称:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.serverName,callback:function(t){e.$set(e.search,"serverName",t)},expression:"search.serverName"}})],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"serviceAdd"})}}},[e._v("新增")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"serverName",label:"服务名称",width:"180",align:"center"}}),a("el-table-column",{attrs:{prop:"requiresActive",label:"是否需要激活",width:"70","show-overflow-tooltip":"",align:"center",formatter:e.formatBoolean}}),a("el-table-column",{attrs:{prop:"validStatus",label:"启用状态",width:"60","show-overflow-tooltip":"",align:"center",formatter:e.formatStatus}}),a("el-table-column",{attrs:{prop:"createUserName",label:"创建人",width:"150",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"170",align:"center",sortable:""}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return["0"===n.validStatus?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"danger",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(n.id,1)}}},[e._v("禁用")]):e._e(),"1"===n.validStatus?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(n.id,0)}}},[e._v("启用")]):e._e(),a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.system",expression:"'self.system'"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"serviceEdit",params:{id:n.id}})}}},[e._v("维护")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"serviceInfo",params:{id:n.id}})}}},[e._v("详情查看")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"servicePriceIndex",params:{id:n.id}})}}},[e._v("配置价格")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},r=[],i=a("1da1"),s=a("5530"),l=(a("96cf"),a("ac1f"),a("841c"),{data:function(){return{dict:{stations:[],numBoolean:{0:"是",1:"否"},numStatus:{0:"启用",1:"禁用"}},search:{serverName:void 0},table:{datas:[],pageInfo:{current:1,total:0}},switchProps:{activeValue:"1",inactiveValue:"0",activeText:"是",inactiveText:"否"}}},mounted:function(){this.fetchDatas()},methods:{handleRadioChange:function(e){this.search.status=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/server/listServer",{params:Object(s["a"])(Object(s["a"])({},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleChangeStatus:function(e,t){var a=this;e&&void 0!=t&&null!=t&&this.$api.post("/bms/server/invalidServer",{id:e,validStatus:t}).then((function(e){"00000"===e.status?(a.$message({type:"success",message:"更新成功!"}),a.fetchDatas()):a.$message({type:"error",message:e.message||"操作失败!"})}))},resetForm:function(){Object.assign(this.$data.search,this.$options.data().search),this.fetchDatas()},formatBoolean:function(e,t,a,n){var r=this.dict.numBoolean;return r[a]},formatStatus:function(e,t,a,n){var r=this.dict.numStatus;return r[a]},toggleOnlineStatus:function(e){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function a(){var n,r;return regeneratorRuntime.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(null!==e&&void 0!==e&&e.id){a.next=2;break}return a.abrupt("return");case 2:return n=e.id,r=e.onlineStatus,a.next=5,t.$api.post("/bms/server/editOnlineStatus",{id:n,onlineStatus:r});case 5:case"end":return a.stop()}}),a)})))()}}}),o=l,c=(a("e6e1"),a("2877")),u=Object(c["a"])(o,n,r,!1,null,"44e7ce3c",null);t["default"]=u.exports},d523:function(e,t,a){},e6e1:function(e,t,a){"use strict";a("d523")}}]);