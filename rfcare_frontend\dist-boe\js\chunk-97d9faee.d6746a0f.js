(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-97d9faee"],{"12e9":function(e,t,i){"use strict";i.r(t);var s=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"房号"}}),i("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[i("page-main",{attrs:{title:"基本信息"}},[i("el-row",{staticClass:"block-row",attrs:{gutter:20}},[i("el-col",{attrs:{md:8}},[i("el-form-item",{attrs:{label:"房号",prop:"name"}},[i("el-input",{attrs:{maxlength:10,disabled:!!e.$route.params.id,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.name,callback:function(t){e.$set(e.reqForm,"name",t)},expression:"reqForm.name"}})],1)],1),i("el-col",{attrs:{md:8}},[i("el-form-item",{attrs:{label:"所属服务站",prop:"stationId"}},[i("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择所属服务站",disabled:!!e.$route.params.id},on:{change:e.stationChange},model:{value:e.reqForm.stationId,callback:function(t){e.$set(e.reqForm,"stationId",t)},expression:"reqForm.stationId"}},e._l(e.dict.stations,(function(e){return i("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})})),1)],1)],1),i("el-col",{attrs:{md:8}},[i("el-form-item",{attrs:{label:"户型",prop:"tplHouseId"}},[i("el-select",{attrs:{clearable:"",placeholder:"请选择",disabled:!!e.$route.params.id},on:{change:e.tplHouseChange},model:{value:e.reqForm.tplHouseId,callback:function(t){e.$set(e.reqForm,"tplHouseId",t)},expression:"reqForm.tplHouseId"}},e._l(e.dict.tplHouses,(function(e){return i("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),i("el-col",{attrs:{md:24}},[i("el-form-item",{attrs:{label:"备注",prop:"remark"}},[i("el-input",{attrs:{type:"textarea",rows:3,maxlength:50,"show-word-limit":"","show-word-limitv-model":"reqForm.remark",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.remark,callback:function(t){e.$set(e.reqForm,"remark",t)},expression:"reqForm.remark"}})],1)],1)],1)],1),i("page-main",{attrs:{title:"设备位置与设备对应信息"}},[i("el-table",{attrs:{data:e.deviceList}},[i("el-table-column",{attrs:{prop:"roomName",label:"房间名称"}}),i("el-table-column",{attrs:{prop:"roomTypeName",label:"房间类型"}}),i("el-table-column",{attrs:{prop:"deviceName",label:"设备位置"}}),i("el-table-column",{attrs:{prop:"devCode",label:"设备(必填)"}}),i("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[i("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(i){return e.configDevice(t.row.tplHouseRoomDeviceId)}}},[e._v("配置")])]}}])})],1)],1),i("page-main",{attrs:{title:"床位与老人对应信息"}},[i("el-table",{attrs:{data:e.bedList}},[i("el-table-column",{attrs:{prop:"roomName",label:"入住房间"}}),i("el-table-column",{attrs:{prop:"bedName",label:"床名称"}}),i("el-table-column",{attrs:{prop:"bedNum",label:"床位(必填)"}}),i("el-table-column",{attrs:{prop:"olderName",label:"老人姓名"}}),i("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[e.$route.params.id&&t.row.olderName?e._e():i("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(i){return e.configBed(t.row.tplHouseRoomRegionId,t.row.bedNum)}}},[e._v("配置")])]}}])})],1)],1),i("div",{staticClass:"item-card"},[i("el-row",{staticClass:"block-row",attrs:{gutter:20}},[i("el-col",{attrs:{md:24}},[i("el-form-item",{staticStyle:{"margin-top":"20px"}},[i("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1),i("el-dialog",{attrs:{title:"设备安装配置",visible:e.devDialogVisible,width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.devDialogVisible=t}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择设备"},on:{change:e.devChange},model:{value:e.selectDevId,callback:function(t){e.selectDevId=t},expression:"selectDevId"}},e._l(e.availableDeviceList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.devCode,value:e.id}})})),1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.devDialogVisible=!1}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.saveDevice}},[e._v("确定")])],1)],1),i("el-dialog",{attrs:{title:"床位入住配置",visible:e.olderDialogVisible,width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.olderDialogVisible=t}}},[i("el-input",{attrs:{maxlength:6,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入床位编号，如：16"},model:{value:e.inputBedNum,callback:function(t){e.inputBedNum=t},expression:"inputBedNum"}}),i("el-select",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{filterable:"",clearable:"",placeholder:"请选择老人"},on:{change:e.olderChange},model:{value:e.selectOlderId,callback:function(t){e.selectOlderId=t},expression:"selectOlderId"}},e._l(e.availableOlderList,(function(e){return i("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1),i("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{on:{click:function(t){e.olderDialogVisible=!1}}},[e._v("取消")]),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.saveBed}},[e._v("确定")])],1)],1)],1)},o=[],l=i("5530"),a=i("1da1"),r=(i("d81d"),i("4de4"),i("b0c0"),i("96cf"),{data:function(){return{dict:{stations:[],tplHouses:[]},deviceList:[],availableDeviceList:[],currTplHouseRoomDeviceId:void 0,selectDevId:void 0,selectDevCode:void 0,devDialogVisible:!1,bedList:[],availableOlderList:[],currBedId:void 0,inputBedNum:void 0,selectOlderId:void 0,selectOlderName:void 0,olderDialogVisible:!1,reqForm:{houseId:void 0,name:void 0,stationId:void 0,tplHouseId:void 0},submitLoading:!1,fullLoading:void 0,reqFormRules:{stationId:[{required:!0,trigger:"change",message:"请选择"}],tplHouseId:[{required:!0,trigger:"change",message:"请选择"}],name:[{required:!0,trigger:"blur",message:"请输入"}]}}},mounted:function(){var e=this;return Object(a["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.fetchStations();case 2:e.fetchDetail(e.$route.params.id);case 3:case"end":return t.stop()}}),t)})))()},methods:{getValuesMethod:function(){return this.reqForm},stationChange:function(){console.log("stationChange"),this.reqForm.tplHouseId=void 0,this.dict.tplHouses=[],this.deviceList=[],this.bedList=[],this.fetchTplHouses(),this.availableOlderList=[],this.availableDeviceList=[]},tplHouseChange:function(){console.log("this.reqForm.tplHouseId:",this.reqForm.tplHouseId),this.deviceList=[],this.bedList=[],this.fetchTplHouseRoomData()},configDevice:function(e){this.fetchDevices(),this.devDialogVisible=!0,this.selectDevId=void 0,this.selectDevCode=void 0,this.currTplHouseRoomDeviceId=e},saveDevice:function(){var e=this;this.selectDevId?(this.devDialogVisible=!1,console.log("deviceMappingList:",this.deviceMappingList),this.deviceList=this.deviceList.map((function(t){return t.tplHouseRoomDeviceId==e.currTplHouseRoomDeviceId&&(t.devCode=e.selectDevCode,t.devId=e.selectDevId),t})),this.selectDevCode=void 0,this.currTplHouseRoomDeviceId=void 0,console.log("deviceList:",this.deviceList)):this.$message({type:"error",message:"请选择设备"})},devChange:function(){var e=this;console.log("devId:",this.selectDevId),this.selectDevCode=this.availableDeviceList.filter((function(t){return t.id===e.selectDevId}))[0].devCode,console.log("devCode:",this.selectDevCode)},configBed:function(e,t){this.fetchOlders(),this.olderDialogVisible=!0,this.inputBedNum=void 0,this.selectOlderId=void 0,this.selectOlderName=void 0,this.currBedId=e,t&&(this.inputBedNum=t)},saveBed:function(){var e=this;this.inputBedNum?(this.olderDialogVisible=!1,console.log("bedList:",this.bedList),this.bedList=this.bedList.map((function(t){return t.tplHouseRoomRegionId==e.currBedId&&(t.olderId=e.selectOlderId,t.olderName=e.selectOlderName,t.bedNum=e.inputBedNum),t})),this.selectOlderName=void 0,this.inputBedNum=void 0,this.selectOlderId=void 0,this.currBedId=void 0,console.log("bedList:",this.bedList)):this.$message({type:"error",message:"请输入床位编号"})},olderChange:function(){var e=this;console.log("olderId:",this.selectOlderId),this.selectOlderName=this.availableOlderList.filter((function(t){return t.id===e.selectOlderId}))[0].name,console.log("olderName:",this.selectOlderName)},fetchDetail:function(e){var t=this;e&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("bms/station/house/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data,t.deviceList=e.data.deviceList,t.bedList=e.data.olderList,t.fetchTplHouses()),t.fullLoading.close()})).catch((function(e){console.log(e),t.fullLoading.close()})))},fetchStations:function(){var e=this;return Object(a["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}));case 1:case"end":return t.stop()}}),t)})))()},fetchDevices:function(){var e=this;this.reqForm.stationId&&(this.availableOlderList.length>0||this.$api.get("/bms/Device/getUnassociatedDeviceList?stationId="+this.reqForm.stationId,{}).then((function(t){"00000"===t.status&&t.data?e.availableDeviceList=t.data:e.availableDeviceList=[]})))},fetchOlders:function(){var e=this;this.reqForm.stationId&&(this.availableOlderList.length>0||this.$api.get("/bms/older/getNoBedOlders?stationId="+this.reqForm.stationId,{}).then((function(t){"00000"===t.status&&t.data?e.availableOlderList=t.data:e.availableOlderList=[]})))},fetchTplHouses:function(){var e=this;this.reqForm.stationId&&this.$api.get("/bms/tplhouse/selectByList?stationId="+this.reqForm.stationId,{}).then((function(t){if("00000"===t.status&&t.data){var i=t.data.map((function(e){return{code:e.tplHouseId,name:e.houseName}}));e.dict.tplHouses=i}else e.dict.tplHouses=[]}))},fetchTplHouseRoomData:function(){var e=this;if(this.reqForm.tplHouseId){var t="tplHouseId="+this.reqForm.tplHouseId;this.reqForm.houseId&&(t=t+"&houseId="+this.reqForm.houseId),this.$api.get("/bms/tplhouse/getDeviceListByHouseId?"+t,{}).then((function(t){"00000"===t.status&&t.data?e.deviceList=t.data:e.deviceList=[]})),this.$api.get("/bms/tplhouse/getBedListByHouseId?"+t,{}).then((function(t){"00000"===t.status&&t.data?e.bedList=t.data:e.bedList=[]}))}},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var i=JSON.parse(JSON.stringify(t.reqForm));i=Object(l["a"])(Object(l["a"])({},i),{},{bedList:t.bedList,deviceList:t.deviceList}),console.log("save:",i),t.$api.post("/bms/station/house/save",i).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},handleClose:function(e){this.$confirm("确认关闭？").then((function(t){e()})).catch((function(e){}))}}}),n=r,d=(i("137b"),i("2877")),c=Object(d["a"])(n,s,o,!1,null,"63236912",null);t["default"]=c.exports},"137b":function(e,t,i){"use strict";i("ad76")},ad76:function(e,t,i){}}]);