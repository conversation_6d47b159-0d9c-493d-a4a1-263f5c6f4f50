(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9e621516"],{"05b3":function(t,e,a){},1392:function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex-col"},[a("Bg",{attrs:{title:""+(t.$store.state.user.member.systemName||"与安服务管理系统"),path:t.path}},[a("div",{staticClass:"content"},[a("table",{staticClass:"table",staticStyle:{width:"100%",height:"100%","table-layout":"fixed"}},[a("tr",{staticStyle:{width:"25%",height:"25%"}},[a("td",[a("Card",{attrs:{title:"床位"}},[a("BedNum",{attrs:{data:t.bedInfo,list:t.bedList,headers:t.headers}})],1)],1),a("td",{attrs:{colspan:"2",rowspan:"3"}},[a("Card",{attrs:{title:"服务站"}},[a("div",{staticClass:"mask-stat"},[a("el-row",{staticClass:"mask-stat-wrap",staticStyle:{padding:"10px 17px",border:"1px solid #32486d",margin:"15px 18px"}},[a("el-col",{attrs:{span:8}},[a("div",{staticClass:"mask-stat-column-text"},[a("span",{staticClass:"mask-stat-column-num"},[t._v(t._s(t.stationCount1+t.stationCount2))])]),a("div",{staticClass:"mask-stat-column-text"},[t._v("服务站总数")])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"mask-stat-column-text"},[a("span",{staticClass:"mask-stat-column-num"},[t._v(t._s(t.stationCount1))])]),a("div",{staticClass:"mask-stat-column-text"},[t._v("集中式")])]),a("el-col",{attrs:{span:8}},[a("div",{staticClass:"mask-stat-column-text"},[a("span",{staticClass:"mask-stat-column-num"},[t._v(t._s(t.stationCount2))])]),a("div",{staticClass:"mask-stat-column-text"},[t._v("居家式")])])],1)],1),a("baidu-map",{staticStyle:{height:"100%"},attrs:{center:t.center,zoom:t.zoom,"map-click":!1,"scroll-wheel-zoom":!0},on:{ready:t.handler,click:t.handleMapClick,moveend:t.x1,zoomend:t.x2,dragend:t.x3}},[t._l(t.dots,(function(e,r){return a("bm-marker",{key:e+r,attrs:{"z-index":5,position:{lng:e.longitude,lat:e.latitude},icon:t.dotHoverId===e.stationId?{url:"/images/home/<USER>",size:{width:32,height:49}}:{url:"/images/home/<USER>",size:{width:27,height:41}}},on:{mouseover:function(a){return t.handleDotMouseOver(e.stationId)},mouseout:t.handleDotMouseOut,click:function(a){return t.handleDotClick(a,e.stationId)}}},[a("StationBMapDot",{directives:[{name:"show",rawName:"v-show",value:t.dotHoverId===e.stationId,expression:"dotHoverId === dot.stationId"}],attrs:{position:{lng:e.longitude,lat:e.latitude},content:e,onClick:t.handleStationDotClick}})],1)})),t.dotSelectedlngLat&&t.dotSelectedlngLat.lat?a("bm-marker",{attrs:{"z-index":4,position:{lng:t.dotSelectedlngLat.lng,lat:t.dotSelectedlngLat.lat},icon:{url:"/images/home/<USER>",size:{width:92,height:92}},offset:{width:0,height:22}}}):t._e()],2)],1)],1),a("td",[a("Card",{attrs:{title:"人员分布"}},[a("OlderAnalysis",{attrs:{data:t.olderData,list:t.olderDataList,headers:t.olderHeaders}})],1)],1)]),a("tr",{staticStyle:{width:"25%",height:"25%"}},[a("td",[a("Card",{attrs:{title:"睡眠",rightText:(new Date).toISOString().split("T")[0]}},[a("Sleep",{attrs:{data:t.sleepScore,data2:t.sleepDuration}})],1)],1),a("td",{attrs:{rowspan:"2"}},[a("Card",{attrs:{title:"安全服务监测"}},[a("SecurityService",{attrs:{todayOrder:t.todayOrder,allOrder:t.allOrder,data:t.stationTodayOrder}})],1)],1)]),a("tr",{staticStyle:{width:"25%",height:"25%"}},[a("td",[a("Card",{attrs:{title:"行为统计",rightText:(new Date).toISOString().split("T")[0]}},[a("Behavior",{attrs:{data:t.sceneDeviceCount,data2:t.toiletDeviceCount}})],1)],1)]),a("tr",{staticStyle:{width:"25%",height:"25%"}},[a("td",[a("Card",{attrs:{title:"设备监测"}},[a("DeviceMonitor",{attrs:{data:t.deviceMonitor}})],1)],1),a("td",{attrs:{colspan:"2"}},[a("Card",{attrs:{title:"服务信息"}},[a("ScrollingTable",{attrs:{data:t.guardOlderList,headers:t.guardOlderHeaders,rowNum:7}})],1)],1),a("td",[a("Card",{attrs:{title:"服务工单"}},[a("ServiceOrder",{attrs:{chamberNum:t.chamberNum,olderNum:t.olderNum,data:t.guardOrderCount,data2:t.orderCount}})],1)],1)])])])])],1)},n=[],i=a("1da1"),s=(a("96cf"),a("99af"),a("d81d"),a("7db0"),a("4de4"),a("13d5"),a("c9b7")),o=(a("5a0c"),a("d193")),l=a("4277"),c=a("560a"),d=a("c7d3"),u=a("3b0e"),h=a("b41a"),g=a("d821"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"sleep-content flex-col"},[a("el-row",{staticClass:"flex-row items-center"},[a("el-col",{attrs:{span:12}},[a("div",{ref:"olderScoreChart",staticStyle:{width:"100%",height:"180px"}})]),a("el-col",{attrs:{span:12}},[a("div",{ref:"sleepTimeChart",staticStyle:{width:"100%",height:"180px"}})])],1)],1)},m=[],f=a("313e"),v=a("5d09"),y={name:"Sleep",props:{data:{type:Array,required:!0},data2:{type:Array,required:!0}},data:function(){return{chart:null,chart2:null}},watch:{data:function(t,e){this.data=t,this.initChart()},data2:function(t,e){this.data2=t,this.initChart2()}},mounted:function(){var t=this;this.initChart(),this.initChart2(),window.addEventListener("resize",(function(){t.chart&&t.chart.resize(),t.chart2&&t.chart2.resize()}))},methods:{initChart:function(){var t={darkMode:!0,title:v["c"]("分数"),grid:v["a"],tooltip:v["d"],xAxis:{type:"category",data:["<60","60-80","80-90",">90"],axisTick:{show:!1}},yAxis:{splitLine:{show:!1},type:"value"},series:[{data:this.data,type:"bar",itemStyle:{color:new f["graphic"].LinearGradient(0,0,0,1,[{offset:1,color:"#000"},{offset:0,color:"#088BFE"}])}}]};this.chart=f["init"](this.$refs.olderScoreChart),this.chart.setOption(t)},initChart2:function(){var t={darkMode:!0,title:v["c"]("时长"),grid:{left:"2%",right:"2%",bottom:"42%",top:"10%",containLabel:!0},tooltip:v["d"],legend:v["b"],series:[{name:"时长",type:"pie",radius:["40%","60%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:2},label:{show:!1,position:"center"},labelLine:{show:!1},data:[{value:this.data2[0],name:"<6小时"},{value:this.data2[1],name:"6-10小时"},{value:this.data2[2],name:">10小时"}]}]};this.chart=f["init"](this.$refs.sleepTimeChart),this.chart.setOption(t)}}},b=y,C=(a("c986"),a("2877")),w=Object(C["a"])(b,p,m,!1,null,"0cbb79af",null),x=w.exports,O=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"behavior-content flex-col"},[a("el-row",{staticClass:"flex-row items-center"},[a("el-col",{attrs:{span:12}},[a("div",{ref:"chart",staticStyle:{width:"100%",height:"180px"}})]),a("el-col",{attrs:{span:12}},[a("div",{ref:"chart2",staticStyle:{width:"100%",height:"180px"}})])],1)],1)},S=[],T={name:"Behavior",props:{data:{type:Array,required:!0},data2:{type:Array,required:!0}},data:function(){return{chart:null,chart2:null}},watch:{data:function(t,e){this.data=t,this.initChart()},data2:function(t,e){this.data2=t,this.initChart2()}},mounted:function(){var t=this;this.initChart(),this.initChart2(),window.addEventListener("resize",(function(){t.chart&&t.chart.resize(),t.chart2&&t.chart2.resize()}))},methods:{initChart:function(){var t={darkMode:!0,title:v["c"]("无人"),grid:{left:"2%",right:"2%",bottom:"42%",top:"10%",containLabel:!0},tooltip:v["d"],legend:{bottom:"5%",itemWidth:10,itemHeight:10,textStyle:{color:"#fff",fontSize:12}},series:[{type:"pie",radius:["40%","60%"],startAngle:180,endAngle:360,avoidLabelOverlap:!1,itemStyle:{borderRadius:2},label:{show:!1,position:"center"},labelLine:{show:!1},data:[{value:this.data[0],name:"客厅"},{value:this.data[1],name:"卫生间"},{value:this.data[2],name:"卧室"},{value:this.data[3],name:"其他"}]}]};this.chart=f["init"](this.$refs.chart),this.chart.setOption(t)},initChart2:function(){var t={darkMode:!0,title:v["c"]("卫生间"),grid:v["a"],tooltip:v["d"],xAxis:{type:"category",data:["白天\n进出频繁","夜间\n进出频繁","停留超过\n30分钟"],axisTick:{show:!1}},yAxis:{splitLine:{show:!1},type:"value"},series:[{data:this.data2,type:"bar",itemStyle:{color:new f["graphic"].LinearGradient(0,0,0,1,[{offset:1,color:"#000"},{offset:0,color:"#088BFE"}])}}]};this.chart2=f["init"](this.$refs.chart2),this.chart2.setOption(t)}}},k=T,I=(a("20d8"),Object(C["a"])(k,O,S,!1,null,"8d64e6b6",null)),D=I.exports,_=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"security-service-content flex-col"},[a("div",{staticClass:"security-service-top flex-row justify-between items-center"},[a("div",{staticClass:"alarm-block flex-row items-center"},[t._m(0),a("div",{staticClass:"alarm-content flex-col justify-around"},[a("div",[a("span",{staticClass:"alarm-title"},[t._v("今日告警")]),a("span",{staticClass:"alarm-title-value"},[t._v(t._s(t.todayOrder.total||0))])]),a("div",{staticClass:"flex-row items-center"},[a("div",{staticClass:"alarm-count"},[a("span",{staticClass:"alarm-label"},[t._v("跌倒")]),a("span",{staticClass:"alarm-value"},[t._v(t._s(t.todayOrder.fall||0))])]),a("div",{staticClass:"alarm-count"},[a("span",{staticClass:"alarm-label"},[t._v("久滞")]),a("span",{staticClass:"alarm-value"},[t._v(t._s(t.todayOrder.longlag||0))])])]),a("div",{staticClass:"flex-row items-center"},[a("div",{staticClass:"alarm-count"},[a("span",{staticClass:"alarm-label"},[t._v("离床")]),a("span",{staticClass:"alarm-value"},[t._v(t._s(t.todayOrder.outbed||0))])]),a("div",{staticClass:"alarm-count"},[a("span",{staticClass:"alarm-label"},[t._v("呼叫")]),a("span",{staticClass:"alarm-value"},[t._v(t._s(t.todayOrder.call||0))])])])])]),a("div",{staticClass:"alarm-block flex-row items-center"},[t._m(1),a("div",{staticClass:"alarm-content flex-col justify-around"},[a("div",[a("span",{staticClass:"alarm-title"},[t._v("告警总数")]),a("span",{staticClass:"alarm-title-value"},[t._v(t._s(t.allOrder.total||0))])]),a("div",{staticClass:"flex-row items-center"},[a("span",{staticClass:"alarm-label"},[t._v("未处理")]),a("span",{staticClass:"alarm-value"},[t._v(t._s(t.allOrder.noHandle||0))])]),a("div",{staticClass:"flex-row items-center"},[a("span",{staticClass:"alarm-label"},[t._v("已处理")]),a("span",{staticClass:"alarm-value"},[t._v(t._s(t.allOrder.handle||0))])])])])]),a("el-row",{staticClass:"security-service-chart flex-row items-center"},[a("el-col",{attrs:{span:24}},[a("div",{ref:"chart",staticStyle:{width:"100%",height:"300px"}})])],1)],1)},L=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"alarm-icon-block"},[a("span",{staticClass:"iconfont icon-huodonggaojing alarm-icon"})])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"alarm-icon-block"},[a("span",{staticClass:"iconfont icon-jinggaodanchuang alarm-icon"})])}],$=(a("159b"),a("caad"),a("2532"),a("cb29"),{name:"SecurityService",props:{todayOrder:{type:Object,required:!1},allOrder:{type:Object,required:!1},data:{type:Array,required:!1}},watch:{data:function(t,e){this.data=t,this.initChart()}},data:function(){return{chart:null,stationIds:[]}},mounted:function(){var t=this;this.initChart(),window.addEventListener("resize",(function(){t.chart&&t.chart.resize()}))},methods:{initChart:function(){var t={darkMode:!0,title:v["c"]("当日事件告警排名"),grid:v["a"],tooltip:v["d"],legend:{right:"2%",itemWidth:10,itemHeight:10,textStyle:{color:"#fff",fontSize:12}},xAxis:{type:"value",axisTick:{show:!1},splitLine:{show:!1}},yAxis:{splitLine:{show:!1},type:"category",data:this.getStationNames(this.data)},series:[{name:"跌倒",type:"bar",data:this.getCntArrayForOrderType(this.data,"21")},{name:"久滞",type:"bar",data:this.getCntArrayForOrderType(this.data,"22")},{name:"离床",type:"bar",data:this.getCntArrayForOrderType(this.data,"24")},{name:"呼叫",type:"bar",data:this.getCntArrayForOrderType(this.data,"31")}]};this.chart=f["init"](this.$refs.chart),this.chart.setOption(t)},getStationNames:function(t){var e=this,a=[];return t.forEach((function(t){e.stationIds.includes(t.stationId)||(a.push(t.stationName),e.stationIds.push(t.stationId))})),console.log("stationnNames:",a),a},getCntArrayForOrderType:function(t,e){var a=this,r=new Array(this.stationIds.length).fill(0);return t.forEach((function(t){if(t.orderType===e){var n=a.stationIds.indexOf(t.stationId);-1!==n&&(r[n]=t.cnt)}})),r}}}),A=$,N=(a("1a88"),Object(C["a"])(A,_,L,!1,null,"f722fc62",null)),R=N.exports,M={components:{Bg:l["a"],Card:c["a"],BedNum:d["a"],Sleep:x,Behavior:D,DeviceMonitor:g["a"],ScrollingTable:o["default"],OlderAnalysis:u["a"],ServiceOrder:h["a"],SecurityService:R},data:function(){return{orgId:void 0,stationCount1:0,stationCount2:0,bedInfo:{bedCount:0,usedCount:0,freeCount:0,usedRate:""},bedList:[],sleepScore:[0,0,0,0],sleepDuration:[],deviceMonitor:[],sceneDeviceCount:[],toiletDeviceCount:[],olderData:{},olderDataList:[],todayOrder:{total:0,fall:0,longlag:0,outbed:0,call:0},allOrder:{total:0,noHandle:0,handle:0},stationTodayOrder:[],chamberNum:0,olderNum:0,orderCount:{},guardOrderCount:{},guardOlderList:[],guardOlderHeaders:[{name:"name",text:"姓名"},{name:"age",text:"年龄"},{name:"genderDesc",text:"性别"},{name:"serverName",text:"信息"},{name:"serverStartDate",text:"服务开始时间"},{name:"serverEndDate",text:"服务结束时间"}],map:void 0,center:{lng:109.**************,lat:36.**************},zoom:13,dots:[],dotHoverId:void 0,dotSelectedlngLat:{},dotSelectedId:void 0,dotSelected:{},path:"",headers:[{text:"服务站",name:"stationName"},{text:"床位数",name:"bedCount"},{text:"已使用",name:"usedCount"},{text:"空闲",name:"freeCount"},{text:"使用率",name:"usedRate"}],olderHeaders:[{text:"服务站",name:"stationName",width:"18%"},{text:"总数",name:"total",width:"15%"},{text:"男",name:"manNum",width:"8%"},{text:"女",name:"womanNum",width:"8%"},{text:"≤60",name:"age60",width:"10%"},{text:"61-70",name:"age6170",width:"12%"},{text:"71-80",name:"age7180",width:"12%"},{text:"81-90",name:"age8190",width:"12%"},{text:">90",name:"age90",width:"5%"}]}},mounted:function(){var t=this,e=this.$route.params.orgId;e||(e=1),this.orgId=e,this.initData(),this.path="".concat("wss://boe.rfcare.cn","/ws?orgId=").concat(e,"&token=").concat(this.$store.state.user.token),setInterval((function(){t.initData()}),12e4)},methods:{initData:function(){this.fetchDatas(),this.fetchDotDatas(),this.getBedCount(),this.getStationBedCount(),this.getSleepScore(),this.getSleepDuration(),this.getDeviceCount(),this.getDeviceCountByScene(),this.getToiletDeviceCount(),this.getOlderAgeCount(),this.getOrgOlderAgeCount(),this.getSecurityServiceData(),this.getServiceOrderData(),this.fetchOrgOlderList()},handler:function(t){var e=t.BMap,a=t.map,r=new e.Point(116.403092,39.931078);a.centerAndZoom(r,13);var n={styleJson:[{featureType:"water",elementType:"all",stylers:{color:"#031628"}},{featureType:"land",elementType:"geometry",stylers:{color:"#000102"}},{featureType:"highway",elementType:"all",stylers:{visibility:"off"}},{featureType:"arterial",elementType:"geometry.fill",stylers:{color:"#000000"}},{featureType:"arterial",elementType:"geometry.stroke",stylers:{color:"#0b3d51"}},{featureType:"local",elementType:"geometry",stylers:{color:"#000000"}},{featureType:"railway",elementType:"geometry.fill",stylers:{color:"#000000"}},{featureType:"railway",elementType:"geometry.stroke",stylers:{color:"#08304b"}},{featureType:"subway",elementType:"labels.icon",stylers:{color:"#ff0000ff",visibility:"off"}},{featureType:"subway",elementType:"geometry",stylers:{lightness:-70}},{featureType:"building",elementType:"geometry.fill",stylers:{color:"#000000"}},{featureType:"all",elementType:"labels.text.fill",stylers:{color:"#0dd2ef"}},{featureType:"all",elementType:"labels.text.stroke",stylers:{color:"#000000"}},{featureType:"building",elementType:"geometry",stylers:{color:"#022338"}},{featureType:"green",elementType:"geometry",stylers:{color:"#062032"}},{featureType:"boundary",elementType:"all",stylers:{color:"#465b6c"}},{featureType:"manmade",elementType:"all",stylers:{visibility:"off"}},{featureType:"label",elementType:"all",stylers:{visibility:"off"}},{featureType:"poilabel",elementType:"labels.icon",stylers:{visibility:"off"}}]};a.setMapStyle(n),this.map=a},handleMapClick:function(t){var e=t.overlay;this.dotHoverId=void 0,!e&&this.dotSelectedId&&(this.dotSelectedlngLat={},this.dotSelectedId=void 0,this.dotSelectedName=void 0)},handleStationDotClick:function(t){this.$router.push({name:"stationscreen",params:{orgId:this.orgId,stationId:t}})},handleDotClick:function(t,e){var a=this;if(e!=this.dotSelectedId){if(e&&this.dots&&this.dots.length){var r=this.dots.find((function(t){return t.stationId===e}));r&&this.$nextTick((function(){a.dotSelectedId=e,a.dotSelectedName=r.stationName,a.dotSelectedlngLat={lng:r.longitude,lat:r.latitude}}))}}else this.handleMapClick()},fetchDatas:function(){var t=this,e="/bms/screen/org/getStationCount?orgId=".concat(this.orgId);this.$api.get(e,{}).then((function(e){if("00000"===e.status&&e.data)for(var a=0;a<e.data.length;a++)1==e.data[a].serviceType?t.stationCount1=e.data[a].cnt:2==e.data[a].serviceType&&(t.stationCount2=e.data[a].cnt)}))},fetchDotDatas:function(){var t=this;this.$api.get("/bms/bigscreen/selectOrgBigScreenStationList",{params:{orgId:this.orgId}}).then((function(e){"00000"===e.status&&e.data&&(t.dots=e.data,setTimeout((function(){t.center={lng:e.data[0].longitude,lat:e.data[0].latitude}}),500))}))},x1:function(){this.getMapPos()},x2:function(t){var e=t.type,a=t.target;console.log(e,a,a.getZoom()),this.getMapPos()},x3:function(){this.getMapPos()},handleDotMouseOver:function(t){this.dotHoverId=t},handleDotMouseOut:function(){},getMapPos:Object(s["a"])((function(){if(this.map){var t=this.map.getBounds(),e=t.getSouthWest(),a=t.getNorthEast(),r=new BMap.Point(e.lng,a.lat),n=new BMap.Point(a.lng,e.lat);console.log("端点",r.lat,n.lat,r.lng,n.lng)}}),500),getBedCount:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getBedCount?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取床位信息失败"),e.abrupt("return");case 9:t.bedInfo=i;case 10:case"end":return e.stop()}}),e)})))()},getStationBedCount:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getStationBedCount?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取床位信息失败"),e.abrupt("return");case 9:t.bedList=i;case 10:case"end":return e.stop()}}),e)})))()},getSleepScore:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getSleepScore?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:t.sleepScore=[i.tyep1Value,i.tyep2Value,i.tyep3Value,i.tyep4Value];case 10:case"end":return e.stop()}}),e)})))()},getSleepDuration:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getSleepDuration?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:t.sleepDuration=[i.tyep1Value,i.tyep2Value,i.tyep3Value];case 10:case"end":return e.stop()}}),e)})))()},getDeviceCount:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getDeviceCount?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:t.deviceMonitor=i||[];case 10:case"end":return e.stop()}}),e)})))()},getDeviceCountByScene:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i,s,o,l,c,d,u,h,g;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return s="/bms/screen/org/getDeviceCountByScene?orgId=".concat(t.orgId),e.next=3,t.$api.get(s,{});case 3:if(o=e.sent,l=o.status,c=o.data,"00000"==l&&c){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:d=(null===(a=c.filter((function(t){return 1==t.devScene}))[0])||void 0===a?void 0:a.cnt)||0,u=(null===(r=c.filter((function(t){return 2==t.devScene}))[0])||void 0===r?void 0:r.cnt)||0,h=(null===(n=c.filter((function(t){return 3==t.devScene}))[0])||void 0===n?void 0:n.cnt)||0,g=(null===(i=c.filter((function(t){return 4==t.devScene}))[0])||void 0===i?void 0:i.cnt)||0,t.sceneDeviceCount=[d,u,h,g];case 14:case"end":return e.stop()}}),e)})))()},getToiletDeviceCount:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getToiletDeviceCount?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:t.toiletDeviceCount=[i.tyep1Value,i.tyep2Value,i.tyep3Value];case 10:case"end":return e.stop()}}),e)})))()},getOlderAgeCount:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getOlderAgeCount?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:t.olderData=i;case 10:case"end":return e.stop()}}),e)})))()},getOrgOlderAgeCount:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getOrgOlderAgeCount?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:t.olderDataList=i||[];case 10:case"end":return e.stop()}}),e)})))()},getSecurityServiceData:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i,s,o,l,c,d,u,h,g,p;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getSecurityServiceData?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:s=i.todayOrder,o=i.unFinishedOrder,l=i.stationTodayOrder,s.length>0&&(t.todayOrder.fall=(null===(c=s.filter((function(t){return 21==t.orderType}))[0])||void 0===c?void 0:c.cnt)||0,t.todayOrder.longlag=(null===(d=s.filter((function(t){return 22==t.orderType}))[0])||void 0===d?void 0:d.cnt)||0,t.todayOrder.total=t.todayOrder.fall+t.todayOrder.longlag,t.todayOrder.outbed=(null===(u=s.filter((function(t){return 24==t.orderType}))[0])||void 0===u?void 0:u.cnt)||0,t.todayOrder.call=(null===(h=s.filter((function(t){return 31==t.orderType}))[0])||void 0===h?void 0:h.cnt)||0,t.todayOrder.total=t.todayOrder.fall+t.todayOrder.longlag+t.todayOrder.outbed+t.todayOrder.call,console.log("todayOrder:",t.todayOrder)),o.length>0&&(t.allOrder.total=o.reduce((function(t,e){return t+e.cnt}),0),t.allOrder.noHandle=(null===(g=o.filter((function(t){return 0==t.status}))[0])||void 0===g?void 0:g.cnt)||0,t.allOrder.handle=(null===(p=o.filter((function(t){return 2==t.status}))[0])||void 0===p?void 0:p.cnt)||0),t.stationTodayOrder=l||[];case 13:case"end":return e.stop()}}),e)})))()},getServiceOrderData:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n,i,s,o,l,c;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a="/bms/screen/org/getServiceOrderData?orgId=".concat(t.orgId),e.next=3,t.$api.get(a,{});case 3:if(r=e.sent,n=r.status,i=r.data,"00000"==n&&i){e.next=9;break}return console.log("获取信息失败"),e.abrupt("return");case 9:s=i.chamberNum,o=i.olderNum,l=i.orderCount,c=i.guardOrderCount,t.chamberNum=s||0,t.olderNum=o||0,console.log("orderCount:",l),t.orderCount=l||{},t.guardOrderCount=c||{};case 15:case"end":return e.stop()}}),e)})))()},fetchOrgOlderList:function(){var t=this;return Object(i["a"])(regeneratorRuntime.mark((function e(){var a,r,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$api.get("/bms/screen/org/getGuardOlders",{params:{orgId:t.orgId}});case 2:a=e.sent,r=a.status,n=a.data,"00000"===r&&n&&(t.guardOlderList=n);case 6:case"end":return e.stop()}}),e)})))()}}},j=M,B=(a("8904"),Object(C["a"])(j,r,n,!1,null,"f7a7ae02",null));e["default"]=B.exports},"1a88":function(t,e,a){"use strict";a("05b3")},"1f91":function(t,e,a){},"20d8":function(t,e,a){"use strict";a("a78e")},"4a24":function(t,e,a){},8904:function(t,e,a){"use strict";a("4a24")},a78e:function(t,e,a){},c986:function(t,e,a){"use strict";a("1f91")}}]);