(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a7b27e72"],{"2e09":function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"station-device-edit"},[o("page-header",{attrs:{title:(e.$route.params.id?"编辑":"新建")+"设备配置"}}),o("el-form",{ref:"reqForm",attrs:{model:e.reqForm,"label-position":"top","label-width":"80px",size:"small"}},[o("page-main",{attrs:{title:"基础信息配置"}},[o("el-row",{staticStyle:{padding:"20px 160px","box-sizing":"border-box"},attrs:{gutter:20}},[o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"设备编码"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.devCode,callback:function(t){e.$set(e.reqForm,"devCode",t)},expression:"reqForm.devCode"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"入库时间"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.createTime,callback:function(t){e.$set(e.reqForm,"createTime",t)},expression:"reqForm.createTime"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"设备名称"}},[o("el-input",{attrs:{placeholder:"请输入设备名称"},model:{value:e.reqForm.devName,callback:function(t){e.$set(e.reqForm,"devName",t)},expression:"reqForm.devName"}})],1)],1),e.isSsb(e.reqForm)?e._e():o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"设备模式"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:e.isSsb(e.reqForm)},model:{value:e.reqForm.devModel,callback:function(t){e.$set(e.reqForm,"devModel",t)},expression:"reqForm.devModel"}},e._l(e.dict.devModels,(function(e){return o("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),e.isSsb(e.reqForm)?e._e():o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"设备场景"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:e.isSsb(e.reqForm)},model:{value:e.reqForm.devScene,callback:function(t){e.$set(e.reqForm,"devScene",t)},expression:"reqForm.devScene"}},e._l(e.dict.devScenes,(function(e){return o("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"地址"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.houseAddr,callback:function(t){e.$set(e.reqForm,"houseAddr",t)},expression:"reqForm.houseAddr"}})],1)],1),o("el-col",{staticStyle:{height:"92px"},attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"门牌号"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.houseNumber,callback:function(t){e.$set(e.reqForm,"houseNumber",t)},expression:"reqForm.houseNumber"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticClass:"field-station",staticStyle:{width:"100%"},attrs:{label:"所属服务站"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择",disabled:e.isSsb(e.reqForm)},on:{change:e.handleFetchChambs,clear:e.handleStationClear},model:{value:e.reqForm.stationId,callback:function(t){e.$set(e.reqForm,"stationId",t)},expression:"reqForm.stationId"}},e._l(e.dict.stations,(function(e){return o("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})})),1)],1)],1),e.isSsb(e.reqForm)?e._e():o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"摔倒响应时间"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.fallResponseTime,callback:function(t){e.$set(e.reqForm,"fallResponseTime",t)},expression:"reqForm.fallResponseTime"}},[o("template",{slot:"append"},[e._v("分")])],2)],1)],1),e.isSsb(e.reqForm)?e._e():o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"久滞响应时间",prop:"longlagResponseTime",rules:e.rules.longlagResponseTime}},[o("el-input",{attrs:{placeholder:"请输入10的倍数",disabled:e.isSsb(e.reqForm)},model:{value:e.reqForm.longlagResponseTime,callback:function(t){e.$set(e.reqForm,"longlagResponseTime",t)},expression:"reqForm.longlagResponseTime"}},[o("template",{slot:"append"},[e._v("分")])],2)],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"激活状态"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.activeStatusName,callback:function(t){e.$set(e.reqForm,"activeStatusName",t)},expression:"reqForm.activeStatusName"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"运行状态"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.statusName,callback:function(t){e.$set(e.reqForm,"statusName",t)},expression:"reqForm.statusName"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"激活时间"}},[o("el-input",{attrs:{disabled:""},model:{value:e.reqForm.activeTime,callback:function(t){e.$set(e.reqForm,"activeTime",t)},expression:"reqForm.activeTime"}})],1)],1)],1)],1),e.isSsb(e.reqForm)?e._e():o("page-main",{attrs:{title:"安装信息"}},[o("el-row",{staticStyle:{padding:"20px 160px","box-sizing":"border-box"},attrs:{gutter:20}},[o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"安装方式"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},on:{change:e.spotTypeChange},model:{value:e.reqForm.roomSensorVO.spotType,callback:function(t){e.$set(e.reqForm.roomSensorVO,"spotType",t)},expression:"reqForm.roomSensorVO.spotType"}},e._l(e.dict.spotTypes,(function(e){return o("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1)],1)],1),e.isSsb(e.reqForm)?e._e():o("page-main",{attrs:{title:"房间尺寸"}},[o("el-popover",{attrs:{placement:"left",width:"700",trigger:"hover"}},[o("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/room-"+e.reqForm.roomSensorVO.spotType+".jpg"}}),o("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),o("el-row",{staticStyle:{"box-sizing":"border-box",padding:"20px 160px"},attrs:{gutter:20}},[e.reqForm.roomSensorVO.spotType===e.dict.spotTypes[1].code?[o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"长(米)",prop:"roomVO.x",rules:e.rules.roomX}},[o("el-input",{attrs:{type:"number"},model:{value:e.reqForm.roomVO.x,callback:function(t){e.$set(e.reqForm.roomVO,"x",t)},expression:"reqForm.roomVO.x"}})],1)],1)]:[o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"长(米)",prop:"roomVO.x",rules:e.rules.roomX}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:e.reqForm.roomVO.x,callback:function(t){e.$set(e.reqForm.roomVO,"x",t)},expression:"reqForm.roomVO.x"}})],1)],1)],o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"宽(米)",prop:"roomVO.y",rules:e.rules.roomY}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:e.reqForm.roomVO.y,callback:function(t){e.$set(e.reqForm.roomVO,"y",t)},expression:"reqForm.roomVO.y"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"高(米)",prop:"roomVO.z",rules:e.rules.roomZ}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:e.reqForm.roomVO.z,callback:function(t){e.$set(e.reqForm.roomVO,"z",t)},expression:"reqForm.roomVO.z"}})],1)],1)],2)],1),e.isSsb(e.reqForm)?e._e():o("page-main",{attrs:{title:"传感器方位"}},[o("el-popover",{attrs:{placement:"left",width:"700",trigger:"hover"}},[o("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/device-"+e.reqForm.roomSensorVO.spotType+".jpg"}}),o("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),o("el-row",{staticStyle:{"box-sizing":"border-box",padding:"20px 160px"},attrs:{gutter:20}},[o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"长(米)",prop:"roomSensorVO.x",rules:e.rules.sensorX}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:e.reqForm.roomSensorVO.x,callback:function(t){e.$set(e.reqForm.roomSensorVO,"x",t)},expression:"reqForm.roomSensorVO.x"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"宽(米)",prop:"roomSensorVO.y",rules:e.rules.sensorY}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:e.reqForm.roomSensorVO.y,callback:function(t){e.$set(e.reqForm.roomSensorVO,"y",t)},expression:"reqForm.roomSensorVO.y"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"高(米)",prop:"roomSensorVO.z",rules:e.rules.sensorZ}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:e.reqForm.roomSensorVO.z,callback:function(t){e.$set(e.reqForm.roomSensorVO,"z",t)},expression:"reqForm.roomSensorVO.z"}})],1)],1)],1)],1),e.isSsb(e.reqForm)?e._e():o("page-main",{attrs:{title:"门方位"}},[e._l(e.reqForm.roomGateVOList,(function(t,r){return o("el-row",{key:"roomGateVOList"+r,staticStyle:{"box-sizing":"border-box",margin:"20px 160px",padding:"20px",border:"1px dashed #92b9ff","border-radius":"10px",background:"#f1faff"},attrs:{gutter:20}},[o("el-col",{staticStyle:{display:"flex","justify-content":"flex-end","align-items":"center"},attrs:{span:24}},[o("el-popconfirm",{attrs:{"confirm-button-text":"确定","cancel-button-text":"取消",icon:"el-icon-info","icon-color":"red",title:"是否删除门方位?"},on:{confirm:function(t){return e.handleRoomGateRemove(r)}}},[o("i",{staticClass:"el-icon-delete",staticStyle:{color:"red",cursor:"pointer"},attrs:{slot:"reference"},slot:"reference"})]),o("div",{staticStyle:{width:"10px"}}),o("el-popover",{attrs:{placement:"left",trigger:"hover"}},[o("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/door-"+e.reqForm.roomSensorVO.spotType+"-"+t.direction+".jpg"}}),o("el-link",{attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"门名称"}},[e._v(" "+e._s(e.dict.doorSide[t.direction].name)+" ")])],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"门所在墙面代号"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.direction,callback:function(o){e.$set(t,"direction",o)},expression:"roomGate.direction"}},e._l(e.dict.doorSide,(function(e,t){return o("el-option",{key:t,attrs:{label:e.name,value:e.code}})})),1)],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"门中心点到右边墙角长(米)",prop:"roomGateVOList."+r+".length",rules:e.rules.roomGateLength}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.length,callback:function(o){e.$set(t,"length",o)},expression:"roomGate.length"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"宽(米)",prop:"roomGateVOList."+r+".width",rules:e.rules.roomGateWidth}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.width,callback:function(o){e.$set(t,"width",o)},expression:"roomGate.width"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"高(米)",prop:"roomGateVOList."+r+".height",rules:e.rules.roomGateHeight}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.height,callback:function(o){e.$set(t,"height",o)},expression:"roomGate.height"}})],1)],1)],1)})),o("el-row",[o("el-col",{attrs:{md:12}},[o("el-button",{staticStyle:{"margin-left":"160px"},attrs:{type:"primary",size:"small"},on:{click:e.handleRoomGateAdd}},[e._v("添加门方位")])],1),o("el-col",{attrs:{md:12}},[e._v(" 注意：长=面对门，计算门中心至右边墙角长度"+e._s((e.x,""))+" ")])],1)],2),e.isSsb(e.reqForm)?e._e():o("page-main",{attrs:{title:"区域"}},[o("el-popover",{attrs:{placement:"left",width:"700",trigger:"hover"}},[o("img",{staticStyle:{width:"100%"},attrs:{src:"/images/device/room-config/area-"+e.reqForm.roomSensorVO.spotType+".jpg"}}),o("el-link",{staticStyle:{position:"absolute",right:"20px",top:"15px"},attrs:{slot:"reference",type:"primary"},slot:"reference"},[e._v("示例图")])],1),e._l(e.reqForm.roomRegionVOList,(function(t,r){return o("el-row",{key:"roomGateVOList"+r,staticStyle:{"box-sizing":"border-box",margin:"20px 160px",padding:"20px",border:"1px dashed #92b9ff","border-radius":"10px",background:"#f1faff"},attrs:{gutter:20}},[o("el-popconfirm",{attrs:{"confirm-button-text":"确定","cancel-button-text":"取消",icon:"el-icon-info","icon-color":"red",title:"是否删除区域?"},on:{confirm:function(t){return e.handleRoomRegionRemove(r)}}},[o("i",{staticClass:"el-icon-delete",staticStyle:{color:"red",cursor:"pointer",position:"absolute",right:"20px",top:"20px"},attrs:{slot:"reference"},slot:"reference"})]),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"区域名称"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"请选择"},model:{value:t.cls,callback:function(o){e.$set(t,"cls",o)},expression:"roomRegion.cls"}},[o("el-option",{attrs:{label:"床",value:1}})],1)],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"长(米)",prop:"roomRegionVOList."+r+".positionX",rules:e.rules.roomRegionPositionX}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.positionX,callback:function(o){e.$set(t,"positionX",o)},expression:"roomRegion.positionX"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"宽(米)",prop:"roomRegionVOList."+r+".positionY",rules:e.rules.roomRegionPositionY}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.positionY,callback:function(o){e.$set(t,"positionY",o)},expression:"roomRegion.positionY"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"床长(米)",prop:"roomRegionVOList."+r+".scaleX",rules:e.rules.roomRegionScaleX}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.scaleX,callback:function(o){e.$set(t,"scaleX",o)},expression:"roomRegion.scaleX"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"床宽(米)",prop:"roomRegionVOList."+r+".scaleY",rules:e.rules.roomRegionScaleY}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.scaleY,callback:function(o){e.$set(t,"scaleY",o)},expression:"roomRegion.scaleY"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"床高(米)",prop:"roomRegionVOList."+r+".scaleZ",rules:e.rules.roomRegionScaleZ}},[o("el-input",{attrs:{type:"number",placeholder:"请输入"},model:{value:t.scaleZ,callback:function(o){e.$set(t,"scaleZ",o)},expression:"roomRegion.scaleZ"}})],1)],1)],1)})),o("el-row",[o("el-col",{attrs:{md:12}},[o("el-button",{staticStyle:{"margin-left":"160px"},attrs:{type:"primary",size:"small"},on:{click:e.handleRoomRegionAdd}},[e._v("添加区域")])],1),o("el-col",{staticStyle:{display:"flex"},attrs:{md:12}},[o("div",{staticStyle:{width:"50px"}},[e._v(" 注意： ")]),o("div",[e._v(" 长=背对设备连线方向，计算床长中心至左边墙长度"),o("br"),e._v(" 宽=背对设备连线方向，计算床宽中心至设备连线所在墙长度"),o("br"),e._v(" 床长=和设备连线方向同一面墙的床长度"),o("br")])])],1)],2),o("page-main",{attrs:{title:"用户信息"}},[o("el-row",{staticStyle:{"box-sizing":"border-box",padding:"20px 160px"},attrs:{gutter:20}},[o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"关联用户",prop:"memberPhone",rules:e.rules.memberPhone}},[o("el-input",{attrs:{placeholder:"请输入"},model:{value:e.reqForm.memberPhone,callback:function(t){e.$set(e.reqForm,"memberPhone",t)},expression:"reqForm.memberPhone"}})],1)],1),o("el-col",{attrs:{md:8}},[o("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"关联管家"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.reqForm.chambId,callback:function(t){e.$set(e.reqForm,"chambId",t)},expression:"reqForm.chambId"}},e._l(e.dict.chambs,(function(e){return o("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1)],1)],1),o("page-main",[o("el-row",{staticStyle:{"box-sizing":"border-box",padding:"20px 160px"},attrs:{gutter:20}},[o("el-col",{attrs:{md:24}},[o("el-form-item",{staticStyle:{"margin-top":"20px"}},[o("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),e.originalDevModel!==e.reqForm.devModel?o("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){e.devModelConfirmVisible=!0}}},[e._v("保存")]):o("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1),o("el-dialog",{attrs:{visible:e.mapSelectDialog.visible,width:"1200px","modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){return e.$set(e.mapSelectDialog,"visible",t)},close:e.mapSelectDialogClose}},[o("baidu-map",{staticStyle:{height:"500px",width:"100%"},attrs:{center:e.center,zoom:e.zoom,"scroll-wheel-zoom":!0},on:{ready:e.handler,click:e.handleMapClick}},[o("bm-control",[o("bm-auto-complete",{attrs:{sugStyle:{zIndex:1}},on:{confirm:e.completeConfirm},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}},[o("el-input",{staticStyle:{top:"20px",left:"20px",width:"360px"},attrs:{type:"text",placeholder:"请输入地名关键字",clearable:""},on:{clear:e.handleClearSearchContent},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearchContent(t)}},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}},[o("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1)],1),o("bm-geolocation",{attrs:{anchor:"BMAP_ANCHOR_BOTTOM_RIGHT",showAddressBar:!0,autoLocation:!0}})],1),o("el-button",{staticClass:"device-edit-map-confirm",attrs:{type:"success",icon:"el-icon-check",circle:""},on:{click:e.mapAddressConfirm}})],1),o("el-dialog",{attrs:{title:"模式提示",visible:e.devModelConfirmVisible,width:"30%",center:""},on:{"update:visible":function(t){e.devModelConfirmVisible=t}}},[o("span",[e._v(" "+e._s("1"===e.reqForm.devModel?"您将进入测试模式，可能会频繁收到信息，是否确认？":"您将进入日常模式，是否确认？")+" ")]),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:function(t){e.devModelConfirmVisible=!1}}},[e._v("取 消")]),o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("确 定")])],1)])],1)},i=[],a=o("ade3"),l=(o("c35a"),o("a9e3"),o("9129"),o("b680"),o("d81d"),o("ac1f"),o("841c"),o("a15b"),o("99af"),o("5319"),o("a434"),o("e1a4")),s=function(e,t,o){t?Object(l["b"])(t)?o():o(new Error("请输入正确的11位手机号码")):o(new Error("请输入电话号码"))},n=function(e,t,o){t?Object(l["b"])(t)?o():o(new Error("请输入正确的11位手机号码")):o()},d=function(e,t,o){(t||0===+t)&&(+t<10||+t%10!==0)?o(new Error("请输入合法的响应时间, 格式为10的倍数")):o()},m={data:function(){return{devModelConfirmVisible:!1,originalDevModel:void 0,mapSelectDialog:{visible:!1,type:void 0,index:void 0},keyword:"",map:void 0,center:{},zoom:13,dict:{stations:[],chambs:[],devModels:[{code:"0",name:"日常模式"},{code:"1",name:"测试模式"}],devScenes:[{code:"1",name:"客厅"},{code:"2",name:"卫生间"},{code:"3",name:"卧室"},{code:"4",name:"其他"}],genders:[{code:"M",name:"男"},{code:"W",name:"女"}],IdcardTypes:[{code:"0",name:"身份证"},{code:"1",name:"护照"},{code:"2",name:"港澳通行证"},{code:"3",name:"军官证"},{code:"4",name:"其他"}],bloodtypes:[{code:"01",name:"A"},{code:"02",name:"B"},{code:"03",name:"O"},{code:"04",name:"AB"},{code:"05",name:"其他"}],relationTypes:[{code:"kinsman",name:"亲属"},{code:"neighbor",name:"邻居"},{code:"chanmb",name:"管家"},{code:"other",name:"其他"}],olderLiveTypes:[],spotTypes:[{code:"1",name:"顶装"},{code:"2",name:"侧装"}],doorSide:[{code:0,name:"左"},{code:1,name:"前"},{code:2,name:"右"},{code:3,name:"后"}],statusOptions:[{name:"全部关闭",code:"0"},{name:"仅测试模式下开启",code:"1"},{name:"仅日常模式下开启",code:"2"},{name:"全部开启",code:"3"}]},reqForm:{id:void 0,devId:void 0,createTime:void 0,devName:void 0,devScene:void 0,devModel:void 0,houseAddr:void 0,houseNumber:void 0,houseId:void 0,stationId:void 0,fallResponseTime:3,longlagResponseTime:10,fallingbedResponseTime:1,activeStatusName:void 0,statusName:void 0,activeTime:void 0,latitude:void 0,longitude:void 0,memberPhone:void 0,chambId:void 0,roomVO:Object(a["a"])({x:void 0,y:void 0,z:void 0,leftX:void 0},"leftX",void 0),roomSensorVO:{x:void 0,y:void 0,z:void 0},roomRegionVOList:[{id:void 0,rid:void 0,cls:void 0,positionY:void 0,positionX:void 0,scaleX:void 0,scaleY:void 0,scaleZ:void 0,rotation:void 0}],roomGateVOList:[{id:void 0,gid:void 0,direction:void 0,length:void 0,width:void 0,height:void 0}],orderOlderVOList:[{id:void 0,name:void 0,gender:void 0,phone:void 0,birthday:void 0,liveType:void 0}],orderContactorVOList:[{id:void 0,contactName:void 0,gender:void 0,relationType:void 0,contactPhone:void 0,addr:void 0}],heartRate:void 0,breathRate:void 0,remark:void 0,spotType:"1"},rules:{valiName:[{required:!0,trigger:"blur",message:"请填写姓名"}],olderValiPhone:[{required:!0,trigger:"blur",validator:s}],memberPhone:[{required:!1,trigger:"blur",validator:n}],roomX:[{required:!0,trigger:"blur",message:"请填写长(米)"}],roomY:[{required:!0,trigger:"blur",message:"请填写宽(米)"}],roomZ:[{required:!0,trigger:"blur",message:"请填写高(米)"}],sensorX:[{required:!0,trigger:"blur",message:"请填写长(米)"}],sensorY:[{required:!0,trigger:"blur",message:"请填写宽(米)"}],sensorZ:[{required:!0,trigger:"blur",message:"请填写高(米)"}],roomGateLength:[{required:!0,trigger:"blur",message:"请填写门中心点到右边墙角长(米)"}],roomGateWidth:[{required:!0,trigger:"blur",message:"请填写宽(米)"}],roomGateHeight:[{required:!0,trigger:"blur",message:"请填写高(米)"}],roomRegionPositionX:[{required:!0,trigger:"blur",message:"请填写区域中心到区域头部(米)"}],roomRegionPositionY:[{required:!0,trigger:"blur",message:"请填写区域中心至侧边墙(米)"}],roomRegionScaleX:[{required:!0,trigger:"blur",message:"请填写区域长(米)"}],roomRegionScaleY:[{required:!0,trigger:"blur",message:"请填写区域宽(米)"}],roomRegionScaleZ:[{required:!0,trigger:"blur",message:"请填写区域高(米)"}],longlagResponseTime:[{required:!1,trigger:"blur",validator:d}]},submitLoading:!1,mapPointName:"",localSearch:void 0,currOverlay:void 0}},computed:{x:function(){if("1"!=this.reqForm.roomSensorVO.spotType&&this.reqForm.roomVO.leftX&&this.reqForm.roomVO.rightX){this.reqForm.roomSensorVO.x=this.reqForm.roomVO.leftX;var e=Number.parseFloat(this.reqForm.roomVO.leftX)+Number.parseFloat(this.reqForm.roomVO.rightX);Number.isNaN(e)?this.reqForm.roomVO.x=void 0:this.reqForm.roomVO.x=e.toFixed(2)}}},beforeCreate:function(){sessionStorage.getItem("deviceEditRandomVal")||(sessionStorage.setItem("deviceEditRandomVal",!0),window.location.reload())},beforeDestroy:function(){sessionStorage.removeItem("deviceEditRandomVal"),this.map},mounted:function(){this.handleFetchStations(),this.fetchDetail(this.$route.params.id),this.fetchLiveTypeDict(),this.$watch((function(){if(!this.reqForm.roomVO.x){var e=parseFloat(this.reqForm.roomVO.leftX)||0,t=parseFloat(this.reqForm.roomVO.rightX)||0,o=e+t;this.reqForm.roomVO.x=o||void 0}}))},methods:{isSsb:function(e){return"3"==e.deviceType||"4"==e.deviceType},spotTypeChange:function(){this.alertDeviceAngle()},alertDeviceAngle:function(){var e=this.reqForm.roomSensorVO.spotType,t=this.reqForm.roomVO.y;if("2"==e&&t){var o;Number.parseFloat(t);o="请将设备安装角度调整到30度",this.$message({showClose:!0,type:"success",message:o})}},openMapSelectDialog:function(e,t){e&&null!=t?(this.mapSelectDialog.type=e,this.mapSelectDialog.index=t):(this.mapSelectDialog.type=void 0,this.mapSelectDialog.index=void 0),this.mapSelectDialog.visible=!0},mapSelectDialogClose:function(){this.mapSelectDialog.type=void 0,this.mapSelectDialog.index=void 0,this.mapSelectDialog.visible=!1},completeConfirm:function(e){e.type,e.target;var t=e.item;this.localSearch&&this.localSearch.search([t.value.city,t.value.district,t.value.business].join(""))},mapAddressConfirm:function(){var e=this;this.$nextTick((function(){var t=document.querySelector("#detailDiv > a:nth-child(1)")||document.querySelector("div.BMap_bubble_title > p"),o=document.querySelector("div.BMap_bubble_content > div > table > tbody > tr > td:nth-child(2)");if(!o){var r=document.querySelector("div.BMap_bubble_content > div:nth-child(1) > p:nth-child(2)");r&&-1!=r.innerText.indexOf("地址")?o=r:(r=document.querySelector("div.BMap_bubble_content > div:nth-child(1) > p:nth-child(1)"),r&&-1!=r.innerText.indexOf("地址")&&(o=r))}if(t&&o){var i=void 0,a=void 0;e.currOverlay&&(i=e.currOverlay.point.lat,a=e.currOverlay.point.lng);var l="".concat(o.innerText.replace("地址：","")).concat(t.innerText.replace("详情»",""));e.mapSelectDialog.type&&null!=e.mapSelectDialog.index?(e.$set(e.reqForm[e.mapSelectDialog.type][e.mapSelectDialog.index],"addr",l),e.$set(e.reqForm[e.mapSelectDialog.type][e.mapSelectDialog.index],"latitude",i),e.$set(e.reqForm[e.mapSelectDialog.type][e.mapSelectDialog.index],"longitude",a)):(e.reqForm.houseAddr=l,e.reqForm.latitude=i,e.reqForm.longitude=a),e.mapSelectDialogClose()}else e.$message({type:"warning",message:"请先选择一个定位点",duration:2500})}))},handleSearchContent:function(e){this.keyword&&this.localSearch?this.localSearch.search(this.keyword):this.map.clearOverlays()},handleClearSearchContent:function(e){this.map.clearOverlays()},searchcomplete:function(e){var t=this;this.$nextTick((function(){if(e&&e.Hr&&e.Hr[0]){t.center=e.Hr[0].point;var o=t.map.getOverlays();t.map.removeOverlay(o[o.length-1])}}))},handler:function(e){var t=this,o=e.BMap,r=e.map;console.log("BMap, map",o,r);var i=this,a=new o.Point(116.403092,39.931078);r.centerAndZoom(a,18);var l=new o.Geolocation;l.getCurrentPosition((function(e){var t=e.point,o=t.lng,r=t.lat,a=e.address;a.province,a.city,a.district,a.street,a.street_number;i.center={lng:o,lat:r}})),this.localSearch=new o.LocalSearch(r,{renderOptions:{map:r}}),this.localSearch.setSearchCompleteCallback((function(e){if(t.localSearch.getStatus()==BMAP_STATUS_SUCCESS){var o=e.getPoi(0);t.currOverlay={point:o.point}}})),this.map=r},handleMapClick:function(e){var t=e.overlay;this.currOverlay=t},fetchLiveTypeDict:function(){var e=this;this.$api.get("/dict/queryDictItemsByType",{params:{typeCode:"older_live_type"}}).then((function(t){"00000"===t.status&&t.data&&(e.dict.olderLiveTypes=t.data)}))},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/Device/getDeviceInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(e.data.roomVO||(e.data.roomVO={}),e.data.roomSensorVO||(e.data.roomSensorVO={}),t.originalDevModel=e.data.devModel,t.reqForm=e.data,t.reqForm.fallResponseTime||(t.reqForm.longlagResponseTime=3),t.reqForm.longlagResponseTime||(t.reqForm.longlagResponseTime=10),t.handleFetchChambs())}))},handleOlderAdd:function(){this.reqForm.orderOlderVOList?this.reqForm.orderOlderVOList.push({id:void 0,name:void 0,gender:void 0,phone:void 0,birthday:void 0,liveType:void 0}):this.reqForm.orderOlderVOList=[{id:void 0,name:void 0,gender:void 0,phone:void 0,birthday:void 0,liveType:void 0}]},handleOlderRemove:function(e){this.reqForm.orderOlderVOList.splice(e,1)},handleRoomGateAdd:function(){var e=0;this.reqForm.roomGateVOList&&this.reqForm.roomGateVOList.length&&(e=this.reqForm.roomGateVOList[this.reqForm.roomGateVOList.length-1].gid),this.reqForm.roomGateVOList?this.reqForm.roomGateVOList.push({id:void 0,gid:parseInt(e)+1,direction:0,length:void 0,width:void 0,height:void 0,roomId:this.reqForm.devCode}):this.reqForm.roomGateVOList=[{id:void 0,gid:parseInt(e)+1,direction:0,length:void 0,width:void 0,height:void 0,roomId:this.reqForm.devCode}]},handleRoomGateRemove:function(e){this.reqForm.roomGateVOList.splice(e,1)},handleRoomRegionAdd:function(){var e=0;this.reqForm.roomRegionVOList&&this.reqForm.roomRegionVOList.length&&(e=this.reqForm.roomRegionVOList[this.reqForm.roomRegionVOList.length-1].rid),this.reqForm.roomRegionVOList?this.reqForm.roomRegionVOList.push({id:void 0,rid:parseInt(e)+1,cls:1,positionY:void 0,positionX:void 0,scaleX:void 0,scaleY:void 0,scaleZ:void 0,rotation:void 0,roomId:this.reqForm.devCode}):this.reqForm.roomRegionVOList=[{id:void 0,rid:parseInt(e)+1,cls:1,positionY:void 0,positionX:void 0,scaleX:void 0,scaleY:void 0,scaleZ:void 0,rotation:void 0,roomId:this.reqForm.devCode}]},handleRoomRegionRemove:function(e){this.reqForm.roomRegionVOList.splice(e,1)},handleContactorAdd:function(){this.reqForm.orderContactorVOList?this.reqForm.orderContactorVOList.push({id:void 0,contactName:void 0,gender:void 0,relationType:void 0,contactPhone:void 0,addr:void 0}):this.reqForm.orderContactorVOList=[{id:void 0,contactName:void 0,gender:void 0,relationType:void 0,contactPhone:void 0,addr:void 0}]},handleContactorRemove:function(e){this.reqForm.orderContactorVOList.splice(e,1)},handleSubmit:function(e){var t=this;this.devModelConfirmVisible=!1,this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var o=JSON.parse(JSON.stringify(t.reqForm));o.roomSensorVO["roomId"]||(o.roomSensorVO["roomId"]=t.reqForm.devCode),o.roomVO["roomId"]||(o.roomVO["roomId"]=t.reqForm.devCode),o.roomGateVOList&&o.roomGateVOList.length&&o.roomGateVOList.map((function(e){e["roomId"]=t.reqForm.devCode})),o.roomRegionVOList&&o.roomRegionVOList.length&&o.roomRegionVOList.map((function(e){e["roomId"]=t.reqForm.devCode,e.rotation||(e.rotation=0)}));var r=t.$route.params.id?t.$api.post("/bms/Device/update",o):t.$api.post("/bms/Device/add",o);r.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},handleFetchStations:function(){var e=this;this.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}))},handleStationClear:function(){this.dict.chambs=[],this.reqForm.houseId=void 0},handleFetchChambs:function(){var e=this;this.reqForm.stationId?this.$api.get("/bms/sysuser/listKeeper4Select",{params:{stationId:this.reqForm.stationId}}).then((function(t){"00000"===t.status&&t.data?e.dict.chambs=t.data:e.dict.chambs=[]})):this.dict.chambs=[]}}},c=m,p=(o("3e29"),o("6d29"),o("2877")),u=Object(p["a"])(c,r,i,!1,null,"df6bd386",null);t["default"]=u.exports},"3e29":function(e,t,o){"use strict";o("ba71")},"6d29":function(e,t,o){"use strict";o("d309")},ba71:function(e,t,o){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},d309:function(e,t,o){},e1a4:function(e,t,o){"use strict";function r(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function i(e){var t=/^\d{11}$/;return t.test(e)}function a(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function l(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function s(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function n(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function d(e){return n(e)||s(e)}o.d(t,"b",(function(){return r})),o.d(t,"c",(function(){return i})),o.d(t,"d",(function(){return a})),o.d(t,"a",(function(){return l})),o.d(t,"e",(function(){return d}))}}]);