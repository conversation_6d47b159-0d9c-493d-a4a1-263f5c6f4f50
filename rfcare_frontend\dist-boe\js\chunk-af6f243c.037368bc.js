(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-af6f243c"],{"08f4":function(e,t,a){},"0e2c":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务设备详情查看"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"设备编码:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.devCode,callback:function(t){e.$set(e.search,"devCode",t)},expression:"search.devCode"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"激活状态:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.activeStatus,callback:function(t){e.$set(e.search,"activeStatus",t)},expression:"search.activeStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"未激活",value:"0"}}),a("el-option",{attrs:{label:"已激活",value:"1"}})],1)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",align:"center"}}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"激活状态",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"运行状态",align:"center"}}),a("el-table-column",{attrs:{prop:"devModelName",label:"运行模式",align:"center"}}),a("el-table-column",{attrs:{prop:"chambName",label:"关联管家",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",align:"center"}}),a("el-table-column",{attrs:{prop:"activeTime",label:"激活时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleOpenDeviceInfoDialog(l.id)}}},[e._v("查看详情")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,size:e.table.pageInfo.pageSize,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1),a("UserDeviceInfoDialog",{ref:"userDeviceInfoDialog"})],1)},i=[],n=a("5530"),s=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,title:"设备"+e.info.devCode+"-详情查看","modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 设备编码: "+e._s(e.info.devCode)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 入库时间: "+e._s(e.info.createTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备名称: "+e._s(e.info.devName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备场景: "+e._s(e.info.devSceneName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 地址: "+e._s(e.info.houseAddr)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 门牌号: "+e._s(e.info.houseNumber||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 所属服务站: "+e._s(e.info.stationName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 摔倒响应时间: "+e._s(e.info.fallResponseTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 久滞响应时间: "+e._s(e.info.longlagResponseTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活状态: "+e._s(e.info.activeStatusName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活时间: "+e._s(e.info.activeTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 运行状态: "+e._s(e.info.statusName)+" ")])],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"用户信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 关联用户: "+e._s(e.info.memberPhone)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 关联管家: "+e._s(e.info.chambName)+" ")])],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"被监护人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.info.orderOlderVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"电话",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"birthday",label:"出生年月",align:"center"}}),a("el-table-column",{attrs:{prop:"liveTypeDesc",label:"居住类型",align:"center"}})],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"紧急联系人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.info.orderContactorVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"contactName",label:"姓名",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"contactPhone",label:"电话",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"relationTypeDesc",label:"联系人标签",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址",align:"center"}})],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"参数信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 心跳频率: "+e._s(e.info.heartRate)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 呼吸频率: "+e._s(e.info.breathRate)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 备注: "+e._s(e.info.remark||"无")+" ")])],1)],1)],1)}),o=[],r={name:"UserDeviceInfoDialog",data:function(){return{visible:!1,info:{}}},mounted:function(){},methods:{show:function(e){this.info=e,this.visible=!0},close:function(){this.info={},this.visible=!1,this.$emit("on-close")}}},c=r,d=(a("1c95"),a("2877")),m=Object(d["a"])(c,s,o,!1,null,"90717fec",null),p=m.exports,u={name:"personalUserList",components:{UserDeviceInfoDialog:p},data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},activeTimes:[],search:{devCode:void 0,startTime:void 0,endTime:void 0,activeStatus:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handleOpenDeviceInfoDialog:function(e){var t=this;this.$api.get("/bms/Device/getDeviceInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data?t.$refs.userDeviceInfoDialog.show(e.data):t.$message({message:"未找到设备相关信息",type:"error"})}))},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=JSON.parse(JSON.stringify(this.search));console.log(this.$route.meta.busiType),"chamb"===this.$route.meta.busiType?t["chambId"]=this.$route.params.id:"station"===this.$route.meta.busiType?t["stationId"]=this.$route.params.id:"org"===this.$route.meta.busiType?t["orgId"]=this.$route.params.id:t["memberId"]=this.$route.params.id,this.activeTimes&&this.activeTimes.length>=2&&(t["startTime"]=this.$dayjs(this.activeTimes[0]).format("YYYY-MM-DD HH:mm:ss"),t["endTime"]=this.$dayjs(this.activeTimes[1]).format("YYYY-MM-DD HH:mm:ss")),this.$api.post("/bms/Device/list",Object(n["a"])(Object(n["a"])({},t),{},{pageReqVo:this.table.pageInfo})).then((function(t){"00000"===t.status&&t.data&&(e.table.datas=t.data,e.table.pageInfo.total=t.page.total)}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.activeTimes=[],this.handleSearch()}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,userListIndex")}))},beforeRouteLeave:function(e,t,a){["personalUserDetail"].includes(e.name)||this.$store.commit("keepAlive/remove","EmptyLayout,userListIndex"),a()}},f=u,b=(a("b8be"),Object(d["a"])(f,l,i,!1,null,"668b7ced",null));t["default"]=b.exports},"1c95":function(e,t,a){"use strict";a("08f4")},a6ea:function(e,t,a){},b8be:function(e,t,a){"use strict";a("a6ea")}}]);