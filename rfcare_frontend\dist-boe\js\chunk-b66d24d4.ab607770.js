(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-b66d24d4"],{6529:function(e,t,a){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},af7b:function(e,t,a){"use strict";a("6529")},d90a:function(e,t,a){"use strict";a("f18b")},e1a4:function(e,t,a){"use strict";function o(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function r(e){var t=/^\d{11}$/;return t.test(e)}function i(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function n(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function l(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function c(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function s(e){return c(e)||l(e)}a.d(t,"b",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return n})),a.d(t,"e",(function(){return s}))},eb93:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"station-device-edit"},[a("page-header",{attrs:{title:(e.$route.params.id?"编辑":"新增")+"服务站"}}),a("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.rules,"label-position":"top","label-width":"80px",size:"small"}},[a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"20px 160px","box-sizing":"border-box"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务站名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入服务站名称"},model:{value:e.reqForm.name,callback:function(t){e.$set(e.reqForm,"name",t)},expression:"reqForm.name"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务站简称",prop:"shortName"}},[a("el-input",{attrs:{placeholder:"请输入服务站简称"},model:{value:e.reqForm.shortName,callback:function(t){e.$set(e.reqForm,"shortName",t)},expression:"reqForm.shortName"}})],1)],1),"2"===e.$store.state.user.member.systemVersion?a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务模式"}},[a("el-radio-group",{model:{value:e.reqForm.serviceType,callback:function(t){e.$set(e.reqForm,"serviceType",t)},expression:"reqForm.serviceType"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("集中式")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("居家式")])],1)],1)],1):e._e(),a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"服务站地址",prop:"addr"}},[a("el-input",{attrs:{placeholder:"请输入服务站地址"},nativeOn:{click:function(t){return e.openMapSelectDialog(t)}},model:{value:e.reqForm.addr,callback:function(t){e.$set(e.reqForm,"addr",t)},expression:"reqForm.addr"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"经度"}},[a("el-input",{attrs:{placeholder:"请输入经度",disabled:""},model:{value:e.reqForm.longitude,callback:function(t){e.$set(e.reqForm,"longitude",t)},expression:"reqForm.longitude"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"纬度"}},[a("el-input",{attrs:{placeholder:"请输入纬度",disabled:""},model:{value:e.reqForm.latitude,callback:function(t){e.$set(e.reqForm,"latitude",t)},expression:"reqForm.latitude"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"联系电话"}},[a("el-input",{attrs:{placeholder:"请输入联系电话"},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{staticStyle:{"margin-top":"20px","text-align":"center"}},[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1),a("el-dialog",{attrs:{visible:e.mapSelectDialog.visible,width:"1200px","modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){return e.$set(e.mapSelectDialog,"visible",t)},close:e.mapSelectDialogClose}},[a("baidu-map",{staticStyle:{height:"500px",width:"100%"},attrs:{center:e.center,zoom:e.zoom,"scroll-wheel-zoom":!0},on:{ready:e.handler,click:e.handleMapClick}},[a("bm-control",[a("bm-auto-complete",{attrs:{sugStyle:{zIndex:1}},on:{confirm:e.completeConfirm},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}},[a("el-input",{staticStyle:{top:"20px",left:"20px",width:"360px"},attrs:{type:"text",placeholder:"请输入地名关键字",clearable:""},on:{clear:e.handleClearSearchContent},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearchContent(t)}},model:{value:e.keyword,callback:function(t){e.keyword=t},expression:"keyword"}},[a("i",{staticClass:"el-input__icon el-icon-search",attrs:{slot:"prefix"},slot:"prefix"})])],1)],1),a("bm-geolocation",{attrs:{anchor:"BMAP_ANCHOR_BOTTOM_RIGHT",showAddressBar:!0,autoLocation:!0}})],1),a("el-button",{staticClass:"device-edit-map-confirm",attrs:{type:"success",icon:"el-icon-check",circle:""},on:{click:e.mapAddressConfirm}})],1)],1)},r=[],i=(a("d81d"),a("ac1f"),a("841c"),a("a15b"),a("99af"),a("5319"),a("e1a4")),n=function(e,t,a){t||a(),Object(i["b"])(t)?a():a(new Error("请输入正确的11位手机号码"))},l={data:function(){return{mapSelectDialog:{visible:!1,type:void 0,index:void 0},keyword:"",map:void 0,center:{},zoom:13,reqForm:{id:void 0,name:void 0,shortName:void 0,addr:void 0,longitude:void 0,latitude:void 0,phone:void 0,serviceType:"2"},rules:{name:[{required:!0,trigger:"blur",message:"请填写服务站名称"}],shortName:[{required:!0,trigger:"blur",message:"请填写服务站简称"}],addr:[{required:!0,trigger:"change",message:"请填写地址"}],phone:[{required:!0,trigger:"blur",validator:n}]},submitLoading:!1,mapPointName:"",localSearch:void 0,currOverlay:void 0}},beforeCreate:function(){sessionStorage.getItem("deviceEditRandomVal")||(sessionStorage.setItem("deviceEditRandomVal",!0),window.location.reload())},beforeDestroy:function(){sessionStorage.removeItem("deviceEditRandomVal"),this.map},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{openMapSelectDialog:function(e,t){e&&null!=t?(this.mapSelectDialog.type=e,this.mapSelectDialog.index=t):(this.mapSelectDialog.type=void 0,this.mapSelectDialog.index=void 0),this.mapSelectDialog.visible=!0},mapSelectDialogClose:function(){this.mapSelectDialog.type=void 0,this.mapSelectDialog.index=void 0,this.mapSelectDialog.visible=!1},completeConfirm:function(e){e.type,e.target;var t=e.item;this.localSearch&&this.localSearch.search([t.value.city,t.value.district,t.value.business].join(""))},mapAddressConfirm:function(){var e=this;this.$nextTick((function(){var t=document.querySelector("#detailDiv > a:nth-child(1)")||document.querySelector("div.BMap_bubble_title > p"),a=document.querySelector("div.BMap_bubble_content > div > table > tbody > tr > td:nth-child(2)");if(!a){var o=document.querySelector("div.BMap_bubble_content > div:nth-child(1) > p:nth-child(2)");o&&-1!=o.innerText.indexOf("地址")?a=o:(o=document.querySelector("div.BMap_bubble_content > div:nth-child(1) > p:nth-child(1)"),o&&-1!=o.innerText.indexOf("地址")&&(a=o))}if(t&&a){var r=void 0,i=void 0;e.currOverlay&&(r=e.currOverlay.point.lat,i=e.currOverlay.point.lng);var n="".concat(a.innerText.replace("地址：","")).concat(t.innerText.replace("详情»",""));e.mapSelectDialog.type&&null!=e.mapSelectDialog.index?(e.$set(e.reqForm[e.mapSelectDialog.type][e.mapSelectDialog.index],"addr",n),e.$set(e.reqForm[e.mapSelectDialog.type][e.mapSelectDialog.index],"latitude",r),e.$set(e.reqForm[e.mapSelectDialog.type][e.mapSelectDialog.index],"longitude",i)):(e.reqForm.addr=n,e.reqForm.latitude=r,e.reqForm.longitude=i),e.mapSelectDialogClose()}else e.$message({type:"warning",message:"请先选择一个定位点",duration:2500})}))},handleSearchContent:function(e){this.keyword&&this.localSearch?this.localSearch.search(this.keyword):this.map.clearOverlays()},handleClearSearchContent:function(e){this.map.clearOverlays()},searchcomplete:function(e){var t=this;this.$nextTick((function(){if(console.log("e.Hr",e.Hr),e&&e.Hr&&e.Hr[0]){t.center=e.Hr[0].point;var a=t.map.getOverlays();t.map.removeOverlay(a[a.length-1])}}))},handler:function(e){var t=this,a=e.BMap,o=e.map;console.log("BMap, map",a,o);var r=this,i=new a.Point(116.403092,39.931078);o.centerAndZoom(i,18);var n=new a.Geolocation;n.getCurrentPosition((function(e){var t=e.point,a=t.lng,o=t.lat,i=e.address;i.province,i.city,i.district,i.street,i.street_number;r.center={lng:a,lat:o}})),this.localSearch=new a.LocalSearch(o,{renderOptions:{map:o}}),this.localSearch.setSearchCompleteCallback((function(e){if(t.localSearch.getStatus()==BMAP_STATUS_SUCCESS){var a=e.getPoi(0);t.currOverlay={point:a.point}}})),this.map=o},handleMapClick:function(e){var t=e.overlay;this.currOverlay=t,console.log("overlay",t)},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/station/getStationInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data)}))},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var a=JSON.parse(JSON.stringify(t.reqForm)),o=t.$route.params.id?t.$api.post("/bms/station/updateStation",a):t.$api.post("/bms/station/addStation",a);o.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),t.submitLoading=!1,!t.reqForm.id&&e.data?t.handleGoAllocation(e.data):setTimeout((function(){t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},handleGoAllocation:function(e){var t=this;this.$confirm("是否为该服务站分配设备?","提醒",{closeOnClickModal:!1,confirmButtonText:"立即前往",cancelButtonText:"取消",type:"success"}).then((function(){console.log(1),t.$router.replace({name:"stationDeviceAllocationInfo",params:{id:e}})})).catch((function(e){console.log(2),setTimeout((function(){t.$router.go(-1)}),500)}))}}},c=l,s=(a("af7b"),a("d90a"),a("2877")),d=Object(s["a"])(c,o,r,!1,null,"41a71a9a",null);t["default"]=d.exports},f18b:function(e,t,a){}}]);