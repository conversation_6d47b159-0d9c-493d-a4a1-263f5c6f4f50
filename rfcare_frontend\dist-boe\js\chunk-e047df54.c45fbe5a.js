(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e047df54"],{"6c12":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("page-header",{attrs:{title:"户型管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:t.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:""}},[a("el-input",{attrs:{placeholder:"根据户型名称索"},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:(e.preventDefault(),t.handleSearch(e))}},model:{value:t.search.keyword,callback:function(e){t.$set(t.search,"keyword",e)},expression:"search.keyword"}})],1),a("el-form-item",{attrs:{label:"服务站:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":t.handleFetchStations},model:{value:t.search.stationId,callback:function(e){t.$set(t.search,"stationId",e)},expression:"search.stationId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),t._l(t.dict.stations,(function(t){return a("el-option",{key:t.id,attrs:{label:t.shortName,value:t.id}})}))],2)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(e){return t.resetForm("ruleForm")}}},[t._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:t.handleSearch}},[t._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(e){return t.$router.push({name:"tplhouseAdd"})}}},[t._v("新增")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:t.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"houseName",label:"户型名称",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"bedroomCount",label:"卧室数",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"livingRoomCount",label:"客厅数",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"kitchenCount",label:"厨房数",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"bathroomCount",label:"卫生间数",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"otherCount",label:"其他",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"remark",label:"备注",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return t.$router.push({name:"tplhouseEdit",params:{id:e.row.tplHouseId}})}}},[t._v("修改")]),e.row.used?t._e():a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return t.handleDelOperate(e.row.tplHouseId)}}},[t._v("删除")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":t.table.pageInfo.current,total:t.table.pageInfo.total,size:t.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":t.handlePageCurrentChange,"size-change":t.handlePageSizeChange}})],1)],1)},l=[],i=a("5530"),o=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),{name:"tplhouseListIndex",data:function(){return{dict:{stations:[]},search:{keyword:void 0,stationId:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(t){this.table.pageInfo.current=t,this.fetchDatas()},handlePageSizeChange:function(t){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=t,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var t=this,e=Object(i["a"])(Object(i["a"])({},this.search),{},{pageReqVo:this.table.pageInfo});this.$api.post("/bms/tplhouse/selectByPage",e).then((function(e){"00000"===e.status&&e.data?(t.table.datas=e.data,t.table.pageInfo.total=e.page.total):(t.table.datas=[],t.table.pageInfo.total=0)}))},handleDelOperate:function(t){var e=this;t&&this.$confirm("您确定要删除该记录?","删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((function(){e.$api.get("/bms/tplhouse/delete?id=".concat(t),{}).then((function(t){"00000"===t.status?(e.$message({type:"success",message:"删除成功"}),e.fetchDatas()):e.$message({type:"error",message:"删除失败"})}))})).catch((function(){e.$message({type:"error",message:"删除失败"})}))},resetForm:function(t){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()},handleFetchStations:function(t){var e=this;!0===t?this.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]})):this.search.stationId||(this.dict.chambs=[],this.search.chambId=void 0)}},beforeRouteEnter:function(t,e,a){a((function(t){t.$store.commit("keepAlive/add","EmptyLayout,butlerListIndex")}))},beforeRouteLeave:function(t,e,a){["butlerInfo"].includes(t.name)||(console.log("移除 EmptyLayout,butlerListIndex"),this.$store.commit("keepAlive/remove","EmptyLayout,butlerListIndex")),a()}}),r=o,s=(a("dc1f"),a("2877")),c=Object(s["a"])(r,n,l,!1,null,"db314480",null);e["default"]=c.exports},"7bae":function(t,e,a){},dc1f:function(t,e,a){"use strict";a("7bae")}}]);