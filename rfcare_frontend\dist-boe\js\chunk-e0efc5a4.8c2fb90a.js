(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e0efc5a4"],{"29fe":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务站管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"95px","label-position":"left"}},[a("el-form-item",{attrs:{label:"服务站名称:"}},[a("el-input",{attrs:{placeholder:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.name,callback:function(t){e.$set(e.search,"name",t)},expression:"search.name"}})],1),a("el-form-item",{attrs:{label:"所属机构:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.search.orgId,callback:function(t){e.$set(e.search,"orgId",t)},expression:"search.orgId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.orgs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{directives:[{name:"auth",rawName:"v-auth",value:"self.org",expression:"'self.org'"}],staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"stationAdd"})}}},[e._v("新增服务站")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"服务站名称",align:"center"}}),a("el-table-column",{attrs:{prop:"shortName",label:"服务站简称",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"服务站地址",align:"center"}}),a("el-table-column",{attrs:{prop:"longitude",label:"经度",align:"center"}}),a("el-table-column",{attrs:{prop:"latitude",label:"纬度",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"serviceType",label:"服务模式",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("1"===a.serviceType?"集中式":"")+" "+e._s("2"===a.serviceType?"居家式":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"stationName",label:"设备服务",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationDeviceList2",params:{id:n.id}})}}},[e._v(e._s(n.devCount||0))])]}}])}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"创建时间",width:"170",align:"center"}}),!e.$store.state.user.member||"org"!==e.$store.state.user.member.role&&"system"!==e.$store.state.user.member.role?e._e():a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:"self.org",expression:"'self.org'"}],attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationEdit",params:{id:n.id}})}}},[e._v("维护")]),a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:["self.org","self.system"],expression:"[ 'self.org', 'self.system' ]"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationInfo",params:{id:n.id}})}}},[e._v("详情查看")]),a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:["self.org","self.system"],expression:"[ 'self.org', 'self.system' ]"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationDeviceAllocationInfo",params:{id:n.id}})}}},[e._v("设备分配")])]}}],null,!1,401572892)})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},r=[],l=a("5530"),s=(a("ac1f"),a("841c"),{name:"stationIndex",data:function(){return{dict:{orgs:[]},search:{name:void 0,orgId:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.$route.params.orgId&&(this.search.orgId=parseInt(this.$route.params.orgId)),this.handleFetchOrgs(),this.fetchDatas()},methods:{handleRadioChange:function(e){this.search.status=e,this.fetchDatas()},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.current=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/station/listStation",{params:Object(l["a"])(Object(l["a"])({},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}}}),i=s,o=(a("97a1"),a("2877")),c=Object(o["a"])(i,n,r,!1,null,"5646855c",null);t["default"]=c.exports},"97a1":function(e,t,a){"use strict";a("a824")},a824:function(e,t,a){}}]);