(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f44f626a"],{"1e4b":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"situation-page"},[t.dotSelectedName?e("div",{staticClass:"org-wrap"},[e("span",[t._v(t._s(t.dotSelectedName))])]):t._e(),t.dotSelectedName?e("div",{staticClass:"curr-reset"},[e("span",{on:{click:t.handleResetStation}},[e("img",{attrs:{src:"/images/common/icon-top-reset-btn.png"}}),t._v("重置")])]):t._e(),e("el-row",{staticStyle:{height:"100%"},attrs:{gutter:11}},[e("el-col",{staticClass:"column-flex",attrs:{span:6}},[e("div",{staticClass:"chart-item",staticStyle:{height:"320px",flex:"initial"}},[e("img",{staticClass:"block-border-top-left",attrs:{src:"/images/common/block-border-top-left.png"}}),e("img",{staticClass:"block-border-top-right",attrs:{src:"/images/common/block-border-top-right.png"}}),e("img",{staticClass:"block-border-bottom-left",attrs:{src:"/images/common/block-border-bottom-left.png"}}),e("img",{staticClass:"block-border-bottom-right",attrs:{src:"/images/common/block-border-bottom-right.png"}}),e("div",{staticClass:"title-wrap"},[e("div",{staticClass:"title"},[t._v("服务能力")])]),e("div",{staticClass:"chart",staticStyle:{height:"calc(100% - 44px)","padding-top":"20px","padding-left":"28px"}},[e("div",{staticClass:"inner-text",staticStyle:{"padding-left":"0"}},[e("span",{staticClass:"text"},[t._v("安全管家总人数: ")]),e("span",{staticClass:"num"},[t._v(t._s(t.allData.chamberlainAmount))])]),e("el-row",{staticStyle:{height:"100%",margin:"0"},attrs:{gutter:0}},[e("el-col",{staticClass:"serivice-ability",attrs:{span:12}},[e("img",{attrs:{src:"/images/home/<USER>"}}),e("div",{staticClass:"num-wrap"},[e("div",{staticClass:"num"},[t._v(t._s(t.allData.serveAmountAvg||0))]),e("div",{staticClass:"text"},[t._v("平均每人服务用户")])])]),e("el-col",{staticClass:"serivice-ability",attrs:{span:12}},[e("img",{attrs:{src:"/images/home/<USER>"}}),e("div",{staticClass:"num-wrap"},[e("div",{staticClass:"num"},[t._v(t._s(t.allData.serveAmountAvg||0))]),e("div",{staticClass:"text"},[t._v("累计服务人次")])])]),e("el-col",{staticClass:"serivice-ability",attrs:{span:12}},[e("img",{attrs:{src:"/images/home/<USER>"}}),e("div",{staticClass:"num-wrap"},[e("div",{staticClass:"num"},[t._v(t._s(t.allData.seatsAmount||0))]),e("div",{staticClass:"text"},[t._v("坐席总人数")])])]),e("el-col",{staticClass:"serivice-ability",attrs:{span:12}},[e("img",{attrs:{src:"/images/home/<USER>"}}),e("div",{staticClass:"num-wrap"},[e("div",{staticClass:"num"},[t._v(t._s(t.allData.seatsOnlineDuration||0)+" "),e("span",{staticStyle:{"font-size":"12px"}},[t._v("小时")])]),e("div",{staticClass:"text"},[t._v("坐席总在线时长")])])])],1)],1)]),e("div",{staticClass:"chart-item"},[e("img",{staticClass:"block-border-top-left",attrs:{src:"/images/common/block-border-top-left.png"}}),e("img",{staticClass:"block-border-top-right",attrs:{src:"/images/common/block-border-top-right.png"}}),e("img",{staticClass:"block-border-bottom-left",attrs:{src:"/images/common/block-border-bottom-left.png"}}),e("img",{staticClass:"block-border-bottom-right",attrs:{src:"/images/common/block-border-bottom-right.png"}}),e("div",{staticClass:"title-wrap"},[e("div",{staticClass:"title"},[t._v("服务用户")])]),e("div",{staticClass:"chart",staticStyle:{height:"calc(100% - 150px)"}},[t.serviceUserObj.names&&t.serviceUserObj.names.length?e("ChartPie",{ref:"leftBottomPie",attrs:{data:t.serviceUserObj}}):t._e(),e("el-row",{staticClass:"service-user-column-wrap"},[e("el-col",{attrs:{span:8}},[e("div",{staticClass:"service-user-column"},[e("span",[t._v(t._s(t.allData.oneLevelAmount||0))])]),e("div",{staticClass:"service-user-column-text"},[t._v("一级守护")])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"service-user-column"},[e("span",[t._v(t._s(t.allData.twoLevelAmount||0))])]),e("div",{staticClass:"service-user-column-text"},[t._v("二级守护")])]),e("el-col",{attrs:{span:8}},[e("div",{staticClass:"service-user-column"},[e("span",[t._v(t._s(t.allData.threeLevelAmount||0))])]),e("div",{staticClass:"service-user-column-text"},[t._v("三级守护")])])],1)],1)])]),e("el-col",{staticClass:"column-flex",attrs:{span:12}},[e("div",{staticClass:"chart-item",staticStyle:{position:"relative",padding:"0"}},[e("div",{staticClass:"chart",staticStyle:{height:"calc(100%)",padding:"0"}},[e("div",{staticClass:"mask-stat"},[e("el-row",{staticClass:"mask-stat-wrap"},[e("el-col",{staticStyle:{"padding-left":"60px"},attrs:{span:8}},[e("div",{staticClass:"mask-stat-column-num"},[e("span",[t._v(t._s(t.allData.olderAmount||0))])]),e("div",{staticClass:"mask-stat-column-text"},[t._v("服务覆盖")]),e("div",{staticClass:"mask-stat-column-text"},[t._v("用户总数")])]),e("el-col",{staticStyle:{"padding-left":"60px"},attrs:{span:8}},[e("div",{staticClass:"mask-stat-column-num"},[e("span",[t._v(t._s(t.allData.older6080Amount||0))])]),e("div",{staticClass:"mask-stat-column-text"},[t._v("60-80岁")]),e("div",{staticClass:"mask-stat-column-text"},[t._v("用户总数 "),e("span",[t._v(t._s(t.allData.older6080Rate?100*t.allData.older6080Rate:0)+"%")])])]),e("el-col",{staticStyle:{"padding-left":"60px"},attrs:{span:8}},[e("div",{staticClass:"mask-stat-column-num"},[e("span",[t._v(t._s(t.allData.older80AboveAmount||0))])]),e("div",{staticClass:"mask-stat-column-text"},[t._v("80岁以上")]),e("div",{staticClass:"mask-stat-column-text"},[t._v("用户总数 "),e("span",[t._v(t._s(t.allData.older80AboveRate?100*t.allData.older80AboveRate:0)+"%")])])])],1)],1),e("baidu-map",{staticStyle:{height:"100%"},attrs:{center:t.center,zoom:t.zoom,"map-click":!1,"scroll-wheel-zoom":!0},on:{ready:t.handler,click:t.handleMapClick,moveend:t.x1,zoomend:t.x2,dragend:t.x3}},[t._l(t.dots,(function(a,s){return e("bm-marker",{key:a+s,attrs:{"z-index":5,position:{lng:a.longitude,lat:a.latitude},icon:t.dotHoverId===a.stationId?{url:t.orgImgs[a.orgId]+"-hover.png",size:{width:32,height:49}}:{url:t.orgImgs[a.orgId]+".png",size:{width:27,height:41}}},on:{mouseover:function(e){return t.handleDotMouseOver(a.stationId)},mouseout:t.handleDotMouseOut,click:function(e){return t.handleDotClick(e,a.stationId)}}},[e("BMapDot",{directives:[{name:"show",rawName:"v-show",value:t.dotHoverId===a.stationId,expression:"dotHoverId === dot.stationId"}],attrs:{position:{lng:a.longitude,lat:a.latitude},content:{userNum:a.olderAmount,butlerNum:a.chamberlainAmount}}})],1)})),t.dotSelectedlngLat&&t.dotSelectedlngLat.lat?e("bm-marker",{attrs:{"z-index":4,position:{lng:t.dotSelectedlngLat.lng,lat:t.dotSelectedlngLat.lat},icon:{url:"/images/home/<USER>",size:{width:92,height:92}},offset:{width:0,height:22}}}):t._e()],2)],1)])]),e("el-col",{staticClass:"column-flex",attrs:{span:6}},[e("div",{staticClass:"chart-item"},[e("img",{staticClass:"block-border-top-left",attrs:{src:"/images/common/block-border-top-left.png"}}),e("img",{staticClass:"block-border-top-right",attrs:{src:"/images/common/block-border-top-right.png"}}),e("img",{staticClass:"block-border-bottom-left",attrs:{src:"/images/common/block-border-bottom-left.png"}}),e("img",{staticClass:"block-border-bottom-right",attrs:{src:"/images/common/block-border-bottom-right.png"}}),e("div",{staticClass:"title-wrap"},[e("div",{staticClass:"title"},[t._v("安全事件")])]),e("div",{staticClass:"chart",staticStyle:{height:"calc(100% - 44px)"}},[e("div",{staticStyle:{height:"134px"}},[e("el-row",{staticClass:"safety-event"},[e("el-col",{staticStyle:{height:"calc(100%)"},attrs:{span:12}},[e("div",{staticClass:"safety-event-num"},[e("span",[t._v(t._s(t.allData.rtEventAmount||0))])]),e("div",{staticClass:"safety-event-name"},[t._v("实时安全事件")]),e("div",{staticClass:"safety-event-line"})]),e("el-col",{staticStyle:{height:"calc(100%)"},attrs:{span:12}},[e("div",{staticClass:"safety-event-num"},[e("span",[t._v(t._s(t.allData.hisEventAmount||0))])]),e("div",{staticClass:"safety-event-name"},[t._v("历史安全事件")]),e("div",{staticClass:"safety-event-line"})])],1)],1),e("el-row",{staticClass:"mask-stat-wrap",staticStyle:{height:"calc(100% - 154px)"}},[e("el-col",{staticStyle:{height:"calc(100%)"},attrs:{span:12}},[t.rtObj.names&&t.rtObj.names.length?e("ChartHorizontalBar",{ref:"rightBarOne",attrs:{data:t.rtObj,colors:["rgb(12,211,238,1)","rgb(0,121,254,1)"]}}):t._e()],1),e("el-col",{staticStyle:{height:"calc(100%)"},attrs:{span:12}},[t.hisObj.names&&t.hisObj.names.length?e("ChartHorizontalBar",{ref:"rightBarTwo",attrs:{data:t.hisObj,colors:["rgb(117,17,231,1)","rgb(166,120,255,1)"],"color-index":1}}):t._e()],1)],1)],1)]),e("div",{staticClass:"chart-item"},[e("img",{staticClass:"block-border-top-left",attrs:{src:"/images/common/block-border-top-left.png"}}),e("img",{staticClass:"block-border-top-right",attrs:{src:"/images/common/block-border-top-right.png"}}),e("img",{staticClass:"block-border-bottom-left",attrs:{src:"/images/common/block-border-bottom-left.png"}}),e("img",{staticClass:"block-border-bottom-right",attrs:{src:"/images/common/block-border-bottom-right.png"}}),e("div",{staticClass:"title-wrap"},[e("div",{staticClass:"title"},[t._v("设备情况")])]),e("div",{staticClass:"chart",staticStyle:{height:"calc(100% - 44px)","padding-top":"20px"}},[e("div",{staticClass:"inner-text"},[e("span",{staticClass:"text",staticStyle:{"font-size":"15px"}},[t._v("设备总数: ")]),e("span",{staticClass:"num"},[t._v(t._s(t.allData.deviceAmount||0))])]),e("el-row",{staticClass:"mask-stat-wrap",staticStyle:{height:"calc(100% - 120px)"}},[e("el-col",{staticStyle:{height:"calc(100%)"},attrs:{span:12}},[e("div",{staticClass:"device-text"},[t._v("室内")]),e("ChartPie1",{ref:"rightPieOne",attrs:{data:{value:t.allData.indoorOnlineRate?100*t.allData.indoorOnlineRate:30},colors:["#00bbf7","#00e9bb","#9183ff"],"sub-title":"在线比例"}}),e("div",{staticClass:"device-color-block"},[e("div",{staticClass:"pic left-online"}),t._v("在线"),e("span",{staticClass:"num"},[t._v(t._s(t.allData.indoorOnlineAmount||0))])]),e("div",{staticClass:"device-color-block"},[e("div",{staticClass:"pic"}),t._v("离线"),e("span",{staticClass:"num"},[t._v(t._s(t.allData.indoorOfflineAmount||0))])])],1),e("el-col",{staticStyle:{height:"calc(100%)"},attrs:{span:12}},[e("div",{staticClass:"device-text"},[t._v("室外")]),e("ChartPie1",{ref:"rightPieTwo",attrs:{data:{value:t.allData.outdoorOnlineRate?100*t.allData.outdoorOnlineRate:30},colors:["#8d48f7","#ad81f9","#6269de"],"sub-title":"在线比例"}}),e("div",{staticClass:"device-color-block"},[e("div",{staticClass:"pic right-online"}),t._v("在线"),e("span",{staticClass:"num"},[t._v(t._s(t.allData.outdoorOnlineAmount||0))])]),e("div",{staticClass:"device-color-block"},[e("div",{staticClass:"pic"}),t._v("离线"),e("span",{staticClass:"num"},[t._v(t._s(t.allData.outdoorOfflineAmount||0))])])],1)],1)],1)])])],1)],1)},l=[],i=(e("d81d"),e("7db0"),e("99af"),e("c9b7")),o={data:function(){return{active:!1,map:void 0,bmap:void 0,center:{lng:109.45744048529967,lat:36.49771311230842},zoom:13,trafficData:{},allData:{},dotSelectedName:void 0,rtObj:{},hisObj:{},serviceUserObj:{},dots:[],dotHoverId:void 0,dotSelectedlngLat:{},dotSelectedId:void 0,orgImgs:{}}},mounted:function(){this.fetchDatas(),this.fetchDotDatas()},methods:{draw:function(t){var a=t.el,e=t.BMap,s=t.map,l={lng:116.403092,lat:39.931078},i=l.lng,o=l.lat,n=s.pointToOverlayPixel(new e.Point(i,o));a.style.left=n.x-170+"px",a.style.top=n.y-70+"px"},handler:function(t){var a=t.BMap,e=t.map,s=new a.Point(116.403092,39.931078);e.centerAndZoom(s,13);var l={styleJson:[{featureType:"water",elementType:"all",stylers:{color:"#031628"}},{featureType:"land",elementType:"geometry",stylers:{color:"#000102"}},{featureType:"highway",elementType:"all",stylers:{visibility:"off"}},{featureType:"arterial",elementType:"geometry.fill",stylers:{color:"#000000"}},{featureType:"arterial",elementType:"geometry.stroke",stylers:{color:"#0b3d51"}},{featureType:"local",elementType:"geometry",stylers:{color:"#000000"}},{featureType:"railway",elementType:"geometry.fill",stylers:{color:"#000000"}},{featureType:"railway",elementType:"geometry.stroke",stylers:{color:"#08304b"}},{featureType:"subway",elementType:"labels.icon",stylers:{color:"#ff0000ff",visibility:"off"}},{featureType:"subway",elementType:"geometry",stylers:{lightness:-70}},{featureType:"building",elementType:"geometry.fill",stylers:{color:"#000000"}},{featureType:"all",elementType:"labels.text.fill",stylers:{color:"#0dd2ef"}},{featureType:"all",elementType:"labels.text.stroke",stylers:{color:"#000000"}},{featureType:"building",elementType:"geometry",stylers:{color:"#022338"}},{featureType:"green",elementType:"geometry",stylers:{color:"#062032"}},{featureType:"boundary",elementType:"all",stylers:{color:"#465b6c"}},{featureType:"manmade",elementType:"all",stylers:{visibility:"off"}},{featureType:"label",elementType:"all",stylers:{visibility:"off"}},{featureType:"poilabel",elementType:"labels.icon",stylers:{visibility:"off"}}]};e.setMapStyle(l),this.map=e,this.bmap=a},handleResetStation:function(){this.dotSelectedlngLat={},this.dotSelectedId=void 0,this.dotSelectedName=void 0,this.fetchDatas(void 0,"fetchMapStat")},handleMapClick:function(t){var a=t.overlay;console.log(333222),!a&&this.dotSelectedId&&(this.dotSelectedlngLat={},this.dotSelectedId=void 0,this.dotSelectedName=void 0,this.fetchDatas(void 0,"fetchMapStat"))},handleDotClick:function(t,a){var e=this;if(console.log(333111),a!=this.dotSelectedId){if(a){if(this.dots&&this.dots.length){var s=this.dots.find((function(t){return t.stationId===a}));s&&this.$nextTick((function(){e.dotSelectedId=a,e.dotSelectedName=s.stationName,e.dotSelectedlngLat={lng:s.longitude,lat:s.latitude}}))}this.fetchDatas(a)}}else this.handleMapClick()},fetchDatas:function(t,a){var e=this,s="/bms/bigscreen/selectBigScreenStatisticsByOp";t&&(s="/bms/bigscreen/selectBigScreenStatisticsByStation?stationId=".concat(t)),this.$api.get(s,{}).then((function(s){if("00000"===s.status&&s.data){(t||"fetchMapStat"===a)&&(s.data.olderAmount=e.allData.olderAmount,s.data.older6080Amount=e.allData.older6080Amount,s.data.older6080Rate=e.allData.older6080Rate,s.data.older80AboveAmount=e.allData.older80AboveAmount,s.data.older80AboveRate=e.allData.older80AboveRate),e.allData=s.data;var l=s.data,i=l.rtFallAmount,o=l.rtStagnationAmount,n=l.rtFallingBedAmount,r=l.rtActiveCallAmount,c=l.rtOutdoorsCallAmount,d={names:["跌倒","久滞","坠床","主动呼叫","户外呼叫"],values:[i,o,n,r,c]};e.rtObj=d;var m=s.data,v=m.hisFallAmount,g=m.hisStagnationAmount,u=m.hisFallingBedAmount,p=m.hisActiveCallAmount,h=m.hisOutdoorsCallAmount,b={names:["跌倒","久滞","坠床","主动呼叫","户外呼叫"],values:[v,g,u,p,h]};e.hisObj=b;var f=s.data,y=f.concentrateLivingAmount,C=f.aloneLivingAmount,_=f.noAloneLivingAmount,k=f.otherLivingAmount,A={names:["集中居住","独居","非独居","其他"],values:[y,C,_,k]};e.serviceUserObj=A,e.$nextTick((function(){e.$refs.leftBottomPie&&e.$refs.leftBottomPie.initChart(),e.$refs.rightBarOne&&e.$refs.rightBarOne.initChart(),e.$refs.rightBarTwo&&e.$refs.rightBarTwo.initChart(),e.$refs.rightPieOne&&e.$refs.rightPieOne.initChart(),e.$refs.rightPieTwo&&e.$refs.rightPieTwo.initChart()}))}}))},fetchDotDatas:function(){var t=this;this.$api.get("/bms/bigscreen/selectBigScreenStationList",{}).then((function(a){if("00000"===a.status&&a.data){var e=["icon-map-position-orange","icon-map-position-red","icon-map-position-blue","icon-map-position-purple"],s={};a.data.map((function(t){!s[t.orgId]&&e.length&&(s[t.orgId]="/images/home/"+e.shift())})),t.orgImgs=s,t.dots=a.data,setTimeout((function(){t.center={lng:a.data[0].longitude,lat:a.data[0].latitude}}),500)}}))},fetchMapStatDatas:function(t,a,e,s){var l=this;this.$api.get("/bms/bigscreen/selectBigScreenStatisticsByLongitudeLatitude?latitudeLeftUp=".concat(t,"&latitudeRightDown=").concat(a,"&longitudeLeftUp=").concat(e,"&longitudeRightDown=").concat(s),{}).then((function(t){"00000"===t.status&&t.data?(l.allData.older80AboveAmount=t.data.older80AboveAmount,l.allData.older80AboveRate=t.data.older80AboveRate,l.allData.older6080Amount=t.data.older6080Amount,l.allData.older6080Rate=t.data.older6080Rate,l.allData.olderAmount=t.data.olderAmount):(l.allData.older80AboveAmount=0,l.allData.older80AboveRate=0,l.allData.older6080Amount=0,l.allData.older6080Rate=0,l.allData.olderAmount=0)}))},x1:function(){this.getMapPos()},x2:function(){this.getMapPos()},x3:function(){this.getMapPos()},handleDotMouseOver:function(t){console.log(111,t),this.dotHoverId=t},handleDotMouseOut:function(){this.dotHoverId=void 0},getMapPos:Object(i["a"])((function(){if(this.map){var t=this.map.getBounds(),a=t.getSouthWest(),e=t.getNorthEast(),s=new BMap.Point(a.lng,e.lat),l=new BMap.Point(e.lng,a.lat);console.log("端点",s.lat,l.lat,s.lng,l.lng),this.fetchMapStatDatas(s.lat,l.lat,s.lng,l.lng)}}),500)}},n=o,r=(e("da5d"),e("db30"),e("2877")),c=Object(r["a"])(n,s,l,!1,null,"9136d8a2",null);a["default"]=c.exports},"8a61":function(t,a,e){},c9b7:function(t,a,e){"use strict";function s(t,a){var e,s=a||200;return function(){var a=this,l=arguments;e&&clearTimeout(e),e=setTimeout((function(){e=null,t.apply(a,l)}),s)}}e.d(a,"a",(function(){return s}))},da5d:function(t,a,e){"use strict";e("f464")},db30:function(t,a,e){"use strict";e("8a61")},f464:function(t,a,e){}}]);