(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["contact"],{1833:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"紧急联系人管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:""}},[a("el-input",{attrs:{placeholder:"根据姓名、手机号、地址搜索"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),a("el-form-item",{attrs:{label:"关系"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择关系"},model:{value:e.search.relationType,callback:function(t){e.$set(e.search,"relationType",t)},expression:"search.relationType"}},e._l(e.dict.relationType,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"contactAdd"})}}},[e._v("新增")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"contactName",label:"姓名",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"contactPhone",label:"手机号",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址",align:"center"}}),a("el-table-column",{attrs:{prop:"relationTypeDesc",label:"关系",width:"60",align:"center"}}),a("el-table-column",{attrs:{prop:"familyName",label:"所属家庭",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"memberName",label:"所属用户",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"contactEdit",params:{id:t.row.id}})}}},[e._v("修改")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return e.handleDelOperate(t.row.id)}}},[e._v("删除")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"contactInfo",params:{id:t.row.id}})}}},[e._v("查看")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},n=[],o=a("5530"),l=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),{name:"contactListIndex",data:function(){return{search:{keyword:void 0,name:void 0,phone:void 0,relationType:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}},dict:{relationType:[{code:"kinsman",name:"亲属"},{code:"neighbor",name:"邻居"},{code:"chanmb",name:"管家"},{code:"other",name:"其他"}]}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=Object(o["a"])(Object(o["a"])({},this.search),{},{pageReqVo:this.table.pageInfo});console.log(t),this.$api.post("/bms/contact/selectByPage",t).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleDelOperate:function(e){var t=this;e&&this.$confirm("您确定要删除该记录?","删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((function(){t.$api.get("/bms/contact/delete?id=".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"已经成功删除!"}),t.fetchDatas()):t.$message({type:"error",message:"删除失败"})}))})).catch((function(){t.$message({type:"error",message:"删除失败"})}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,butlerListIndex")}))},beforeRouteLeave:function(e,t,a){["butlerInfo"].includes(e.name)||(console.log("移除 EmptyLayout,butlerListIndex"),this.$store.commit("keepAlive/remove","EmptyLayout,butlerListIndex")),a()}}),i=l,c=(a("6179"),a("2877")),s=Object(c["a"])(i,r,n,!1,null,"2c2d2e20",null);t["default"]=s.exports},3591:function(e,t,a){},5337:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"详情查看"}}),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:12}},[e._v(" 姓名: "+e._s(e.vo.contactName||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 性别: "+e._s(e.vo.genderDesc||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 手机号: "+e._s(e.vo.contactPhone||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 关系: "+e._s(e.vo.relationTypeDesc||"")+" ")]),a("el-col",{attrs:{md:24}},[e._v(" 地址: "+e._s(e.vo.addr||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 经度: "+e._s(e.vo.longitude||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 纬度: "+e._s(e.vo.latitude||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 创建时间: "+e._s(e.vo.createTime||"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 修改时间: "+e._s(e.vo.modifyTime||"")+" ")])],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1)},n=[],o={name:"ContactInfo",data:function(){return{vo:{},status:"",table:{datas:[],total:0},processEvent:{status:""}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("bms/contact/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.vo=e.data)}))}}},l=o,i=a("2877"),c=Object(i["a"])(l,r,n,!1,null,null,null);t["default"]=c.exports},"54a9":function(e,t,a){},6179:function(e,t,a){"use strict";a("3591")},b165:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"紧急联系人"}}),a("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[a("page-main",{attrs:{title:"基本信息"}},[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[e.$route.params.id?e._e():a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"所属用户",prop:"memberId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{clear:e.handleMemberClear},model:{value:e.reqForm.memberId,callback:function(t){e.$set(e.reqForm,"memberId",t)},expression:"reqForm.memberId"}},e._l(e.dict.memberList,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"姓名",prop:"contactName"}},[a("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入名称"},model:{value:e.reqForm.contactName,callback:function(t){e.$set(e.reqForm,"contactName",t)},expression:"reqForm.contactName"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"性别",prop:"gender"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择性别"},model:{value:e.reqForm.gender,callback:function(t){e.$set(e.reqForm,"gender",t)},expression:"reqForm.gender"}},e._l(e.dict.genders,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"手机号",prop:"contactPhone"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入手机号"},model:{value:e.reqForm.contactPhone,callback:function(t){e.$set(e.reqForm,"contactPhone",t)},expression:"reqForm.contactPhone"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"关系",prop:"relationType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择关系"},model:{value:e.reqForm.relationType,callback:function(t){e.$set(e.reqForm,"relationType",t)},expression:"reqForm.relationType"}},e._l(e.dict.relationType,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[a("el-input",{attrs:{type:"number",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入经度"},model:{value:e.reqForm.longitude,callback:function(t){e.$set(e.reqForm,"longitude",t)},expression:"reqForm.longitude"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[a("el-input",{attrs:{type:"number",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入纬度"},model:{value:e.reqForm.latitude,callback:function(t){e.$set(e.reqForm,"latitude",t)},expression:"reqForm.latitude"}})],1)],1),a("el-col",{attrs:{md:16}},[a("el-form-item",{attrs:{label:"地址",prop:"addr"}},[a("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入地址"},model:{value:e.reqForm.addr,callback:function(t){e.$set(e.reqForm,"addr",t)},expression:"reqForm.addr"}})],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{staticStyle:{"margin-top":"20px"}},[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},n=[],o=a("1da1"),l=(a("96cf"),a("e1a4")),i={data:function(){var e=function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t,a,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a?Object(l["b"])(a)||r(new Error("请输入正确的11位手机号码")):r(new Error("请输入手机号")),r();case 2:case"end":return e.stop()}}),e)})));return function(t,a,r){return e.apply(this,arguments)}}(),t=function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t,a,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a?Object(l["e"])(a)||r(new Error("姓名只支持中/英文，中文2-10个字，英文50字符")):r(new Error("请输入姓名")),r();case 2:case"end":return e.stop()}}),e)})));return function(t,a,r){return e.apply(this,arguments)}}();return{dict:{memberList:[],genders:[{code:"M",name:"男"},{code:"W",name:"女"}],relationType:[{code:"kinsman",name:"亲属"},{code:"neighbor",name:"邻居"},{code:"chanmb",name:"管家"},{code:"other",name:"其他"}]},reqForm:{id:void 0,name:void 0,addr:void 0,houseNumber:void 0},submitLoading:!1,fullLoading:void 0,reqFormRules:{memberId:[{required:!0,trigger:"change",message:"请选择"}],contactName:[{required:!0,trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],contactPhone:[{required:!0,trigger:"blur",validator:e,getValuesMethod:this.getValuesMethod}]}}},mounted:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.handleFetchMember();case 2:e.fetchDetail(e.$route.params.id);case 3:case"end":return t.stop()}}),t)})))()},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("bms/contact/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data),t.fullLoading.close()})).catch((function(e){console.log(e),t.fullLoading.close()})))},handleFetchMember:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get("/bms/memberinfo/listByStation",{}).then((function(t){"00000"===t.status&&t.data?e.dict.memberList=t.data:e.dict.memberList=[]}));case 1:case"end":return t.stop()}}),t)})))()},handleMemberClear:function(){this.reqForm.memberId=void 0},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var a=JSON.parse(JSON.stringify(t.reqForm));t.$api.post("/bms/contact/save",a).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},c=i,s=(a("d5ce"),a("2877")),u=Object(s["a"])(c,r,n,!1,null,"134d10c4",null);t["default"]=u.exports},d5ce:function(e,t,a){"use strict";a("54a9")},e1a4:function(e,t,a){"use strict";function r(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function n(e){var t=/^\d{11}$/;return t.test(e)}function o(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function l(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function i(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function c(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function s(e){return c(e)||i(e)}a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return s}))}}]);