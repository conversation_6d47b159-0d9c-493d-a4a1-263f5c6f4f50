(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["daily"],{c6d9:function(t,e,s){"use strict";s("d93e")},d93e:function(t,e,s){},df7f:function(t,e,s){"use strict";s.r(e);var a=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}]},[1==t.dataStatus?s("div",{staticClass:"main"},[s("div",{staticClass:"card"},[s("div",{staticClass:"flex-col items-center"},[t._m(0),s("div",{staticClass:"notice"},[t._v(" 注意：报告仅作为参考，请勿用于医学证明。 ")]),s("div",{staticClass:"flex-row justify-between"},[s("div",[s("span",{staticClass:"info-label"},[t._v("设备：")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.info.devCode))])]),s("div",[s("span",{staticClass:"info-label"},[t._v("报告日期：")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.info.reportDate))])]),t.info.reportStatStartTime?s("div",[s("span",{staticClass:"info-label"},[t._v("统计周期：")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.getYYYYMMDDHHMM(t.info.reportStatStartTime)))]),t._v(" 至 "),s("span",{staticClass:"info-value"},[t._v(t._s(t.getYYYYMMDDHHMM(t.info.reportStatEndTime)))])]):t._e()])])]),s("div",{staticClass:"card"},[s("div",{staticClass:"flex-row justify-around"},[s("div",{staticClass:"flex-col"},[s("span",{staticClass:"item-label"},[t._v("今日告警")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.info.eventCount||0))])]),s("div",{staticClass:"flex-col"},[s("span",{staticClass:"item-label"},[t._v("跌倒数")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.info.fallEventCount||0))])]),s("div",{staticClass:"flex-col"},[s("span",{staticClass:"item-label"},[t._v("久滞数")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.info.longlagEventCount||0))])]),s("div",{staticClass:"flex-col"},[s("span",{staticClass:"item-label"},[t._v("已处理数")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.info.hadHandleEventCount||0))])]),s("div",{staticClass:"flex-col"},[s("span",{staticClass:"item-label"},[t._v("待处理数")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.info.noHandleEventCount||0))])]),s("div",{staticClass:"flex-col"},[s("span",{staticClass:"item-label"},[t._v("延期处理数")]),s("span",{staticClass:"item-value"},[t._v(t._s(t.info.delayHandleEventCount||0))])])])]),s("div",{staticClass:"card"},[s("div",{staticClass:"flex-row items-center"},[s("div",{staticClass:"flex-col items-center sleep-score"},[s("div",[s("span",{staticClass:"sleep-score-num"},[t._v(t._s(t.info.sleepMetrics.sleepScore||"--"))]),s("span",{staticClass:"sleep-score-unit"},[t._v("分")])]),s("div",{staticClass:"flex-col items-center sleep-tag",style:{background:t.getColor(t.info.sleepMetrics.sleepScore>=80)}},[t.info.sleepMetrics.sleepScore<60?s("span",{staticClass:"sleep-tag-text"},[t._v("差")]):t.info.sleepMetrics.sleepScore>=60&&t.info.sleepMetrics.sleepScore<80?s("span",{staticClass:"sleep-tag-text"},[t._v("一般")]):t.info.sleepMetrics.sleepScore>=80&&t.info.sleepMetrics.sleepScore<90?s("span",{staticClass:"sleep-tag-text"},[t._v("良好")]):s("span",{staticClass:"sleep-tag-text"},[t._v("优秀")])])]),s("div",{staticClass:"flex-col sleep-data"},[s("div",{staticClass:"flex-row justify-around"},[s("div",{staticClass:"sleep-data-item"},[s("span",{staticClass:"sleep-data-label"},[t._v("在床时间：")]),s("span",{staticClass:"sleep-data-value"},[t._v(t._s(t.getHHMM(t.info.sleepMetrics.nightInbedEarliestTime)||t.getHHMM(t.info.reportStatStartTime)+"前"))])]),s("div",{staticClass:"sleep-data-item"},[s("span",{staticClass:"sleep-data-label"},[t._v("入睡时间：")]),s("span",{staticClass:"sleep-data-value"},[t._v(t._s(t.getHHMM(t.info.sleepMetrics.sleepTime)||"--:--"))])])]),s("div",{staticClass:"flex-row justify-around"},[s("div",{staticClass:"sleep-data-item"}),s("div",{staticClass:"sleep-data-item"},[s("span",{staticClass:"sleep-data-warn",style:{visibility:t.showSleepWarnText()?"visible":"hidden"}},[t._v("建议23:00前入睡")])])]),s("div",{staticClass:"flex-row justify-around"},[s("div",{staticClass:"sleep-data-item"},[s("span",{staticClass:"sleep-data-label"},[t._v("清醒时间：")]),s("span",{staticClass:"sleep-data-value"},[t._v(t._s(t.getHHMM(t.info.sleepMetrics.awakeTime)||"--:--"))])]),s("div",{staticClass:"sleep-data-item"},[s("span",{staticClass:"sleep-data-label"},[t._v("起床时间：")]),s("span",{staticClass:"sleep-data-value"},[t._v(t._s(t.getHHMM(t.info.sleepMetrics.dayOutBedEarliestTime)||t.getHHMM(t.info.reportStatEndTime)+"后"))])])])])])]),s("div",{staticClass:"card sleep-cycle-card flex-col"},[t._m(1),t.info.sleepMetrics.sleepCycleList&&t.info.sleepMetrics.sleepCycleList.length?s("RectChart",{attrs:{datas:t.info.sleepMetrics.sleepCycleList}}):t._e(),s("div",{staticClass:"flex-col sleep-chart-bottom"},[s("div",{staticClass:"flex-row justify-between"},[s("span",{staticClass:"time"},[t._v("在床"+t._s(t.getHHMM(t.info.sleepMetrics.nightInbedEarliestTime)||t.getHHMM(t.info.reportStatStartTime)+"前"))]),s("span",{staticClass:"time"},[t._v("起床"+t._s(t.getHHMM(t.info.sleepMetrics.dayOutBedEarliestTime)||t.getHHMM(t.info.reportStatEndTime)+"后"))])]),s("div",{staticClass:"flex-row justify-between"},[s("span",{staticClass:"date"},[t._v(t._s(t.convertToMonthDayFormat(t.getPreviousDay(t.info.reportDate))))]),s("span",{staticClass:"date"},[t._v(t._s(t.convertToMonthDayFormat(t.info.reportDate)))])])]),s("div",{staticClass:"sleep-time-count"},[s("div",{staticClass:"flex-row justify-around"},[s("div",{staticClass:"flex-col sleep-time-item"},[s("span",{staticClass:"title color1"},[t._v("清醒")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.formatSecond2String(60*t.info.sleepMetrics.awakeDuration)||"--"))])]),s("div",{staticClass:"flex-col sleep-time-item"},[s("span",{staticClass:"title color2"},[t._v("离床")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.formatSecond2String(60*t.info.sleepMetrics.outOfBedDuration)||"--"))])]),s("div",{staticClass:"flex-col sleep-time-item"},[s("span",{staticClass:"title color3"},[t._v("浅睡")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.formatSecond2String(60*t.info.sleepMetrics.lightSleepDuration)||"--"))])]),s("div",{staticClass:"flex-col sleep-time-item"},[s("span",{staticClass:"title color4"},[t._v("深睡")]),s("span",{staticClass:"info-value"},[t._v(t._s(t.formatSecond2String(60*t.info.sleepMetrics.deepSleepDuration)||"--"))])])])]),s("div",{staticClass:"flex-col gray-card"},[s("span",{staticClass:"title"},[t._v("睡眠周期"),s("span",{style:{color:t.getColor(t.info.sleepMetrics.sleepCycleCount>=3&&t.info.sleepMetrics.sleepCycleCount<=6)}},[t._v(t._s(t.info.sleepMetrics.sleepCycleCount||0)+"次")])]),s("span",{staticClass:"descr"},[t._v("参考值：3~6次")]),t.info.sleepMetrics.sleepCycleCount<3?s("div",{staticClass:"flex-col item-warn"},[t._m(2),s("span",{staticClass:"warn-advise"},[t._v(" 尽量每天在相同的时间上床睡觉和起床，避免睡前使用电子设备；保持安静、黑暗和凉爽的睡眠环境，使用舒适的床上用品；限制咖啡因、尼古丁和酒精的摄入。 ")])]):t._e(),t.info.sleepMetrics.sleepCycleCount>6?s("div",{staticClass:"flex-col item-warn"},[t._m(3),s("span",{staticClass:"warn-advise"},[t._v(" 每天在相同的时间起床，不睡懒觉，避免在临近睡觉的时候进食，也不要空腹睡觉；多接触阳光有助于调整生物钟。 ")])]):t._e()]),s("div",{staticClass:"flex-col sleep-cycle-item"},[s("span",{staticClass:"title"},[t._v("睡眠时长"),s("span",{style:{color:t.getColor(t.info.sleepMetrics.sleepDuration/60>=6&&t.info.sleepMetrics.sleepDuration/60<=10)}},[t._v(t._s(t.formatSecond2String(60*t.info.sleepMetrics.sleepDuration)||"--"))])]),s("span",{staticClass:"descr"},[t._v("参考值：6~10小时")]),t.info.sleepMetrics.sleepDuration/60<6?s("div",{staticClass:"flex-col item-warn"},[t._m(4),s("span",{staticClass:"warn-advise"},[t._v(" 请您要建立规律的作息时间表，尽量每天都在相同的时间入睡和起床。 ")])]):t._e(),t.info.sleepMetrics.sleepDuration/60>10?s("div",{staticClass:"flex-col item-warn"},[t._m(5),s("span",{staticClass:"warn-advise"},[t._v(" 保存规律的作息时间，不要睡懒觉，建议午睡时间在30分钟内，建立良好的睡前习惯。 ")])]):t._e()]),s("LineDivider",{attrs:{direction:"horizontal",lineWidth:"1px",lineColor:"#eee",lineLength:"100%"}}),s("div",{staticClass:"flex-col sleep-cycle-item"},[s("span",{staticClass:"title"},[t._v("清醒时长 "),s("span",{style:{color:t.getColor(t.info.sleepMetrics.awakeDurationRatio<.35)}},[t._v(" "+t._s(t.roundToDecimal(100*t.info.sleepMetrics.awakeDurationRatio,0)||0)+"% ")])]),s("span",{staticClass:"descr"},[t._v("参考值：35%")]),t.info.sleepMetrics.awakeDurationRatio>=.35?s("div",{staticClass:"flex-col item-warn"},[t._m(6),s("span",{staticClass:"warn-advise"},[t._v(" 确保睡眠环境安静、黑暗且舒适，减少噪音和光线的干扰；避免咖啡因、尼古丁和究竟的摄入；压力、焦虑或抑郁等心理问题可能导致睡眠问题，如有需要请寻求专业帮助。 ")])]):t._e()]),s("LineDivider",{attrs:{direction:"horizontal",lineWidth:"1px",lineColor:"#eee",lineLength:"100%"}}),s("div",{staticClass:"flex-col sleep-cycle-item"},[s("span",{staticClass:"title"},[t._v("浅睡时长 "),s("span",{style:{color:t.getColor(t.info.sleepMetrics.lightSleepDurationRatio>=.5&&t.info.sleepMetrics.lightSleepDurationRatio<=.6)}},[t._v(" "+t._s(t.roundToDecimal(100*t.info.sleepMetrics.lightSleepDurationRatio,0)||0)+"%")])]),s("span",{staticClass:"descr"},[t._v("参考值：50%~60%")]),t.info.sleepMetrics.lightSleepDurationRatio<.5?s("div",{staticClass:"flex-col item-warn"},[t._m(7),s("span",{staticClass:"warn-advise"},[t._v(" 在睡前进行一些放松活动，如阅读、瑜伽或深呼吸练习。 ")])]):t._e(),t.info.sleepMetrics.lightSleepDurationRatio>.5?s("div",{staticClass:"flex-col item-warn"},[t._m(8),s("span",{staticClass:"warn-advise"},[t._v(" 创建舒适的睡眠环境，使用舒适的床上用品，保持充足的运动和户外活动可以帮助改善睡眠质量。 ")])]):t._e()]),s("LineDivider",{attrs:{direction:"horizontal",lineWidth:"1px",lineColor:"#eee",lineLength:"100%"}}),s("div",{staticClass:"flex-col sleep-cycle-item"},[s("span",{staticClass:"title"},[t._v("深睡时长 "),s("span",{style:{color:t.getColor(t.info.sleepMetrics.deepSleepDurationRatio>=.15&&t.info.sleepMetrics.deepSleepDurationRatio<=.3)}},[t._v(" "+t._s(t.roundToDecimal(100*t.info.sleepMetrics.deepSleepDurationRatio,0)||0)+"% ")])]),s("span",{staticClass:"descr"},[t._v("参考值：15%~30%")]),t.info.sleepMetrics.deepSleepDurationRatio<.15?s("div",{staticClass:"flex-col item-warn"},[t._m(9),s("span",{staticClass:"warn-advise"},[t._v(" 每天尽量在相同的时间上床睡觉和起床，保持舒适的睡眠环境，减少噪音；考虑心理因素，如有需要请寻求专业帮助。 ")])]):t._e(),t.info.sleepMetrics.deepSleepDurationRatio>.3?s("div",{staticClass:"flex-col item-warn"},[t._m(10),s("span",{staticClass:"warn-advise"},[t._v(" 每天尽量在相同的时间上床睡觉和起床，保持舒适的睡眠环境，减少噪音。 ")])]):t._e()]),s("LineDivider",{attrs:{direction:"horizontal",lineWidth:"1px",lineColor:"#eee",lineLength:"100%"}}),s("div",{staticClass:"flex-col sleep-cycle-item"},[s("span",{staticClass:"title"},[t._v("入睡时长"),s("span",{style:{color:t.getColor(t.info.sleepMetrics.sleepOnsetDuration<30)}},[t._v(t._s(t.info.sleepMetrics.sleepOnsetDuration||0)+"分")])]),s("span",{staticClass:"descr"},[t._v("参考值：＜30分钟")]),t.info.sleepMetrics.sleepOnsetDuration>=30?s("div",{staticClass:"flex-col item-warn"},[t._m(11),s("span",{staticClass:"warn-advise"},[t._v(" 有入睡困难的风险，可在睡前进行一些放松活动，如阅读、瑜伽或深呼吸练习；尝试播放白噪音，有助于放松身心，更快入睡。 ")])]):t._e()]),s("LineDivider",{attrs:{direction:"horizontal",lineWidth:"1px",lineColor:"#eee",lineLength:"100%"}}),s("div",{staticClass:"flex-col sleep-cycle-item"},[s("span",{staticClass:"title"},[t._v("睡眠效率 "),s("span",{style:{color:t.getColor(t.info.sleepMetrics.sleepEfficiency>=.85)}},[t._v(" "+t._s(t.roundToDecimal(100*t.info.sleepMetrics.sleepEfficiency,0)||0)+"% ")])]),s("span",{staticClass:"descr"},[t._v("参考值：≥85%")]),t.info.sleepMetrics.sleepEfficiency<=.85?s("div",{staticClass:"flex-col item-warn"},[t._m(12),s("span",{staticClass:"warn-advise"},[t._v(" 睡眠效率不佳可能会导致您在睡觉时感觉没有得到充分的休息，请保持规律的作息时间，创建舒适的睡眠环境，养成良好的睡眠习惯。 ")])]):t._e()])],1),s("div",{staticClass:"card body-move-card flex-col"},[s("div",{staticClass:"flex-row items-center"},[s("span",{staticClass:"card-icon iconfont icon-zaichuangshijian"}),s("span",{staticClass:"care-title"},[t._v("体动")]),t.info.sleepMetrics.motionCount>=75&&t.info.sleepMetrics.motionCount<=175?s("el-tag",{attrs:{size:"mini",color:t.getColor(!0),effect:"dark"}},[t._v("正常")]):t.info.sleepMetrics.motionCount<75?s("el-tag",{attrs:{size:"mini",color:t.getColor(!1),effect:"dark"}},[t._v("偏低")]):t.info.sleepMetrics.motionCount>175?s("el-tag",{attrs:{size:"mini",color:t.getColor(!1),effect:"dark"}},[t._v("偏高")]):t._e()],1),s("div",{staticClass:"flex-row justify-between"},[s("span",{staticClass:"reference-value"},[t._v("参考值：75~175次")]),s("span",[t._v("共"+t._s(t.info.sleepMetrics.motionCount||0)+"次")])]),t.info.sleepMetrics.motionList&&t.info.sleepMetrics.motionList.length?s("BarChat",{attrs:{datas:t.info.sleepMetrics.motionList}}):t._e()],1),s("div",{staticClass:"card heart-card flex-col"},[t._m(13),s("span",{staticClass:"reference-value"},[t._v("最低"+t._s(t.heartLinesMin||0)+"次/分钟，平均"+t._s(t.heartLinesAvg||0)+"次/分钟，最高"+t._s(t.heartLinesMax||0)+"次/分钟")]),t.heartLines&&t.heartLines.length?s("LineChart",{attrs:{"line-color":"#20CD8A",datas:t.heartLines,title:"心率",markLines:[100,60,20],max:130}}):t._e(),s("div",{staticClass:"flex-col gray-card"},[s("span",{staticClass:"text"},[t._v("当次睡眠基准心率"),s("span",{style:{color:t.getColor(t.info.benchmarkHeartRate>55&&t.info.benchmarkHeartRate<65)}},[t._v(t._s(t.info.benchmarkHeartRate||0)+"次/分")])]),t.info.benchmarkHeartRate<55?s("div",{staticClass:"flex-col item-warn"},[t._m(14),s("span",{staticClass:"warn-advise"},[t._v(" 如果您正在服用药物，建议咨询医生是否需要调整剂量或更换药物；心率过低且伴有其他症状，如头晕、气短等，建议及时就医，以排除心脏疾病的可能。 ")])]):t._e(),t.info.benchmarkHeartRate>65?s("div",{staticClass:"flex-col item-warn"},[t._m(15),s("span",{staticClass:"warn-advise"},[t._v(" 减少咖啡因、尼古丁和酒精的摄入，心率过高且伴有其他症状，如心悸、气短等，建议及时就医，以排除心脏疾病的可能。 ")])]):t._e()]),s("div",[s("div",{staticClass:"heart-chart-title"},[t._v("近两周心率数据对比")]),s("div",{staticStyle:{"margin-top":"10px"}}),t.heartLines14days&&t.heartLines14days.length?s("HbLineChart",{attrs:{legendData:t.heartLegendData14days,xAxisDatas:t.xAxisDatas14days,seriesDatas:t.heartLines14days}}):t._e(),t._m(16)],1)],1),s("div",{staticClass:"card breathe-card flex-col"},[t._m(17),s("span",{staticClass:"reference-value"},[t._v("最低"+t._s(t.breathLinesMin||0)+"次/分钟，平均"+t._s(t.breathLinesAvg||0)+"次/分钟，最高"+t._s(t.breathLinesMax||0)+"次/分钟")]),t.breathLines&&t.breathLines.length?s("LineChart",{attrs:{"line-color":"#20CD8A",datas:t.breathLines,title:"呼吸",markLines:[20,12,1],max:28}}):t._e(),s("div",[s("div",{staticClass:"heart-chart-title"},[t._v("近两周呼吸数据对比")]),s("div",{staticStyle:{"margin-top":"10px"}}),t.breathLines14days&&t.breathLines14days.length?s("HbLineChart",{attrs:{legendData:t.breathLegendData14days,xAxisDatas:t.xAxisDatas14days,seriesDatas:t.breathLines14days}}):t._e(),t._m(18)],1)],1)]):2==t.dataStatus?s("div",{staticStyle:{"margin-top":"64px"}},[s("el-result",{attrs:{icon:"info",title:"温馨提示",subTitle:"当日没有报告"}})],1):t._e()])},i=[function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row justify-center items-center"},[s("span",{staticClass:"title"},[t._v("睡眠报告")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row items-center"},[s("span",{staticClass:"card-icon iconfont icon-chuang",staticStyle:{"font-size":"32px"}}),s("span",{staticClass:"care-title"},[t._v("睡眠周期")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("睡眠周期")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("睡眠周期过多")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("睡眠时长缺乏")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("睡眠时长过长")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("清醒时长过长")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("浅睡时长过短")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("浅睡时长过长")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("深睡时长过短")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("深睡时长过长")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("入睡时较慢")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("睡眠效率低")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row items-center"},[s("span",{staticClass:"card-icon iconfont icon-xinshuaijiance"}),s("span",{staticClass:"care-title"},[t._v("心率")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("建议信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("建议信息")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-col item-warn"},[s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("近两周心率数据对比结果")])]),s("span",{staticClass:"warn-advise"},[t._v(" 考虑个人的年龄、身体状况和运动水平等因素，正常心率范围会有所不同，如出现呼吸困难、疲劳、胸痛等症状，请及时就医。 ")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-row items-center"},[s("span",{staticClass:"card-icon iconfont icon-huxijiance"}),s("span",{staticClass:"care-title"},[t._v("呼吸")])])},function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"flex-col item-warn"},[s("div",{staticClass:"flex-row"},[s("span",{staticClass:"icon iconfont icon-caozuorizhi warn-title"}),s("span",{staticClass:"warn-title"},[t._v("近两周呼吸数据对比结果")])]),s("span",{staticClass:"warn-advise"},[t._v(" 在安静状态下，正常成人的呼吸频率为12~20次/分，如出现呼吸困难、持续的呼吸增快或减缓、胸痛头晕等症状，请及时就医。 ")])])}],n=(s("99af"),s("d81d"),s("ca00")),l=s("e702"),r=s("f9e6"),o=s("4242"),c=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("view",[s("div",{ref:"chart",staticStyle:{width:"100%",height:"100%"}})])},p=[],d=(s("a9e3"),s("b0c0"),s("cb29"),s("313e")),f={name:"ExceptionChart",props:{leftOffset:{type:String||Number,default:"130"},lineColor:{type:String,default:"#01B09A"},times:{type:Array,default:function(){return[]}}},data:function(){return{chart:null,option:{tooltip:{show:!0,trigger:"axis",formatter:function(t){for(var e="",s=0;s<t.length;s++)e+="".concat(t[s].name);return e},textStyle:{fontSize:12}},grid:{top:"5%",right:"3%",left:this.leftOffset,bottom:"0%",containLabel:!0},xAxis:[{type:"category",data:this.times||[],axisLine:{lineStyle:{width:3,color:"#26ded3"}},axisLabel:{},axisTick:{show:!1}}],yAxis:[{axisLabel:{show:!1},axisLine:{show:!0,lineStyle:{width:3,color:"#26ded3"}},splitLine:{show:!1},axisTick:{show:!1}}],series:[{type:"bar",data:this.times.length?Array(this.times.length).fill(1):[],barWidth:"3rpx",itemStyle:{color:"#F66C3E"},markLine:{symbol:"none",data:[{yAxis:1500,lineStyle:{color:"#aac1ff"},label:{normal:{show:!1}}}]}}]}}},mounted:function(){var t=this;this.initChart(),window.addEventListener("resize",(function(){t.chart.resize()}))},methods:{initChart:function(){this.chart=d.init(this.$refs.chart),this.chart.setOption(this.option)}}},h=f,v=s("2877"),u=Object(v["a"])(h,c,p,!1,null,null,null),C=u.exports,_=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{ref:"chart",staticStyle:{width:"100%",height:"300px"}})},m=[],x=s("313e"),w={name:"HbLineChart",props:{xAxisDatas:{type:Array,default:function(){return[]}},seriesDatas:{type:Array,default:function(){return[]}},legendData:{type:Array,default:function(){return[]}}},data:function(){return{chart:null,option:{title:{show:!1,textStyle:{color:"gray",fontSize:13},text:"暂无数据",left:"center",top:"center"},tooltip:{trigger:"axis"},legend:{data:[]},grid:{top:20,bottom:20},toolbox:{feature:{saveAsImage:!1}},xAxis:{type:"category",boundaryGap:!1,data:[]},yAxis:{type:"value"},series:[]}}},mounted:function(){var t=this;this.initChart(),window.addEventListener("resize",(function(){t.chart.resize()}))},methods:{initChart:function(){this.legendData.length<1||this.xAxisDatas.length<1||this.seriesDatas.length<1?this.option.title.show=!0:(this.option.title.show=!1,this.option.legend.data=this.legendData,this.option.xAxis.data=this.xAxisDatas,this.option.series=this.seriesDatas),this.chart=x.init(this.$refs.chart),this.chart.setOption(this.option)}}},g=w,y=Object(v["a"])(g,_,m,!1,null,null,null),M=y.exports,L=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{ref:"chart",staticStyle:{width:"100%",height:"300px"}})},b=[],D=s("313e"),S={name:"BarChat",props:{datas:{type:Array,default:function(){return[]}}},data:function(){return{chart:null,option:{title:{show:!1,textStyle:{color:"gray",fontSize:13},text:"暂无数据",left:"center",top:"center"},tooltip:{show:!0},color:["#20CD8A"],grid:{top:10,bottom:2},xAxis:{type:"category",axisTick:{show:!1},data:[]},yAxis:{show:!1},series:[{data:[],type:"bar"}]}}},mounted:function(){var t=this;this.initChart(),window.addEventListener("resize",(function(){t.chart.resize()}))},methods:{initChart:function(){!this.datas||this.datas<1?this.option.title.show=!0:(this.option.series[0].data=this.datas.map((function(t){return t.correctedValue})),this.option.xAxis.data=this.datas.map((function(t){return t.createDate}))),this.chart=D.init(this.$refs.chart),this.chart.setOption(this.option)}}},z=S,E=Object(v["a"])(z,L,b,!1,null,null,null),H=E.exports,T={name:"sleepDaily",components:{LineChart:l["default"],ExceptionChart:C,LineDivider:o["default"],RectChart:r["default"],BarChat:H,HbLineChart:M},data:function(){return{dataStatus:0,loading:!0,info:{sleepMetrics:{}},heartLines:[],breathLines:[],heartSlowTimes:[],heartFastTimes:[],breathSlowTimes:[],breathFastTimes:[],heartLinesMax:0,heartLinesMin:0,heartLinesAvg:0,breathLinesMax:0,breathLinesMin:0,breathLinesAvg:0,xAxisDatas14days:[],breathLines14days:[],heartLines14days:[],heartLegendData14days:["最低心率","最高心率","基准心率"],breathLegendData14days:["最低呼吸","最高呼吸"]}},created:function(){var t=this.$route.query,e=t.id,s=t.date;this.fetchDatas(e,s)},methods:{getSFM:n["g"],formatSecond2String:n["e"],convertToMonthDayFormat:n["c"],roundToDecimal:n["h"],getPreviousDay:n["f"],calculateStats:n["a"],getColor:function(t){return t?"#20CD8A":"#FF7D17"},getHHMM:function(t){return t?t.substring(11,16):t},getYYYYMMDDHHMM:function(t){return t?t.substring(0,16):t},showSleepWarnText:function(){var t=new Date(this.info.sleepMetrics.sleepTime),e=new Date(this.getPreviousDay(this.info.reportDate)+" 23:00:00");return console.log("sleepTime:"+t),console.log("pre23:"+e),t>e},fetchDatas:function(t,e){if(t&&e){var s=this;this.$api.get("/bms/report/getReport?devId=".concat(t,"&date=").concat(e),{}).then((function(t){s.loading=!1,"00000"===t.status&&t.data&&(s.info=t.data,s.dataStatus=1,s.handler(s.info))})).catch((function(t){console.log(t),s.dataStatus=2,s.loading=!1}))}else this.loading=!1},handler:function(t){var e=(t.heartVOList||[]).map((function(t){return[t.dateTime,t.heart||0]})),s=(t.breathVOList||[]).map((function(t){return[t.dateTime,t.breath||0]}));this.heartSlowTimes=t.heartSlowMomentList,this.heartFastTimes=t.heartFastMomentList,this.breathSlowTimes=t.breathSlowMomentList,this.breathFastTimes=t.breathFastMomentList,this.heartLines=e,this.breathLines=s;var a=this.calculateStats(this.heartLines,1);this.heartLinesAvg=a.average||0,this.heartLinesMax=a.max||0,this.heartLinesMin=a.min||0;var i=this.calculateStats(this.breathLines,1);this.breathLinesAvg=i.average||0,this.breathLinesMax=i.max||0,this.breathLinesMin=i.min||0,this.xAxisDatas14days=(t.sleepMetrics.heartBreathList||[]).map((function(t){return[t.reportDate.substring(5)]}));var n=(t.sleepMetrics.heartBreathList||[]).map((function(t){return t.benchmarkHeartRate||0})),l=(t.sleepMetrics.heartBreathList||[]).map((function(t){return t.maxHeart||0})),r=(t.sleepMetrics.heartBreathList||[]).map((function(t){return t.minHeart||0})),o=(t.sleepMetrics.heartBreathList||[]).map((function(t){return t.maxBreath||0})),c=(t.sleepMetrics.heartBreathList||[]).map((function(t){return t.minBreath||0}));this.heartLines14days=[{name:this.heartLegendData14days[0],type:"line",data:r},{name:this.heartLegendData14days[1],type:"line",data:l},{name:this.heartLegendData14days[2],type:"line",data:n}],this.breathLines14days=[{name:this.breathLegendData14days[0],type:"line",data:c},{name:this.breathLegendData14days[1],type:"line",data:o}]}}},A=T,$=(s("c6d9"),Object(v["a"])(A,a,i,!1,null,"6d66b7b3",null));e["default"]=$.exports}}]);