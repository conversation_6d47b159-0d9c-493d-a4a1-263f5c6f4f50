(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["older"],{2565:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("page-header",{attrs:{title:"被监护人管理"}}),r("page-main",[r("div",{staticClass:"table-search"},[r("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[r("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:""}},[r("el-input",{attrs:{placeholder:"根据姓名、手机号搜索"},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.preventDefault(),e.handleSearch(t))}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),r("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[r("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),r("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),r("div",{staticClass:"page-oper-wrap"},[r("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"olderAdd"})}}},[e._v("新增")])],1),r("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[r("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),r("el-table-column",{attrs:{prop:"name",label:"姓名",width:"100",align:"center"}}),r("el-table-column",{attrs:{prop:"guard",label:"是否备案",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.guard?"是":"否")+" ")]}}])}),r("el-table-column",{attrs:{prop:"hasOrder",label:"守护会员",width:"80",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.hasOrder?"是":"否")+" ")]}}])}),r("el-table-column",{attrs:{prop:"genderDesc",label:"性别",width:"60",align:"center"}}),r("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"120",align:"center"}}),r("el-table-column",{attrs:{prop:"idcardTypeDesc",label:"证件类型",align:"center"}}),r("el-table-column",{attrs:{prop:"idcardCode",label:"证件号码",align:"center"}}),r("el-table-column",{attrs:{prop:"liveTypeDesc",label:"居住类型",align:"center"}}),r("el-table-column",{attrs:{prop:"familyName",label:"所属家庭",width:"100",align:"center"}}),r("el-table-column",{attrs:{prop:"memberName",label:"所属用户",width:"100",align:"center"}}),r("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(r){return e.$router.push({name:"olderEdit",params:{id:t.row.id}})}}},[e._v("修改")]),t.row.guard||t.row.hasOrder?e._e():r("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(r){return e.handleDelOperate(t.row.id)}}},[e._v("删除")]),r("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(r){return e.$router.push({name:"olderInfo",params:{id:t.row.id}})}}},[e._v("查看")])]}}])})],1),r("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},l=[],o=r("5530"),n=(r("ac1f"),r("841c"),r("caad"),r("b0c0"),{name:"olderListIndex",data:function(){return{search:{keyword:void 0,name:void 0,phone:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=Object(o["a"])(Object(o["a"])({},this.search),{},{pageReqVo:this.table.pageInfo});console.log(t),this.$api.post("/bms/older/selectByPage",t).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleDelOperate:function(e){var t=this;e&&this.$confirm("您确定要删除该记录?","删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((function(){t.$api.get("/bms/older/delete?id=".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"已经成功删除!"}),t.fetchDatas()):t.$message({type:"error",message:"删除失败"})}))})).catch((function(){t.$message({type:"error",message:"删除失败"})}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}},beforeRouteEnter:function(e,t,r){r((function(e){e.$store.commit("keepAlive/add","EmptyLayout,butlerListIndex")}))},beforeRouteLeave:function(e,t,r){["butlerInfo"].includes(e.name)||(console.log("移除 EmptyLayout,butlerListIndex"),this.$store.commit("keepAlive/remove","EmptyLayout,butlerListIndex")),r()}}),i=n,s=(r("da1c"),r("2877")),c=Object(s["a"])(i,a,l,!1,null,"4ba185da",null);t["default"]=c.exports},4669:function(e,t,r){"use strict";r("e012")},6278:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"被监护人"}}),r("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[r("page-main",{attrs:{title:"基本信息"}},[r("el-row",{staticClass:"block-row",attrs:{gutter:20}},[e.$route.params.id?e._e():r("el-col",{attrs:{md:8}},[r("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"所属用户",prop:"memberId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{clear:e.handleMemberClear},model:{value:e.reqForm.memberId,callback:function(t){e.$set(e.reqForm,"memberId",t)},expression:"reqForm.memberId"}},e._l(e.dict.memberList,(function(e){return r("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"姓名",prop:"name"}},[r("el-input",{attrs:{maxlength:50,disabled:e.reqForm.hasOrder,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.name,callback:function(t){e.$set(e.reqForm,"name",t)},expression:"reqForm.name"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"性别",prop:"gender"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.gender,callback:function(t){e.$set(e.reqForm,"gender",t)},expression:"reqForm.gender"}},e._l(e.dict.genders,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[r("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入",disabled:e.reqForm.hasOrder},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"证件类型",prop:"idcardType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择",disabled:e.reqForm.hasOrder},model:{value:e.reqForm.idcardType,callback:function(t){e.$set(e.reqForm,"idcardType",t)},expression:"reqForm.idcardType"}},e._l(e.dict.idcardType,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"证件号码",prop:"idcardCode"}},[r("el-input",{attrs:{maxlength:30,disabled:e.reqForm.hasOrder,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.idcardCode,callback:function(t){e.$set(e.reqForm,"idcardCode",t)},expression:"reqForm.idcardCode"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"居住类型",prop:"liveType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.liveType,callback:function(t){e.$set(e.reqForm,"liveType",t)},expression:"reqForm.liveType"}},e._l(e.dict.liveType,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"床号或门牌号",prop:"bedNumber"}},[r("el-input",{attrs:{maxlength:10,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.bedNumber,callback:function(t){e.$set(e.reqForm,"bedNumber",t)},expression:"reqForm.bedNumber"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"安全级别",prop:"safeLevel"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.safeLevel,callback:function(t){e.$set(e.reqForm,"safeLevel",t)},expression:"reqForm.safeLevel"}},e._l(e.dict.safeLevel,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"出生日期",prop:"birthday"}},[r("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择开始日期"},model:{value:e.reqForm.birthday,callback:function(t){e.$set(e.reqForm,"birthday",t)},expression:"reqForm.birthday"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"身高(cm)",prop:"height"}},[r("el-input",{attrs:{type:"number",max:300,min:0,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.height,callback:function(t){e.$set(e.reqForm,"height",t)},expression:"reqForm.height"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"体重(kg)",prop:"weight"}},[r("el-input",{attrs:{type:"number",max:999,min:0,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.weight,callback:function(t){e.$set(e.reqForm,"weight",t)},expression:"reqForm.weight"}})],1)],1),r("el-col",{attrs:{md:8}},[r("el-form-item",{attrs:{label:"血型",prop:"bloodtype"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.bloodtype,callback:function(t){e.$set(e.reqForm,"bloodtype",t)},expression:"reqForm.bloodtype"}},e._l(e.dict.bloodtype,(function(e){return r("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"既往病史",prop:"medicalHistory"}},[r("el-input",{attrs:{type:"textarea",rows:5,maxlength:200,"show-word-limit":"",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.medicalHistory,callback:function(t){e.$set(e.reqForm,"medicalHistory",t)},expression:"reqForm.medicalHistory"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{attrs:{label:"过敏史",prop:"allergies"}},[r("el-input",{attrs:{type:"textarea",rows:5,maxlength:200,"show-word-limit":"","show-word-limitv-model":"reqForm.allergies",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.allergies,callback:function(t){e.$set(e.reqForm,"allergies",t)},expression:"reqForm.allergies"}})],1)],1),r("el-col",{attrs:{md:24}},[r("el-form-item",{staticStyle:{"margin-top":"20px"}},[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),r("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},l=[],o=r("1da1"),n=(r("96cf"),r("e1a4")),i={data:function(){var e=function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t,r,a){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:r?Object(n["b"])(r)||a(new Error("请输入正确的11位手机号码")):a(new Error("请输入手机号")),a();case 2:case"end":return e.stop()}}),e)})));return function(t,r,a){return e.apply(this,arguments)}}(),t=function(){var e=Object(o["a"])(regeneratorRuntime.mark((function e(t,r,a){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:r?Object(n["e"])(r)||a(new Error("姓名只支持中/英文，中文2-10个字，英文50字符")):a(new Error("请输入姓名")),a();case 2:case"end":return e.stop()}}),e)})));return function(t,r,a){return e.apply(this,arguments)}}();return{dict:{memberList:[],genders:[{code:"M",name:"男"},{code:"W",name:"女"}],bloodtype:[{code:"01",name:"A"},{code:"02",name:"B"},{code:"03",name:"O"},{code:"04",name:"AB"},{code:"05",name:"其他"}],liveType:[{code:"1",name:"独居"},{code:"2",name:"非独居"},{code:"3",name:"集中居住"},{code:"4",name:"其他"}],idcardType:[{code:"0",name:"身份证"},{code:"1",name:"护照"},{code:"2",name:"港澳通行证"},{code:"3",name:"军官证"},{code:"4",name:"其他"}],safeLevel:[{code:"1",name:"一级"},{code:"2",name:"二级"},{code:"3",name:"三级"}]},reqForm:{id:void 0,name:void 0,addr:void 0,houseNumber:void 0},submitLoading:!1,fullLoading:void 0,reqFormRules:{memberId:[{required:!0,trigger:"change",message:"请选择"}],name:[{required:!0,trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],phone:[{required:!0,trigger:"blur",validator:e,getValuesMethod:this.getValuesMethod}],idcardType:[{required:!0,message:"请选择证件类型",trigger:"change"}],idcardCode:[{required:!0,message:"请输入证件号码",trigger:"blur"},{min:2,max:30,message:"长度在 2 到 30 个字符",trigger:"blur"}]}}},mounted:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.handleFetchMember();case 2:e.fetchDetail(e.$route.params.id);case 3:case"end":return t.stop()}}),t)})))()},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("bms/older/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data),t.fullLoading.close()})).catch((function(e){console.log(e),t.fullLoading.close()})))},handleFetchMember:function(){var e=this;return Object(o["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get("/bms/memberinfo/listByStation",{}).then((function(t){"00000"===t.status&&t.data?e.dict.memberList=t.data:e.dict.memberList=[]}));case 1:case"end":return t.stop()}}),t)})))()},handleMemberClear:function(){this.reqForm.memberId=void 0},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var r=JSON.parse(JSON.stringify(t.reqForm));t.$api.post("/bms/older/save",r).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},s=i,c=(r("4669"),r("2877")),d=Object(c["a"])(s,a,l,!1,null,"e27035fc",null);t["default"]=d.exports},"75ab":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{"padding-bottom":"80px"}},[r("page-header",{attrs:{title:"详情查看"}}),r("page-main",{attrs:{title:"基础信息"}},[r("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[r("el-col",{attrs:{md:12}},[e._v(" 姓名: "+e._s(e.vo.name||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 性别: "+e._s(e.vo.genderDesc||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 是否备案: "+e._s(e.vo.guard?"是":"否")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 守护会员: "+e._s(e.vo.hasOrder?"是":"否")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 手机号: "+e._s(e.vo.phone||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 出生日期: "+e._s(e.vo.birthday||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 证件类型: "+e._s(e.vo.idcardTypeDesc||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 证件号码: "+e._s(e.vo.idcardCode||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 国家: "+e._s(e.vo.country||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 省份: "+e._s(e.vo.province||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 城市: "+e._s(e.vo.city||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 身高: "+e._s(e.vo.height||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 体重: "+e._s(e.vo.weight||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 居住类型: "+e._s(e.vo.liveTypeDesc||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 床号或门牌号: "+e._s(e.vo.bedNumber||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 安全级别: "+e._s(e.vo.safeLevelDesc||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 血型: "+e._s(e.vo.bloodtype||"")+" ")]),r("el-col",{attrs:{md:24}},[e._v(" 既往病史: "+e._s(e.vo.medicalHistory||"")+" ")]),r("el-col",{attrs:{md:24}},[e._v(" 过敏史: "+e._s(e.vo.allergies||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 创建时间: "+e._s(e.vo.createTime||"")+" ")]),r("el-col",{attrs:{md:12}},[e._v(" 修改时间: "+e._s(e.vo.modifyTime||"")+" ")])],1)],1),r("fixed-action-bar",[r("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1)},l=[],o={name:"OlderInfo",data:function(){return{vo:{},status:"",table:{datas:[],total:0},processEvent:{status:""}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("bms/older/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.vo=e.data)}))}}},n=o,i=r("2877"),s=Object(i["a"])(n,a,l,!1,null,null,null);t["default"]=s.exports},b146:function(e,t,r){},da1c:function(e,t,r){"use strict";r("b146")},e012:function(e,t,r){},e1a4:function(e,t,r){"use strict";function a(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function l(e){var t=/^\d{11}$/;return t.test(e)}function o(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function n(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function i(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function s(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function c(e){return s(e)||i(e)}r.d(t,"b",(function(){return a})),r.d(t,"c",(function(){return l})),r.d(t,"d",(function(){return o})),r.d(t,"a",(function(){return n})),r.d(t,"e",(function(){return c}))}}]);