(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["person_butler","chunk-af6f243c"],{"08f4":function(e,t,a){},"0e2c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务设备详情查看"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"设备编码:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.devCode,callback:function(t){e.$set(e.search,"devCode",t)},expression:"search.devCode"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"激活状态:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.activeStatus,callback:function(t){e.$set(e.search,"activeStatus",t)},expression:"search.activeStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"未激活",value:"0"}}),a("el-option",{attrs:{label:"已激活",value:"1"}})],1)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",align:"center"}}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"激活状态",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"运行状态",align:"center"}}),a("el-table-column",{attrs:{prop:"devModelName",label:"运行模式",align:"center"}}),a("el-table-column",{attrs:{prop:"chambName",label:"关联管家",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",align:"center"}}),a("el-table-column",{attrs:{prop:"activeTime",label:"激活时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleOpenDeviceInfoDialog(r.id)}}},[e._v("查看详情")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,size:e.table.pageInfo.pageSize,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1),a("UserDeviceInfoDialog",{ref:"userDeviceInfoDialog"})],1)},n=[],l=a("5530"),i=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,title:"设备"+e.info.devCode+"-详情查看","modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 设备编码: "+e._s(e.info.devCode)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 入库时间: "+e._s(e.info.createTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备名称: "+e._s(e.info.devName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备场景: "+e._s(e.info.devSceneName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 地址: "+e._s(e.info.houseAddr)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 门牌号: "+e._s(e.info.houseNumber||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 所属服务站: "+e._s(e.info.stationName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 摔倒响应时间: "+e._s(e.info.fallResponseTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 久滞响应时间: "+e._s(e.info.longlagResponseTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活状态: "+e._s(e.info.activeStatusName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活时间: "+e._s(e.info.activeTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 运行状态: "+e._s(e.info.statusName)+" ")])],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"用户信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 关联用户: "+e._s(e.info.memberPhone)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 关联管家: "+e._s(e.info.chambName)+" ")])],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"被监护人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.info.orderOlderVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"电话",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"birthday",label:"出生年月",align:"center"}}),a("el-table-column",{attrs:{prop:"liveTypeDesc",label:"居住类型",align:"center"}})],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"紧急联系人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.info.orderContactorVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"contactName",label:"姓名",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"contactPhone",label:"电话",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"relationTypeDesc",label:"联系人标签",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址",align:"center"}})],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"参数信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 心跳频率: "+e._s(e.info.heartRate)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 呼吸频率: "+e._s(e.info.breathRate)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 备注: "+e._s(e.info.remark||"无")+" ")])],1)],1)],1)}),s=[],o={name:"UserDeviceInfoDialog",data:function(){return{visible:!1,info:{}}},mounted:function(){},methods:{show:function(e){this.info=e,this.visible=!0},close:function(){this.info={},this.visible=!1,this.$emit("on-close")}}},c=o,u=(a("1c95"),a("2877")),d=Object(u["a"])(c,i,s,!1,null,"90717fec",null),m=d.exports,p={name:"personalUserList",components:{UserDeviceInfoDialog:m},data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},activeTimes:[],search:{devCode:void 0,startTime:void 0,endTime:void 0,activeStatus:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handleOpenDeviceInfoDialog:function(e){var t=this;this.$api.get("/bms/Device/getDeviceInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data?t.$refs.userDeviceInfoDialog.show(e.data):t.$message({message:"未找到设备相关信息",type:"error"})}))},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=JSON.parse(JSON.stringify(this.search));console.log(this.$route.meta.busiType),"chamb"===this.$route.meta.busiType?t["chambId"]=this.$route.params.id:"station"===this.$route.meta.busiType?t["stationId"]=this.$route.params.id:"org"===this.$route.meta.busiType?t["orgId"]=this.$route.params.id:t["memberId"]=this.$route.params.id,this.activeTimes&&this.activeTimes.length>=2&&(t["startTime"]=this.$dayjs(this.activeTimes[0]).format("YYYY-MM-DD HH:mm:ss"),t["endTime"]=this.$dayjs(this.activeTimes[1]).format("YYYY-MM-DD HH:mm:ss")),this.$api.post("/bms/Device/list",Object(l["a"])(Object(l["a"])({},t),{},{pageReqVo:this.table.pageInfo})).then((function(t){"00000"===t.status&&t.data&&(e.table.datas=t.data,e.table.pageInfo.total=t.page.total)}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.activeTimes=[],this.handleSearch()}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,userListIndex")}))},beforeRouteLeave:function(e,t,a){["personalUserDetail"].includes(e.name)||this.$store.commit("keepAlive/remove","EmptyLayout,userListIndex"),a()}},f=p,h=(a("b8be"),Object(u["a"])(f,r,n,!1,null,"668b7ced",null));t["default"]=h.exports},"1c95":function(e,t,a){"use strict";a("08f4")},3170:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"管家"}}),a("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入姓名"},model:{value:e.reqForm.name,callback:function(t){e.$set(e.reqForm,"name",t)},expression:"reqForm.name"}})],1)],1),a("el-col",{attrs:{md:12}},[a("el-form-item",{attrs:{label:"用户名",prop:"userName"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入用户名",disabled:!!e.$route.params.id},model:{value:e.reqForm.userName,callback:function(t){e.$set(e.reqForm,"userName",t)},expression:"reqForm.userName"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入手机号",disabled:!!e.$route.params.id},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),a("el-col",{attrs:{md:12}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入邮箱"},model:{value:e.reqForm.email,callback:function(t){e.$set(e.reqForm,"email",t)},expression:"reqForm.email"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"性别",prop:"gender"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择性别"},model:{value:e.reqForm.gender,callback:function(t){e.$set(e.reqForm,"gender",t)},expression:"reqForm.gender"}},e._l(e.dict.genders,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"所属服务站",prop:"stationId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择所属服务站"},model:{value:e.reqForm.stationId,callback:function(t){e.$set(e.reqForm,"stationId",t)},expression:"reqForm.stationId"}},e._l(e.dict.stations,(function(e){return a("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"服务入职时间",prop:"hiredate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期","value-format":"yyyy-MM-dd","default-time":"12:00:00"},model:{value:e.reqForm.hiredate,callback:function(t){e.$set(e.reqForm,"hiredate",t)},expression:"reqForm.hiredate"}})],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{attrs:{label:"默认密码"}},[e._v(" 123456 ")])],1)],1)],1),a("page-main",{attrs:{title:"服务信息"}},[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("el-form-item",{attrs:{label:"请选择管家所要服务的设备"}},[a("el-transfer",{ref:"transfer",attrs:{filterable:"","filter-placeholder":"请输入设备名称/ID","filter-method":e.filterMethod,titles:["未分配的设备",(e.reqForm.name||"管家")+"的设备"],"target-order":"unshift",data:e.transferDatas},on:{change:e.handleRightToLeft},model:{value:e.transferValues,callback:function(t){e.transferValues=t},expression:"transferValues"}})],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{staticStyle:{"margin-top":"20px"}},[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},n=[],l=a("2909"),i=a("1da1"),s=(a("d81d"),a("99af"),a("4de4"),a("caad"),a("2532"),a("a434"),a("c740"),a("07ac"),a("96cf"),a("e1a4")),o={data:function(){var e=this,t=function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a,r,n){var l,i,o,c,u,d,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(l=a.getValuesMethod,i=l(),i.email,o=i.id,r){t.next=8;break}n(new Error("请输入手机号")),t.next=18;break;case 8:return Object(s["b"])(r)||n(new Error("请输入正确的11位手机号码")),c="phone=".concat(r),o&&(c+="&userId=".concat(o)),t.next=13,e.$api.get("/bms/sysuser/validPhoneUnique?".concat(c));case 13:u=t.sent,d=u.status,u.message,m=u.data,"00000"===d&&!0===m&&n(new Error("手机号码已存在"));case 18:n();case 19:case"end":return t.stop()}}),t)})));return function(e,a,r){return t.apply(this,arguments)}}(),a=function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a,r,n){var l,i,o,c,u,d,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r){t.next=4;break}n(new Error("请输入用户名")),t.next=17;break;case 4:return Object(s["d"])(r)||n(new Error("请输入4到16位（字母，数字，下划线，减号）")),l=a.getValuesMethod,i=l(),o=i.id,c="userName=".concat(r),o&&(c+="&userId=".concat(o)),t.next=12,e.$api.get("/bms/sysuser/validUserNameUnique?".concat(c));case 12:u=t.sent,d=u.status,u.message,m=u.data,"00000"===d&&!0===m&&n(new Error("用户名已存在"));case 17:n();case 18:case"end":return t.stop()}}),t)})));return function(e,a,r){return t.apply(this,arguments)}}(),r=function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a,r,n){var l,i,o,c,u,d,m,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(l=a.getValuesMethod,i=l(),o=i.phone,c=i.id,r){t.next=8;break}o||n(new Error("手机号码或邮箱必填一个")),t.next=18;break;case 8:return Object(s["a"])(r)||n(new Error("请输入正确的邮箱")),u="email=".concat(r),c&&(u+="&userId=".concat(c)),t.next=13,e.$api.get("/bms/sysuser/validEmailUnique?".concat(u));case 13:d=t.sent,m=d.status,d.message,p=d.data,"00000"===m&&!0===p&&n(new Error("邮箱已存在"));case 18:n();case 19:case"end":return t.stop()}}),t)})));return function(e,a,r){return t.apply(this,arguments)}}();return{dict:{stations:[],genders:[{code:"M",name:"男"},{code:"W",name:"女"}]},transferDatas:[],filterMethod:function(e,t){return!e||(-1!==t.key.toLowerCase().indexOf(e.toLowerCase())||-1!==t.label.toLowerCase().indexOf(e.toLowerCase()))},transferValues:[],oldRightVals:[],keeperFreeDevs:[],reqForm:{id:void 0,userName:void 0,name:void 0,phone:void 0,email:void 0,gender:void 0,stationId:void 0,hiredate:void 0,freeDeviceVOList:void 0},reqFormRules:{name:[{required:!0,message:"请输入姓名",trigger:"blur"},{min:2,max:5,message:"长度在 2 到 5 个字符",trigger:"blur"}],userName:[{required:!0,trigger:"blur",validator:a,getValuesMethod:this.getValuesMethod}],phone:[{required:!0,trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],email:[{trigger:"blur",validator:r,getValuesMethod:this.getValuesMethod}],stationId:[{required:!0,message:"请选择所属服务站",trigger:"change"}]},submitLoading:!1,fullLoading:void 0}},watch:{"reqForm.stationId":function(e){this.fetchKeeperFreeDev()}},mounted:function(){this.fetchStations(),this.fetchDetail(this.$route.params.id),this.$route.params.id||this.fetchKeeperFreeDev()},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("/bms/sysuser/getKeeperInfo/".concat(e),{}).then((function(e){if("00000"===e.status&&e.data){t.reqForm=e.data;var a=[],r=[],n=[];(t.reqForm.freeDeviceVOList||[]).map((function(e){a.push({label:"(".concat(e.devCode,")")+e.devName,key:e.devCode})})),(t.reqForm.hasDeviceVOList||[]).map((function(e){a.push({label:"(".concat(e.devCode,")")+e.devName,key:e.devCode}),n.push({label:"(".concat(e.devCode,")")+e.devName,key:e.devCode}),r.push(e.devCode)})),console.log(a,r),t.transferDatas=a,t.oldRightVals=n,t.transferValues=r}})).catch((function(e){console.log(e)})))},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var a=JSON.parse(JSON.stringify(t.reqForm));if(t.$route.params.id){var r=[].concat(Object(l["a"])(t.reqForm.freeDeviceVOList||[]),Object(l["a"])(t.reqForm.hasDeviceVOList||[])),n=r.filter((function(e){return t.transferValues.includes(e.devCode)}));a["hasDeviceVOList"]=n}else{var i=t.keeperFreeDevs.filter((function(e){return t.transferValues.includes(e.devCode)}));a["hasDeviceVOList"]=i}var s=t.$route.params.id?t.$api.post("/bms/sysuser/updateKeeper",a):t.$api.post("/bms/sysuser/addKeeper",a);s.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},fetchStations:function(){var e=this;this.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}))},handleRightToLeft:function(e,t,a){var r=this;"left"===t&&a&&a.length?a.map((function(e){r.oldRightVals.splice(r.oldRightVals.findIndex((function(t){return a.includes(e.key)})),1)})):"right"===t&&a&&a.length&&a.map((function(e){r.oldRightVals.findIndex((function(t){return a.includes(e.key)}))<=-1&&r.oldRightVals.push(r.transferDatas.filter((function(t){return t.key===e}))[0])}))},fetchKeeperFreeDev:function(){var e=this;this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("/bms/sysuser/getKeeperFreeDev",{params:{stationId:this.reqForm.stationId}}).then((function(t){if("00000"===t.status&&t.data){var a=[];(t.data||[]).map((function(e){a.push({label:"(".concat(e.devCode,")")+e.devName,key:e.devCode})})),e.keeperFreeDevs=t.data;var r={};[].concat(a,Object(l["a"])(e.oldRightVals)).map((function(e){r[e.key]||(r[e.key]=e)})),e.transferDatas=Object.values(r)}else e.keeperFreeDevs=[],e.transferDatas=[];e.fullLoading.close()})).catch((function(t){console.log(t),e.fullLoading.close()}))}}},c=o,u=(a("50d9"),a("2877")),d=Object(u["a"])(c,r,n,!1,null,"6bf74e12",null);t["default"]=d.exports},3922:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"padding-bottom":"80px"}},[a("page-header",{attrs:{title:"详情查看"}}),a("page-main",{attrs:{title:"基础信息"}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:12}},[e._v(" 姓名: "+e._s(e.vo.name)+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 用户名: "+e._s(e.vo.userName)+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 手机号: "+e._s(e.vo.phone)+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 邮箱: "+e._s(e.vo.email)+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 性别: "+e._s("M"===e.vo.gender?"男":"")+" "+e._s("W"===e.vo.gender?"女":"")+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 所属服务站: "+e._s(e.vo.stationName)+" ")]),a("el-col",{attrs:{md:12}},[e._v(" 服务入职时间: "+e._s(e.vo.hiredate)+" ")])],1)],1),a("page-main",{attrs:{title:"服务信息"}},[a("el-row",{staticStyle:{"box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[e._v(" 管家所要服务的设备: "),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.vo.hasDeviceVOList,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"memberPhone",label:"用户号码",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"houseAddr",label:"地址",align:"center"}}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"是否激活设备",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"modifyTime",label:"激活时间",width:"200",align:"center"}})],1)],1)],1)],1),a("page-main",{attrs:{title:"管家处理事件"}},[a("div",{staticStyle:{position:"absolute",left:"125px",top:"15px","font-size":"14px",color:"#666"}},[e._v(" (共"),a("span",{staticStyle:{color:"#40b0ff",margin:"0 3px"}},[e._v(e._s(e.table.total||0))]),e._v("条) ")]),a("div",{staticStyle:{position:"absolute",right:"20px",top:"10px"}},[a("el-radio-group",{attrs:{size:"small"},on:{change:e.handleRadioChange},model:{value:e.status,callback:function(t){e.status=t},expression:"status"}},[a("el-radio-button",{attrs:{label:""}},[e._v("全部")]),a("el-radio-button",{attrs:{label:"0"}},[e._v("未处理")]),a("el-radio-button",{attrs:{label:"1"}},[e._v("进行中")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("已完成")]),a("el-radio-button",{attrs:{label:"3"}},[e._v("主动取消")])],1),a("el-button",{staticStyle:{"margin-left":"20px"},attrs:{type:"primary",size:"small",plain:""},on:{click:e.gotoChambEventPage}},[e._v("查看全部事件")])],1),a("el-row",{staticStyle:{"box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"title",label:"事件位置",minWidth:"300",align:"center"}}),a("el-table-column",{attrs:{prop:"orderTypeName",label:"告警事件",width:"100",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"red-dot"}),e._v(" "+e._s(t.row.orderTypeName)+" "),t.row.tag?a("div",[a("el-tag",{attrs:{size:"mini",type:"danger"}},[e._v(e._s(t.row.tag))])],1):e._e()]}}])}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",width:"130",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"事件处理状态",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"seatName",label:"事件处理人",width:"110",align:"center"}}),a("el-table-column",{attrs:{prop:"createTime",label:"事件发生时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"modifyTime",label:"事件更新时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"servicePlatformEventDetail",params:{id:t.row.id}})}}},[e._v("查看详情")])]}}])})],1)],1)],1)],1),a("fixed-action-bar",[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("返回")])],1)],1)},n=[],l=(a("caad"),a("b0c0"),{name:"ButlerInfo",data:function(){return{vo:{},status:"",table:{datas:[],total:0},processEvent:{status:""}}},mounted:function(){this.fetchDetail(this.$route.params.id)},methods:{fetchDetail:function(e){var t=this;e&&this.$api.get("bms/sysuser/getKeeperInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.vo=e.data,t.fetchEventList())}))},handleRadioChange:function(e){this.fetchEventList(e)},fetchEventList:function(e){var t=this;this.vo.id&&this.$api.get("/bms/event/listByChamb",{params:{chambId:this.vo.id,status:e,pageSize:5}}).then((function(e){"00000"===e.status&&e.data&&(t.table.datas=e.data,t.table.total=e.page.total)}))},gotoChambEventPage:function(){this.$router.push({name:"servicePlatformEventQueryByChamb",params:{chambId:this.vo.id}})}},beforeRouteLeave:function(e,t,a){var r=["butlerListIndex"].includes(e.name);!1===r&&this.$store.commit("keepAlive/remove","EmptyLayout,butlerListIndex"),a()}}),i=l,s=a("2877"),o=Object(s["a"])(i,r,n,!1,null,null,null);t["default"]=o.exports},"50d9":function(e,t,a){"use strict";a("97c1")},"6aeda":function(e,t,a){"use strict";a("a0cc")},"97c1":function(e,t,a){},a0cc:function(e,t,a){},a6ea:function(e,t,a){},b8be:function(e,t,a){"use strict";a("a6ea")},e1a4:function(e,t,a){"use strict";function r(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function n(e){var t=/^\d{11}$/;return t.test(e)}function l(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function i(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function s(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function o(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function c(e){return o(e)||s(e)}a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return i})),a.d(t,"e",(function(){return c}))},f48f:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"管家管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"管家姓名:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.name,callback:function(t){e.$set(e.search,"name",t)},expression:"search.name"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"电话:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.phone,callback:function(t){e.$set(e.search,"phone",t)},expression:"search.phone"}})],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap"},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"personalButlerAdd"})}}},[e._v("新增管家")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"phone",label:"电话",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",align:"center"}}),a("el-table-column",{attrs:{prop:"devCount",label:"服务设备",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"butlerDeviceInfo",params:{id:r.id}})}}},[e._v(e._s(r.devCount))])]}}])}),a("el-table-column",{attrs:{prop:"serveCount",label:"服务次数",align:"center"}}),a("el-table-column",{attrs:{prop:"hiredate",label:"服务平台时间",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"personalButlerEdit",params:{id:t.row.id}})}}},[e._v("修改")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(a){return e.handleDelOperate(t.row.id)}}},[e._v("删除")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(a){return e.$router.push({name:"butlerInfo",params:{id:t.row.id}})}}},[e._v("查看详情")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},n=[],l=a("5530"),i=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),{name:"butlerListIndex",data:function(){return{search:{name:void 0,phone:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/sysuser/listKeeper",{params:Object(l["a"])(Object(l["a"])({},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleDelOperate:function(e){var t=this;e&&this.$confirm("您确定要删除该记录?","删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}).then((function(){t.$api.post("/bms/sysuser/deleteKeeper/".concat(e),{}).then((function(e){"00000"===e.status?(t.$message({type:"success",message:"已经成功删除!"}),t.fetchDatas()):t.$message({type:"error",message:"删除失败"})}))})).catch((function(){t.$message({type:"error",message:"删除失败"})}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,butlerListIndex")}))},beforeRouteLeave:function(e,t,a){["butlerInfo"].includes(e.name)||(console.log("移除 EmptyLayout,butlerListIndex"),this.$store.commit("keepAlive/remove","EmptyLayout,butlerListIndex")),a()}}),s=i,o=(a("6aeda"),a("2877")),c=Object(o["a"])(s,r,n,!1,null,"8efa1cf6",null);t["default"]=c.exports}}]);