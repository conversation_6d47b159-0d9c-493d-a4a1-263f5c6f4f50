(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["person_user","chunk-af6f243c"],{"08f4":function(e,t,a){},"0e2c":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务设备详情查看"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"设备编码:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.devCode,callback:function(t){e.$set(e.search,"devCode",t)},expression:"search.devCode"}})],1),a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"激活状态:"}},[a("el-select",{attrs:{placeholder:"请选择"},model:{value:e.search.activeStatus,callback:function(t){e.$set(e.search,"activeStatus",t)},expression:"search.activeStatus"}},[a("el-option",{attrs:{label:"全部",value:""}}),a("el-option",{attrs:{label:"未激活",value:"0"}}),a("el-option",{attrs:{label:"已激活",value:"1"}})],1)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"devCode",label:"设备编码",align:"center"}}),a("el-table-column",{attrs:{prop:"activeStatusName",label:"激活状态",align:"center"}}),a("el-table-column",{attrs:{prop:"statusName",label:"运行状态",align:"center"}}),a("el-table-column",{attrs:{prop:"devModelName",label:"运行模式",align:"center"}}),a("el-table-column",{attrs:{prop:"chambName",label:"关联管家",align:"center"}}),a("el-table-column",{attrs:{prop:"orgName",label:"所属机构",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",align:"center"}}),a("el-table-column",{attrs:{prop:"activeTime",label:"激活时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleOpenDeviceInfoDialog(r.id)}}},[e._v("查看详情")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,size:e.table.pageInfo.pageSize,total:e.table.pageInfo.total,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1),a("UserDeviceInfoDialog",{ref:"userDeviceInfoDialog"})],1)},n=[],i=a("5530"),s=(a("ac1f"),a("841c"),a("caad"),a("b0c0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,title:"设备"+e.info.devCode+"-详情查看","modal-append-to-body":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.visible=t},close:e.close}},[a("el-row",{staticStyle:{padding:"10px 20px","box-sizing":"border-box","line-height":"36px"},attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 设备编码: "+e._s(e.info.devCode)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 入库时间: "+e._s(e.info.createTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备名称: "+e._s(e.info.devName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 设备场景: "+e._s(e.info.devSceneName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 地址: "+e._s(e.info.houseAddr)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 门牌号: "+e._s(e.info.houseNumber||"-")+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 所属服务站: "+e._s(e.info.stationName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 摔倒响应时间: "+e._s(e.info.fallResponseTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 久滞响应时间: "+e._s(e.info.longlagResponseTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活状态: "+e._s(e.info.activeStatusName)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 激活时间: "+e._s(e.info.activeTime)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 运行状态: "+e._s(e.info.statusName)+" ")])],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"用户信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 关联用户: "+e._s(e.info.memberPhone)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 关联管家: "+e._s(e.info.chambName)+" ")])],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"被监护人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.info.orderOlderVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"电话",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"birthday",label:"出生年月",align:"center"}}),a("el-table-column",{attrs:{prop:"liveTypeDesc",label:"居住类型",align:"center"}})],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"紧急联系人"}},[a("el-table",{staticClass:"list-table",attrs:{data:e.info.orderContactorVOList,border:"",stripe:"",size:"small","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"contactName",label:"姓名",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"contactPhone",label:"电话",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"gender",label:"性别",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s("M"===a.gender?"男":"")+" "+e._s("W"===a.gender?"女":"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"relationTypeDesc",label:"联系人标签",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址",align:"center"}})],1)],1),a("page-main",{staticStyle:{padding:"0"},attrs:{title:"参数信息"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[e._v(" 心跳频率: "+e._s(e.info.heartRate)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 呼吸频率: "+e._s(e.info.breathRate)+" ")]),a("el-col",{attrs:{md:8}},[e._v(" 备注: "+e._s(e.info.remark||"无")+" ")])],1)],1)],1)}),l=[],o={name:"UserDeviceInfoDialog",data:function(){return{visible:!1,info:{}}},mounted:function(){},methods:{show:function(e){this.info=e,this.visible=!0},close:function(){this.info={},this.visible=!1,this.$emit("on-close")}}},c=o,u=(a("1c95"),a("2877")),d=Object(u["a"])(c,s,l,!1,null,"90717fec",null),m=d.exports,p={name:"personalUserList",components:{UserDeviceInfoDialog:m},data:function(){return{pickerOptions:{shortcuts:[{text:"最近一周",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,a=new Date;a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},activeTimes:[],search:{devCode:void 0,startTime:void 0,endTime:void 0,activeStatus:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handleOpenDeviceInfoDialog:function(e){var t=this;this.$api.get("/bms/Device/getDeviceInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data?t.$refs.userDeviceInfoDialog.show(e.data):t.$message({message:"未找到设备相关信息",type:"error"})}))},handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=JSON.parse(JSON.stringify(this.search));console.log(this.$route.meta.busiType),"chamb"===this.$route.meta.busiType?t["chambId"]=this.$route.params.id:"station"===this.$route.meta.busiType?t["stationId"]=this.$route.params.id:"org"===this.$route.meta.busiType?t["orgId"]=this.$route.params.id:t["memberId"]=this.$route.params.id,this.activeTimes&&this.activeTimes.length>=2&&(t["startTime"]=this.$dayjs(this.activeTimes[0]).format("YYYY-MM-DD HH:mm:ss"),t["endTime"]=this.$dayjs(this.activeTimes[1]).format("YYYY-MM-DD HH:mm:ss")),this.$api.post("/bms/Device/list",Object(i["a"])(Object(i["a"])({},t),{},{pageReqVo:this.table.pageInfo})).then((function(t){"00000"===t.status&&t.data&&(e.table.datas=t.data,e.table.pageInfo.total=t.page.total)}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.activeTimes=[],this.handleSearch()}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,userListIndex")}))},beforeRouteLeave:function(e,t,a){["personalUserDetail"].includes(e.name)||this.$store.commit("keepAlive/remove","EmptyLayout,userListIndex"),a()}},h=p,f=(a("b8be"),Object(u["a"])(h,r,n,!1,null,"668b7ced",null));t["default"]=f.exports},"19b0":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"服务站账号管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"95px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:"服务站名称:"}},[a("el-input",{attrs:{placeholder:"请输入"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearch(t)}},model:{value:e.search.stationName,callback:function(t){e.$set(e.search,"stationName",t)},expression:"search.stationName"}})],1),a("el-form-item",{attrs:{label:"所属机构:"}},[a("el-select",{attrs:{filterable:"",placeholder:"请选择"},on:{"visible-change":e.handleFetchOrgs},model:{value:e.search.orgId,callback:function(t){e.$set(e.search,"orgId",t)},expression:"search.orgId"}},[a("el-option",{attrs:{label:"全部",value:void 0}}),e._l(e.dict.orgs,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})}))],2)],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"personalStationAdd"})}}},[e._v("新增服务站账号")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"stationName",label:"所属服务站",minWidth:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"200",align:"center"}}),a("el-table-column",{attrs:{prop:"devCount",label:"设备服务",width:"200",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"stationDeviceList",params:{id:r.stationId}})}}},[e._v(e._s(r.devCount))])]}}])}),a("el-table-column",{attrs:{prop:"name",label:"服务有效期",width:"220",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[e._v(" "+e._s(a.servStartDate&&a.servEndDate?a.servStartDate+"~"+a.servEndDate:"")+" "+e._s(a.servStartDate&&!a.servEndDate?a.servStartDate+"~":"")+" "+e._s(!a.servStartDate&&a.servEndDate?"~"+a.servStartDate:"")+" ")]}}])}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"240",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return["0"===r.validStatus?a("el-link",{attrs:{type:"danger",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(r.id,1)}}},[e._v("禁用")]):e._e(),"1"===r.validStatus?a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(r.id,0)}}},[e._v("启用")]):e._e(),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"personalStationEdit",params:{id:r.id}})}}},[e._v("编辑")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"personalStationInfo",params:{stationId:r.stationId,id:r.id}})}}},[e._v("查看详情")]),a("el-link",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},on:{click:function(t){return e.handleResetPwd(r.id,r.stationName)}}},[e._v("重置密码")])]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},n=[],i=a("5530"),s=(a("ac1f"),a("841c"),a("d3b7"),{name:"userListIndex",data:function(){return{dict:{orgs:[]},search:{stationName:void 0,orgId:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this;this.$api.get("/bms/sysuser/listStationAccount",{params:Object(i["a"])(Object(i["a"])({},this.search),this.table.pageInfo)}).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleChangeStatus:function(e,t){var a=this;e&&void 0!=t&&null!=t&&this.$api.get("/bms/sysuser/changeStatus",{params:{id:e,validStatus:t}}).then((function(e){"00000"===e.status?(a.$message({type:"success",message:"更新成功!"}),a.fetchDatas()):a.$message({type:"error",message:e.message||"操作失败!"})}))},handleResetPwd:function(e,t){var a=this;this.$confirm("是否重置该服务站 [".concat(t||"-","] 的密码?"),"提醒",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",beforeClose:function(t,r,n){"confirm"===t?(r.confirmButtonLoading=!0,a.$api.post("/bms/sysuser/resetStationAccountPassword/".concat(e),{}).then((function(e){a.$message({type:"success",message:"重置成功!"}),n(!0)})).catch((function(){n(!1)})).finally((function(){r.confirmButtonLoading=!1}))):n()}}).catch((function(){}))},handleFetchOrgs:function(){var e=this;this.$api.get("/bms/org/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.orgs=t.data:e.dict.orgs=[]}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}}}),l=s,o=(a("bb62"),a("2877")),c=Object(o["a"])(l,r,n,!1,null,"5bb3935b",null);t["default"]=c.exports},"1c95":function(e,t,a){"use strict";a("08f4")},2420:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:"用户管理"}}),a("page-main",[a("div",{staticClass:"table-search"},[a("el-form",{ref:"ruleForm",staticClass:"demo-form-inline",staticStyle:{position:"relative","text-align":"left"},attrs:{inline:!0,model:e.search,size:"small","label-width":"75px","label-position":"left"}},[a("el-form-item",{staticStyle:{"margin-left":"20px"},attrs:{label:""}},[a("el-input",{attrs:{placeholder:"根据姓名、昵称、手机号、证件号搜索"},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:(t.preventDefault(),e.handleSearch(t))}},model:{value:e.search.keyword,callback:function(t){e.$set(e.search,"keyword",t)},expression:"search.keyword"}})],1),a("el-form-item",{staticStyle:{position:"absolute",right:"0","margin-right":"0"}},[a("el-button",{on:{click:function(t){return e.resetForm("ruleForm")}}},[e._v("重置")]),a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("查询")])],1)],1)],1),a("div",{staticClass:"page-oper-wrap",staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",icon:"el-icon-plus",size:"small"},nativeOn:{click:function(t){return e.$router.push({name:"userAdd"})}}},[e._v("新增")])],1),a("el-table",{ref:"table",staticClass:"list-table",attrs:{data:e.table.datas,border:"",stripe:"","default-sort":{prop:"x7",order:"descending"}}},[a("el-table-column",{attrs:{type:"index",width:"50",align:"center"}}),a("el-table-column",{attrs:{prop:"name",label:"姓名",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"nickname",label:"昵称",width:"100",align:"center"}}),a("el-table-column",{attrs:{prop:"phone",label:"手机号",width:"120",align:"center"}}),a("el-table-column",{attrs:{prop:"addr",label:"地址",align:"center"}}),a("el-table-column",{attrs:{prop:"devCount",label:"设备数",width:"60",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"userDeviceInfo",params:{id:r.id}})}}},[e._v(e._s(r.devCount))])]}}])}),a("el-table-column",{attrs:{prop:"createTime",label:"注册时间",width:"160",align:"center"}}),a("el-table-column",{attrs:{prop:"x9",label:"操作",width:"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[a("el-link",{attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.$router.push({name:"memberEdit",params:{id:r.id}})}}},[e._v("修改")]),"0"===r.status?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:["self.org","self.station"],expression:"[ 'self.org', 'self.station' ]"}],staticStyle:{"margin-left":"10px"},attrs:{type:"danger",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(r.id,1)}}},[e._v("禁用")]):e._e(),"1"===r.status?a("el-link",{directives:[{name:"auth",rawName:"v-auth",value:["self.org","self.station"],expression:"[ 'self.org', 'self.station' ]"}],staticStyle:{"margin-left":"10px"},attrs:{type:"primary",underline:!1},nativeOn:{click:function(t){return e.handleChangeStatus(r.id,0)}}},[e._v("启用")]):e._e()]}}])})],1),a("el-pagination",{staticClass:"pagination",attrs:{"current-page":e.table.pageInfo.current,total:e.table.pageInfo.total,size:e.table.pageInfo.pageSize,layout:"total, sizes, ->, prev, pager, next, jumper","hide-on-single-page":!1},on:{"current-change":e.handlePageCurrentChange,"size-change":e.handlePageSizeChange}})],1)],1)},n=[],i=a("5530"),s=(a("ac1f"),a("841c"),a("99af"),a("caad"),a("b0c0"),{name:"userListIndex",data:function(){return{search:{keyword:void 0},table:{datas:[],pageInfo:{current:1,pageSize:10,total:0}}}},mounted:function(){this.fetchDatas()},methods:{handlePageCurrentChange:function(e){this.table.pageInfo.current=e,this.fetchDatas()},handlePageSizeChange:function(e){this.table.pageInfo.current=1,this.table.pageInfo.pageSize=e,this.fetchDatas()},handleSearch:function(){this.table.pageInfo.page=1,this.fetchDatas()},fetchDatas:function(){var e=this,t=Object(i["a"])(Object(i["a"])({},this.search),{},{pageReqVo:this.table.pageInfo});this.$api.post("/bms/memberinfo/listMemberInfo",t).then((function(t){"00000"===t.status&&t.data?(e.table.datas=t.data,e.table.pageInfo.total=t.page.total):(e.table.datas=[],e.table.pageInfo.total=0)}))},handleChangeStatus:function(e,t){var a=this;e&&void 0!=t&&null!=t&&this.$api.post("/bms/memberinfo/updateMemberInfoStatus/".concat(e,"/").concat(t),{}).then((function(e){"00000"===e.status?(a.$message({type:"success",message:"更新成功!"}),a.fetchDatas()):a.$message({type:"error",message:e.message||"操作失败!"})}))},resetForm:function(e){Object.assign(this.$data.search,this.$options.data().search),this.handleSearch()}},beforeRouteEnter:function(e,t,a){a((function(e){e.$store.commit("keepAlive/add","EmptyLayout,userListIndex")}))},beforeRouteLeave:function(e,t,a){["personalUserDetail"].includes(e.name)||this.$store.commit("keepAlive/remove","EmptyLayout,userListIndex"),a()}}),l=s,o=(a("6d2d"),a("2877")),c=Object(o["a"])(l,r,n,!1,null,"2eb748cf",null);t["default"]=c.exports},"49a8":function(e,t,a){"use strict";a("b939")},"6c17":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("page-header",{attrs:{title:(e.$route.params.id?"修改":"新建")+"与安宝用户"}}),a("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[a("page-main",{attrs:{title:"基本信息"}},[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"姓名",prop:"name"}},[a("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.name,callback:function(t){e.$set(e.reqForm,"name",t)},expression:"reqForm.name"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"昵称",prop:"nickname"}},[a("el-input",{attrs:{maxlength:15,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.nickname,callback:function(t){e.$set(e.reqForm,"nickname",t)},expression:"reqForm.nickname"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"性别",prop:"gender"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.gender,callback:function(t){e.$set(e.reqForm,"gender",t)},expression:"reqForm.gender"}},e._l(e.dict.genders,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"证件类型",prop:"idcardType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"请选择"},model:{value:e.reqForm.idcardType,callback:function(t){e.$set(e.reqForm,"idcardType",t)},expression:"reqForm.idcardType"}},e._l(e.dict.idcardType,(function(e){return a("el-option",{key:e.code,attrs:{label:e.name,value:e.code}})})),1)],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"证件号码",prop:"idcardCode"}},[a("el-input",{attrs:{maxlength:30,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入"},model:{value:e.reqForm.idcardCode,callback:function(t){e.$set(e.reqForm,"idcardCode",t)},expression:"reqForm.idcardCode"}})],1)],1)],1)],1),e.$route.params.id?e._e():a("page-main",{attrs:{title:"家庭信息"}},[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[a("el-col",{attrs:{md:16}},[a("el-form-item",{attrs:{label:"地址",prop:"addr"}},[a("el-input",{attrs:{maxlength:50,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入地址"},model:{value:e.reqForm.addr,callback:function(t){e.$set(e.reqForm,"addr",t)},expression:"reqForm.addr"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"门牌号",prop:"houseNumber"}},[a("el-input",{attrs:{maxlength:20,onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入门牌号"},model:{value:e.reqForm.houseNumber,callback:function(t){e.$set(e.reqForm,"houseNumber",t)},expression:"reqForm.houseNumber"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"经度",prop:"longitude"}},[a("el-input",{attrs:{type:"number",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入经度"},model:{value:e.reqForm.longitude,callback:function(t){e.$set(e.reqForm,"longitude",t)},expression:"reqForm.longitude"}})],1)],1),a("el-col",{attrs:{md:8}},[a("el-form-item",{attrs:{label:"纬度",prop:"latitude"}},[a("el-input",{attrs:{type:"number",onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入纬度"},model:{value:e.reqForm.latitude,callback:function(t){e.$set(e.reqForm,"latitude",t)},expression:"reqForm.latitude"}})],1)],1)],1)],1),e.$route.params.id?e._e():a("page-main",{attrs:{title:"关联信息"}},[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[a("el-col",{attrs:{md:8}},[a("el-form-item",{staticStyle:{width:"100%"},attrs:{label:"绑定的设备",prop:"devId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{clear:e.handleDeviceClear},model:{value:e.reqForm.devId,callback:function(t){e.$set(e.reqForm,"devId",t)},expression:"reqForm.devId"}},e._l(e.dict.devices,(function(e){return a("el-option",{key:e.id,attrs:{label:e.devCode,value:e.id}})})),1)],1)],1)],1)],1),a("page-main",[a("el-row",{staticClass:"block-row",attrs:{gutter:20}},[a("el-col",{attrs:{md:24}},[a("el-form-item",{staticStyle:{"margin-top":"20px"}},[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},n=[],i=a("1da1"),s=(a("96cf"),a("e1a4")),l={data:function(){var e=function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,a,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a?Object(s["b"])(a)||r(new Error("请输入正确的11位手机号码")):r(new Error("请输入手机号")),r();case 2:case"end":return e.stop()}}),e)})));return function(t,a,r){return e.apply(this,arguments)}}(),t=function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,a,r){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:a?Object(s["e"])(a)||r(new Error("姓名只支持中/英文，中文2-10个字，英文50字符")):r(new Error("请输入姓名")),r();case 2:case"end":return e.stop()}}),e)})));return function(t,a,r){return e.apply(this,arguments)}}();return{dict:{devices:[],genders:[{code:"M",name:"男"},{code:"W",name:"女"}],bloodtype:[{code:"01",name:"A"},{code:"02",name:"B"},{code:"03",name:"O"},{code:"04",name:"AB"},{code:"05",name:"其他"}],liveType:[{code:"1",name:"独居"},{code:"2",name:"非独居"},{code:"3",name:"集中居住"},{code:"4",name:"其他"}],idcardType:[{code:"0",name:"身份证"},{code:"1",name:"护照"},{code:"2",name:"港澳通行证"},{code:"3",name:"军官证"},{code:"4",name:"其他"}]},reqForm:{id:void 0,name:void 0,addr:void 0,houseNumber:void 0},submitLoading:!1,fullLoading:void 0,reqFormRules:{name:[{required:!0,trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],phone:[{required:!0,trigger:"blur",validator:e,getValuesMethod:this.getValuesMethod}],longitude:[{required:!0,message:"请输入",trigger:"change"}],latitude:[{required:!0,message:"请输入",trigger:"blur"}],addr:[{required:!0,message:"请输入",trigger:"blur"}],houseNumber:[{required:!0,message:"请输入",trigger:"blur"}],devId:[{required:!0,message:"请选择",trigger:"change"}]}}},mounted:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.handleFetchDev();case 2:e.fetchDetail(e.$route.params.id);case 3:case"end":return t.stop()}}),t)})))()},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&(this.fullLoading=this.$loading({lock:!0,text:"加载中",spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0.7)"}),this.$api.get("bms/memberinfo/getById?id=".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data),t.fullLoading.close()})).catch((function(e){console.log(e),t.fullLoading.close()})))},handleFetchDev:function(){var e=this;return Object(i["a"])(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$api.get("/bms/Device/getNotBoundDeviceList",{}).then((function(t){"00000"===t.status&&t.data?e.dict.devices=t.data:e.dict.devices=[]}));case 1:case"end":return t.stop()}}),t)})))()},handleDeviceClear:function(){this.reqForm.houseId=void 0},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var a=JSON.parse(JSON.stringify(t.reqForm));t.$api.post("/bms/memberinfo/save",a).then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新建")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新建")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))}}},o=l,c=(a("a0a8"),a("2877")),u=Object(c["a"])(o,r,n,!1,null,"189d8762",null);t["default"]=u.exports},"6d2d":function(e,t,a){"use strict";a("e9df")},"7d50":function(e,t,a){e.exports={g_main_sidebar_width:"60px",g_sub_sidebar_width:"220px"}},8283:function(e,t,a){"use strict";a("7d50")},"9ae5":function(e,t,a){},a0a8:function(e,t,a){"use strict";a("a206")},a206:function(e,t,a){},a6ea:function(e,t,a){},b8be:function(e,t,a){"use strict";a("a6ea")},b939:function(e,t,a){},bb62:function(e,t,a){"use strict";a("9ae5")},e1a4:function(e,t,a){"use strict";function r(e){var t=/^1[3|4|5|6|7|8|9][0-9]\d{8}$/;return t.test(e)}function n(e){var t=/^\d{11}$/;return t.test(e)}function i(e){var t=/^[a-zA-Z0-9_-]{4,16}$/;return t.test(e)}function s(e){var t=/\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*/;return t.test(e)}function l(e){var t=/^[\u4e00-\u9fa5]{2,10}$/;return t.test(e)}function o(e){var t=/^[A-Za-z]*(\s[A-Za-z]*)*$/;return t.test(e)}function c(e){return o(e)||l(e)}a.d(t,"b",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return c}))},e9df:function(e,t,a){},eef6:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"station-edit"},[a("page-header",{attrs:{title:(e.$route.params.id?"编辑":"新增")+"服务站账号"}}),a("el-form",{ref:"reqForm",attrs:{model:e.reqForm,rules:e.reqFormRules,"label-position":"top","label-width":"80px",size:"small"}},[a("page-main",{attrs:{title:"服务站账号"}},[a("el-row",{staticStyle:{padding:"20px 160px","box-sizing":"border-box"},attrs:{gutter:20}},[a("el-col",{attrs:{md:12}},[a("el-form-item",{attrs:{label:"所属服务站",prop:"stationId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",clearable:"",placeholder:"请选择所属服务站"},model:{value:e.reqForm.stationId,callback:function(t){e.$set(e.reqForm,"stationId",t)},expression:"reqForm.stationId"}},e._l(e.dict.stations,(function(e){return a("el-option",{key:e.id,attrs:{label:e.shortName,value:e.id}})})),1)],1)],1),a("el-col",{attrs:{md:12}},[a("el-form-item",{attrs:{label:"用户名",prop:"userName"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入用户名",disabled:!!e.$route.params.id},model:{value:e.reqForm.userName,callback:function(t){e.$set(e.reqForm,"userName",t)},expression:"reqForm.userName"}})],1)],1),a("el-col",{attrs:{md:12}},[a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入手机号",disabled:!!e.$route.params.id},model:{value:e.reqForm.phone,callback:function(t){e.$set(e.reqForm,"phone",t)},expression:"reqForm.phone"}})],1)],1),a("el-col",{attrs:{md:12}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{onkeyup:"this.value=this.value.replace(/[, ]/g,'')",placeholder:"请输入邮箱"},model:{value:e.reqForm.email,callback:function(t){e.$set(e.reqForm,"email",t)},expression:"reqForm.email"}})],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{attrs:{label:"服务有效期",prop:"servStartDate"}},[a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择开始日期"},model:{value:e.reqForm.servStartDate,callback:function(t){e.$set(e.reqForm,"servStartDate",t)},expression:"reqForm.servStartDate"}}),a("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[e._v("至")]),a("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择结束日期"},model:{value:e.reqForm.servEndDate,callback:function(t){e.$set(e.reqForm,"servEndDate",t)},expression:"reqForm.servEndDate"}})],1)],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{attrs:{label:"默认密码"}},[e._v(" 123456 ")])],1),a("el-col",{attrs:{md:24}},[a("el-form-item",{staticStyle:{"margin-top":"20px","text-align":"center"}},[a("el-button",{on:{click:function(t){return e.$router.go(-1)}}},[e._v("取消")]),a("el-button",{attrs:{type:"primary",loading:e.submitLoading},on:{click:function(t){return e.handleSubmit("reqForm")}}},[e._v("保存")])],1)],1)],1)],1)],1)],1)},n=[],i=a("1da1"),s=(a("96cf"),a("e1a4")),l={data:function(){var e=this,t=function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a,r,n){var i,l,o,c,u,d,m,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i=a.getValuesMethod,l=i(),o=l.email,c=l.id,r){t.next=8;break}o||n(new Error("手机号码或邮箱必填一个")),t.next=18;break;case 8:return Object(s["b"])(r)||n(new Error("请输入正确的11位手机号码")),u="phone=".concat(r),c&&(u+="&userId=".concat(c)),t.next=13,e.$api.get("/bms/sysuser/validPhoneUnique?".concat(u));case 13:d=t.sent,m=d.status,d.message,p=d.data,"00000"===m&&!0===p&&n(new Error("手机号码已存在"));case 18:n();case 19:case"end":return t.stop()}}),t)})));return function(e,a,r){return t.apply(this,arguments)}}(),a=function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a,r,n){var i,l,o,c,u,d,m;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(r){t.next=4;break}n(new Error("请输入用户名")),t.next=17;break;case 4:return Object(s["d"])(r)||n(new Error("请输入4到16位（字母，数字，下划线，减号）")),i=a.getValuesMethod,l=i(),o=l.id,c="userName=".concat(r),o&&(c+="&userId=".concat(o)),t.next=12,e.$api.get("/bms/sysuser/validUserNameUnique?".concat(c));case 12:u=t.sent,d=u.status,u.message,m=u.data,"00000"===d&&!0===m&&n(new Error("用户名已存在"));case 17:n();case 18:case"end":return t.stop()}}),t)})));return function(e,a,r){return t.apply(this,arguments)}}(),r=function(){var t=Object(i["a"])(regeneratorRuntime.mark((function t(a,r,n){var i,l,o,c,u,d,m,p;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(i=a.getValuesMethod,l=i(),o=l.phone,c=l.id,r){t.next=8;break}o||n(new Error("手机号码或邮箱必填一个")),t.next=18;break;case 8:return Object(s["a"])(r)||n(new Error("请输入正确的邮箱")),u="email=".concat(r),c&&(u+="&userId=".concat(c)),t.next=13,e.$api.get("/bms/sysuser/validEmailUnique?".concat(u));case 13:d=t.sent,m=d.status,d.message,p=d.data,"00000"===m&&!0===p&&n(new Error("邮箱已存在"));case 18:n();case 19:case"end":return t.stop()}}),t)})));return function(e,a,r){return t.apply(this,arguments)}}();return{dict:{stations:[]},reqForm:{id:void 0,stationId:void 0,userName:void 0,phone:void 0,email:void 0,servStartDate:void 0,servEndDate:void 0},reqFormRules:{stationId:[{required:!0,trigger:"change",message:"请选择服务站"}],userName:[{required:!0,trigger:"blur",validator:a,getValuesMethod:this.getValuesMethod}],phone:[{trigger:"blur",validator:t,getValuesMethod:this.getValuesMethod}],email:[{trigger:"blur",validator:r,getValuesMethod:this.getValuesMethod}]},submitLoading:!1}},beforeDestroy:function(){},mounted:function(){this.handleFetchStations(),this.fetchDetail(this.$route.params.id)},methods:{getValuesMethod:function(){return this.reqForm},fetchDetail:function(e){var t=this;e&&this.$api.get("/bms/sysuser/getStationAccountInfo/".concat(e),{}).then((function(e){"00000"===e.status&&e.data&&(t.reqForm=e.data)}))},handleSubmit:function(e){var t=this;this.submitLoading=!0,this.$refs[e].validate((function(e){if(!e)return t.submitLoading=!1,t.$message({type:"error",message:"验证失败"}),!1;var a=JSON.parse(JSON.stringify(t.reqForm)),r=t.$route.params.id?t.$api.post("/bms/sysuser/updateStationAccount",a):t.$api.post("/bms/sysuser/addStationAccount",a);r.then((function(e){"00000"===e.status?(t.$message({type:"success",message:(t.reqForm.id?"修改":"新增")+"完成"}),setTimeout((function(){t.submitLoading=!1,t.$router.go(-1)}),500)):(t.submitLoading=!1,t.$message({type:"error",message:(t.reqForm.id?"修改":"新增")+"失败"}))})).catch((function(e){t.submitLoading=!1}))}))},handleFetchStations:function(){var e=this;this.$api.get("/bms/station/list4Select",{}).then((function(t){"00000"===t.status&&t.data?e.dict.stations=t.data:e.dict.stations=[]}))}}},o=l,c=(a("8283"),a("49a8"),a("2877")),u=Object(c["a"])(o,r,n,!1,null,"4eb16ee8",null);t["default"]=u.exports}}]);