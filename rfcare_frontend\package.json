{"name": "fantastic-admin-pro", "version": "1.0.0", "private": true, "scripts": {"start": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "build:test": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build --mode test --dest dist-test", "build": "cross-env NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build", "start:boe": "node --openssl-legacy-provider scripts/conditional-compile.js boe && vue-cli-service serve", "build:boe": " node --openssl-legacy-provider scripts/conditional-compile.js boe && vue-cli-service build --dest dist-boe", "start:default": " node --openssl-legacy-provider scripts/conditional-compile.js default && vue-cli-service serve", "build:default": " node --openssl-legacy-provider scripts/conditional-compile.js default && vue-cli-service build --dest dist", "lint": "vue-cli-service lint", "stylelint": "vue-cli-service lint:style", "svgo": "svgo -f src/assets/icons", "new": "plop"}, "dependencies": {"@tinymce/tinymce-vue": "^3.2.6", "animate.css": "^4.1.1", "axios": "^0.21.0", "babel-plugin-transform-remove-strict-mode": "^0.0.2", "babel-preset-env": "^1.7.0", "caniuse-lite": "^1.0.30001582", "core-js": "^3.6.4", "dayjs": "^1.9.4", "echarts": "^5.5.0", "echarts-liquidfill": "^3.0.0", "element-ui": "^2.15.14", "hotkeys-js": "^3.8.1", "jquery": "^3.6.0", "jssip": "3.6.1", "mqtt": "4.2.8", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.0", "remixicon": "^2.5.0", "screenfull": "^5.0.2", "sip.js": "0.21.2", "spinkit": "^2.0.1", "tinymce": "^5.5.1", "vue": "^2.6.12", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.3", "vue-contextmenujs": "^1.3.13", "vue-cookies": "^1.7.4", "vue-i18n": "^8.22.1", "vue-meta": "^2.4.0", "vue-router": "^3.4.8", "vuedraggable": "^2.24.3", "vuex": "^3.5.1", "watermark-dom": "^2.3.0"}, "devDependencies": {"@kazupon/vue-i18n-loader": "^0.5.0", "@vue/cli-plugin-babel": "^4.5.8", "@vue/cli-plugin-eslint": "^4.5.8", "@vue/cli-service": "^4.5.8", "@winner-fed/vue-cli-plugin-stylelint": "^1.0.4", "babel-eslint": "^10.1.0", "babel-plugin-dynamic-import-node": "^2.3.3", "compression-webpack-plugin": "^6.1.1", "fs-extra": "^11.0.0", "cross-env": "^10.0.0", "eslint": "^7.12.1", "eslint-plugin-vue": "^7.1.0", "html-webpack-plugin": "^4.5.0", "husky": "^4.3.0", "lint-staged": "^10.5.1", "mockjs": "^1.1.0", "plop": "^2.7.4", "sass": "^1.28.0", "sass-loader": "^10.0.4", "sass-resources-loader": "^2.1.1", "stylelint": "^13.7.2", "stylelint-config-recommended-scss": "^4.2.0", "stylelint-config-standard": "^20.0.0", "stylelint-scss": "^3.18.0", "svg-sprite-loader": "^5.0.0", "svgo": "^1.3.0", "vue-cli-plugin-mock": "^1.0.1", "vue-template-compiler": "^2.6.12", "webpack-spritesmith": "^1.1.0"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}