const fs = require('fs-extra');  // 修复：引入 fs 模块
const path = require('path');

class PlatformConfig {
  constructor() {
    this.platforms = new Map();
    this.loadPlatforms();
  }

  loadPlatforms() {
    try {
      const platformsDir = path.join(__dirname);
      const files = fs.readdirSync(platformsDir);

      files.forEach(file => {
        // 排除自身和其他非平台配置文件
        if (file.endsWith('.js') && file !== 'platform-config.js' && file !== 'index.js') {
          const platformName = path.basename(file, '.js');
          try {
            const config = require(path.join(platformsDir, file));
            this.platforms.set(platformName, config);
            console.log(`加载平台配置: ${platformName}`);
          } catch (error) {
            console.warn(`加载平台配置 ${file} 失败:`, error.message);
          }
        }
      });

      if (this.platforms.size === 0) {
        console.warn('未找到任何平台配置文件');
      }
    } catch (error) {
      console.error('加载平台配置失败:', error.message);
    }
  }

  getPlatformConfig(platform) {
    const config = this.platforms.get(platform);
    if (!config) {
      console.warn(`未找到平台 ${platform} 的配置，使用默认配置`);
      return this.platforms.get('default') || {};
    }
    return config;
  }

  getAllPlatforms() {
    return Array.from(this.platforms.keys());
  }
}

module.exports = new PlatformConfig();
