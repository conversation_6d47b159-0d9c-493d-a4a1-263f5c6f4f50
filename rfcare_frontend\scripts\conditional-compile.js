const fs = require('fs-extra');
const path = require('path');
const ConditionalPreprocessor = require('./preprocessor');
const platformConfig = require('../platforms/platform-config');  // 修复路径

class ConditionalCompiler {
  constructor(platform) {
    this.platform = platform;
    this.config = platformConfig.getPlatformConfig(platform);
    this.preprocessor = new ConditionalPreprocessor(platform);
    this.sourceDir = path.join(process.cwd(), 'src');
    this.buildDir = path.join(process.cwd(), 'dist', platform);

    console.log(`初始化编译器，平台: ${platform}`);
  }

  async cleanBuildDir() {
    if (await fs.pathExists(this.buildDir)) {
      await fs.remove(this.buildDir);
      console.log(`清理构建目录: ${this.buildDir}`);
    }
    await fs.ensureDir(this.buildDir);
  }

  async copyAndProcessFiles() {
    console.log('开始复制和处理文件...');

    // 检查源目录是否存在
    if (!(await fs.pathExists(this.sourceDir))) {
      throw new Error(`源目录不存在: ${this.sourceDir}`);
    }

    // 复制源文件到构建目录
    await fs.copy(this.sourceDir, this.buildDir);
    console.log('文件复制完成');

    // 处理条件编译
    await this.processFiles();

    // 生成平台配置文件
    await this.generatePlatformConfig();
  }

  async processFiles() {
    console.log('开始条件编译处理...');

    const processDirectory = async (dirPath) => {
      const files = await fs.readdir(dirPath);

      for (const file of files) {
        const fullPath = path.join(dirPath, file);
        const stat = await fs.stat(fullPath);

        if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules') {
          await processDirectory(fullPath);
        } else if (stat.isFile()) {
          await this.processSingleFile(fullPath);
        }
      }
    };

    await processDirectory(this.buildDir);
    console.log('条件编译处理完成');
  }

  async processSingleFile(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    let content = await fs.readFile(filePath, 'utf8');
    let changed = false;

    switch (ext) {
      case '.vue':
        content = this.preprocessor.processVue(content);
        changed = true;
        break;
      case '.js':
      case '.ts':
      case '.jsx':
      case '.tsx':
        content = this.preprocessor.processJS(content);
        changed = true;
        break;
      case '.css':
      case '.scss':
      case '.less':
      case '.styl':
        content = this.preprocessor.processCSS(content);
        changed = true;
        break;
      case '.html':
      case '.htm':
        content = this.preprocessor.processTemplate(content);
        changed = true;
        break;
    }

    if (changed) {
      await fs.writeFile(filePath, content, 'utf8');
    }
  }

  async generatePlatformConfig() {
    const configPath = path.join(this.buildDir, 'platform.config.js');
    const configContent = `window.PLATFORM_CONFIG = ${JSON.stringify(this.config, null, 2)};`;
    await fs.writeFile(configPath, configContent);
    console.log(`生成平台配置文件: ${configPath}`);
  }

  async compile() {
    console.log(`开始为平台 ${this.platform} 进行条件编译...`);

    try {
      await this.cleanBuildDir();
      await this.copyAndProcessFiles();

      console.log(`✅ 条件编译完成！输出目录: ${this.buildDir}`);
      return this.buildDir;
    } catch (error) {
      console.error('❌ 条件编译失败:', error);
      throw error;
    }
  }
}

// 命令行接口
if (require.main === module) {
  const platform = process.argv[2];

  if (!platform) {
    console.error('❌ 请指定平台名称，例如: node scripts/conditional-compile.js boe');
    console.error('可用平台:', platformConfig.getAllPlatforms().join(', '));
    process.exit(1);
  }

  if (!platformConfig.getAllPlatforms().includes(platform)) {
    console.error(`❌ 平台 ${platform} 未配置，可用平台:`, platformConfig.getAllPlatforms().join(', '));
    process.exit(1);
  }

  const compiler = new ConditionalCompiler(platform);
  compiler.compile().catch(() => process.exit(1));
}

module.exports = ConditionalCompiler;
