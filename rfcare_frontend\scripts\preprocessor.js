const fs = require('fs-extra');  // 统一使用 fs-extra
const path = require('path');

class ConditionalPreprocessor {
  constructor(platform) {
    this.platform = platform;
    this.platforms = [platform, 'common'];
  }

  // 处理 JS/TS 文件中的条件编译
  processJS(content) {
    const platformRegex = /\/\/\s*#ifdef\s+(\w+)([\s\S]*?)(?:\/\/\s*#else([\s\S]*?))?\/\/\s*#endif/g;
    const ifndefRegex = /\/\/\s*#ifndef\s+(\w+)([\s\S]*?)(?:\/\/\s*#else([\s\S]*?))?\/\/\s*#endif/g;

    let processed = content;

    // 处理 #ifdef
    processed = processed.replace(platformRegex, (match, condition, ifBlock, elseBlock) => {
      if (this.platforms.includes(condition)) {
        return ifBlock || '';
      }
      return elseBlock || '';
    });

    // 处理 #ifndef
    processed = processed.replace(ifndefRegex, (match, condition, ifBlock, elseBlock) => {
      if (!this.platforms.includes(condition)) {
        return ifBlock || '';
      }
      return elseBlock || '';
    });

    return processed;
  }

  // 处理 Vue 模板中的条件编译
  processTemplate(content) {
    const templateRegex = /<!--\s*#ifdef\s+(\w+)\s*-->([\s\S]*?)(?:<!--\s*#else\s*-->([\s\S]*?))?<!--\s*#endif\s*-->/g;
    const templateIfndefRegex = /<!--\s*#ifndef\s+(\w+)\s*-->([\s\S]*?)(?:<!--\s*#else\s*-->([\s\S]*?))?<!--\s*#endif\s*-->/g;

    let processed = content;

    processed = processed.replace(templateRegex, (match, condition, ifBlock, elseBlock) => {
      if (this.platforms.includes(condition)) {
        return ifBlock || '';
      }
      return elseBlock || '';
    });

    processed = processed.replace(templateIfndefRegex, (match, condition, ifBlock, elseBlock) => {
      if (!this.platforms.includes(condition)) {
        return ifBlock || '';
      }
      return elseBlock || '';
    });

    return processed;
  }

  // 处理 CSS 中的条件编译
  processCSS(content) {
    const cssRegex = /\/\*\s*#ifdef\s+(\w+)\s*\*\/([\s\S]*?)(?:\/\*\s*#else\s*\*\/([\s\S]*?))?\/\*\s*#endif\s*\*\//g;
    const cssIfndefRegex = /\/\*\s*#ifndef\s+(\w+)\s*\*\/([\s\S]*?)(?:\/\*\s*#else\s*\*\/([\s\S]*?))?\/\*\s*#endif\s*\*\//g;

    let processed = content;

    processed = processed.replace(cssRegex, (match, condition, ifBlock, elseBlock) => {
      if (this.platforms.includes(condition)) {
        return ifBlock || '';
      }
      return elseBlock || '';
    });

    processed = processed.replace(cssIfndefRegex, (match, condition, ifBlock, elseBlock) => {
      if (!this.platforms.includes(condition)) {
        return ifBlock || '';
      }
      return elseBlock || '';
    });

    return processed;
  }

  // 处理 Vue 单文件组件
  processVue(content) {
    const templateBlockRegex = /<template>([\s\S]*?)<\/template>/;
    const scriptBlockRegex = /<script[^>]*>([\s\S]*?)<\/script>/;
    const styleBlockRegex = /<style[^>]*>([\s\S]*?)<\/style>/g;

    let processed = content;

    // 处理 template
    if (templateBlockRegex.test(processed)) {
      processed = processed.replace(templateBlockRegex, (match, templateContent) => {
        return `<template>${this.processTemplate(templateContent)}</template>`;
      });
    }

    // 处理 script
    if (scriptBlockRegex.test(processed)) {
      processed = processed.replace(scriptBlockRegex, (match) => {
        const scriptContent = match.match(/<script[^>]*>([\s\S]*?)<\/script>/)[1];
        return match.replace(scriptContent, this.processJS(scriptContent));
      });
    }

    // 处理 style
    if (styleBlockRegex.test(processed)) {
      processed = processed.replace(styleBlockRegex, (match) => {
        const styleMatch = match.match(/<style[^>]*>([\s\S]*?)<\/style>/);
        if (styleMatch) {
          const styleContent = styleMatch[1];
          return match.replace(styleContent, this.processCSS(styleContent));
        }
        return match;
      });
    }

    return processed;
  }
}

module.exports = ConditionalPreprocessor;
